{"project_path": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check", "files": {"__init__.py": {"path": "__init__.py", "name": "__init__.py", "size": 845, "lines": 37, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "__main__.py": {"path": "__main__.py", "name": "__main__.py", "size": 199, "lines": 11, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/__init__.py": {"path": "ai/__init__.py", "name": "__init__.py", "size": 962, "lines": 27, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/explanation/__init__.py": {"path": "ai/explanation/__init__.py", "name": "__init__.py", "size": 508, "lines": 16, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/explanation/comment_analyzer.py": {"path": "ai/explanation/comment_analyzer.py", "name": "comment_analyzer.py", "size": 20510, "lines": 526, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 41, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'CommentType' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'CommentQuality' has only data, no behavior", "line": 30, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'extract_comments' has complexity 11, consider refactoring (max: 10)", "line": 107, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_calculate_quality_score' has complexity 16, consider refactoring (max: 10)", "line": 253, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_identify_issues' has complexity 11, consider refactoring (max: 10)", "line": 306, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 230, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 392, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/explanation/documentation_generator.py": {"path": "ai/explanation/documentation_generator.py", "name": "documentation_generator.py", "size": 22716, "lines": 636, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 41, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DocumentationFormat' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DocumentationStyle' has only data, no behavior", "line": 30, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze_code_elements' has complexity 12, consider refactoring (max: 10)", "line": 152, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 324, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/explanation/explanation_engine.py": {"path": "ai/explanation/explanation_engine.py", "name": "explanation_engine.py", "size": 19774, "lines": 496, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 40, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ExplanationLevel' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ExplanationType' has only data, no behavior", "line": 29, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze_code_structure' has complexity 13, consider refactoring (max: 10)", "line": 86, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'detect_patterns' has complexity 12, consider refactoring (max: 10)", "line": 140, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 230, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/infrastructure/__init__.py": {"path": "ai/infrastructure/__init__.py", "name": "__init__.py", "size": 706, "lines": 24, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/infrastructure/model_manager.py": {"path": "ai/infrastructure/model_manager.py", "name": "model_manager.py", "size": 18876, "lines": 517, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ModelType' has only data, no behavior", "line": 22, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ModelStatus' has only data, no behavior", "line": 31, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 510, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/infrastructure/model_optimizer.py": {"path": "ai/infrastructure/model_optimizer.py", "name": "model_optimizer.py", "size": 19619, "lines": 503, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'OptimizationStrategy' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'optimize_model_for_target' has complexity 11, consider refactoring (max: 10)", "line": 300, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/infrastructure/privacy_processor.py": {"path": "ai/infrastructure/privacy_processor.py", "name": "privacy_processor.py", "size": 14712, "lines": 422, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'PrivacyLevel' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/refactoring/__init__.py": {"path": "ai/refactoring/__init__.py", "name": "__init__.py", "size": 788, "lines": 26, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/refactoring/code_smell_detector.py": {"path": "ai/refactoring/code_smell_detector.py", "name": "code_smell_detector.py", "size": 24977, "lines": 575, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'CodeSmell' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'SmellSeverity' has only data, no behavior", "line": 44, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'detect_smells' has complexity 11, consider refactoring (max: 10)", "line": 363, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 375, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/refactoring/impact_analyzer.py": {"path": "ai/refactoring/impact_analyzer.py", "name": "impact_analyzer.py", "size": 27065, "lines": 648, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ImpactLevel' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ImpactType' has only data, no behavior", "line": 28, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze_dependencies' has complexity 17, consider refactoring (max: 10)", "line": 112, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 405, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/refactoring/pattern_recommender.py": {"path": "ai/refactoring/pattern_recommender.py", "name": "pattern_recommender.py", "size": 26047, "lines": 621, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DesignPattern' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'PatternCategory' has only data, no behavior", "line": 44, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze_patterns' has complexity 13, consider refactoring (max: 10)", "line": 429, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 441, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/refactoring/refactoring_engine.py": {"path": "ai/refactoring/refactoring_engine.py", "name": "refactoring_engine.py", "size": 26835, "lines": 603, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'RefactoringType' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'RefactoringSeverity' has only data, no behavior", "line": 37, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 281, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/temporal/__init__.py": {"path": "ai/temporal/__init__.py", "name": "__init__.py", "size": 793, "lines": 26, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/temporal/debt_predictor.py": {"path": "ai/temporal/debt_predictor.py", "name": "debt_predictor.py", "size": 22562, "lines": 553, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DebtCategory' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DebtSeverity' has only data, no behavior", "line": 32, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 426, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/temporal/productivity_analyzer.py": {"path": "ai/temporal/productivity_analyzer.py", "name": "productivity_analyzer.py", "size": 29710, "lines": 726, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 41, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ProductivityMetric' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DeveloperInsight' has only data, no behavior", "line": 34, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_generate_trend_insights' has complexity 11, consider refactoring (max: 10)", "line": 392, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 466, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/temporal/temporal_engine.py": {"path": "ai/temporal/temporal_engine.py", "name": "temporal_engine.py", "size": 23200, "lines": 580, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 40, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'EvolutionMetric' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AnalysisTimeframe' has only data, no behavior", "line": 35, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_generate_insights' has complexity 12, consider refactoring (max: 10)", "line": 457, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_generate_recommendations' has complexity 12, consider refactoring (max: 10)", "line": 494, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 137, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 397, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/temporal/trend_visualizer.py": {"path": "ai/temporal/trend_visualizer.py", "name": "trend_visualizer.py", "size": 22349, "lines": 640, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'VisualizationType' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'TrendDirection' has only data, no behavior", "line": 34, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 298, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 367, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/visualization/__init__.py": {"path": "ai/visualization/__init__.py", "name": "__init__.py", "size": 734, "lines": 26, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/visualization/dashboard_engine.py": {"path": "ai/visualization/dashboard_engine.py", "name": "dashboard_engine.py", "size": 27952, "lines": 778, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 36, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DashboardType' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'LayoutType' has only data, no behavior", "line": 33, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/visualization/data_aggregator.py": {"path": "ai/visualization/data_aggregator.py", "name": "data_aggregator.py", "size": 20080, "lines": 552, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 42, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AggregationType' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_aggregate_simple' has complexity 20, consider refactoring (max: 10)", "line": 273, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'create_time_series_aggregation' has complexity 13, consider refactoring (max: 10)", "line": 448, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'create_time_series_aggregation' has 6 arguments, consider reducing (max: 5)", "line": 448, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 216, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/visualization/interactive_charts.py": {"path": "ai/visualization/interactive_charts.py", "name": "interactive_charts.py", "size": 19728, "lines": 641, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ChartInteraction' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AnimationType' has only data, no behavior", "line": 34, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'create_interactive_chart' has 8 arguments, consider reducing (max: 5)", "line": 345, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ai/visualization/report_generator.py": {"path": "ai/visualization/report_generator.py", "name": "report_generator.py", "size": 25364, "lines": 712, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 40, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ReportFormat' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ReportStyle' has only data, no behavior", "line": 32, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'generate_report' has 6 arguments, consider reducing (max: 5)", "line": 444, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 470, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/__init__.py": {"path": "cli/__init__.py", "name": "__init__.py", "size": 346, "lines": 18, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/commands.py": {"path": "cli/commands.py", "name": "commands.py", "size": 29278, "lines": 692, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 43, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze_command' has complexity 50, consider refactoring (max: 10)", "line": 20, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'analyze_command' has 10 arguments, consider reducing (max: 5)", "line": 20, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'run_vcs_analysis' has complexity 12, consider refactoring (max: 10)", "line": 188, "column": 16, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 336, "column": 20, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B110", "name": "try_except_pass", "line": 336, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/completion.py": {"path": "cli/completion.py", "name": "completion.py", "size": 6856, "lines": 217, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 46, "column": 4, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B110", "name": "try_except_pass", "line": 46, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/error_handler.py": {"path": "cli/error_handler.py", "name": "error_handler.py", "size": 8331, "lines": 198, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'format_error_results' has complexity 17, consider refactoring (max: 10)", "line": 17, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'handle_analysis_error' has complexity 12, consider refactoring (max: 10)", "line": 98, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/format_tool.py": {"path": "cli/format_tool.py", "name": "format_tool.py", "size": 6268, "lines": 144, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'vibe_format' has complexity 25, consider refactoring (max: 10)", "line": 35, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'vibe_format' has 8 arguments, consider reducing (max: 5)", "line": 35, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'run_format' has complexity 23, consider refactoring (max: 10)", "line": 39, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/formatters.py": {"path": "cli/formatters.py", "name": "formatters.py", "size": 12522, "lines": 292, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 42, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_format_line_count' has complexity 12, consider refactoring (max: 10)", "line": 97, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_format_issue_count' has complexity 16, consider refactoring (max: 10)", "line": 150, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_format_generated_reports' has complexity 12, consider refactoring (max: 10)", "line": 260, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/handlers.py": {"path": "cli/handlers.py", "name": "handlers.py", "size": 11905, "lines": 301, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'handle_analyze_command' has complexity 23, consider refactoring (max: 10)", "line": 19, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'handle_analyze_command' has 18 arguments, consider reducing (max: 5)", "line": 19, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'handle_debug_command' has complexity 11, consider refactoring (max: 10)", "line": 235, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'handle_debug_command' has 7 arguments, consider reducing (max: 5)", "line": 235, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/knowledge_manager.py": {"path": "cli/knowledge_manager.py", "name": "knowledge_manager.py", "size": 11230, "lines": 323, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'add_rule' has 7 arguments, consider reducing (max: 5)", "line": 136, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'validate' has complexity 14, consider refactoring (max: 10)", "line": 275, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/main.py": {"path": "cli/main.py", "name": "main.py", "size": 11145, "lines": 286, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'analyze' has 18 arguments, consider reducing (max: 5)", "line": 46, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'debug' has 7 arguments, consider reducing (max: 5)", "line": 205, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/output_formats.py": {"path": "cli/output_formats.py", "name": "output_formats.py", "size": 10007, "lines": 290, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/parallel_processing.py": {"path": "cli/parallel_processing.py", "name": "parallel_processing.py", "size": 15206, "lines": 375, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze_files_parallel' has complexity 11, consider refactoring (max: 10)", "line": 35, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze_files_parallel' has complexity 11, consider refactoring (max: 10)", "line": 302, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/standalone.py": {"path": "cli/standalone.py", "name": "standalone.py", "size": 19197, "lines": 471, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 42, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'check' has complexity 15, consider refactoring (max: 10)", "line": 128, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'check' has 8 arguments, consider reducing (max: 5)", "line": 128, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'run_check' has complexity 15, consider refactoring (max: 10)", "line": 131, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'fix' has complexity 20, consider refactoring (max: 10)", "line": 227, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'run_fix' has complexity 20, consider refactoring (max: 10)", "line": 229, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/standalone_suite.py": {"path": "cli/standalone_suite.py", "name": "standalone_suite.py", "size": 11572, "lines": 259, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'vibe_check_standalone' has complexity 40, consider refactoring (max: 10)", "line": 43, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'vibe_check_standalone' has 12 arguments, consider reducing (max: 5)", "line": 43, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'run_analysis' has complexity 40, consider refactoring (max: 10)", "line": 48, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "cli/watch_mode.py": {"path": "cli/watch_mode.py", "name": "watch_mode.py", "size": 9654, "lines": 260, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "compat.py": {"path": "compat.py", "name": "compat.py", "size": 6749, "lines": 259, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B108", "name": "hardcoded_tmp_directory", "line": 135, "message": "Probable insecure usage of temp file/directory.", "severity": "MEDIUM", "confidence": "MEDIUM", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b108_hardcoded_tmp_directory.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/__init__.py": {"path": "core/__init__.py", "name": "__init__.py", "size": 1182, "lines": 49, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/__init__.py": {"path": "core/analysis/__init__.py", "name": "__init__.py", "size": 590, "lines": 23, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/dependency_analyzer.py": {"path": "core/analysis/dependency_analyzer.py", "name": "dependency_analyzer.py", "size": 28180, "lines": 697, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 43, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ImportInfo' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DependencyNode' has only data, no behavior", "line": 32, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'CircularDependency' has only data, no behavior", "line": 43, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DependencyAnalysisResult' has only data, no behavior", "line": 53, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Import' should use snake_case naming", "line": 71, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 83, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/file_analyzer.py": {"path": "core/analysis/file_analyzer.py", "name": "file_analyzer.py", "size": 5569, "lines": 161, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function '__init__' has 6 arguments, consider reducing (max: 5)", "line": 33, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/framework_detector.py": {"path": "core/analysis/framework_detector.py", "name": "framework_detector.py", "size": 17547, "lines": 421, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 40, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'FrameworkSignature' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'FrameworkDetection' has only data, no behavior", "line": 35, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'FrameworkAnalysisResult' has only data, no behavior", "line": 47, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_evaluate_framework' has complexity 23, consider refactoring (max: 10)", "line": 221, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Import' should use snake_case naming", "line": 372, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 378, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 387, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 398, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/framework_rules.py": {"path": "core/analysis/framework_rules.py", "name": "framework_rules.py", "size": 19628, "lines": 476, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'check' has complexity 12, consider refactoring (max: 10)", "line": 29, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/import_analyzer.py": {"path": "core/analysis/import_analyzer.py", "name": "import_analyzer.py", "size": 18024, "lines": 495, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ImportInfo' has only data, no behavior", "line": 30, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'CircularDependency' has only data, no behavior", "line": 42, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ImportAnalysisResult' has only data, no behavior", "line": 51, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 256, "column": 8, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B110", "name": "try_except_pass", "line": 256, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/import_visualizer.py": {"path": "core/analysis/import_visualizer.py", "name": "import_visualizer.py", "size": 2457, "lines": 68, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 20, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/meta_analyzer.py": {"path": "core/analysis/meta_analyzer.py", "name": "meta_analyzer.py", "size": 23713, "lines": 540, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 40, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "error", "message": "God class detected: 'MetaAnalyzer' has 21 methods", "line": 25, "column": 0, "suggestion": "Consider breaking this class into smaller, more focused classes"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/metrics_aggregator.py": {"path": "core/analysis/metrics_aggregator.py", "name": "metrics_aggregator.py", "size": 6314, "lines": 161, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 27, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/performance_optimizer.py": {"path": "core/analysis/performance_optimizer.py", "name": "performance_optimizer.py", "size": 16378, "lines": 436, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AnalysisCache' has only data, no behavior", "line": 23, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'PerformanceMetrics' has only data, no behavior", "line": 34, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'cache_result' has 6 arguments, consider reducing (max: 5)", "line": 79, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B403", "name": "blacklist", "line": 11, "message": "Consider possible security implications associated with pickle module.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b403-import-pickle"}, {"code": "B301", "name": "blacklist", "line": 103, "message": "Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.", "severity": "MEDIUM", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b301-pickle"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/project_analyzer.py": {"path": "core/analysis/project_analyzer.py", "name": "project_analyzer.py", "size": 8200, "lines": 193, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze_project' has complexity 16, consider refactoring (max: 10)", "line": 81, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/project_meritocracy_analyzer.py": {"path": "core/analysis/project_meritocracy_analyzer.py", "name": "project_meritocracy_analyzer.py", "size": 21275, "lines": 520, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 40, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ArchitecturalPattern' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'QualityIndicator' has only data, no behavior", "line": 32, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ProjectMeritocracyResult' has only data, no behavior", "line": 43, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_analyze_success_factors' has complexity 11, consider refactoring (max: 10)", "line": 424, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/python_semantic_analyzer.py": {"path": "core/analysis/python_semantic_analyzer.py", "name": "python_semantic_analyzer.py", "size": 18115, "lines": 499, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 40, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'SemanticContext' has only data, no behavior", "line": 18, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'SemanticIssue' has only data, no behavior", "line": 32, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'SemanticAnalysisResult' has only data, no behavior", "line": 44, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Import' should use snake_case naming", "line": 384, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 397, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 412, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 438, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 464, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Name' should use snake_case naming", "line": 490, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/python_version_analyzer.py": {"path": "core/analysis/python_version_analyzer.py", "name": "python_version_analyzer.py", "size": 14710, "lines": 375, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'VersionFeature' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'CompatibilityIssue' has only data, no behavior", "line": 31, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'VersionCompatibilityResult' has only data, no behavior", "line": 42, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_NamedExpr' should use snake_case naming", "line": 152, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 164, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_BinOp' should use snake_case naming", "line": 177, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Subscript' should use snake_case naming", "line": 187, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Match' should use snake_case naming", "line": 203, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/result_processor.py": {"path": "core/analysis/result_processor.py", "name": "result_processor.py", "size": 6473, "lines": 165, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/semantic_output_formatter.py": {"path": "core/analysis/semantic_output_formatter.py", "name": "semantic_output_formatter.py", "size": 17640, "lines": 409, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 36, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/semantic_rules.py": {"path": "core/analysis/semantic_rules.py", "name": "semantic_rules.py", "size": 16170, "lines": 453, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/standalone_analyzer.py": {"path": "core/analysis/standalone_analyzer.py", "name": "standalone_analyzer.py", "size": 15757, "lines": 421, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/tool_executor.py": {"path": "core/analysis/tool_executor.py", "name": "tool_executor.py", "size": 5114, "lines": 140, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 27, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/type_analyzer.py": {"path": "core/analysis/type_analyzer.py", "name": "type_analyzer.py", "size": 13990, "lines": 370, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'TypeInfo' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 101, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 126, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_analyze_function_args' has complexity 13, consider refactoring (max: 10)", "line": 149, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_all_parameters_typed' has complexity 12, consider refactoring (max: 10)", "line": 188, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'check' has complexity 11, consider refactoring (max: 10)", "line": 307, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/visualization/__init__.py": {"path": "core/analysis/visualization/__init__.py", "name": "__init__.py", "size": 1165, "lines": 47, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/visualization/chart_generators.py": {"path": "core/analysis/visualization/chart_generators.py", "name": "chart_generators.py", "size": 8376, "lines": 257, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/visualization/html_generators.py": {"path": "core/analysis/visualization/html_generators.py", "name": "html_generators.py", "size": 8971, "lines": 240, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/analysis/visualization/interactive_dashboard.py": {"path": "core/analysis/visualization/interactive_dashboard.py", "name": "interactive_dashboard.py", "size": 7934, "lines": 234, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 25, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/compatibility.py": {"path": "core/compatibility.py", "name": "compatibility.py", "size": 2290, "lines": 81, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 15, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/config.py": {"path": "core/config.py", "name": "config.py", "size": 7196, "lines": 229, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'validate_config' has complexity 15, consider refactoring (max: 10)", "line": 194, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/dependency_manager.py": {"path": "core/dependency_manager.py", "name": "dependency_manager.py", "size": 12945, "lines": 358, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DependencyStatus' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DependencyInfo' has only data, no behavior", "line": 30, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B404", "name": "blacklist", "line": 9, "message": "Consider possible security implications associated with the subprocess module.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess"}, {"code": "B603", "name": "subprocess_without_shell_equals_true", "line": 225, "message": "subprocess call - check for execution of untrusted input.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/docs/__init__.py": {"path": "core/docs/__init__.py", "name": "__init__.py", "size": 1128, "lines": 48, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/docs/templates.py": {"path": "core/docs/templates.py", "name": "templates.py", "size": 1492, "lines": 97, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/docs/utils.py": {"path": "core/docs/utils.py", "name": "utils.py", "size": 10097, "lines": 349, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'generate_function_docstring' has complexity 11, consider refactoring (max: 10)", "line": 104, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'generate_function_docstring' has 7 arguments, consider reducing (max: 5)", "line": 104, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'generate_async_function_docstring' has complexity 11, consider refactoring (max: 10)", "line": 175, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'generate_async_function_docstring' has 7 arguments, consider reducing (max: 5)", "line": 175, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'generate_file_header' has 6 arguments, consider reducing (max: 5)", "line": 311, "column": 0, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/error_handling.py": {"path": "core/error_handling.py", "name": "error_handling.py", "size": 9935, "lines": 320, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ConfigurationError' has only data, no behavior", "line": 41, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/error_handling/__init__.py": {"path": "core/error_handling/__init__.py", "name": "__init__.py", "size": 1125, "lines": 55, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/error_handling/decorators.py": {"path": "core/error_handling/decorators.py", "name": "decorators.py", "size": 4170, "lines": 126, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/error_handling/error_manager.py": {"path": "core/error_handling/error_manager.py", "name": "error_manager.py", "size": 3674, "lines": 115, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 25, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/error_handling/exceptions.py": {"path": "core/error_handling/exceptions.py", "name": "exceptions.py", "size": 5609, "lines": 193, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 13, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/error_handling/handlers.py": {"path": "core/error_handling/handlers.py", "name": "handlers.py", "size": 5650, "lines": 185, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/fs_utils.py": {"path": "core/fs_utils.py", "name": "fs_utils.py", "size": 7848, "lines": 285, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 36, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'find_files' has complexity 13, consider refactoring (max: 10)", "line": 98, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/knowledge/framework_knowledge_base.py": {"path": "core/knowledge/framework_knowledge_base.py", "name": "framework_knowledge_base.py", "size": 26263, "lines": 626, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'FrameworkRule' has only data, no behavior", "line": 22, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'FrameworkKnowledge' has only data, no behavior", "line": 52, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/knowledge/rule_engine.py": {"path": "core/knowledge/rule_engine.py", "name": "rule_engine.py", "size": 15741, "lines": 396, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 41, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'RuleExecutionContext' has only data, no behavior", "line": 22, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 218, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 224, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Call' should use snake_case naming", "line": 230, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Assign' should use snake_case naming", "line": 236, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_check_function_rule' has complexity 11, consider refactoring (max: 10)", "line": 285, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/logging.py": {"path": "core/logging.py", "name": "logging.py", "size": 7942, "lines": 261, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/logging/__init__.py": {"path": "core/logging/__init__.py", "name": "__init__.py", "size": 1032, "lines": 49, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/logging/contextual_logger.py": {"path": "core/logging/contextual_logger.py", "name": "contextual_logger.py", "size": 4356, "lines": 134, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 20, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/logging/correlation.py": {"path": "core/logging/correlation.py", "name": "correlation.py", "size": 6189, "lines": 214, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 26, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/logging/setup.py": {"path": "core/logging/setup.py", "name": "setup.py", "size": 2661, "lines": 94, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 22, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/logging/structured_logger.py": {"path": "core/logging/structured_logger.py", "name": "structured_logger.py", "size": 6970, "lines": 220, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 25, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'LogLevel' has only data, no behavior", "line": 18, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/models/__init__.py": {"path": "core/models/__init__.py", "name": "__init__.py", "size": 666, "lines": 23, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/models/directory_metrics.py": {"path": "core/models/directory_metrics.py", "name": "directory_metrics.py", "size": 10334, "lines": 286, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/models/file_metrics.py": {"path": "core/models/file_metrics.py", "name": "file_metrics.py", "size": 15733, "lines": 400, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '__init__' has complexity 12, consider refactoring (max: 10)", "line": 90, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function '__init__' has 33 arguments, consider reducing (max: 5)", "line": 90, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/models/progress_tracker.py": {"path": "core/models/progress_tracker.py", "name": "progress_tracker.py", "size": 19610, "lines": 566, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 31, "column": 0, "suggestion": "Add proper exception handling or at least log the exception"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 165, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B110", "name": "try_except_pass", "line": 165, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/models/project_metrics.py": {"path": "core/models/project_metrics.py", "name": "project_metrics.py", "size": 17774, "lines": 450, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "error", "message": "God class detected: 'ProjectMetrics' has 24 methods", "line": 19, "column": 0, "suggestion": "Consider breaking this class into smaller, more focused classes"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'from_dict' has complexity 17, consider refactoring (max: 10)", "line": 386, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/progress.py": {"path": "core/progress.py", "name": "progress.py", "size": 31533, "lines": 997, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ProgressState' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/simple_analyzer.py": {"path": "core/simple_analyzer.py", "name": "simple_analyzer.py", "size": 6027, "lines": 144, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'simple_analyze_project' has complexity 14, consider refactoring (max: 10)", "line": 31, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/trend_analysis/__init__.py": {"path": "core/trend_analysis/__init__.py", "name": "__init__.py", "size": 345, "lines": 16, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/trend_analysis/trend_analyzer.py": {"path": "core/trend_analysis/trend_analyzer.py", "name": "trend_analyzer.py", "size": 14128, "lines": 340, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 36, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_generate_trend_summary' has complexity 18, consider refactoring (max: 10)", "line": 223, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/trend_analysis/trend_storage.py": {"path": "core/trend_analysis/trend_storage.py", "name": "trend_storage.py", "size": 6893, "lines": 196, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 26, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/trend_analysis/trend_visualizer.py": {"path": "core/trend_analysis/trend_visualizer.py", "name": "trend_visualizer.py", "size": 16415, "lines": 476, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'visualize_trends' has complexity 12, consider refactoring (max: 10)", "line": 33, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function '_create_trend_chart' has 6 arguments, consider reducing (max: 5)", "line": 145, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/__init__.py": {"path": "core/utils/__init__.py", "name": "__init__.py", "size": 2650, "lines": 99, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/async_utils.py": {"path": "core/utils/async_utils.py", "name": "async_utils.py", "size": 8828, "lines": 264, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/config_utils.py": {"path": "core/utils/config_utils.py", "name": "config_utils.py", "size": 4409, "lines": 139, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 25, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/dict_utils.py": {"path": "core/utils/dict_utils.py", "name": "dict_utils.py", "size": 6188, "lines": 211, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/error_handling.py": {"path": "core/utils/error_handling.py", "name": "error_handling.py", "size": 3996, "lines": 127, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 19, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/error_utils.py": {"path": "core/utils/error_utils.py", "name": "error_utils.py", "size": 9011, "lines": 309, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/file_utils.py": {"path": "core/utils/file_utils.py", "name": "file_utils.py", "size": 4937, "lines": 175, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 54, "column": 4, "suggestion": "Add proper exception handling or at least log the exception"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'list_files' has complexity 13, consider refactoring (max: 10)", "line": 92, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/fs_utils.py": {"path": "core/utils/fs_utils.py", "name": "fs_utils.py", "size": 11307, "lines": 392, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 117, "column": 4, "suggestion": "Add proper exception handling or at least log the exception"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'list_files' has complexity 13, consider refactoring (max: 10)", "line": 168, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/gitignore_utils.py": {"path": "core/utils/gitignore_utils.py", "name": "gitignore_utils.py", "size": 2212, "lines": 75, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 24, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/preset_manager.py": {"path": "core/utils/preset_manager.py", "name": "preset_manager.py", "size": 3029, "lines": 113, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 21, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/report_utils.py": {"path": "core/utils/report_utils.py", "name": "report_utils.py", "size": 8264, "lines": 239, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 27, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/utils/tool_utils.py": {"path": "core/utils/tool_utils.py", "name": "tool_utils.py", "size": 5096, "lines": 145, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/__init__.py": {"path": "core/vcs/__init__.py", "name": "__init__.py", "size": 1213, "lines": 40, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/auto_fix.py": {"path": "core/vcs/auto_fix.py", "name": "auto_fix.py", "size": 12776, "lines": 345, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'FixResult' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/cache.py": {"path": "core/vcs/cache.py", "name": "cache.py", "size": 14603, "lines": 423, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B403", "name": "blacklist", "line": 11, "message": "Consider possible security implications associated with pickle module.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b403-import-pickle"}, {"code": "B301", "name": "blacklist", "line": 100, "message": "Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.", "severity": "MEDIUM", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b301-pickle"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/config.py": {"path": "core/vcs/config.py", "name": "config.py", "size": 11200, "lines": 283, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 36, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'from_dict' has complexity 19, consider refactoring (max: 10)", "line": 88, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 246, "column": 20, "suggestion": "Add proper exception handling or at least log the exception"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 251, "column": 20, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/dependency_tracker.py": {"path": "core/vcs/dependency_tracker.py", "name": "dependency_tracker.py", "size": 14406, "lines": 382, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/engine.py": {"path": "core/vcs/engine.py", "name": "engine.py", "size": 39650, "lines": 1103, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 48, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "error", "message": "God class detected: 'VibeCheckEngine' has 55 methods", "line": 47, "column": 0, "suggestion": "Consider breaking this class into smaller, more focused classes"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'initialize' has complexity 16, consider refactoring (max: 10)", "line": 116, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'stop' has complexity 14, consider refactoring (max: 10)", "line": 211, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'create_analysis_request' has 6 arguments, consider reducing (max: 5)", "line": 818, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/incremental_analysis.py": {"path": "core/vcs/incremental_analysis.py", "name": "incremental_analysis.py", "size": 12294, "lines": 332, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ChangeType' has only data, no behavior", "line": 23, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/integration/__init__.py": {"path": "core/vcs/integration/__init__.py", "name": "__init__.py", "size": 438, "lines": 18, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/integration/ecosystem_integrator.py": {"path": "core/vcs/integration/ecosystem_integrator.py", "name": "ecosystem_integrator.py", "size": 1137, "lines": 39, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 16, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/integration/integration_manager.py": {"path": "core/vcs/integration/integration_manager.py", "name": "integration_manager.py", "size": 12178, "lines": 311, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'perform_comprehensive_analysis' has complexity 11, consider refactoring (max: 10)", "line": 36, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'perform_comprehensive_analysis' has 6 arguments, consider reducing (max: 5)", "line": 36, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/integration/meta_analyzer.py": {"path": "core/vcs/integration/meta_analyzer.py", "name": "meta_analyzer.py", "size": 12802, "lines": 308, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_generate_recommendations' has complexity 11, consider refactoring (max: 10)", "line": 223, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/integration/unified_reporter.py": {"path": "core/vcs/integration/unified_reporter.py", "name": "unified_reporter.py", "size": 15801, "lines": 417, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'create_unified_report' has 6 arguments, consider reducing (max: 5)", "line": 58, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/memory_manager.py": {"path": "core/vcs/memory_manager.py", "name": "memory_manager.py", "size": 13403, "lines": 355, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'MemoryPressure' has only data, no behavior", "line": 31, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/models.py": {"path": "core/vcs/models.py", "name": "models.py", "size": 8339, "lines": 246, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 27, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'EngineMode' has only data, no behavior", "line": 16, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'RuleCategory' has only data, no behavior", "line": 22, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'IssueSeverity' has only data, no behavior", "line": 32, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/performance.py": {"path": "core/vcs/performance.py", "name": "performance.py", "size": 14490, "lines": 410, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'PerformanceMetric' has only data, no behavior", "line": 23, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AnalysisPerformance' has only data, no behavior", "line": 34, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/plugins/__init__.py": {"path": "core/vcs/plugins/__init__.py", "name": "__init__.py", "size": 473, "lines": 20, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/plugins/plugin_interface.py": {"path": "core/vcs/plugins/plugin_interface.py", "name": "plugin_interface.py", "size": 9105, "lines": 306, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/plugins/plugin_loader.py": {"path": "core/vcs/plugins/plugin_loader.py", "name": "plugin_loader.py", "size": 10183, "lines": 264, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/plugins/plugin_manager.py": {"path": "core/vcs/plugins/plugin_manager.py", "name": "plugin_manager.py", "size": 10696, "lines": 280, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/plugins/plugin_registry.py": {"path": "core/vcs/plugins/plugin_registry.py", "name": "plugin_registry.py", "size": 11850, "lines": 301, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "error", "message": "God class detected: 'PluginRegistry' has 23 methods", "line": 22, "column": 0, "suggestion": "Consider breaking this class into smaller, more focused classes"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/registry.py": {"path": "core/vcs/registry.py", "name": "registry.py", "size": 11855, "lines": 362, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function '__init__' has 6 arguments, consider reducing (max: 5)", "line": 30, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'create_issue' has 8 arguments, consider reducing (max: 5)", "line": 101, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/__init__.py": {"path": "core/vcs/rules/__init__.py", "name": "__init__.py", "size": 479, "lines": 19, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/advanced_python_rules.py": {"path": "core/vcs/rules/advanced_python_rules.py", "name": "advanced_python_rules.py", "size": 16266, "lines": 412, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/complexity_rules.py": {"path": "core/vcs/rules/complexity_rules.py", "name": "complexity_rules.py", "size": 14820, "lines": 350, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 34, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 47, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_If' should use snake_case naming", "line": 58, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_While' should use snake_case naming", "line": 65, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_For' should use snake_case naming", "line": 69, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Except<PERSON><PERSON><PERSON>' should use snake_case naming", "line": 73, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_With' should use snake_case naming", "line": 77, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Assert' should use snake_case naming", "line": 81, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_BoolOp' should use snake_case naming", "line": 85, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Compare' should use snake_case naming", "line": 90, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 125, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 150, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 198, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 202, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_If' should use snake_case naming", "line": 222, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_While' should use snake_case naming", "line": 225, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_For' should use snake_case naming", "line": 228, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_With' should use snake_case naming", "line": 231, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Try' should use snake_case naming", "line": 234, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Except<PERSON><PERSON><PERSON>' should use snake_case naming", "line": 237, "column": 20, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 278, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 302, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 330, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/documentation_rules.py": {"path": "core/vcs/rules/documentation_rules.py", "name": "documentation_rules.py", "size": 15225, "lines": 337, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 44, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 56, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 69, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze' has complexity 14, consider refactoring (max: 10)", "line": 91, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 96, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 102, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 105, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 283, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 318, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/framework_rules/__init__.py": {"path": "core/vcs/rules/framework_rules/__init__.py", "name": "__init__.py", "size": 468, "lines": 19, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/framework_rules/django_rules.py": {"path": "core/vcs/rules/framework_rules/django_rules.py", "name": "django_rules.py", "size": 12945, "lines": 326, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_check_setting_security' has complexity 13, consider refactoring (max: 10)", "line": 212, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/framework_rules/fastapi_rules.py": {"path": "core/vcs/rules/framework_rules/fastapi_rules.py", "name": "fastapi_rules.py", "size": 16065, "lines": 389, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/framework_rules/flask_rules.py": {"path": "core/vcs/rules/framework_rules/flask_rules.py", "name": "flask_rules.py", "size": 14170, "lines": 351, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze' has complexity 13, consider refactoring (max: 10)", "line": 79, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze' has complexity 12, consider refactoring (max: 10)", "line": 133, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze' has complexity 11, consider refactoring (max: 10)", "line": 245, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/framework_rules/framework_detector.py": {"path": "core/vcs/rules/framework_rules/framework_detector.py", "name": "framework_detector.py", "size": 12563, "lines": 322, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'FrameworkType' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_detect_from_ast' has complexity 13, consider refactoring (max: 10)", "line": 148, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/import_rules.py": {"path": "core/vcs/rules/import_rules.py", "name": "import_rules.py", "size": 15739, "lines": 382, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Import' should use snake_case naming", "line": 39, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 44, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Name' should use snake_case naming", "line": 53, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Attribute' should use snake_case naming", "line": 56, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Import' should use snake_case naming", "line": 121, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 126, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 206, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 242, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 287, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Import' should use snake_case naming", "line": 304, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Import' should use snake_case naming", "line": 345, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ImportFrom' should use snake_case naming", "line": 348, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/performance_rules.py": {"path": "core/vcs/rules/performance_rules.py", "name": "performance_rules.py", "size": 15961, "lines": 400, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_check_function_calls' has complexity 12, consider refactoring (max: 10)", "line": 157, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/rule_loader.py": {"path": "core/vcs/rules/rule_loader.py", "name": "rule_loader.py", "size": 14467, "lines": 445, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'load_framework_rules' has complexity 11, consider refactoring (max: 10)", "line": 355, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/security_rules.py": {"path": "core/vcs/rules/security_rules.py", "name": "security_rules.py", "size": 13419, "lines": 298, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 27, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze' has complexity 11, consider refactoring (max: 10)", "line": 73, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_BinOp' should use snake_case naming", "line": 78, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Call' should use snake_case naming", "line": 92, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Call' should use snake_case naming", "line": 150, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Call' should use snake_case naming", "line": 207, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Call' should use snake_case naming", "line": 270, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/style_rules.py": {"path": "core/vcs/rules/style_rules.py", "name": "style_rules.py", "size": 11730, "lines": 300, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 151, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 163, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Name' should use snake_case naming", "line": 175, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze' has complexity 13, consider refactoring (max: 10)", "line": 260, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/rules/type_rules.py": {"path": "core/vcs/rules/type_rules.py", "name": "type_rules.py", "size": 21121, "lines": 487, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 33, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 69, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 101, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 140, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze' has complexity 13, consider refactoring (max: 10)", "line": 177, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 182, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 212, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 266, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 276, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 323, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 337, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'analyze' has complexity 16, consider refactoring (max: 10)", "line": 381, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 386, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 402, "column": 12, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/vcs/type_checking.py": {"path": "core/vcs/type_checking.py", "name": "type_checking.py", "size": 14228, "lines": 335, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B404", "name": "blacklist", "line": 9, "message": "Consider possible security implications associated with the subprocess module.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess"}, {"code": "B603", "name": "subprocess_without_shell_equals_true", "line": 250, "message": "subprocess call - check for execution of untrusted input.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html"}, {"code": "B603", "name": "subprocess_without_shell_equals_true", "line": 266, "message": "subprocess call - check for execution of untrusted input.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "core/version.py": {"path": "core/version.py", "name": "version.py", "size": 125, "lines": 8, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/__init__.py": {"path": "enterprise/__init__.py", "name": "__init__.py", "size": 704, "lines": 25, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/api/__init__.py": {"path": "enterprise/api/__init__.py", "name": "__init__.py", "size": 716, "lines": 30, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/api/graphql.py": {"path": "enterprise/api/graphql.py", "name": "graphql.py", "size": 17918, "lines": 523, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "error", "message": "God class detected: 'GraphQLServer' has 22 methods", "line": 20, "column": 0, "suggestion": "Consider breaking this class into smaller, more focused classes"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/api/models.py": {"path": "enterprise/api/models.py", "name": "models.py", "size": 8741, "lines": 289, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 13, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'APIMethod' has only data, no behavior", "line": 15, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'APIStatus' has only data, no behavior", "line": 24, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'WebSocketMessageType' has only data, no behavior", "line": 31, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/api/rest.py": {"path": "enterprise/api/rest.py", "name": "rest.py", "size": 18201, "lines": 500, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "error", "message": "God class detected: 'RestAPIServer' has 23 methods", "line": 23, "column": 0, "suggestion": "Consider breaking this class into smaller, more focused classes"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function '_register_endpoint' has 6 arguments, consider reducing (max: 5)", "line": 175, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 220, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/api/unified.py": {"path": "enterprise/api/unified.py", "name": "unified.py", "size": 14671, "lines": 390, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/api/websocket.py": {"path": "enterprise/api/websocket.py", "name": "websocket.py", "size": 13872, "lines": 369, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 124, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/cicd/__init__.py": {"path": "enterprise/cicd/__init__.py", "name": "__init__.py", "size": 892, "lines": 31, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/cicd/azure_devops.py": {"path": "enterprise/cicd/azure_devops.py", "name": "azure_devops.py", "size": 18868, "lines": 549, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/cicd/github_actions.py": {"path": "enterprise/cicd/github_actions.py", "name": "github_actions.py", "size": 12919, "lines": 368, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/cicd/gitlab_ci.py": {"path": "enterprise/cicd/gitlab_ci.py", "name": "gitlab_ci.py", "size": 14355, "lines": 436, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/cicd/jenkins.py": {"path": "enterprise/cicd/jenkins.py", "name": "jenkins.py", "size": 14643, "lines": 413, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B405", "name": "blacklist", "line": 8, "message": "Using xml.etree.ElementTree to parse untrusted XML data is known to be vulnerable to XML attacks. Replace xml.etree.ElementTree with the equivalent defusedxml package, or make sure defusedxml.defuse_stdlib() is called.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b405-import-xml-etree"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/cicd/manager.py": {"path": "enterprise/cicd/manager.py", "name": "manager.py", "size": 16007, "lines": 409, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/cicd/models.py": {"path": "enterprise/cicd/models.py", "name": "models.py", "size": 9277, "lines": 274, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 18, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'CICDPlatform' has only data, no behavior", "line": 15, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'PipelineStatus' has only data, no behavior", "line": 25, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'BuildResult' has only data, no behavior", "line": 35, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'QualityGateStatus' has only data, no behavior", "line": 43, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/collaboration.py": {"path": "enterprise/collaboration.py", "name": "collaboration.py", "size": 29281, "lines": 875, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 43, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'UserRole' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'TeamPermission' has only data, no behavior", "line": 29, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'add_team_member' has 7 arguments, consider reducing (max: 5)", "line": 361, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'save_configuration' has 6 arguments, consider reducing (max: 5)", "line": 628, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/collaboration/__init__.py": {"path": "enterprise/collaboration/__init__.py", "name": "__init__.py", "size": 761, "lines": 31, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/collaboration/analysis.py": {"path": "enterprise/collaboration/analysis.py", "name": "analysis.py", "size": 15815, "lines": 457, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AnalysisStatus' has only data, no behavior", "line": 22, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AnalysisPriority' has only data, no behavior", "line": 31, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'create_analysis_request' has 11 arguments, consider reducing (max: 5)", "line": 247, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'add_comment' has 8 arguments, consider reducing (max: 5)", "line": 332, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/collaboration/shared_config.py": {"path": "enterprise/collaboration/shared_config.py", "name": "shared_config.py", "size": 12357, "lines": 347, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'save_configuration' has 6 arguments, consider reducing (max: 5)", "line": 100, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/collaboration/team_manager.py": {"path": "enterprise/collaboration/team_manager.py", "name": "team_manager.py", "size": 12811, "lines": 365, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 36, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'UserRole' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'TeamPermission' has only data, no behavior", "line": 29, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/dashboard/__init__.py": {"path": "enterprise/dashboard/__init__.py", "name": "__init__.py", "size": 880, "lines": 32, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/dashboard/components.py": {"path": "enterprise/dashboard/components.py", "name": "components.py", "size": 18927, "lines": 504, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'get_data' has complexity 15, consider refactoring (max: 10)", "line": 83, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/dashboard/models.py": {"path": "enterprise/dashboard/models.py", "name": "models.py", "size": 7823, "lines": 259, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 6, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'DashboardTheme' has only data, no behavior", "line": 15, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ChartType' has only data, no behavior", "line": 22, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'WidgetSize' has only data, no behavior", "line": 34, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/dashboard/static_assets.py": {"path": "enterprise/dashboard/static_assets.py", "name": "static_assets.py", "size": 11671, "lines": 487, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 22, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/dashboard/templates.py": {"path": "enterprise/dashboard/templates.py", "name": "templates.py", "size": 21406, "lines": 778, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/dashboard/web_server.py": {"path": "enterprise/dashboard/web_server.py", "name": "web_server.py", "size": 20524, "lines": 519, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_get_metrics_card_data' has complexity 11, consider refactoring (max: 10)", "line": 367, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/integration.py": {"path": "enterprise/integration.py", "name": "integration.py", "size": 864, "lines": 30, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 12, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/monitoring.py": {"path": "enterprise/monitoring.py", "name": "monitoring.py", "size": 1825, "lines": 62, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 18, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/monitoring/__init__.py": {"path": "enterprise/monitoring/__init__.py", "name": "__init__.py", "size": 996, "lines": 35, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/monitoring/alerting.py": {"path": "enterprise/monitoring/alerting.py", "name": "alerting.py", "size": 19258, "lines": 536, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 533, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/monitoring/dashboards.py": {"path": "enterprise/monitoring/dashboards.py", "name": "dashboards.py", "size": 19785, "lines": 530, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 40, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_get_system_health_data' has complexity 13, consider refactoring (max: 10)", "line": 170, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 285, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/monitoring/models.py": {"path": "enterprise/monitoring/models.py", "name": "models.py", "size": 9681, "lines": 300, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AlertSeverity' has only data, no behavior", "line": 15, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AlertStatus' has only data, no behavior", "line": 23, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'QualityGateStatus' has only data, no behavior", "line": 31, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'MetricType' has only data, no behavior", "line": 40, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ThresholdOperator' has only data, no behavior", "line": 48, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/monitoring/monitoring.py": {"path": "enterprise/monitoring/monitoring.py", "name": "monitoring.py", "size": 17443, "lines": 486, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'record_metric' has 6 arguments, consider reducing (max: 5)", "line": 53, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 211, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/monitoring/quality_gates.py": {"path": "enterprise/monitoring/quality_gates.py", "name": "quality_gates.py", "size": 19813, "lines": 515, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'create_gate' has 6 arguments, consider reducing (max: 5)", "line": 378, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B307", "name": "blacklist", "line": 224, "message": "Use of possibly insecure function - consider using safer ast.literal_eval.", "severity": "MEDIUM", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b307-eval"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/performance/__init__.py": {"path": "enterprise/performance/__init__.py", "name": "__init__.py", "size": 1018, "lines": 30, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/performance/cache_manager.py": {"path": "enterprise/performance/cache_manager.py", "name": "cache_manager.py", "size": 13546, "lines": 404, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'CacheStrategy' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 400, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B324", "name": "<PERSON><PERSON><PERSON>", "line": 239, "message": "Use of weak MD5 hash for security. Consider usedforsecurity=False", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b324_hashlib.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/performance/distributed_processor.py": {"path": "enterprise/performance/distributed_processor.py", "name": "distributed_processor.py", "size": 15768, "lines": 450, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 37, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'TaskStatus' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'NodeStatus' has only data, no behavior", "line": 30, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 447, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/performance/load_balancer.py": {"path": "enterprise/performance/load_balancer.py", "name": "load_balancer.py", "size": 1791, "lines": 59, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 16, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'LoadBalancingStrategy' has only data, no behavior", "line": 17, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/performance/performance_optimizer.py": {"path": "enterprise/performance/performance_optimizer.py", "name": "performance_optimizer.py", "size": 15383, "lines": 376, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'OptimizationProfile' has only data, no behavior", "line": 21, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_auto_optimize' has complexity 12, consider refactoring (max: 10)", "line": 221, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 359, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 366, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/performance/scalability_manager.py": {"path": "enterprise/performance/scalability_manager.py", "name": "scalability_manager.py", "size": 2222, "lines": 71, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 18, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/reporting/__init__.py": {"path": "enterprise/reporting/__init__.py", "name": "__init__.py", "size": 863, "lines": 30, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/reporting/customization.py": {"path": "enterprise/reporting/customization.py", "name": "customization.py", "size": 3970, "lines": 109, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 25, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/reporting/engine.py": {"path": "enterprise/reporting/engine.py", "name": "engine.py", "size": 16588, "lines": 448, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ReportFormat' has only data, no behavior", "line": 22, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ReportType' has only data, no behavior", "line": 32, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/reporting/executive.py": {"path": "enterprise/reporting/executive.py", "name": "executive.py", "size": 16505, "lines": 434, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'RiskLevel' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'BusinessImpact' has only data, no behavior", "line": 28, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_assess_risks' has complexity 15, consider refactoring (max: 10)", "line": 255, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/reporting/formats.py": {"path": "enterprise/reporting/formats.py", "name": "formats.py", "size": 15828, "lines": 448, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/reporting/templates.py": {"path": "enterprise/reporting/templates.py", "name": "templates.py", "size": 14477, "lines": 391, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'TemplateType' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/security/__init__.py": {"path": "enterprise/security/__init__.py", "name": "__init__.py", "size": 1140, "lines": 36, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/security/access_control.py": {"path": "enterprise/security/access_control.py", "name": "access_control.py", "size": 15627, "lines": 475, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'Permission' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/security/audit_trail.py": {"path": "enterprise/security/audit_trail.py", "name": "audit_trail.py", "size": 15439, "lines": 470, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AuditEventType' has only data, no behavior", "line": 20, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AuditSeverity' has only data, no behavior", "line": 43, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'search_events' has complexity 17, consider refactoring (max: 10)", "line": 138, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'search_events' has 9 arguments, consider reducing (max: 5)", "line": 138, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'log_user_event' has 12 arguments, consider reducing (max: 5)", "line": 295, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'log_system_event' has 7 arguments, consider reducing (max: 5)", "line": 347, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'log_api_call' has 10 arguments, consider reducing (max: 5)", "line": 385, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'log_analysis_event' has 7 arguments, consider reducing (max: 5)", "line": 415, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/security/compliance_manager.py": {"path": "enterprise/security/compliance_manager.py", "name": "compliance_manager.py", "size": 16723, "lines": 409, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 31, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ComplianceFramework' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ComplianceStatus' has only data, no behavior", "line": 29, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'update_control_status' has 6 arguments, consider reducing (max: 5)", "line": 205, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/security/encryption_manager.py": {"path": "enterprise/security/encryption_manager.py", "name": "encryption_manager.py", "size": 3133, "lines": 103, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'EncryptionAlgorithm' has only data, no behavior", "line": 18, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "enterprise/security/security_monitor.py": {"path": "enterprise/security/security_monitor.py", "name": "security_monitor.py", "size": 11595, "lines": 336, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 32, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'ThreatLevel' has only data, no behavior", "line": 19, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AlertType' has only data, no behavior", "line": 27, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "too_many_arguments", "severity": "warning", "message": "Function 'create_alert' has 9 arguments, consider reducing (max: 5)", "line": 156, "column": 4, "suggestion": "Consider using a configuration object or breaking down the function"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "plugins/__init__.py": {"path": "plugins/__init__.py", "name": "__init__.py", "size": 693, "lines": 26, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "plugins/base_plugin.py": {"path": "plugins/base_plugin.py", "name": "base_plugin.py", "size": 336, "lines": 17, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "plugins/manager.py": {"path": "plugins/manager.py", "name": "manager.py", "size": 16227, "lines": 536, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 42, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_scan_module_for_plugins' has complexity 12, consider refactoring (max: 10)", "line": 143, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 167, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 180, "column": 12, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B110", "name": "try_except_pass", "line": 167, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}, {"code": "B110", "name": "try_except_pass", "line": 180, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "plugins/plugin_base.py": {"path": "plugins/plugin_base.py", "name": "plugin_base.py", "size": 5924, "lines": 246, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 25, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "plugins/plugin_interface.py": {"path": "plugins/plugin_interface.py", "name": "plugin_interface.py", "size": 4624, "lines": 194, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "plugins/plugin_registry.py": {"path": "plugins/plugin_registry.py", "name": "plugin_registry.py", "size": 7537, "lines": 256, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/custom_rules/__init__.py": {"path": "tools/custom_rules/__init__.py", "name": "__init__.py", "size": 193, "lines": 12, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/custom_rules/python_rules.py": {"path": "tools/custom_rules/python_rules.py", "name": "python_rules.py", "size": 11350, "lines": 328, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 36, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 85, "column": 8, "suggestion": "Add proper exception handling or at least log the exception"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_check_resource_management' has complexity 11, consider refactoring (max: 10)", "line": 153, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/__init__.py": {"path": "tools/parsers/__init__.py", "name": "__init__.py", "size": 1047, "lines": 35, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/bandit_parser.py": {"path": "tools/parsers/bandit_parser.py", "name": "bandit_parser.py", "size": 5924, "lines": 185, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 24, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/base_parser.py": {"path": "tools/parsers/base_parser.py", "name": "base_parser.py", "size": 2221, "lines": 81, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 20, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/complexity_parser.py": {"path": "tools/parsers/complexity_parser.py", "name": "complexity_parser.py", "size": 6960, "lines": 191, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 26, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'parse' has complexity 12, consider refactoring (max: 10)", "line": 30, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/custom_rules_parser.py": {"path": "tools/parsers/custom_rules_parser.py", "name": "custom_rules_parser.py", "size": 4943, "lines": 158, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 22, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/doc_analyzer_parser.py": {"path": "tools/parsers/doc_analyzer_parser.py", "name": "doc_analyzer_parser.py", "size": 9804, "lines": 259, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'parse' has complexity 27, consider refactoring (max: 10)", "line": 30, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/mypy_parser.py": {"path": "tools/parsers/mypy_parser.py", "name": "mypy_parser.py", "size": 5229, "lines": 171, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/parser_registry.py": {"path": "tools/parsers/parser_registry.py", "name": "parser_registry.py", "size": 1684, "lines": 64, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 14, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/pyflakes_parser.py": {"path": "tools/parsers/pyflakes_parser.py", "name": "pyflakes_parser.py", "size": 4949, "lines": 158, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 25, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/pylint_parser.py": {"path": "tools/parsers/pylint_parser.py", "name": "pylint_parser.py", "size": 4587, "lines": 151, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 22, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/parsers/ruff_parser.py": {"path": "tools/parsers/ruff_parser.py", "name": "ruff_parser.py", "size": 5639, "lines": 187, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 21, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/__init__.py": {"path": "tools/runners/__init__.py", "name": "__init__.py", "size": 1131, "lines": 38, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/bandit_runner.py": {"path": "tools/runners/bandit_runner.py", "name": "bandit_runner.py", "size": 6546, "lines": 167, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 27, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'run' has complexity 11, consider refactoring (max: 10)", "line": 35, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/base_runner.py": {"path": "tools/runners/base_runner.py", "name": "base_runner.py", "size": 6631, "lines": 215, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 184, "column": 16, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B404", "name": "blacklist", "line": 10, "message": "Consider possible security implications associated with the subprocess module.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess"}, {"code": "B110", "name": "try_except_pass", "line": 184, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/complexity_runner.py": {"path": "tools/runners/complexity_runner.py", "name": "complexity_runner.py", "size": 11211, "lines": 362, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 35, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 75, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 84, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_If' should use snake_case naming", "line": 104, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_For' should use snake_case naming", "line": 116, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFor' should use snake_case naming", "line": 128, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_While' should use snake_case naming", "line": 140, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Try' should use snake_case naming", "line": 152, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_With' should use snake_case naming", "line": 169, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncWith' should use snake_case naming", "line": 180, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_BoolOp' should use snake_case naming", "line": 191, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/custom_rules_runner.py": {"path": "tools/runners/custom_rules_runner.py", "name": "custom_rules_runner.py", "size": 2912, "lines": 101, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 19, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/doc_analyzer_runner.py": {"path": "tools/runners/doc_analyzer_runner.py", "name": "doc_analyzer_runner.py", "size": 11853, "lines": 345, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_Module' should use snake_case naming", "line": 36, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_ClassDef' should use snake_case naming", "line": 62, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_FunctionDef' should use snake_case naming", "line": 100, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_naming", "severity": "info", "message": "Function 'visit_AsyncFunctionDef' should use snake_case naming", "line": 133, "column": 4, "suggestion": "Use snake_case for function names (e.g., my_function)"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'run' has complexity 17, consider refactoring (max: 10)", "line": 204, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/mypy_runner.py": {"path": "tools/runners/mypy_runner.py", "name": "mypy_runner.py", "size": 8320, "lines": 226, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 208, "column": 24, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/pyflakes_runner.py": {"path": "tools/runners/pyflakes_runner.py", "name": "pyflakes_runner.py", "size": 4647, "lines": 135, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 26, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/pylint_runner.py": {"path": "tools/runners/pylint_runner.py", "name": "pylint_runner.py", "size": 7813, "lines": 204, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/ruff_runner.py": {"path": "tools/runners/ruff_runner.py", "name": "ruff_runner.py", "size": 7542, "lines": 209, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "empty_except", "severity": "error", "message": "Empty except block silently ignores exceptions", "line": 188, "column": 8, "suggestion": "Add proper exception handling or at least log the exception"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B110", "name": "try_except_pass", "line": 188, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tools/runners/tool_registry.py": {"path": "tools/runners/tool_registry.py", "name": "tool_registry.py", "size": 2923, "lines": 119, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 22, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/cli/__init__.py": {"path": "ui/cli/__init__.py", "name": "__init__.py", "size": 524, "lines": 20, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/cli/commands.py": {"path": "ui/cli/commands.py", "name": "commands.py", "size": 4468, "lines": 142, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/cli/formatter.py": {"path": "ui/cli/formatter.py", "name": "formatter.py", "size": 7670, "lines": 258, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/gui/__init__.py": {"path": "ui/gui/__init__.py", "name": "__init__.py", "size": 491, "lines": 22, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/gui/app.py": {"path": "ui/gui/app.py", "name": "app.py", "size": 1607, "lines": 67, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 15, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/gui/main_window.py": {"path": "ui/gui/main_window.py", "name": "main_window.py", "size": 12802, "lines": 364, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "error", "message": "God class detected: 'VibeCheckGUI' has 21 methods", "line": 22, "column": 0, "suggestion": "Consider breaking this class into smaller, more focused classes"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/gui/simple_gui.py": {"path": "ui/gui/simple_gui.py", "name": "simple_gui.py", "size": 16257, "lines": 463, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B404", "name": "blacklist", "line": 15, "message": "Consider possible security implications associated with the subprocess module.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess"}, {"code": "B603", "name": "subprocess_without_shell_equals_true", "line": 332, "message": "subprocess call - check for execution of untrusted input.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/gui/themes.py": {"path": "ui/gui/themes.py", "name": "themes.py", "size": 8422, "lines": 325, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/reporting/__init__.py": {"path": "ui/reporting/__init__.py", "name": "__init__.py", "size": 885, "lines": 34, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/reporting/custom_report_generator.py": {"path": "ui/reporting/custom_report_generator.py", "name": "custom_report_generator.py", "size": 7272, "lines": 185, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_create_json_report' has complexity 11, consider refactoring (max: 10)", "line": 119, "column": 4, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/reporting/formatters.py": {"path": "ui/reporting/formatters.py", "name": "formatters.py", "size": 5040, "lines": 159, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'format_analysis_results' has complexity 12, consider refactoring (max: 10)", "line": 97, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/reporting/generators.py": {"path": "ui/reporting/generators.py", "name": "generators.py", "size": 5713, "lines": 192, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 28, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function '_generate_top_issues' has complexity 11, consider refactoring (max: 10)", "line": 118, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/reporting/markdown.py": {"path": "ui/reporting/markdown.py", "name": "markdown.py", "size": 3682, "lines": 141, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/reporting/report_generator.py": {"path": "ui/reporting/report_generator.py", "name": "report_generator.py", "size": 17925, "lines": 577, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 22, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/reporting/templates.py": {"path": "ui/reporting/templates.py", "name": "templates.py", "size": 2436, "lines": 99, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/__init__.py": {"path": "ui/tui/__init__.py", "name": "__init__.py", "size": 600, "lines": 22, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/app.py": {"path": "ui/tui/app.py", "name": "app.py", "size": 6573, "lines": 256, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 35, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'handle_input' has complexity 16, consider refactoring (max: 10)", "line": 43, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B605", "name": "start_process_with_a_shell", "line": 102, "message": "Starting a process with a shell, possible injection detected, security issue.", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b605_start_process_with_a_shell.html"}, {"code": "B605", "name": "start_process_with_a_shell", "line": 208, "message": "Starting a process with a shell, possible injection detected, security issue.", "severity": "HIGH", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b605_start_process_with_a_shell.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/components.py": {"path": "ui/tui/components.py", "name": "components.py", "size": 2304, "lines": 70, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 21, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/config_components.py": {"path": "ui/tui/config_components.py", "name": "config_components.py", "size": 3425, "lines": 107, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 20, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/header_footer.py": {"path": "ui/tui/header_footer.py", "name": "header_footer.py", "size": 1998, "lines": 78, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 20, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/menu_components.py": {"path": "ui/tui/menu_components.py", "name": "menu_components.py", "size": 2500, "lines": 102, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 20, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/progress_components.py": {"path": "ui/tui/progress_components.py", "name": "progress_components.py", "size": 3001, "lines": 105, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 24, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/results_components.py": {"path": "ui/tui/results_components.py", "name": "results_components.py", "size": 10714, "lines": 309, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 34, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'render_issues_view' has complexity 13, consider refactoring (max: 10)", "line": 141, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/tui/state_manager.py": {"path": "ui/tui/state_manager.py", "name": "state_manager.py", "size": 7311, "lines": 269, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'TUIScreen' has only data, no behavior", "line": 22, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AnalysisState' has only data, no behavior", "line": 33, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/visualization/__init__.py": {"path": "ui/visualization/__init__.py", "name": "__init__.py", "size": 1609, "lines": 51, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/visualization/charts.py": {"path": "ui/visualization/charts.py", "name": "charts.py", "size": 7121, "lines": 284, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 21, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/visualization/exporters.py": {"path": "ui/visualization/exporters.py", "name": "exporters.py", "size": 3648, "lines": 135, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 18, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/visualization/generators.py": {"path": "ui/visualization/generators.py", "name": "generators.py", "size": 12676, "lines": 338, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 24, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/visualization/interactive_charts.py": {"path": "ui/visualization/interactive_charts.py", "name": "interactive_charts.py", "size": 31338, "lines": 1080, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'create_import_dependency_graph' has complexity 17, consider refactoring (max: 10)", "line": 703, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/visualization/visualization_generator.py": {"path": "ui/visualization/visualization_generator.py", "name": "visualization_generator.py", "size": 9063, "lines": 282, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 27, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/web/__init__.py": {"path": "ui/web/__init__.py", "name": "__init__.py", "size": 567, "lines": 23, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/web/app.py": {"path": "ui/web/app.py", "name": "app.py", "size": 6627, "lines": 218, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'create_app' has complexity 13, consider refactoring (max: 10)", "line": 30, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/web/components.py": {"path": "ui/web/components.py", "name": "components.py", "size": 20335, "lines": 631, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'render_issues' has complexity 13, consider refactoring (max: 10)", "line": 203, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"tool": "semantic_analyzer", "rule": "function_complexity", "severity": "warning", "message": "Function 'render_dependency_graph' has complexity 11, consider refactoring (max: 10)", "line": 487, "column": 0, "suggestion": "Break down the function into smaller, more focused functions"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/web/run_web_ui.py": {"path": "ui/web/run_web_ui.py", "name": "run_web_ui.py", "size": 2101, "lines": 84, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 6, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "ui/web/state_manager.py": {"path": "ui/web/state_manager.py", "name": "state_manager.py", "size": 8146, "lines": 265, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 29, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'AnalysisState' has only data, no behavior", "line": 26, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"tool": "semantic_analyzer", "rule": "architectural_smell", "severity": "info", "message": "Potential anemic model: 'UIState' has only data, no behavior", "line": 35, "column": 0, "suggestion": "Consider adding behavior methods to this class"}, {"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}}, "directories": {"": {"path": "", "files": ["__init__.py", "__main__.py", "compat.py"], "total_lines": 307, "avg_lines": 102.33333333333333, "max_file_lines": 259, "max_file": "compat.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 10.666666666666666, "_max_complexity": 32, "_issue_count": 4}, "ai": {"path": "ai", "files": ["ai/__init__.py"], "total_lines": 27, "avg_lines": 27.0, "max_file_lines": 27, "max_file": "ai/__init__.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 0.0, "_max_complexity": 0, "_issue_count": 1}, "ai/explanation": {"path": "ai/explanation", "files": ["ai/explanation/__init__.py", "ai/explanation/comment_analyzer.py", "ai/explanation/documentation_generator.py", "ai/explanation/explanation_engine.py"], "total_lines": 1674, "avg_lines": 418.5, "max_file_lines": 636, "max_file": "ai/explanation/documentation_generator.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 30.5, "_max_complexity": 41, "_issue_count": 20}, "ai/infrastructure": {"path": "ai/infrastructure", "files": ["ai/infrastructure/__init__.py", "ai/infrastructure/model_manager.py", "ai/infrastructure/model_optimizer.py", "ai/infrastructure/privacy_processor.py"], "total_lines": 1466, "avg_lines": 366.5, "max_file_lines": 517, "max_file": "ai/infrastructure/model_manager.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 27.75, "_max_complexity": 39, "_issue_count": 10}, "ai/refactoring": {"path": "ai/refactoring", "files": ["ai/refactoring/__init__.py", "ai/refactoring/code_smell_detector.py", "ai/refactoring/impact_analyzer.py", "ai/refactoring/pattern_recommender.py", "ai/refactoring/refactoring_engine.py"], "total_lines": 2473, "avg_lines": 494.6, "max_file_lines": 648, "max_file": "ai/refactoring/impact_analyzer.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 31.0, "_max_complexity": 39, "_issue_count": 20}, "ai/temporal": {"path": "ai/temporal", "files": ["ai/temporal/__init__.py", "ai/temporal/debt_predictor.py", "ai/temporal/productivity_analyzer.py", "ai/temporal/temporal_engine.py", "ai/temporal/trend_visualizer.py"], "total_lines": 2525, "avg_lines": 505.0, "max_file_lines": 726, "max_file": "ai/temporal/productivity_analyzer.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 30.6, "_max_complexity": 41, "_issue_count": 22}, "ai/visualization": {"path": "ai/visualization", "files": ["ai/visualization/__init__.py", "ai/visualization/dashboard_engine.py", "ai/visualization/data_aggregator.py", "ai/visualization/interactive_charts.py", "ai/visualization/report_generator.py"], "total_lines": 2709, "avg_lines": 541.8, "max_file_lines": 778, "max_file": "ai/visualization/dashboard_engine.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 30.4, "_max_complexity": 42, "_issue_count": 19}, "cli": {"path": "cli", "files": ["cli/__init__.py", "cli/commands.py", "cli/completion.py", "cli/error_handler.py", "cli/format_tool.py", "cli/formatters.py", "cli/handlers.py", "cli/knowledge_manager.py", "cli/main.py", "cli/output_formats.py", "cli/parallel_processing.py", "cli/standalone.py", "cli/standalone_suite.py", "cli/watch_mode.py"], "total_lines": 4126, "avg_lines": 294.7142857142857, "max_file_lines": 692, "max_file": "cli/commands.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 32.357142857142854, "_max_complexity": 43, "_issue_count": 47}, "core": {"path": "core", "files": ["core/__init__.py", "core/compatibility.py", "core/config.py", "core/dependency_manager.py", "core/error_handling.py", "core/fs_utils.py", "core/logging.py", "core/progress.py", "core/simple_analyzer.py", "core/version.py"], "total_lines": 2732, "avg_lines": 273.2, "max_file_lines": 997, "max_file": "core/progress.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 24.2, "_max_complexity": 36, "_issue_count": 19}, "core/analysis": {"path": "core/analysis", "files": ["core/analysis/__init__.py", "core/analysis/dependency_analyzer.py", "core/analysis/file_analyzer.py", "core/analysis/framework_detector.py", "core/analysis/framework_rules.py", "core/analysis/import_analyzer.py", "core/analysis/import_visualizer.py", "core/analysis/meta_analyzer.py", "core/analysis/metrics_aggregator.py", "core/analysis/performance_optimizer.py", "core/analysis/project_analyzer.py", "core/analysis/project_meritocracy_analyzer.py", "core/analysis/python_semantic_analyzer.py", "core/analysis/python_version_analyzer.py", "core/analysis/result_processor.py", "core/analysis/semantic_output_formatter.py", "core/analysis/semantic_rules.py", "core/analysis/standalone_analyzer.py", "core/analysis/tool_executor.py", "core/analysis/type_analyzer.py"], "total_lines": 7023, "avg_lines": 351.15, "max_file_lines": 697, "max_file": "core/analysis/dependency_analyzer.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 32.75, "_max_complexity": 43, "_issue_count": 75}, "core/analysis/visualization": {"path": "core/analysis/visualization", "files": ["core/analysis/visualization/__init__.py", "core/analysis/visualization/chart_generators.py", "core/analysis/visualization/html_generators.py", "core/analysis/visualization/interactive_dashboard.py"], "total_lines": 778, "avg_lines": 194.5, "max_file_lines": 257, "max_file": "core/analysis/visualization/chart_generators.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 20.75, "_max_complexity": 30, "_issue_count": 4}, "core/docs": {"path": "core/docs", "files": ["core/docs/__init__.py", "core/docs/templates.py", "core/docs/utils.py"], "total_lines": 494, "avg_lines": 164.66666666666666, "max_file_lines": 349, "max_file": "core/docs/utils.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 11.333333333333334, "_max_complexity": 34, "_issue_count": 8}, "core/error_handling": {"path": "core/error_handling", "files": ["core/error_handling/__init__.py", "core/error_handling/decorators.py", "core/error_handling/error_manager.py", "core/error_handling/exceptions.py", "core/error_handling/handlers.py"], "total_lines": 674, "avg_lines": 134.8, "max_file_lines": 193, "max_file": "core/error_handling/exceptions.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 17.8, "_max_complexity": 28, "_issue_count": 5}, "core/knowledge": {"path": "core/knowledge", "files": ["core/knowledge/framework_knowledge_base.py", "core/knowledge/rule_engine.py"], "total_lines": 1022, "avg_lines": 511.0, "max_file_lines": 626, "max_file": "core/knowledge/framework_knowledge_base.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 38.0, "_max_complexity": 41, "_issue_count": 10}, "core/logging": {"path": "core/logging", "files": ["core/logging/__init__.py", "core/logging/contextual_logger.py", "core/logging/correlation.py", "core/logging/setup.py", "core/logging/structured_logger.py"], "total_lines": 711, "avg_lines": 142.2, "max_file_lines": 220, "max_file": "core/logging/structured_logger.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 18.6, "_max_complexity": 26, "_issue_count": 6}, "core/models": {"path": "core/models", "files": ["core/models/__init__.py", "core/models/directory_metrics.py", "core/models/file_metrics.py", "core/models/progress_tracker.py", "core/models/project_metrics.py"], "total_lines": 1725, "avg_lines": 345.0, "max_file_lines": 566, "max_file": "core/models/progress_tracker.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 26.2, "_max_complexity": 38, "_issue_count": 12}, "core/trend_analysis": {"path": "core/trend_analysis", "files": ["core/trend_analysis/__init__.py", "core/trend_analysis/trend_analyzer.py", "core/trend_analysis/trend_storage.py", "core/trend_analysis/trend_visualizer.py"], "total_lines": 1028, "avg_lines": 257.0, "max_file_lines": 476, "max_file": "core/trend_analysis/trend_visualizer.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 23.25, "_max_complexity": 36, "_issue_count": 7}, "core/utils": {"path": "core/utils", "files": ["core/utils/__init__.py", "core/utils/async_utils.py", "core/utils/config_utils.py", "core/utils/dict_utils.py", "core/utils/error_handling.py", "core/utils/error_utils.py", "core/utils/file_utils.py", "core/utils/fs_utils.py", "core/utils/gitignore_utils.py", "core/utils/preset_manager.py", "core/utils/report_utils.py", "core/utils/tool_utils.py"], "total_lines": 2288, "avg_lines": 190.66666666666666, "max_file_lines": 392, "max_file": "core/utils/fs_utils.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 25.25, "_max_complexity": 39, "_issue_count": 16}, "core/vcs": {"path": "core/vcs", "files": ["core/vcs/__init__.py", "core/vcs/auto_fix.py", "core/vcs/cache.py", "core/vcs/config.py", "core/vcs/dependency_tracker.py", "core/vcs/engine.py", "core/vcs/incremental_analysis.py", "core/vcs/memory_manager.py", "core/vcs/models.py", "core/vcs/performance.py", "core/vcs/registry.py", "core/vcs/type_checking.py"], "total_lines": 4616, "avg_lines": 384.6666666666667, "max_file_lines": 1103, "max_file": "core/vcs/engine.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 34.083333333333336, "_max_complexity": 48, "_issue_count": 34}, "core/vcs/integration": {"path": "core/vcs/integration", "files": ["core/vcs/integration/__init__.py", "core/vcs/integration/ecosystem_integrator.py", "core/vcs/integration/integration_manager.py", "core/vcs/integration/meta_analyzer.py", "core/vcs/integration/unified_reporter.py"], "total_lines": 1093, "avg_lines": 218.6, "max_file_lines": 417, "max_file": "core/vcs/integration/unified_reporter.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 23.4, "_max_complexity": 34, "_issue_count": 9}, "core/vcs/plugins": {"path": "core/vcs/plugins", "files": ["core/vcs/plugins/__init__.py", "core/vcs/plugins/plugin_interface.py", "core/vcs/plugins/plugin_loader.py", "core/vcs/plugins/plugin_manager.py", "core/vcs/plugins/plugin_registry.py"], "total_lines": 1171, "avg_lines": 234.2, "max_file_lines": 306, "max_file": "core/vcs/plugins/plugin_interface.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 28.2, "_max_complexity": 38, "_issue_count": 6}, "core/vcs/rules": {"path": "core/vcs/rules", "files": ["core/vcs/rules/__init__.py", "core/vcs/rules/advanced_python_rules.py", "core/vcs/rules/complexity_rules.py", "core/vcs/rules/documentation_rules.py", "core/vcs/rules/import_rules.py", "core/vcs/rules/performance_rules.py", "core/vcs/rules/rule_loader.py", "core/vcs/rules/security_rules.py", "core/vcs/rules/style_rules.py", "core/vcs/rules/type_rules.py"], "total_lines": 3430, "avg_lines": 343.0, "max_file_lines": 487, "max_file": "core/vcs/rules/type_rules.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 29.3, "_max_complexity": 39, "_issue_count": 80}, "core/vcs/rules/framework_rules": {"path": "core/vcs/rules/framework_rules", "files": ["core/vcs/rules/framework_rules/__init__.py", "core/vcs/rules/framework_rules/django_rules.py", "core/vcs/rules/framework_rules/fastapi_rules.py", "core/vcs/rules/framework_rules/flask_rules.py", "core/vcs/rules/framework_rules/framework_detector.py"], "total_lines": 1407, "avg_lines": 281.4, "max_file_lines": 389, "max_file": "core/vcs/rules/framework_rules/fastapi_rules.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 28.8, "_max_complexity": 38, "_issue_count": 11}, "enterprise": {"path": "enterprise", "files": ["enterprise/__init__.py", "enterprise/collaboration.py", "enterprise/integration.py", "enterprise/monitoring.py"], "total_lines": 992, "avg_lines": 248.0, "max_file_lines": 875, "max_file": "enterprise/collaboration.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 18.25, "_max_complexity": 43, "_issue_count": 8}, "enterprise/api": {"path": "enterprise/api", "files": ["enterprise/api/__init__.py", "enterprise/api/graphql.py", "enterprise/api/models.py", "enterprise/api/rest.py", "enterprise/api/unified.py", "enterprise/api/websocket.py"], "total_lines": 2101, "avg_lines": 350.1666666666667, "max_file_lines": 523, "max_file": "enterprise/api/graphql.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 26.666666666666668, "_max_complexity": 38, "_issue_count": 14}, "enterprise/cicd": {"path": "enterprise/cicd", "files": ["enterprise/cicd/__init__.py", "enterprise/cicd/azure_devops.py", "enterprise/cicd/github_actions.py", "enterprise/cicd/gitlab_ci.py", "enterprise/cicd/jenkins.py", "enterprise/cicd/manager.py", "enterprise/cicd/models.py"], "total_lines": 2480, "avg_lines": 354.2857142857143, "max_file_lines": 549, "max_file": "enterprise/cicd/azure_devops.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 26.0, "_max_complexity": 37, "_issue_count": 12}, "enterprise/collaboration": {"path": "enterprise/collaboration", "files": ["enterprise/collaboration/__init__.py", "enterprise/collaboration/analysis.py", "enterprise/collaboration/shared_config.py", "enterprise/collaboration/team_manager.py"], "total_lines": 1200, "avg_lines": 300.0, "max_file_lines": 457, "max_file": "enterprise/collaboration/analysis.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 25.25, "_max_complexity": 36, "_issue_count": 11}, "enterprise/dashboard": {"path": "enterprise/dashboard", "files": ["enterprise/dashboard/__init__.py", "enterprise/dashboard/components.py", "enterprise/dashboard/models.py", "enterprise/dashboard/static_assets.py", "enterprise/dashboard/templates.py", "enterprise/dashboard/web_server.py"], "total_lines": 2579, "avg_lines": 429.8333333333333, "max_file_lines": 778, "max_file": "enterprise/dashboard/templates.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 19.666666666666668, "_max_complexity": 38, "_issue_count": 11}, "enterprise/monitoring": {"path": "enterprise/monitoring", "files": ["enterprise/monitoring/__init__.py", "enterprise/monitoring/alerting.py", "enterprise/monitoring/dashboards.py", "enterprise/monitoring/models.py", "enterprise/monitoring/monitoring.py", "enterprise/monitoring/quality_gates.py"], "total_lines": 2402, "avg_lines": 400.3333333333333, "max_file_lines": 536, "max_file": "enterprise/monitoring/alerting.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 29.0, "_max_complexity": 40, "_issue_count": 18}, "enterprise/performance": {"path": "enterprise/performance", "files": ["enterprise/performance/__init__.py", "enterprise/performance/cache_manager.py", "enterprise/performance/distributed_processor.py", "enterprise/performance/load_balancer.py", "enterprise/performance/performance_optimizer.py", "enterprise/performance/scalability_manager.py"], "total_lines": 1390, "avg_lines": 231.66666666666666, "max_file_lines": 450, "max_file": "enterprise/performance/distributed_processor.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 24.666666666666668, "_max_complexity": 39, "_issue_count": 17}, "enterprise/reporting": {"path": "enterprise/reporting", "files": ["enterprise/reporting/__init__.py", "enterprise/reporting/customization.py", "enterprise/reporting/engine.py", "enterprise/reporting/executive.py", "enterprise/reporting/formats.py", "enterprise/reporting/templates.py"], "total_lines": 1860, "avg_lines": 310.0, "max_file_lines": 448, "max_file": "enterprise/reporting/engine.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 25.666666666666668, "_max_complexity": 35, "_issue_count": 12}, "enterprise/security": {"path": "enterprise/security", "files": ["enterprise/security/__init__.py", "enterprise/security/access_control.py", "enterprise/security/audit_trail.py", "enterprise/security/compliance_manager.py", "enterprise/security/encryption_manager.py", "enterprise/security/security_monitor.py"], "total_lines": 1829, "avg_lines": 304.8333333333333, "max_file_lines": 475, "max_file": "enterprise/security/access_control.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 25.833333333333332, "_max_complexity": 38, "_issue_count": 22}, "plugins": {"path": "plugins", "files": ["plugins/__init__.py", "plugins/base_plugin.py", "plugins/manager.py", "plugins/plugin_base.py", "plugins/plugin_interface.py", "plugins/plugin_registry.py"], "total_lines": 1275, "avg_lines": 212.5, "max_file_lines": 536, "max_file": "plugins/manager.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 20.0, "_max_complexity": 42, "_issue_count": 11}, "tools/custom_rules": {"path": "tools/custom_rules", "files": ["tools/custom_rules/__init__.py", "tools/custom_rules/python_rules.py"], "total_lines": 340, "avg_lines": 170.0, "max_file_lines": 328, "max_file": "tools/custom_rules/python_rules.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 18.0, "_max_complexity": 36, "_issue_count": 4}, "tools/parsers": {"path": "tools/parsers", "files": ["tools/parsers/__init__.py", "tools/parsers/bandit_parser.py", "tools/parsers/base_parser.py", "tools/parsers/complexity_parser.py", "tools/parsers/custom_rules_parser.py", "tools/parsers/doc_analyzer_parser.py", "tools/parsers/mypy_parser.py", "tools/parsers/parser_registry.py", "tools/parsers/pyflakes_parser.py", "tools/parsers/pylint_parser.py", "tools/parsers/ruff_parser.py"], "total_lines": 1640, "avg_lines": 149.0909090909091, "max_file_lines": 259, "max_file": "tools/parsers/doc_analyzer_parser.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 20.90909090909091, "_max_complexity": 33, "_issue_count": 13}, "tools/runners": {"path": "tools/runners", "files": ["tools/runners/__init__.py", "tools/runners/bandit_runner.py", "tools/runners/base_runner.py", "tools/runners/complexity_runner.py", "tools/runners/custom_rules_runner.py", "tools/runners/doc_analyzer_runner.py", "tools/runners/mypy_runner.py", "tools/runners/pyflakes_runner.py", "tools/runners/pylint_runner.py", "tools/runners/ruff_runner.py", "tools/runners/tool_registry.py"], "total_lines": 2121, "avg_lines": 192.8181818181818, "max_file_lines": 362, "max_file": "tools/runners/complexity_runner.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 25.636363636363637, "_max_complexity": 35, "_issue_count": 34}, "ui/cli": {"path": "ui/cli", "files": ["ui/cli/__init__.py", "ui/cli/commands.py", "ui/cli/formatter.py"], "total_lines": 420, "avg_lines": 140.0, "max_file_lines": 258, "max_file": "ui/cli/formatter.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 17.333333333333332, "_max_complexity": 29, "_issue_count": 3}, "ui/gui": {"path": "ui/gui", "files": ["ui/gui/__init__.py", "ui/gui/app.py", "ui/gui/main_window.py", "ui/gui/simple_gui.py", "ui/gui/themes.py"], "total_lines": 1241, "avg_lines": 248.2, "max_file_lines": 463, "max_file": "ui/gui/simple_gui.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 20.8, "_max_complexity": 33, "_issue_count": 8}, "ui/reporting": {"path": "ui/reporting", "files": ["ui/reporting/__init__.py", "ui/reporting/custom_report_generator.py", "ui/reporting/formatters.py", "ui/reporting/generators.py", "ui/reporting/markdown.py", "ui/reporting/report_generator.py", "ui/reporting/templates.py"], "total_lines": 1387, "avg_lines": 198.14285714285714, "max_file_lines": 577, "max_file": "ui/reporting/report_generator.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 21.714285714285715, "_max_complexity": 28, "_issue_count": 10}, "ui/tui": {"path": "ui/tui", "files": ["ui/tui/__init__.py", "ui/tui/app.py", "ui/tui/components.py", "ui/tui/config_components.py", "ui/tui/header_footer.py", "ui/tui/menu_components.py", "ui/tui/progress_components.py", "ui/tui/results_components.py", "ui/tui/state_manager.py"], "total_lines": 1318, "avg_lines": 146.44444444444446, "max_file_lines": 309, "max_file": "ui/tui/results_components.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 22.666666666666668, "_max_complexity": 35, "_issue_count": 15}, "ui/visualization": {"path": "ui/visualization", "files": ["ui/visualization/__init__.py", "ui/visualization/charts.py", "ui/visualization/exporters.py", "ui/visualization/generators.py", "ui/visualization/interactive_charts.py", "ui/visualization/visualization_generator.py"], "total_lines": 2170, "avg_lines": 361.6666666666667, "max_file_lines": 1080, "max_file": "ui/visualization/interactive_charts.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 21.5, "_max_complexity": 39, "_issue_count": 7}, "ui/web": {"path": "ui/web", "files": ["ui/web/__init__.py", "ui/web/app.py", "ui/web/components.py", "ui/web/run_web_ui.py", "ui/web/state_manager.py"], "total_lines": 1221, "avg_lines": 244.2, "max_file_lines": 631, "max_file": "ui/web/components.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 20.6, "_max_complexity": 38, "_issue_count": 10}}, "issue_count": 685, "max_complexity": 48, "issues_by_severity": {"LOW": 283, "info": 216, "warning": 131, "HIGH": 19, "error": 32, "MEDIUM": 4}}
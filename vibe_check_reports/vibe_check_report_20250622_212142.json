{"timestamp": "2025-06-22T21:21:42.426410", "project_root": "/private/var/folders/9w/6xgkldyj2v1359xfzypsphw00000gn/T/pytest-of-przemyslawzajac/pytest-28/test_complete_project_analysis0", "analysis_type": "unified", "summary": {"total_files": 3, "successful_analyses": 3, "failed_analyses": 0, "total_issues": 19, "issues_per_file": 6.333333333333333, "category_breakdown": {"style": 0, "complexity": 0, "security": 1, "documentation": 6, "imports": 0, "types": 12}, "severity_breakdown": {"error": 5, "warning": 2, "info": 12, "hint": 0}, "quality_score": 52.33}, "detailed_results": [{"file_path": "/private/var/folders/9w/6xgkldyj2v1359xfzypsphw00000gn/T/pytest-of-przemyslawzajac/pytest-28/test_complete_project_analysis0/models.py", "success": true, "execution_time": 0.38378190994262695, "rules_executed": ["S004", "S003", "D003", "FLASK003", "I005", "T006", "T002", "FLASK002", "FLASK005", "PERF003", "C004", "D001", "PERF004", "S006", "C001", "ADV001", "PERF005", "FLASK006", "S001", "SEC001", "ADV005", "T007", "I002", "S002", "T008", "I001", "S005", "D004", "ADV002", "SEC003", "C003", "FLASK001", "SEC002", "ADV003", "PERF002", "SEC005", "I004", "FLASK004", "T001", "ADV004", "T005", "C005", "SEC004", "I006", "I003", "T003", "PERF001", "C002", "D002", "T004"], "issues": [{"line": 1, "column": 0, "message": "<PERSON><PERSON><PERSON> missing docstring", "severity": "info", "category": "documentation", "rule_id": "D001", "source": "vcs", "fix_suggestion": "Add a module-level docstring describing the module's purpose"}, {"line": 2, "column": 0, "message": "Class 'User' missing docstring", "severity": "info", "category": "documentation", "rule_id": "D001", "source": "vcs", "fix_suggestion": "Add a docstring describing the class purpose and usage"}, {"line": 5, "column": 13, "message": "Possible hardcoded password detected", "severity": "error", "category": "security", "rule_id": "SEC001", "source": "vcs", "fix_suggestion": "Use environment variables or secure configuration for password"}, {"line": 3, "column": 4, "message": "Parameter 'name' in function '__init__' missing type annotation", "severity": "info", "category": "types", "rule_id": "T002", "source": "vcs_type_checker", "fix_suggestion": "Add type annotation: name: ParameterType"}, {"line": 3, "column": 5, "message": "Function is missing a type annotation", "severity": "error", "category": "types", "rule_id": "MYPY_no-untyped-def", "source": "mypy", "fix_suggestion": null}], "error_message": null}, {"file_path": "/private/var/folders/9w/6xgkldyj2v1359xfzypsphw00000gn/T/pytest-of-przemyslawzajac/pytest-28/test_complete_project_analysis0/utils.py", "success": true, "execution_time": 0.3248441219329834, "rules_executed": ["S004", "S003", "D003", "FLASK003", "I005", "T006", "T002", "FLASK002", "FLASK005", "PERF003", "C004", "D001", "PERF004", "S006", "C001", "ADV001", "PERF005", "FLASK006", "S001", "SEC001", "ADV005", "T007", "I002", "S002", "T008", "I001", "S005", "D004", "ADV002", "SEC003", "C003", "FLASK001", "SEC002", "ADV003", "PERF002", "SEC005", "I004", "FLASK004", "T001", "ADV004", "T005", "C005", "SEC004", "I006", "I003", "T003", "PERF001", "C002", "D002", "T004"], "issues": [{"line": 1, "column": 0, "message": "<PERSON><PERSON><PERSON> missing docstring", "severity": "info", "category": "documentation", "rule_id": "D001", "source": "vcs", "fix_suggestion": "Add a module-level docstring describing the module's purpose"}, {"line": 2, "column": 0, "message": "Function 'helper_function' missing return type annotation", "severity": "warning", "category": "types", "rule_id": "T001", "source": "vcs_type_checker", "fix_suggestion": "Add return type annotation: def helper_function(...) -> ReturnType:"}, {"line": 2, "column": 0, "message": "Parameter 'x' in function 'helper_function' missing type annotation", "severity": "info", "category": "types", "rule_id": "T002", "source": "vcs_type_checker", "fix_suggestion": "Add type annotation: x: ParameterType"}, {"line": 2, "column": 0, "message": "Parameter 'y' in function 'helper_function' missing type annotation", "severity": "info", "category": "types", "rule_id": "T002", "source": "vcs_type_checker", "fix_suggestion": "Add type annotation: y: ParameterType"}, {"line": 2, "column": 1, "message": "Function is missing a type annotation", "severity": "error", "category": "types", "rule_id": "MYPY_no-untyped-def", "source": "mypy", "fix_suggestion": null}, {"line": 2, "column": 0, "message": "Function 'helper_function' missing type hints for: return type, parameter 'x', parameter 'y'", "severity": "info", "category": "types", "rule_id": "T001", "source": "vcs", "fix_suggestion": "Add type hints to improve code clarity and enable static type checking"}, {"line": 2, "column": 0, "message": "Function 'helper_function' with parameters should document them", "severity": "info", "category": "documentation", "rule_id": "D002", "source": "vcs", "fix_suggestion": "Add Args: section documenting function parameters"}], "error_message": null}, {"file_path": "/private/var/folders/9w/6xgkldyj2v1359xfzypsphw00000gn/T/pytest-of-przemyslawzajac/pytest-28/test_complete_project_analysis0/main.py", "success": true, "execution_time": 0.32959628105163574, "rules_executed": ["S004", "S003", "D003", "FLASK003", "I005", "T006", "T002", "FLASK002", "FLASK005", "PERF003", "C004", "D001", "PERF004", "S006", "C001", "ADV001", "PERF005", "FLASK006", "S001", "SEC001", "ADV005", "T007", "I002", "S002", "T008", "I001", "S005", "D004", "ADV002", "SEC003", "C003", "FLASK001", "SEC002", "ADV003", "PERF002", "SEC005", "I004", "FLASK004", "T001", "ADV004", "T005", "C005", "SEC004", "I006", "I003", "T003", "PERF001", "C002", "D002", "T004"], "issues": [{"line": 1, "column": 0, "message": "<PERSON><PERSON><PERSON> missing docstring", "severity": "info", "category": "documentation", "rule_id": "D001", "source": "vcs", "fix_suggestion": "Add a module-level docstring describing the module's purpose"}, {"line": 2, "column": 0, "message": "Function 'main' missing docstring", "severity": "info", "category": "documentation", "rule_id": "D001", "source": "vcs", "fix_suggestion": "Add a docstring describing the function's purpose, parameters, and return value"}, {"line": 2, "column": 0, "message": "Function 'main' missing return type annotation", "severity": "warning", "category": "types", "rule_id": "T001", "source": "vcs_type_checker", "fix_suggestion": "Add return type annotation: def main(...) -> ReturnType:"}, {"line": 2, "column": 1, "message": "Function is missing a return type annotation", "severity": "error", "category": "types", "rule_id": "MYPY_no-untyped-def", "source": "mypy", "fix_suggestion": null}, {"line": 2, "column": 1, "message": "Use \"-> None\" if function does not return a value", "severity": "info", "category": "types", "rule_id": "MYPY_MYPY", "source": "mypy", "fix_suggestion": null}, {"line": 6, "column": 5, "message": "Call to untyped function \"main\" in typed context", "severity": "error", "category": "types", "rule_id": "MYPY_no-untyped-call", "source": "mypy", "fix_suggestion": null}, {"line": 2, "column": 0, "message": "Function 'main' missing type hints for: return type", "severity": "info", "category": "types", "rule_id": "T001", "source": "vcs", "fix_suggestion": "Add type hints to improve code clarity and enable static type checking"}], "error_message": null}], "meta_analysis": {"project_root": "/private/var/folders/9w/6xgkldyj2v1359xfzypsphw00000gn/T/pytest-of-przemyslawzajac/pytest-28/test_complete_project_analysis0", "total_files": 3, "total_issues": 19, "issue_distribution": {"documentation": 6, "security": 1, "types": 12}, "severity_distribution": {"info": 12, "error": 5, "warning": 2}, "hotspot_files": [["/private/var/folders/9w/6xgkldyj2v1359xfzypsphw00000gn/T/pytest-of-przemyslawzajac/pytest-28/test_complete_project_analysis0/utils.py", 7], ["/private/var/folders/9w/6xgkldyj2v1359xfzypsphw00000gn/T/pytest-of-przemyslawzajac/pytest-28/test_complete_project_analysis0/main.py", 7], ["/private/var/folders/9w/6xgkldyj2v1359xfzypsphw00000gn/T/pytest-of-przemyslawzajac/pytest-28/test_complete_project_analysis0/models.py", 5]], "rule_effectiveness": {"D001": 5, "SEC001": 1, "T002": 3, "MYPY_no-untyped-def": 3, "T001": 4, "D002": 1, "MYPY_MYPY": 1, "MYPY_no-untyped-call": 1}, "complexity_metrics": {"estimated_total_lines": 300, "complexity_issues": 0, "complexity_density": 0.0, "average_issues_per_file": 6.333333333333333}, "quality_score": 52.33, "recommendations": ["⚠️ Warning: Project quality needs improvement.", "🔒 Security: Address security issues immediately."], "trends": {"files_trend": "stable", "issues_trend": "stable", "quality_trend": "stable", "historical_data_points": 0}}, "plugin_results": [], "performance_metrics": {"total_files": 3, "successful_analyses": 3, "failed_analyses": 0, "total_issues": 19, "total_execution_time": 2.068305730819702, "chunks_processed": 1, "memory_report": {"current": {"total_memory_mb": 36864.0, "available_memory_mb": 10246.34375, "used_memory_mb": 12573.765625, "memory_percent": 72.2, "process_memory_mb": 148.5, "gc_collections": 5296, "pressure_level": "medium"}, "baseline_mb": 148.453125, "peak_mb": 148.5, "growth_mb": 0.046875, "operations_processed": 3, "current_chunk_size": 50, "gc_frequency": 10, "gc_threshold_adjustments": 0}, "max_workers": 8, "streaming_used": true}, "recommendations": ["⚠️ Warning: Project quality needs improvement.", "🔒 Security: Address security issues immediately.", "⚠️ Moderate number of issues - consider addressing high-priority ones first."], "export_formats": ["json", "html", "markdown", "csv", "xml"]}
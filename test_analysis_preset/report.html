<!DOCTYPE html>
<html>
<head>
    <title>Project Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .severity-high { color: #d9534f; }
        .severity-medium { color: #f0ad4e; }
        .severity-low { color: #5bc0de; }
    </style>
</head>
<body>
    <h1>Project Analysis Report</h1>
    <p><strong>Project:</strong> /Users/<USER>/Lokalne_Kody/PAT_project_analysis/test_projects/vibe_check_test_project</p>
    <p><strong>Date:</strong> 2025-06-21 18:27:18</p>

    <h2>Summary</h2>
    <ul>
        <li><strong>Total Files:</strong> 4</li>
        <li><strong>Total Directories:</strong> 1</li>
        <li><strong>Total Issues:</strong> 7</li>
        <li><strong>Max Complexity:</strong> 26</li>
    </ul>

    <h2>Files</h2>
    <table>
        <tr>
            <th>File</th>
            <th>Lines</th>
            <th>Issues</th>
            <th>Complexity</th>
        </tr>
        <tr>
            <td>__init__.py</td>
            <td>8</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>main.py</td>
            <td>111</td>
            <td>3</td>
            <td>26</td>
        </tr>
        <tr>
            <td>models.py</td>
            <td>81</td>
            <td>1</td>
            <td>14</td>
        </tr>
        <tr>
            <td>utils.py</td>
            <td>111</td>
            <td>2</td>
            <td>23</td>
        </tr>
    </table>

    <h2>Issues</h2>
    <h3>__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>main.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>49</td>
            <td>B106</td>
            <td class="severity-low">LOW</td>
            <td>Possible hardcoded password: 'admin123'</td>
        </tr>
        <tr>
            <td>91</td>
            <td>B608</td>
            <td class="severity-medium">MEDIUM</td>
            <td>Possible SQL injection vector through string-based query construction.</td>
        </tr>
    </table>
    <h3>models.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>107</td>
            <td>B307</td>
            <td class="severity-medium">MEDIUM</td>
            <td>Use of possibly insecure function - consider using safer ast.literal_eval.</td>
        </tr>
    </table>
</body>
</html>

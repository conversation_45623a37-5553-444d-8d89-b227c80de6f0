# Project Analysis Report

**Project:** /Users/<USER>/Lokalne_Kody/PAT_project_analysis/test_projects/vibe_check_test_project

**Date:** 2025-06-21 18:27:18

## Summary

- **Total Files:** 4
- **Total Directories:** 1
- **Total Issues:** 7
- **Max Complexity:** 26

## Files

| File | Lines | Issues | Complexity |
|------|-------|--------|------------|
| __init__.py | 8 | 1 | 0 |
| main.py | 111 | 3 | 26 |
| models.py | 81 | 1 | 14 |
| utils.py | 111 | 2 | 23 |

## Issues

### __init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### main.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 49 | B106 | LOW | Possible hardcoded password: 'admin123' |
| 91 | B608 | MEDIUM | Possible SQL injection vector through string-based query construction. |

### models.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### utils.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 107 | B307 | MEDIUM | Use of possibly insecure function - consider using safer ast.literal_eval. |


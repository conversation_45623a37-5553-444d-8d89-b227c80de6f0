{"project_path": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/test_projects/vibe_check_test_project", "files": {"__init__.py": {"path": "__init__.py", "name": "__init__.py", "size": 170, "lines": 8, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "main.py": {"path": "main.py", "name": "main.py", "size": 2981, "lines": 111, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 26, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B106", "name": "hardcoded_password_funcarg", "line": 49, "message": "Possible hardcoded password: 'admin123'", "severity": "LOW", "confidence": "MEDIUM", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b106_hardcoded_password_funcarg.html"}, {"code": "B608", "name": "hardcoded_sql_expressions", "line": 91, "message": "Possible SQL injection vector through string-based query construction.", "severity": "MEDIUM", "confidence": "LOW", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b608_hardcoded_sql_expressions.html"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "models.py": {"path": "models.py", "name": "models.py", "size": 1968, "lines": 81, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 14, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "utils.py": {"path": "utils.py", "name": "utils.py", "size": 2612, "lines": 111, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 23, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B307", "name": "blacklist", "line": 107, "message": "Use of possibly insecure function - consider using safer ast.literal_eval.", "severity": "MEDIUM", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_calls.html#b307-eval"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}}, "directories": {"": {"path": "", "files": ["__init__.py", "main.py", "models.py", "utils.py"], "total_lines": 311, "avg_lines": 77.75, "max_file_lines": 111, "max_file": "main.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 15.75, "_max_complexity": 26, "_issue_count": 7}}, "issue_count": 7, "max_complexity": 26, "issues_by_severity": {"LOW": 5, "MEDIUM": 2}}
{"project_path": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis", "files": {"__init__.py": {"path": "__init__.py", "name": "__init__.py", "size": 651, "lines": 24, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "file_analyzer.py": {"path": "file_analyzer.py", "name": "file_analyzer.py", "size": 3960, "lines": 124, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "import_analyzer.py": {"path": "import_analyzer.py", "name": "import_analyzer.py", "size": 18024, "lines": 495, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "import_visualizer.py": {"path": "import_visualizer.py", "name": "import_visualizer.py", "size": 22430, "lines": 569, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "metrics_aggregator.py": {"path": "metrics_aggregator.py", "name": "metrics_aggregator.py", "size": 6314, "lines": 161, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "project_analyzer.py": {"path": "project_analyzer.py", "name": "project_analyzer.py", "size": 3546, "lines": 103, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "result_processor.py": {"path": "result_processor.py", "name": "result_processor.py", "size": 6543, "lines": 165, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tool_executor.py": {"path": "tool_executor.py", "name": "tool_executor.py", "size": 3926, "lines": 113, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}}, "directories": {"": {"path": "", "files": ["__init__.py", "file_analyzer.py", "import_analyzer.py", "import_visualizer.py", "metrics_aggregator.py", "project_analyzer.py", "result_processor.py", "tool_executor.py"], "total_lines": 1754, "avg_lines": 219.25, "max_file_lines": 569, "max_file": "import_visualizer.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 0.0, "_max_complexity": 0, "_issue_count": 0}}, "issue_count": 0, "max_complexity": 0, "issues_by_severity": {}}
"""
Comprehensive tests for VCS (Vibe Check Standalone) system.
"""

import pytest
import asyncio
from pathlib import Path
from unittest.mock import Mock, patch

from vibe_check.core.vcs.engine import VibeCheckEngine
from vibe_check.core.vcs.models import (
    AnalysisTarget, AnalysisContext, EngineMode, RuleCategory
)
from vibe_check.core.vcs.config import VCSConfig
from vibe_check.core.vcs.rules.rule_loader import load_built_in_rules, load_framework_rules
from vibe_check.core.vcs.registry import RuleRegistry


class TestVCSEngine:
    """Test VCS engine functionality."""
    
    @pytest.fixture
    async def engine(self):
        """Create a VCS engine for testing."""
        config = VCSConfig()
        engine = VibeCheckEngine(config)
        await engine.start()
        yield engine
        await engine.stop()
    
    @pytest.mark.asyncio
    async def test_engine_lifecycle(self):
        """Test engine start/stop lifecycle."""
        config = VCSConfig()
        engine = VibeCheckEngine(config)
        
        assert not engine.is_enabled()
        
        await engine.start()
        assert engine.is_enabled()
        
        await engine.stop()
        assert not engine.is_enabled()
    
    @pytest.mark.asyncio
    async def test_basic_analysis(self, engine):
        """Test basic file analysis."""
        # Create a test Python file
        test_code = '''
def test_function():
    x = 1
    y = 2
    return x + y
'''
        
        target = AnalysisTarget.from_content(test_code, "test.py")
        context = AnalysisContext.create_default(EngineMode.STANDALONE)
        
        result = await engine.analyze(target, context)
        
        assert result.success
        assert result.target == target
        assert isinstance(result.issues, list)
    
    @pytest.mark.asyncio
    async def test_analysis_with_issues(self, engine):
        """Test analysis that should find issues."""
        # Code with obvious issues
        test_code = '''
def bad_function():
    password = "secret123"  # Hardcoded password
    x=1;y=2  # Multiple statements
    return x+y
'''
        
        target = AnalysisTarget.from_content(test_code, "bad.py")
        context = AnalysisContext.create_default(EngineMode.STANDALONE)
        
        result = await engine.analyze(target, context)
        
        assert result.success
        assert len(result.issues) > 0
        
        # Check for specific issue types
        issue_rules = [issue.rule_id for issue in result.issues]
        assert any("SEC" in rule for rule in issue_rules)  # Security issue
        assert any("S006" in rule for rule in issue_rules)  # Style issue
    
    @pytest.mark.asyncio
    async def test_framework_detection(self, engine):
        """Test framework-specific analysis."""
        # Django-like code
        django_code = '''
from django.db import models

class User(models.Model):
    username = models.CharField(max_length=100)
    password = models.CharField(max_length=100)  # Should trigger security warning
'''
        
        target = AnalysisTarget.from_content(django_code, "models.py")
        context = AnalysisContext.create_default(EngineMode.STANDALONE)
        
        result = await engine.analyze(target, context)
        
        assert result.success
        # Should detect Django-specific issues
        django_issues = [issue for issue in result.issues if "DJANGO" in issue.rule_id]
        assert len(django_issues) >= 0  # May or may not find Django-specific issues
    
    @pytest.mark.asyncio
    async def test_performance_analysis(self, engine):
        """Test performance rule analysis."""
        # Code with performance issues
        perf_code = '''
def inefficient_function():
    items = [1, 2, 3, 4, 5]
    result = []
    for i in range(len(items)):  # Should suggest enumerate
        result.append(items[i] * 2)
    return result
'''
        
        target = AnalysisTarget.from_content(perf_code, "perf.py")
        context = AnalysisContext.create_default(EngineMode.STANDALONE)
        
        result = await engine.analyze(target, context)
        
        assert result.success
        # Should detect performance issues
        perf_issues = [issue for issue in result.issues if "PERF" in issue.rule_id]
        assert len(perf_issues) >= 0  # May or may not find performance issues


class TestRuleSystem:
    """Test rule loading and management."""
    
    def test_rule_loading(self):
        """Test loading built-in rules."""
        registry = RuleRegistry()
        loaded_count = load_built_in_rules(registry)
        
        assert loaded_count > 0
        assert len(registry.rules) == loaded_count
        
        # Check that all categories are represented
        categories = set()
        for rule in registry.rules.values():
            categories.add(rule.category)
        
        expected_categories = {
            RuleCategory.STYLE,
            RuleCategory.SECURITY,
            RuleCategory.COMPLEXITY,
            RuleCategory.DOCS,
            RuleCategory.IMPORTS,
            RuleCategory.TYPES
        }
        
        assert expected_categories.issubset(categories)
    
    def test_framework_rule_loading(self, tmp_path):
        """Test loading framework-specific rules."""
        registry = RuleRegistry()
        
        # Create a mock Django project
        django_file = tmp_path / "models.py"
        django_file.write_text("""
from django.db import models

class TestModel(models.Model):
    name = models.CharField(max_length=100)
""")
        
        loaded_count = load_framework_rules(registry, tmp_path)
        
        # Should load some framework rules
        assert loaded_count >= 0
    
    def test_rule_categories(self):
        """Test rule categorization."""
        registry = RuleRegistry()
        load_built_in_rules(registry)
        
        # Test getting rules by category
        style_rules = registry.get_rules_for_category(RuleCategory.STYLE)
        security_rules = registry.get_rules_for_category(RuleCategory.SECURITY)
        
        assert len(style_rules) > 0
        assert len(security_rules) > 0
        
        # Verify rules are in correct categories
        for rule in style_rules:
            assert rule.category == RuleCategory.STYLE
        
        for rule in security_rules:
            assert rule.category == RuleCategory.SECURITY


class TestAnalysisModels:
    """Test analysis model classes."""
    
    def test_analysis_target_from_content(self):
        """Test creating AnalysisTarget from content."""
        content = "print('hello world')"
        target = AnalysisTarget.from_content(content, "test.py")
        
        assert target.content == content
        assert target.path.name == "test.py"
        assert target.get_content() == content
    
    def test_analysis_target_from_file(self, tmp_path):
        """Test creating AnalysisTarget from file."""
        test_file = tmp_path / "test.py"
        content = "print('hello world')"
        test_file.write_text(content)
        
        target = AnalysisTarget.from_file(test_file)
        
        assert target.path == test_file
        assert target.get_content() == content
    
    def test_analysis_context_creation(self):
        """Test AnalysisContext creation."""
        context = AnalysisContext.create_default(EngineMode.STANDALONE)
        
        assert context.mode == EngineMode.STANDALONE
        assert isinstance(context.enabled_categories, set)
        assert len(context.enabled_categories) > 0


class TestIntegrationFeatures:
    """Test integration features like plugins and meta-analysis."""
    
    @pytest.mark.asyncio
    async def test_plugin_system(self):
        """Test plugin system integration."""
        config = VCSConfig()
        engine = VibeCheckEngine(config)
        
        await engine.start()
        
        # Test plugin manager is initialized
        assert engine.plugin_manager is not None
        
        # Test plugin statistics
        stats = engine.get_plugin_statistics()
        assert isinstance(stats, dict)
        assert "total_plugins" in stats
        
        await engine.stop()
    
    @pytest.mark.asyncio
    async def test_integration_manager(self):
        """Test integration manager functionality."""
        config = VCSConfig()
        engine = VibeCheckEngine(config)
        
        await engine.start()
        
        # Test integration manager is initialized
        assert engine.integration_manager is not None
        
        # Test integration statistics
        stats = engine.get_integration_statistics()
        assert isinstance(stats, dict)
        assert "initialized" in stats
        
        await engine.stop()
    
    @pytest.mark.asyncio
    async def test_memory_management(self):
        """Test memory management features."""
        config = VCSConfig()
        engine = VibeCheckEngine(config)
        
        await engine.start()
        
        # Test memory manager is initialized
        assert engine.memory_manager is not None
        
        # Test memory statistics
        stats = engine.get_memory_statistics()
        assert isinstance(stats, dict)
        
        await engine.stop()


class TestPerformanceOptimization:
    """Test performance optimization features."""
    
    @pytest.mark.asyncio
    async def test_caching_system(self):
        """Test caching system functionality."""
        config = VCSConfig(cache_enabled=True)
        engine = VibeCheckEngine(config)
        
        await engine.start()
        
        # Test cache manager is initialized
        assert engine.cache_manager is not None
        
        # Test cache statistics
        stats = engine.cache_manager.get_cache_stats()
        assert isinstance(stats, dict)
        
        await engine.stop()
    
    @pytest.mark.asyncio
    async def test_incremental_analysis(self):
        """Test incremental analysis functionality."""
        config = VCSConfig(cache_enabled=True)
        engine = VibeCheckEngine(config)
        
        await engine.start()
        
        # Test incremental analyzer is available
        if engine.incremental_analyzer:
            stats = engine.get_incremental_statistics()
            assert isinstance(stats, dict)
        
        await engine.stop()


@pytest.mark.integration
class TestEndToEndWorkflow:
    """Test complete end-to-end workflows."""
    
    @pytest.mark.asyncio
    async def test_complete_project_analysis(self, tmp_path):
        """Test analyzing a complete project."""
        # Create a mock Python project
        project_files = {
            "main.py": '''
def main():
    print("Hello, World!")

if __name__ == "__main__":
    main()
''',
            "utils.py": '''
def helper_function(x, y):
    """Helper function."""
    return x + y
''',
            "models.py": '''
class User:
    def __init__(self, name):
        self.name = name
        self.password = "default123"  # Security issue
'''
        }
        
        # Create files
        for filename, content in project_files.items():
            (tmp_path / filename).write_text(content)
        
        # Analyze project
        config = VCSConfig()
        engine = VibeCheckEngine(config)
        
        await engine.start()
        
        # Test comprehensive analysis if available
        if hasattr(engine, 'perform_comprehensive_analysis'):
            try:
                report, stats = await engine.perform_comprehensive_analysis(
                    tmp_path, export_formats=["json"]
                )
                
                assert report is not None
                assert isinstance(stats, dict)
                assert stats["main_results_count"] > 0
                
            except Exception as e:
                # Comprehensive analysis might not be fully implemented
                pytest.skip(f"Comprehensive analysis not available: {e}")
        
        await engine.stop()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

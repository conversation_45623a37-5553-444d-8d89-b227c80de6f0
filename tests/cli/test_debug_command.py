"""
End-to-end tests for the debug command.

This module tests the CLI debug command, which uses the enhanced timeline visualization.
"""

import os
import tempfile
import pytest
from typing import Any, Dict
from unittest.mock import patch, MagicMock

from vibe_check.cli.commands import debug_command


class TestDebugCommand:
    """Test class for the debug command."""

    def setup_method(self) -> None:
        """Set up the test environment."""
        # Create a temporary directory for output
        self.temp_dir = tempfile.mkdtemp()

        # Mock the actor system for testing
        self.mock_actor_system = MagicMock()

        # Create a patch for the actor system
        self.actor_system_patch = patch(
            'vibe_check.core.actor_system.actor_system.ActorSystem',
            return_value=self.mock_actor_system
        )

        # Enable debug mode
        from vibe_check.core.actor_system.logging.initialization_debug import enable_init_debugging
        enable_init_debugging()

    def teardown_method(self) -> None:
        """Clean up after the test."""
        # Clean up temporary directory
        import shutil
        shutil.rmtree(self.temp_dir)

    def test_debug_command(self) -> None:
        """Test the debug command."""
        # Start the actor system patch
        with self.actor_system_patch:
            # Mock the debugger
            mock_debugger = MagicMock()

            # Mock the timeline path
            timeline_path = os.path.join(self.temp_dir, "initialization_timeline.html")
            with open(timeline_path, "w") as f:
                f.write("<html><body>Mock Timeline</body></html>")

            # Mock the registry visualization path
            registry_viz_path = os.path.join(self.temp_dir, "registry_visualization.html")
            with open(registry_viz_path, "w") as f:
                f.write("<html><body>Mock Registry Visualization</body></html>")

            # Mock the registry synchronization visualization path
            registry_sync_viz_path = os.path.join(self.temp_dir, "registry_sync_visualization.html")
            with open(registry_sync_viz_path, "w") as f:
                f.write("<html><body>Mock Registry Sync Visualization</body></html>")

            # Mock the dependency graph path
            dependency_graph_path = os.path.join(self.temp_dir, "dependency_graph.html")
            with open(dependency_graph_path, "w") as f:
                f.write("<html><body>Mock Dependency Graph</body></html>")

            # Set up the mock debugger to return the mock paths
            # For async methods, we need to create awaitable results
            async def mock_async_return(value: Any) -> Any:
                return value

            mock_debugger.generate_initialization_timeline.return_value = mock_async_return(timeline_path)
            mock_debugger.generate_registry_visualization.return_value = mock_async_return(registry_viz_path)
            mock_debugger.generate_registry_sync_visualization.return_value = mock_async_return(registry_sync_viz_path)
            mock_debugger.generate_dependency_graph_visualization.return_value = mock_async_return(dependency_graph_path)
            mock_debugger.collect_system_state.return_value = mock_async_return({})
            mock_debugger.check_for_deadlocks.return_value = mock_async_return([])
            mock_debugger.analyze_initialization_issues.return_value = mock_async_return({})
            mock_debugger.analyze_timeout_issues.return_value = mock_async_return({})
            mock_debugger.analyze_dependency_resolution.return_value = mock_async_return({})
            mock_debugger.analyze_registry_synchronization.return_value = mock_async_return({})
            mock_debugger.suggest_fixes.return_value = mock_async_return({})
            mock_debugger.generate_debug_report.return_value = mock_async_return("report.html")

            # Create a mock result
            mock_result = {
                "success": True,
                "timeline_path": timeline_path,
                "registry_viz_path": registry_viz_path,
                "registry_sync_viz_path": registry_sync_viz_path,
                "dependency_graph_path": dependency_graph_path,
                "duration": 0.5  # Mock duration
            }

            # Patch the ActorSystemDebugger
            with patch('vibe_check.core.actor_system.debug_utils.ActorSystemDebugger', return_value=mock_debugger):
                # Patch asyncio.run to return our mock result
                with patch('asyncio.run', return_value=mock_result):
                    # Run the debug command
                    results = debug_command(
                        project_path="test_project",
                        output_dir=self.temp_dir,
                        timeout=10.0
                    )

                # Verify that the command returned success
                assert results["success"] is True

                # Verify that the timeline was generated
                assert "timeline_path" in results
                assert os.path.exists(results["timeline_path"])

                # Verify that the registry visualization was generated
                assert "registry_viz_path" in results
                assert os.path.exists(results["registry_viz_path"])

                # Verify that the registry synchronization visualization was generated
                assert "registry_sync_viz_path" in results
                assert os.path.exists(results["registry_sync_viz_path"])

                # Verify that the dependency graph was generated
                assert "dependency_graph_path" in results
                assert os.path.exists(results["dependency_graph_path"])

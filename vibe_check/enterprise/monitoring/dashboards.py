"""
File: vibe_check/enterprise/monitoring/dashboards.py
Purpose: Real-time dashboard system for enterprise monitoring
Related Files: vibe_check/enterprise/monitoring/
Dependencies: typing, pathlib, json, asyncio
"""

import json
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime, timedelta
import uuid

from .models import Dashboard, DashboardWidget, MonitoringMetric
from .monitoring import MonitoringSystem
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class DashboardDataProvider:
    """Provides data for dashboard widgets."""
    
    def __init__(self, monitoring_system: MonitoringSystem):
        self.monitoring_system = monitoring_system
        self.data_cache: Dict[str, Any] = {}
        self.cache_ttl: Dict[str, datetime] = {}
    
    async def get_widget_data(
        self,
        widget: DashboardWidget,
        refresh: bool = False
    ) -> Dict[str, Any]:
        """
        Get data for a dashboard widget.
        
        Args:
            widget: Dashboard widget
            refresh: Force refresh of cached data
            
        Returns:
            Widget data
        """
        cache_key = f"{widget.widget_id}_{widget.data_source}"
        
        # Check cache
        if not refresh and cache_key in self.data_cache:
            if cache_key in self.cache_ttl and datetime.now() < self.cache_ttl[cache_key]:
                return self.data_cache[cache_key]
        
        # Generate new data
        data = await self._generate_widget_data(widget)
        
        # Cache data
        self.data_cache[cache_key] = data
        self.cache_ttl[cache_key] = datetime.now() + timedelta(seconds=widget.refresh_interval)
        
        return data
    
    async def _generate_widget_data(self, widget: DashboardWidget) -> Dict[str, Any]:
        """Generate data for a specific widget type."""
        widget_type = widget.widget_type
        config = widget.config
        
        if widget_type == "metric_chart":
            return await self._get_metric_chart_data(config)
        elif widget_type == "alert_summary":
            return await self._get_alert_summary_data(config)
        elif widget_type == "quality_gates":
            return await self._get_quality_gates_data(config)
        elif widget_type == "system_health":
            return await self._get_system_health_data(config)
        elif widget_type == "analysis_trends":
            return await self._get_analysis_trends_data(config)
        else:
            return {"error": f"Unknown widget type: {widget_type}"}
    
    async def _get_metric_chart_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get data for metric chart widget."""
        metric_name = config.get("metric_name", "")
        duration_minutes = config.get("duration_minutes", 60)
        
        if not metric_name:
            return {"error": "No metric name specified"}
        
        # Get metric values
        end_time = datetime.now()
        start_time = end_time - timedelta(minutes=duration_minutes)
        
        metrics = self.monitoring_system.metrics_collector.get_metric_values(
            metric_name, start_time, end_time, limit=100
        )
        
        # Format for chart
        data_points = [
            {
                "timestamp": metric.timestamp.isoformat(),
                "value": metric.value
            }
            for metric in sorted(metrics, key=lambda m: m.timestamp)
        ]
        
        # Calculate summary statistics
        values = [m.value for m in metrics]
        summary = {}
        if values:
            summary = {
                "current": values[-1],
                "min": min(values),
                "max": max(values),
                "avg": sum(values) / len(values),
                "count": len(values)
            }
        
        return {
            "metric_name": metric_name,
            "data_points": data_points,
            "summary": summary,
            "duration_minutes": duration_minutes
        }
    
    async def _get_alert_summary_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get data for alert summary widget."""
        # This would integrate with the alerting system
        return {
            "active_alerts": 3,
            "critical_alerts": 1,
            "high_alerts": 1,
            "medium_alerts": 1,
            "low_alerts": 0,
            "recent_alerts": [
                {
                    "id": "alert_1",
                    "title": "High CPU Usage",
                    "severity": "high",
                    "created_at": datetime.now().isoformat()
                },
                {
                    "id": "alert_2", 
                    "title": "Quality Gate Failed",
                    "severity": "critical",
                    "created_at": (datetime.now() - timedelta(minutes=15)).isoformat()
                }
            ]
        }
    
    async def _get_quality_gates_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get data for quality gates widget."""
        return {
            "total_gates": 5,
            "passed_gates": 3,
            "failed_gates": 1,
            "warning_gates": 1,
            "success_rate": 60.0,
            "recent_evaluations": [
                {
                    "gate_name": "Basic Quality Gate",
                    "status": "passed",
                    "evaluated_at": datetime.now().isoformat()
                },
                {
                    "gate_name": "Security Gate",
                    "status": "failed",
                    "evaluated_at": (datetime.now() - timedelta(minutes=30)).isoformat()
                }
            ]
        }
    
    async def _get_system_health_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get data for system health widget."""
        # Get latest system metrics
        cpu_usage = self.monitoring_system.metrics_collector.get_latest_value("system.cpu_percent") or 0
        memory_usage = self.monitoring_system.metrics_collector.get_latest_value("system.memory_percent") or 0
        disk_usage = self.monitoring_system.metrics_collector.get_latest_value("system.disk_percent") or 0
        
        # Determine health status
        health_score = 100
        if cpu_usage > 80:
            health_score -= 30
        elif cpu_usage > 60:
            health_score -= 15
        
        if memory_usage > 85:
            health_score -= 25
        elif memory_usage > 70:
            health_score -= 10
        
        if disk_usage > 90:
            health_score -= 20
        elif disk_usage > 80:
            health_score -= 10
        
        status = "healthy"
        if health_score < 50:
            status = "critical"
        elif health_score < 70:
            status = "warning"
        elif health_score < 90:
            status = "degraded"
        
        return {
            "health_score": max(0, health_score),
            "status": status,
            "metrics": {
                "cpu_usage": cpu_usage,
                "memory_usage": memory_usage,
                "disk_usage": disk_usage
            },
            "last_updated": datetime.now().isoformat()
        }
    
    async def _get_analysis_trends_data(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Get data for analysis trends widget."""
        duration_hours = config.get("duration_hours", 24)
        
        # Get analysis metrics over time
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=duration_hours)
        
        files_analyzed = self.monitoring_system.metrics_collector.get_metric_values(
            "analysis.files_analyzed", start_time, end_time
        )
        
        total_issues = self.monitoring_system.metrics_collector.get_metric_values(
            "analysis.total_issues", start_time, end_time
        )
        
        # Calculate trends
        files_trend = []
        issues_trend = []
        
        for metric in sorted(files_analyzed, key=lambda m: m.timestamp):
            files_trend.append({
                "timestamp": metric.timestamp.isoformat(),
                "value": metric.value
            })
        
        for metric in sorted(total_issues, key=lambda m: m.timestamp):
            issues_trend.append({
                "timestamp": metric.timestamp.isoformat(),
                "value": metric.value
            })
        
        return {
            "files_analyzed_trend": files_trend,
            "issues_trend": issues_trend,
            "duration_hours": duration_hours,
            "summary": {
                "total_files": sum(m.value for m in files_analyzed),
                "total_issues": sum(m.value for m in total_issues),
                "avg_issues_per_file": (
                    sum(m.value for m in total_issues) / max(sum(m.value for m in files_analyzed), 1)
                )
            }
        }


class RealTimeDashboard:
    """Real-time dashboard with WebSocket support."""
    
    def __init__(self, dashboard: Dashboard, data_provider: DashboardDataProvider):
        self.dashboard = dashboard
        self.data_provider = data_provider
        self.subscribers: List[Callable] = []
        self.update_task: Optional[asyncio.Task] = None
        self._running = False
    
    async def start(self) -> None:
        """Start real-time dashboard updates."""
        if self._running:
            return
        
        self._running = True
        self.update_task = asyncio.create_task(self._update_loop())
        logger.info(f"Started real-time dashboard: {self.dashboard.name}")
    
    async def stop(self) -> None:
        """Stop real-time dashboard updates."""
        self._running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        
        logger.info(f"Stopped real-time dashboard: {self.dashboard.name}")
    
    def subscribe(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Subscribe to dashboard updates."""
        self.subscribers.append(callback)
    
    def unsubscribe(self, callback: Callable[[Dict[str, Any]], None]) -> None:
        """Unsubscribe from dashboard updates."""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
    
    async def _update_loop(self) -> None:
        """Main update loop for real-time data."""
        while self._running:
            try:
                # Get data for all widgets
                dashboard_data = {
                    "dashboard_id": self.dashboard.dashboard_id,
                    "name": self.dashboard.name,
                    "updated_at": datetime.now().isoformat(),
                    "widgets": {}
                }
                
                for widget in self.dashboard.widgets:
                    if not widget.enabled:
                        continue
                    
                    widget_data = await self.data_provider.get_widget_data(widget)
                    dashboard_data["widgets"][widget.widget_id] = {
                        "widget_id": widget.widget_id,
                        "title": widget.title,
                        "type": widget.widget_type,
                        "data": widget_data
                    }
                
                # Notify subscribers
                for callback in self.subscribers:
                    try:
                        callback(dashboard_data)
                    except Exception as e:
                        logger.error(f"Error in dashboard subscriber: {e}")
                
                # Wait before next update (use minimum refresh interval)
                min_refresh = min(
                    (widget.refresh_interval for widget in self.dashboard.widgets if widget.enabled),
                    default=30
                )
                await asyncio.sleep(min_refresh)
                
            except Exception as e:
                logger.error(f"Error in dashboard update loop: {e}")
                await asyncio.sleep(30)


class DashboardManager:
    """Manages enterprise dashboards."""
    
    def __init__(self, monitoring_system: MonitoringSystem, data_dir: Optional[Path] = None):
        """
        Initialize dashboard manager.
        
        Args:
            monitoring_system: Monitoring system instance
            data_dir: Directory for storing dashboard data
        """
        self.monitoring_system = monitoring_system
        self.data_dir = data_dir or Path.cwd() / "dashboard_data"
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        self.dashboards: Dict[str, Dashboard] = {}
        self.real_time_dashboards: Dict[str, RealTimeDashboard] = {}
        self.data_provider = DashboardDataProvider(monitoring_system)
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize dashboard manager."""
        if self._initialized:
            return
        
        await self._load_dashboards()
        await self._create_default_dashboard()
        
        self._initialized = True
        logger.info(f"Dashboard manager initialized with {len(self.dashboards)} dashboards")
    
    async def _load_dashboards(self) -> None:
        """Load dashboards from storage."""
        dashboards_file = self.data_dir / "dashboards.json"
        if dashboards_file.exists():
            try:
                with open(dashboards_file, 'r', encoding='utf-8') as f:
                    dashboards_data = json.load(f)
                
                for dashboard_data in dashboards_data:
                    dashboard = self._dashboard_from_dict(dashboard_data)
                    self.dashboards[dashboard.dashboard_id] = dashboard
                
                logger.debug(f"Loaded {len(self.dashboards)} dashboards")
                
            except Exception as e:
                logger.error(f"Failed to load dashboards: {e}")
    
    async def _save_dashboards(self) -> None:
        """Save dashboards to storage."""
        dashboards_file = self.data_dir / "dashboards.json"
        try:
            dashboards_data = [dashboard.to_dict() for dashboard in self.dashboards.values()]
            with open(dashboards_file, 'w', encoding='utf-8') as f:
                json.dump(dashboards_data, f, indent=2)
            
            logger.debug("Saved dashboards to storage")
            
        except Exception as e:
            logger.error(f"Failed to save dashboards: {e}")
    
    def _dashboard_from_dict(self, data: Dict[str, Any]) -> Dashboard:
        """Create Dashboard from dictionary."""
        widgets = []
        for widget_data in data.get("widgets", []):
            widget = DashboardWidget(
                widget_id=widget_data["widget_id"],
                title=widget_data["title"],
                widget_type=widget_data["widget_type"],
                position=widget_data["position"],
                config=widget_data.get("config", {}),
                data_source=widget_data.get("data_source", ""),
                refresh_interval=widget_data.get("refresh_interval", 30),
                enabled=widget_data.get("enabled", True)
            )
            widgets.append(widget)
        
        return Dashboard(
            dashboard_id=data["dashboard_id"],
            name=data["name"],
            description=data["description"],
            widgets=widgets,
            layout=data.get("layout", {}),
            permissions=data.get("permissions", {}),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )
    
    async def _create_default_dashboard(self) -> None:
        """Create default dashboard if none exist."""
        if self.dashboards:
            return
        
        # Create default widgets
        widgets = [
            DashboardWidget(
                widget_id="system_health",
                title="System Health",
                widget_type="system_health",
                position={"x": 0, "y": 0, "width": 6, "height": 4},
                refresh_interval=30
            ),
            DashboardWidget(
                widget_id="alert_summary",
                title="Active Alerts",
                widget_type="alert_summary",
                position={"x": 6, "y": 0, "width": 6, "height": 4},
                refresh_interval=15
            ),
            DashboardWidget(
                widget_id="quality_gates",
                title="Quality Gates",
                widget_type="quality_gates",
                position={"x": 0, "y": 4, "width": 12, "height": 4},
                refresh_interval=60
            ),
            DashboardWidget(
                widget_id="cpu_chart",
                title="CPU Usage",
                widget_type="metric_chart",
                position={"x": 0, "y": 8, "width": 6, "height": 4},
                config={"metric_name": "system.cpu_percent", "duration_minutes": 60},
                refresh_interval=30
            ),
            DashboardWidget(
                widget_id="memory_chart",
                title="Memory Usage",
                widget_type="metric_chart",
                position={"x": 6, "y": 8, "width": 6, "height": 4},
                config={"metric_name": "system.memory_percent", "duration_minutes": 60},
                refresh_interval=30
            )
        ]
        
        default_dashboard = Dashboard(
            dashboard_id="default_dashboard",
            name="Default Enterprise Dashboard",
            description="Default dashboard showing system health and monitoring metrics",
            widgets=widgets,
            layout={"grid_size": 12, "row_height": 60}
        )
        
        self.dashboards[default_dashboard.dashboard_id] = default_dashboard
        await self._save_dashboards()
        logger.info("Created default dashboard")
    
    async def create_real_time_dashboard(self, dashboard_id: str) -> Optional[RealTimeDashboard]:
        """
        Create a real-time dashboard instance.
        
        Args:
            dashboard_id: Dashboard ID
            
        Returns:
            Real-time dashboard instance or None if dashboard not found
        """
        dashboard = self.dashboards.get(dashboard_id)
        if not dashboard:
            logger.error(f"Dashboard {dashboard_id} not found")
            return None
        
        real_time_dashboard = RealTimeDashboard(dashboard, self.data_provider)
        self.real_time_dashboards[dashboard_id] = real_time_dashboard
        
        await real_time_dashboard.start()
        logger.info(f"Created real-time dashboard: {dashboard.name}")
        
        return real_time_dashboard
    
    def get_dashboard_statistics(self) -> Dict[str, Any]:
        """Get dashboard system statistics."""
        return {
            "total_dashboards": len(self.dashboards),
            "active_real_time_dashboards": len(self.real_time_dashboards),
            "total_widgets": sum(len(d.widgets) for d in self.dashboards.values()),
            "cache_size": len(self.data_provider.data_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup dashboard manager."""
        # Stop all real-time dashboards
        for rt_dashboard in self.real_time_dashboards.values():
            await rt_dashboard.stop()
        
        # Save dashboards
        await self._save_dashboards()
        
        logger.info("Dashboard manager cleaned up")

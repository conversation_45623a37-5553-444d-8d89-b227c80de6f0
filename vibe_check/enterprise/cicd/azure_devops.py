"""
File: vibe_check/enterprise/cicd/azure_devops.py
Purpose: Azure DevOps integration for CI/CD workflows
Related Files: vibe_check/enterprise/cicd/
Dependencies: typing, yaml, pathlib
"""

import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime

from .models import (
    CICDPlatform, PipelineConfiguration, PipelineExecution, 
    PipelineStep, QualityGate, WebhookPayload, PipelineStatus
)
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class AzureDevOpsIntegration:
    """Azure DevOps integration for Vibe Check."""
    
    def __init__(self):
        self.platform = CICDPlatform.AZURE_DEVOPS
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize Azure DevOps integration."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Azure DevOps integration initialized")
    
    def generate_pipeline_file(
        self,
        config: PipelineConfiguration,
        output_path: Optional[Path] = None
    ) -> str:
        """
        Generate Azure DevOps pipeline YAML file.
        
        Args:
            config: Pipeline configuration
            output_path: Optional path to save the pipeline file
            
        Returns:
            Generated pipeline YAML content
        """
        pipeline = {
            "trigger": self._generate_triggers(config.trigger_events),
            "pr": self._generate_pr_triggers(config.trigger_events),
            "pool": {
                "vmImage": "ubuntu-latest"
            },
            "variables": self._generate_variables(config),
            "stages": self._generate_stages(config)
        }
        
        yaml_content = yaml.dump(pipeline, default_flow_style=False, sort_keys=False)
        
        # Save to file if path provided
        if output_path:
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(yaml_content)
            logger.info(f"Generated Azure DevOps pipeline: {output_path}")
        
        return yaml_content
    
    def _generate_triggers(self, trigger_events: List[str]) -> Dict[str, Any]:
        """Generate pipeline triggers."""
        triggers = {
            "branches": {
                "include": ["main", "develop"]
            },
            "paths": {
                "exclude": ["docs/*", "*.md"]
            }
        }
        
        if "schedule" in trigger_events:
            triggers["schedules"] = [{
                "cron": "0 2 * * 1",  # Weekly on Monday 2 AM
                "displayName": "Weekly Vibe Check",
                "branches": {
                    "include": ["main"]
                }
            }]
        
        return triggers
    
    def _generate_pr_triggers(self, trigger_events: List[str]) -> Dict[str, Any]:
        """Generate pull request triggers."""
        if "pull_request" in trigger_events:
            return {
                "branches": {
                    "include": ["main", "develop"]
                },
                "paths": {
                    "exclude": ["docs/*", "*.md"]
                }
            }
        return "none"
    
    def _generate_variables(self, config: PipelineConfiguration) -> Dict[str, str]:
        """Generate pipeline variables."""
        variables = {
            "pythonVersion": "3.11",
            "vibeCheckOutputDir": "$(Build.ArtifactStagingDirectory)/vibe-check-reports"
        }
        
        # Add custom environment variables
        variables.update(config.environment_variables)
        
        return variables
    
    def _generate_stages(self, config: PipelineConfiguration) -> List[Dict[str, Any]]:
        """Generate pipeline stages."""
        stages = []
        
        # Analysis stage
        analysis_jobs = []
        
        # Main analysis job
        analysis_jobs.append(self._generate_analysis_job(config))
        
        # Quality gates job (if configured)
        if config.quality_gates:
            analysis_jobs.append(self._generate_quality_gates_job(config))
        
        stages.append({
            "stage": "Analysis",
            "displayName": "Vibe Check Analysis",
            "jobs": analysis_jobs
        })
        
        # Report stage
        if config.analysis_config.get("generate_report", True):
            stages.append({
                "stage": "Report",
                "displayName": "Generate Reports",
                "dependsOn": "Analysis",
                "jobs": [self._generate_report_job(config)]
            })
        
        return stages
    
    def _generate_analysis_job(self, config: PipelineConfiguration) -> Dict[str, Any]:
        """Generate main analysis job."""
        steps = [
            {
                "task": "UsePythonVersion@0",
                "inputs": {
                    "versionSpec": "$(pythonVersion)"
                },
                "displayName": "Use Python $(pythonVersion)"
            },
            {
                "script": "python -m pip install --upgrade pip && pip install vibe-check",
                "displayName": "Install Vibe Check"
            }
        ]
        
        # Add custom preparation steps
        for step in config.steps:
            if step.name.lower().startswith("prepare"):
                steps.append({
                    "script": step.command,
                    "displayName": step.name
                })
        
        # Add main analysis step
        analysis_cmd = self._generate_analysis_command(config.analysis_config)
        steps.append({
            "script": analysis_cmd,
            "displayName": "Run Vibe Check Analysis"
        })
        
        # Add test results publishing
        steps.extend([
            {
                "task": "PublishTestResults@2",
                "condition": "succeededOrFailed()",
                "inputs": {
                    "testResultsFiles": "$(vibeCheckOutputDir)/junit.xml",
                    "testRunTitle": "Vibe Check Analysis Results"
                }
            },
            {
                "task": "PublishHtmlReport@1",
                "condition": "succeededOrFailed()",
                "inputs": {
                    "reportDir": "$(vibeCheckOutputDir)",
                    "tabName": "Vibe Check Report"
                }
            },
            {
                "task": "PublishBuildArtifacts@1",
                "condition": "succeededOrFailed()",
                "inputs": {
                    "pathToPublish": "$(vibeCheckOutputDir)",
                    "artifactName": "vibe-check-reports"
                }
            }
        ])
        
        return {
            "job": "VibeCheckAnalysis",
            "displayName": "Vibe Check Analysis",
            "steps": steps
        }
    
    def _generate_quality_gates_job(self, config: PipelineConfiguration) -> Dict[str, Any]:
        """Generate quality gates job."""
        return {
            "job": "QualityGates",
            "displayName": "Quality Gates Evaluation",
            "dependsOn": "VibeCheckAnalysis",
            "steps": [
                {
                    "task": "UsePythonVersion@0",
                    "inputs": {
                        "versionSpec": "$(pythonVersion)"
                    }
                },
                {
                    "script": "pip install vibe-check",
                    "displayName": "Install Vibe Check"
                },
                {
                    "script": self._generate_quality_gate_command(config.quality_gates),
                    "displayName": "Evaluate Quality Gates"
                }
            ]
        }
    
    def _generate_report_job(self, config: PipelineConfiguration) -> Dict[str, Any]:
        """Generate report generation job."""
        return {
            "job": "GenerateReports",
            "displayName": "Generate Comprehensive Reports",
            "steps": [
                {
                    "task": "UsePythonVersion@0",
                    "inputs": {
                        "versionSpec": "$(pythonVersion)"
                    }
                },
                {
                    "script": "pip install vibe-check",
                    "displayName": "Install Vibe Check"
                },
                {
                    "script": """
vibe-check-report \\
  --input $(vibeCheckOutputDir) \\
  --format html,json,pdf \\
  --include-trends \\
  --include-recommendations
                    """.strip(),
                    "displayName": "Generate Comprehensive Reports"
                },
                {
                    "task": "PublishBuildArtifacts@1",
                    "inputs": {
                        "pathToPublish": "$(vibeCheckOutputDir)",
                        "artifactName": "comprehensive-reports"
                    }
                }
            ]
        }
    
    def _generate_analysis_command(self, analysis_config: Dict[str, Any]) -> str:
        """Generate Vibe Check analysis command."""
        cmd_parts = ["vibe-check-standalone", "."]
        
        # Add configuration options
        if "enabled_categories" in analysis_config:
            categories = ",".join(analysis_config["enabled_categories"])
            cmd_parts.extend(["--categories", categories])
        
        if "severity_threshold" in analysis_config:
            cmd_parts.extend(["--severity", analysis_config["severity_threshold"]])
        
        if analysis_config.get("generate_report", True):
            cmd_parts.extend([
                "--format", "html",
                "--output", "$(vibeCheckOutputDir)/report.html",
                "--junit-output", "$(vibeCheckOutputDir)/junit.xml",
                "--json-output", "$(vibeCheckOutputDir)/results.json"
            ])
        
        if analysis_config.get("fail_on_error", True):
            cmd_parts.append("--fail-on-error")
        
        return " \\\n  ".join(cmd_parts)
    
    def _generate_quality_gate_command(self, quality_gates: List[QualityGate]) -> str:
        """Generate quality gate evaluation command."""
        return """
vibe-check-quality-gates \\
  --config quality-gates.json \\
  --input $(vibeCheckOutputDir) \\
  --fail-on-error
        """.strip()
    
    async def parse_webhook(self, payload: Dict[str, Any]) -> Optional[WebhookPayload]:
        """
        Parse Azure DevOps webhook payload.
        
        Args:
            payload: Raw webhook payload
            
        Returns:
            Parsed webhook payload or None if invalid
        """
        try:
            # Handle build completion events
            if payload.get("eventType") == "build.complete":
                resource = payload["resource"]
                return WebhookPayload(
                    platform=self.platform,
                    event_type="build",
                    repository_url=resource["repository"]["url"],
                    branch=resource["sourceBranch"].replace("refs/heads/", ""),
                    commit_sha=resource["sourceVersion"],
                    commit_message=resource.get("reason", "Build triggered"),
                    author=resource.get("requestedFor", {}).get("displayName", "unknown"),
                    timestamp=datetime.fromisoformat(resource["startTime"].replace("Z", "+00:00")),
                    raw_payload=payload
                )
            
            # Handle Git push events
            elif payload.get("eventType") == "git.push":
                resource = payload["resource"]
                commits = resource.get("commits", [])
                latest_commit = commits[0] if commits else {}
                
                return WebhookPayload(
                    platform=self.platform,
                    event_type="push",
                    repository_url=resource["repository"]["url"],
                    branch=resource["refUpdates"][0]["name"].replace("refs/heads/", ""),
                    commit_sha=latest_commit.get("commitId", ""),
                    commit_message=latest_commit.get("comment", ""),
                    author=latest_commit.get("author", {}).get("displayName", "unknown"),
                    timestamp=datetime.fromisoformat(resource["date"].replace("Z", "+00:00")),
                    raw_payload=payload
                )
            
            # Handle pull request events
            elif payload.get("eventType") == "git.pullrequest.created":
                resource = payload["resource"]
                return WebhookPayload(
                    platform=self.platform,
                    event_type="pull_request",
                    repository_url=resource["repository"]["url"],
                    branch=resource["sourceRefName"].replace("refs/heads/", ""),
                    commit_sha=resource["lastMergeSourceCommit"]["commitId"],
                    commit_message=resource["title"],
                    author=resource["createdBy"]["displayName"],
                    timestamp=datetime.fromisoformat(resource["creationDate"].replace("Z", "+00:00")),
                    raw_payload=payload
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Failed to parse Azure DevOps webhook: {e}")
            return None
    
    async def get_pipeline_status(
        self,
        organization: str,
        project: str,
        pipeline_id: str,
        run_id: str
    ) -> Optional[PipelineExecution]:
        """
        Get pipeline execution status.
        
        Args:
            organization: Azure DevOps organization
            project: Project name
            pipeline_id: Pipeline ID
            run_id: Pipeline run ID
            
        Returns:
            Pipeline execution status or None if not found
        """
        # This would integrate with Azure DevOps REST API
        # For now, return a mock execution
        return PipelineExecution(
            execution_id=run_id,
            config_id=pipeline_id,
            platform=self.platform,
            repository_url=f"https://dev.azure.com/{organization}/{project}",
            branch="main",
            commit_sha="abc123",
            trigger_event="push",
            status=PipelineStatus.SUCCESS
        )
    
    def generate_azure_pipelines_template(self) -> str:
        """Generate a comprehensive Azure Pipelines template."""
        template = """
# Vibe Check Azure DevOps Pipeline Template
# This template provides comprehensive code analysis using Vibe Check

trigger:
  branches:
    include:
      - main
      - develop
  paths:
    exclude:
      - docs/*
      - '*.md'

pr:
  branches:
    include:
      - main
      - develop
  paths:
    exclude:
      - docs/*
      - '*.md'

schedules:
  - cron: "0 2 * * 1"
    displayName: Weekly Vibe Check
    branches:
      include:
        - main

pool:
  vmImage: 'ubuntu-latest'

variables:
  pythonVersion: '3.11'
  vibeCheckOutputDir: '$(Build.ArtifactStagingDirectory)/vibe-check-reports'
  vibeCheckVersion: 'latest'

stages:
  - stage: Analysis
    displayName: 'Vibe Check Analysis'
    jobs:
      - job: VibeCheckAnalysis
        displayName: 'Run Vibe Check Analysis'
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: '$(pythonVersion)'
            displayName: 'Use Python $(pythonVersion)'
          
          - script: |
              python -m pip install --upgrade pip
              pip install vibe-check==$(vibeCheckVersion)
            displayName: 'Install Vibe Check'
          
          - script: |
              mkdir -p $(vibeCheckOutputDir)
              vibe-check-standalone . \\
                --categories style,security,complexity,docs,imports,types \\
                --severity warning \\
                --format html \\
                --output $(vibeCheckOutputDir)/report.html \\
                --junit-output $(vibeCheckOutputDir)/junit.xml \\
                --json-output $(vibeCheckOutputDir)/results.json \\
                --fail-on-error
            displayName: 'Run Comprehensive Analysis'
          
          - task: PublishTestResults@2
            condition: succeededOrFailed()
            inputs:
              testResultsFiles: '$(vibeCheckOutputDir)/junit.xml'
              testRunTitle: 'Vibe Check Analysis Results'
            displayName: 'Publish Test Results'
          
          - task: PublishHtmlReport@1
            condition: succeededOrFailed()
            inputs:
              reportDir: '$(vibeCheckOutputDir)'
              tabName: 'Vibe Check Report'
            displayName: 'Publish HTML Report'
          
          - task: PublishBuildArtifacts@1
            condition: succeededOrFailed()
            inputs:
              pathToPublish: '$(vibeCheckOutputDir)'
              artifactName: 'vibe-check-reports'
            displayName: 'Publish Analysis Artifacts'

      - job: QualityGates
        displayName: 'Quality Gates Evaluation'
        dependsOn: VibeCheckAnalysis
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: '$(pythonVersion)'
          
          - script: pip install vibe-check==$(vibeCheckVersion)
            displayName: 'Install Vibe Check'
          
          - script: |
              vibe-check-quality-gates \\
                --config quality-gates.json \\
                --input $(vibeCheckOutputDir) \\
                --fail-on-error
            displayName: 'Evaluate Quality Gates'

  - stage: Report
    displayName: 'Generate Reports'
    dependsOn: Analysis
    jobs:
      - job: GenerateReports
        displayName: 'Generate Comprehensive Reports'
        steps:
          - task: UsePythonVersion@0
            inputs:
              versionSpec: '$(pythonVersion)'
          
          - script: pip install vibe-check==$(vibeCheckVersion)
            displayName: 'Install Vibe Check'
          
          - script: |
              vibe-check-report \\
                --input $(vibeCheckOutputDir) \\
                --format html,json,pdf \\
                --include-trends \\
                --include-recommendations
            displayName: 'Generate Comprehensive Reports'
          
          - task: PublishBuildArtifacts@1
            inputs:
              pathToPublish: '$(vibeCheckOutputDir)'
              artifactName: 'comprehensive-reports'
            displayName: 'Publish Comprehensive Reports'
        """.strip()
        
        return template
    
    async def cleanup(self) -> None:
        """Cleanup Azure DevOps integration."""
        logger.info("Azure DevOps integration cleaned up")

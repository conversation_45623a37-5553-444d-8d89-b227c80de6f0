"""
File: vibe_check/enterprise/security/security_monitor.py
Purpose: Security monitoring and threat detection for enterprise security
Related Files: vibe_check/enterprise/security/
Dependencies: typing, datetime, enum, dataclasses
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ThreatLevel(Enum):
    """Threat severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class AlertType(Enum):
    """Security alert types."""
    FAILED_LOGIN = "failed_login"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    DATA_BREACH = "data_breach"
    MALWARE_DETECTED = "malware_detected"
    POLICY_VIOLATION = "policy_violation"


@dataclass
class SecurityAlert:
    """Security alert."""
    alert_id: str
    alert_type: AlertType
    threat_level: ThreatLevel
    title: str
    description: str
    timestamp: datetime
    source: str
    affected_resource: Optional[str] = None
    user_id: Optional[str] = None
    source_ip: Optional[str] = None
    resolved: bool = False
    resolved_at: Optional[datetime] = None
    resolved_by: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "alert_id": self.alert_id,
            "alert_type": self.alert_type.value,
            "threat_level": self.threat_level.value,
            "title": self.title,
            "description": self.description,
            "timestamp": self.timestamp.isoformat(),
            "source": self.source,
            "affected_resource": self.affected_resource,
            "user_id": self.user_id,
            "source_ip": self.source_ip,
            "resolved": self.resolved,
            "resolved_at": self.resolved_at.isoformat() if self.resolved_at else None,
            "resolved_by": self.resolved_by
        }


class ThreatDetector:
    """Threat detection engine."""
    
    def __init__(self):
        """Initialize threat detector."""
        self.failed_login_attempts: Dict[str, List[datetime]] = {}
        self.suspicious_ips: Dict[str, int] = {}
        self.alert_thresholds = {
            "failed_login_threshold": 5,
            "failed_login_window_minutes": 15,
            "suspicious_ip_threshold": 10
        }
    
    def detect_failed_login_pattern(self, user_id: str, source_ip: str) -> Optional[SecurityAlert]:
        """Detect failed login patterns."""
        now = datetime.now()
        window_start = now - timedelta(minutes=self.alert_thresholds["failed_login_window_minutes"])
        
        # Track failed login attempts
        if user_id not in self.failed_login_attempts:
            self.failed_login_attempts[user_id] = []
        
        self.failed_login_attempts[user_id].append(now)
        
        # Remove old attempts outside the window
        self.failed_login_attempts[user_id] = [
            attempt for attempt in self.failed_login_attempts[user_id]
            if attempt >= window_start
        ]
        
        # Check if threshold exceeded
        if len(self.failed_login_attempts[user_id]) >= self.alert_thresholds["failed_login_threshold"]:
            return SecurityAlert(
                alert_id=str(uuid.uuid4()),
                alert_type=AlertType.FAILED_LOGIN,
                threat_level=ThreatLevel.MEDIUM,
                title="Multiple Failed Login Attempts",
                description=f"User {user_id} has {len(self.failed_login_attempts[user_id])} failed login attempts in the last {self.alert_thresholds['failed_login_window_minutes']} minutes",
                timestamp=now,
                source="threat_detector",
                user_id=user_id,
                source_ip=source_ip
            )
        
        return None
    
    def detect_suspicious_ip(self, source_ip: str) -> Optional[SecurityAlert]:
        """Detect suspicious IP activity."""
        self.suspicious_ips[source_ip] = self.suspicious_ips.get(source_ip, 0) + 1
        
        if self.suspicious_ips[source_ip] >= self.alert_thresholds["suspicious_ip_threshold"]:
            return SecurityAlert(
                alert_id=str(uuid.uuid4()),
                alert_type=AlertType.SUSPICIOUS_ACTIVITY,
                threat_level=ThreatLevel.HIGH,
                title="Suspicious IP Activity",
                description=f"IP address {source_ip} has generated {self.suspicious_ips[source_ip]} security events",
                timestamp=datetime.now(),
                source="threat_detector",
                source_ip=source_ip
            )
        
        return None


class SecurityMonitor:
    """Enterprise security monitoring system."""
    
    def __init__(self):
        """Initialize security monitor."""
        self.threat_detector = ThreatDetector()
        self.alerts: Dict[str, SecurityAlert] = {}
        self.active_alerts: List[str] = []
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize security monitor."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Security monitor initialized")
    
    def create_alert(
        self,
        alert_type: AlertType,
        threat_level: ThreatLevel,
        title: str,
        description: str,
        source: str,
        affected_resource: Optional[str] = None,
        user_id: Optional[str] = None,
        source_ip: Optional[str] = None
    ) -> str:
        """
        Create security alert.
        
        Args:
            alert_type: Type of alert
            threat_level: Threat severity level
            title: Alert title
            description: Alert description
            source: Alert source
            affected_resource: Affected resource
            user_id: User ID if applicable
            source_ip: Source IP if applicable
            
        Returns:
            Alert ID
        """
        alert = SecurityAlert(
            alert_id=str(uuid.uuid4()),
            alert_type=alert_type,
            threat_level=threat_level,
            title=title,
            description=description,
            timestamp=datetime.now(),
            source=source,
            affected_resource=affected_resource,
            user_id=user_id,
            source_ip=source_ip
        )
        
        self.alerts[alert.alert_id] = alert
        self.active_alerts.append(alert.alert_id)
        
        # Log alert based on severity
        log_message = f"Security Alert: {title} - {description}"
        if threat_level == ThreatLevel.CRITICAL:
            logger.critical(log_message)
        elif threat_level == ThreatLevel.HIGH:
            logger.error(log_message)
        elif threat_level == ThreatLevel.MEDIUM:
            logger.warning(log_message)
        else:
            logger.info(log_message)
        
        return alert.alert_id
    
    def resolve_alert(self, alert_id: str, resolved_by: str) -> bool:
        """
        Resolve security alert.
        
        Args:
            alert_id: Alert ID
            resolved_by: User who resolved the alert
            
        Returns:
            True if resolved successfully, False otherwise
        """
        if alert_id not in self.alerts:
            return False
        
        alert = self.alerts[alert_id]
        alert.resolved = True
        alert.resolved_at = datetime.now()
        alert.resolved_by = resolved_by
        
        # Remove from active alerts
        if alert_id in self.active_alerts:
            self.active_alerts.remove(alert_id)
        
        logger.info(f"Security alert {alert_id} resolved by {resolved_by}")
        return True
    
    def process_failed_login(self, user_id: str, source_ip: str) -> Optional[str]:
        """
        Process failed login attempt and check for threats.
        
        Args:
            user_id: User ID
            source_ip: Source IP address
            
        Returns:
            Alert ID if threat detected, None otherwise
        """
        # Check for failed login pattern
        alert = self.threat_detector.detect_failed_login_pattern(user_id, source_ip)
        if alert:
            self.alerts[alert.alert_id] = alert
            self.active_alerts.append(alert.alert_id)
            return alert.alert_id
        
        # Check for suspicious IP
        alert = self.threat_detector.detect_suspicious_ip(source_ip)
        if alert:
            self.alerts[alert.alert_id] = alert
            self.active_alerts.append(alert.alert_id)
            return alert.alert_id
        
        return None
    
    def get_active_alerts(self, threat_level: Optional[ThreatLevel] = None) -> List[SecurityAlert]:
        """
        Get active security alerts.
        
        Args:
            threat_level: Filter by threat level
            
        Returns:
            List of active alerts
        """
        alerts = []
        for alert_id in self.active_alerts:
            alert = self.alerts.get(alert_id)
            if alert and (not threat_level or alert.threat_level == threat_level):
                alerts.append(alert)
        
        # Sort by timestamp (newest first)
        alerts.sort(key=lambda a: a.timestamp, reverse=True)
        return alerts
    
    def get_alert(self, alert_id: str) -> Optional[SecurityAlert]:
        """Get alert by ID."""
        return self.alerts.get(alert_id)
    
    def get_alerts_by_user(self, user_id: str) -> List[SecurityAlert]:
        """Get alerts for specific user."""
        user_alerts = []
        for alert in self.alerts.values():
            if alert.user_id == user_id:
                user_alerts.append(alert)
        
        user_alerts.sort(key=lambda a: a.timestamp, reverse=True)
        return user_alerts
    
    def get_security_summary(self) -> Dict[str, Any]:
        """Get security monitoring summary."""
        total_alerts = len(self.alerts)
        active_alerts = len(self.active_alerts)
        resolved_alerts = total_alerts - active_alerts
        
        # Count alerts by threat level
        alerts_by_level = {}
        for alert in self.alerts.values():
            level = alert.threat_level.value
            alerts_by_level[level] = alerts_by_level.get(level, 0) + 1
        
        # Count alerts by type
        alerts_by_type = {}
        for alert in self.alerts.values():
            alert_type = alert.alert_type.value
            alerts_by_type[alert_type] = alerts_by_type.get(alert_type, 0) + 1
        
        # Recent activity (last 24 hours)
        recent_cutoff = datetime.now() - timedelta(hours=24)
        recent_alerts = len([a for a in self.alerts.values() if a.timestamp >= recent_cutoff])
        
        return {
            "total_alerts": total_alerts,
            "active_alerts": active_alerts,
            "resolved_alerts": resolved_alerts,
            "recent_alerts_24h": recent_alerts,
            "alerts_by_threat_level": alerts_by_level,
            "alerts_by_type": alerts_by_type,
            "threat_detector_stats": {
                "tracked_users": len(self.threat_detector.failed_login_attempts),
                "suspicious_ips": len(self.threat_detector.suspicious_ips)
            }
        }
    
    async def cleanup(self) -> None:
        """Cleanup security monitor."""
        logger.info("Security monitor cleaned up")

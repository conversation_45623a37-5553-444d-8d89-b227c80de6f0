"""
Base Tool Runner
============

This module defines the base class for all tool runners.
"""

import logging
import os
import subprocess
import tempfile
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger("pat_tool_runner")


class ToolRunner(ABC):
    """
    Base class for tool runners.

    Tool runners are responsible for executing specific analysis tools
    on file content and capturing their output in a standardized format.
    """

    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the tool runner.

        Args:
            name: Tool name
            config: Tool configuration
        """
        self.name = name
        self.config = config or {}

    @abstractmethod
    async def run(self, file_path: Union[str, Path],
                content: str) -> Dict[str, Any]:
        """
        Run the tool on the file content.

        Args:
            file_path: Path to the file being analyzed
            content: File content as a string

        Returns:
            Dictionary with analysis results
        """
        pass

    def get_fallback_analysis(self, file_path: Union[str, Path],
                            content: str) -> Dict[str, Any]:
        """
        Provide fallback analysis when the external tool is not available.

        Args:
            file_path: Path to the file being analyzed
            content: File content as a string

        Returns:
            Dictionary with comprehensive analysis results using standalone analyzer
        """
        from ...core.analysis.standalone_analyzer import StandaloneCodeAnalyzer

        try:
            analyzer = StandaloneCodeAnalyzer()
            result = analyzer.analyze_file(file_path, content)

            # Adapt the result format to match tool runner expectations
            issues = result.get("issues", [])

            return {
                "issues": issues,
                "summary": {
                    "total": len(issues),
                    "by_type": self._categorize_issues(issues)
                },
                "metrics": result.get("metrics", {}),
                "tool_status": "unavailable",
                "fallback_used": True,
                "analyzer": "vibe_check_standalone",
                "message": f"{self.name} is not available - using comprehensive standalone analysis"
            }
        except Exception as e:
            logger.error(f"Error in fallback analysis: {e}")
            return {
                "issues": [],
                "summary": {
                    "total": 0,
                    "by_type": {}
                },
                "tool_status": "unavailable",
                "fallback_used": True,
                "error": str(e),
                "message": f"{self.name} is not available - fallback analysis failed"
            }

    def _categorize_issues(self, issues: List[Dict[str, Any]]) -> Dict[str, int]:
        """Categorize issues by severity."""
        categories: Dict[str, int] = {}
        for issue in issues:
            severity = issue.get("severity", "unknown")
            categories[severity] = categories.get(severity, 0) + 1
        return categories

    @abstractmethod
    def is_available(self) -> bool:
        """
        Check if the tool is available on the system.

        Returns:
            True if the tool is available, False otherwise
        """
        pass

    def create_temp_file(self, content: str,
                       suffix: str = '.py') -> str:
        """
        Create a temporary file with the given content.

        Args:
            content: File content
            suffix: File suffix

        Returns:
            Path to the temporary file
        """
        with tempfile.NamedTemporaryFile(mode='w', suffix=suffix, delete=False) as temp_file:
            temp_path = temp_file.name
            temp_file.write(content)

        return temp_path

    def cleanup_temp_file(self, temp_path: str) -> None:
        """
        Clean up a temporary file.

        Args:
            temp_path: Path to the temporary file
        """
        try:
            os.unlink(temp_path)
        except Exception as e:
            logger.warning(f"Error removing temporary file {temp_path}: {e}")

    async def run_process(self, cmd: List[str],
                       timeout: Optional[float] = None) -> Dict[str, Any]:
        """
        Run a subprocess and capture its output.

        Args:
            cmd: Command to run
            timeout: Timeout in seconds

        Returns:
            Dictionary with stdout, stderr, and return code
        """
        try:
            # Use asyncio.create_subprocess_exec for async subprocess execution
            import asyncio
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            try:
                if timeout is not None:
                    stdout, stderr = await asyncio.wait_for(process.communicate(), timeout=timeout)
                else:
                    stdout, stderr = await process.communicate()

                return {
                    "stdout": stdout.decode('utf-8', errors='replace'),
                    "stderr": stderr.decode('utf-8', errors='replace'),
                    "returncode": process.returncode
                }
            except asyncio.TimeoutError:
                # Kill the process if it times out
                try:
                    process.kill()
                except Exception:
                    pass

                return {
                    "stdout": "",
                    "stderr": "Process timed out",
                    "returncode": -1,
                    "error": f"Process timed out after {timeout} seconds"
                }
        except Exception as e:
            logger.error(f"Error running command {' '.join(cmd)}: {e}")
            return {
                "stdout": "",
                "stderr": str(e),
                "returncode": -1,
                "error": str(e)
            }

    def get_config_args(self) -> List[str]:
        """
        Get command-line arguments from the configuration.

        Returns:
            List of command-line arguments
        """
        args = []

        # Add args from config
        if "args" in self.config and isinstance(self.config["args"], list):
            args.extend(self.config["args"])

        return args

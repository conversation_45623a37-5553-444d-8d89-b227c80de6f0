"""
Ruff Tool Runner
=============

This module provides a runner for the Ruff linter, which is a fast Python linter
written in Rust.

Ruff is used to find and fix common issues in Python code.
"""

import json
import logging
import os
import shutil
from pathlib import Path
from typing import Any, Dict, List, Union, Optional

from .base_runner import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .tool_registry import register_tool, check_command_availability

logger = logging.getLogger("pat_ruff_runner")


class RuffRunner(ToolRunner):
    """Runner for the Ruff linter."""
    
    def __init__(self, name: str = "ruff", config: Optional[Dict[str, Any]] = None):
        """
        Initialize the Ruff runner.
        
        Args:
            name: Tool name
            config: Tool configuration
        """
        super().__init__(name, config)
    
    async def run(self, file_path: Union[str, Path], content: str) -> Dict[str, Any]:
        """
        Run Ruff on the file content.
        
        Args:
            file_path: Path to the file being analyzed
            content: File content as a string
            
        Returns:
            Dictionary with analysis results
        """
        try:
            # Create a temporary file with the content
            temp_path = self.create_temp_file(content, suffix='.py')
            
            try:
                # Build the command
                cmd = ["ruff", "check", "--format=json", temp_path]
                
                # Add any additional arguments from config
                cmd.extend(self.get_config_args())
                
                # Run the command
                process_result = await self.run_process(cmd)
                
                # Parse the output
                if process_result["returncode"] == 0:
                    # No issues found
                    return {
                        "issues": [],
                        "summary": {
                            "total": 0,
                            "by_type": {}
                        }
                    }
                else:
                    try:
                        stdout = process_result["stdout"]
                        issues = json.loads(stdout)
                        
                        # Group issues by type
                        by_type: Dict[str, int] = {}
                        for issue in issues:
                            issue_type = issue.get("code", "").split(".")[0]
                            by_type[issue_type] = by_type.get(issue_type, 0) + 1
                        
                        return {
                            "issues": issues,
                            "summary": {
                                "total": len(issues),
                                "by_type": by_type
                            }
                        }
                    except json.JSONDecodeError:
                        # Fall back to text parsing if JSON parsing fails
                        issues = []
                        lines = process_result["stdout"].splitlines()
                        for line in lines:
                            if ':' in line:
                                parts = line.split(':', 3)
                                if len(parts) >= 3:
                                    issues.append({
                                        "file": parts[0],
                                        "line": int(parts[1]) if parts[1].strip().isdigit() else 0,
                                        "col": int(parts[2]) if parts[2].strip().isdigit() else 0,
                                        "message": parts[3].strip() if len(parts) > 3 else "",
                                        "code": "unknown"
                                    })
                        
                        return {
                            "issues": issues,
                            "summary": {
                                "total": len(issues),
                                "by_type": {}
                            }
                        }
            finally:
                # Clean up the temporary file
                self.cleanup_temp_file(temp_path)
                
        except Exception as e:
            logger.error(f"Error running Ruff: {e}")
            return {
                "issues": [],
                "summary": {
                    "total": 0,
                    "by_type": {}
                },
                "error": str(e)
            }
    
    def is_available(self) -> bool:
        """
        Check if Ruff is available on the system.

        Returns:
            True if Ruff is available, False otherwise
        """
        return check_command_availability("ruff")

    def get_fallback_analysis(self, file_path: Union[str, Path],
                            content: str) -> Dict[str, Any]:
        """
        Provide fallback analysis when ruff is not available.
        Uses basic Python AST analysis for common issues.
        """
        import ast
        from pathlib import Path

        issues = []

        try:
            # Parse the Python code
            tree = ast.parse(content, filename=str(file_path))

            # Check for long lines (basic detection)
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if len(line) > 88:  # PEP 8 line length
                    issues.append({
                        "code": "E501",
                        "message": f"line too long ({len(line)} > 88 characters)",
                        "line": i,
                        "column": 89,
                        "severity": "warning",
                        "source": "vibe_check_fallback"
                    })

            # Check for basic style issues
            for node in ast.walk(tree):
                # Check for missing docstrings in functions/classes
                if isinstance(node, (ast.FunctionDef, ast.ClassDef)):
                    if not ast.get_docstring(node):
                        issues.append({
                            "code": "D100",
                            "message": f"Missing docstring in {type(node).__name__.lower()}",
                            "line": node.lineno,
                            "column": node.col_offset,
                            "severity": "info",
                            "source": "vibe_check_fallback"
                        })

        except SyntaxError as e:
            issues.append({
                "code": "E999",
                "message": f"SyntaxError: {e.msg}",
                "line": e.lineno or 1,
                "column": e.offset or 0,
                "severity": "error",
                "source": "vibe_check_fallback"
            })
        except Exception:
            # If AST parsing fails, provide minimal analysis
            pass

        return {
            "issues": issues,
            "summary": {
                "total": len(issues),
                "by_type": {
                    "error": len([i for i in issues if i["severity"] == "error"]),
                    "warning": len([i for i in issues if i["severity"] == "warning"]),
                    "info": len([i for i in issues if i["severity"] == "info"])
                }
            },
            "tool_status": "unavailable",
            "fallback_used": True,
            "message": "Ruff not available - using basic Python AST analysis"
        }


# Register this tool runner
register_tool("ruff", RuffRunner)

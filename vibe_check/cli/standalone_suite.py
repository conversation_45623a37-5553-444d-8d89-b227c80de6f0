"""
File: vibe_check/cli/standalone_suite.py
Purpose: Full analysis suite (vibe-check-standalone command)
Related Files: vibe_check/cli/standalone.py, vibe_check/core/vcs/
Dependencies: click, vibe_check.core.vcs
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import List, Optional, Tuple

import click
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.table import Table

from vibe_check.core.vcs.engine import VibeCheckEngine
from vibe_check.core.vcs.config import VCSConfig
from vibe_check.core.vcs.models import AnalysisResult, AnalysisTarget, AnalysisContext, EngineMode
from vibe_check.cli.formatters import format_analysis_results
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)
console = Console()


@click.command()
@click.argument('paths', nargs=-1, type=click.Path(exists=True, path_type=Path))
@click.option('--rules', '-r', help='Comma-separated list of rule categories (style,security,complexity,documentation,imports,types)')
@click.option('--external-tools', '-e', help='Comma-separated list of external tools (ruff,mypy,bandit)')
@click.option('--format', '-f', 'output_format', default='text', type=click.Choice(['text', 'json', 'yaml', 'sarif', 'github']), help='Output format')
@click.option('--output', '-o', type=click.Path(path_type=Path), help='Output file (default: stdout)')
@click.option('--config', '-c', type=click.Path(exists=True, path_type=Path), help='Configuration file')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.option('--quiet', '-q', is_flag=True, help='Quiet mode (minimal output)')
@click.option('--fix', is_flag=True, help='Auto-fix issues where possible')
@click.option('--diff', is_flag=True, help='Show diff for fixes')
@click.option('--stats', is_flag=True, help='Show detailed statistics')
@click.option('--benchmark', is_flag=True, help='Show performance benchmarks')
@click.version_option()
def vibe_check_standalone(paths: Tuple[Path, ...], rules: Optional[str], external_tools: Optional[str],
                         output_format: str, output: Optional[Path], config: Optional[Path],
                         verbose: bool, quiet: bool, fix: bool, diff: bool, stats: bool, benchmark: bool):
    """Vibe Check Standalone - Complete Python code analysis suite."""
    
    async def run_analysis():
        engine = None
        start_time = time.time()
        
        try:
            # Initialize VCS engine
            vcs_config = VCSConfig(mode=EngineMode.STANDALONE)
            if config:
                # Load custom configuration (implementation coming in Sprint VCS-2.2)
                if verbose:
                    console.print(f"Loading configuration from {config}")
            
            engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=vcs_config)
            await engine.start()
            
            if verbose:
                console.print("VCS analysis engine initialized")
            
            # Determine paths to analyze
            if not paths:
                paths_list = [Path.cwd()]
            else:
                paths_list = list(paths)
            
            # Parse options
            rules_list = rules.split(',') if rules else None
            external_tools_list = external_tools.split(',') if external_tools else None
            
            # Collect Python files
            python_files = []
            for path in paths_list:
                if path.is_file() and path.suffix == '.py':
                    python_files.append(path)
                elif path.is_dir():
                    # Use smart filtering (exclude venv, __pycache__, etc.)
                    for file_path in path.rglob("*.py"):
                        # Skip common ignore patterns
                        if any(part in str(file_path) for part in ['venv', '__pycache__', '.git', 'node_modules']):
                            continue
                        python_files.append(file_path)
            
            if not python_files:
                console.print("[yellow]No Python files found to analyze[/yellow]")
                return
            
            if not quiet:
                console.print(f"Found {len(python_files)} Python files to analyze...")
                if rules_list:
                    console.print(f"Using rule categories: {', '.join(rules_list)}")
                if external_tools_list:
                    console.print(f"Coordinating with tools: {', '.join(external_tools_list)}")
            
            # Run analysis with progress tracking
            results = []
            context = AnalysisContext.create_default(EngineMode.STANDALONE)

            if quiet:
                # Simple progress for quiet mode
                for i, file_path in enumerate(python_files):
                    target = AnalysisTarget.from_file(file_path)
                    result = await engine.analyze(target, context)
                    results.append(result)
                    if i % 10 == 0:  # Progress every 10 files
                        console.print(f"Analyzed {i+1}/{len(python_files)} files...", end='\r')
            else:
                # Rich progress bar for normal mode
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    BarColumn(),
                    TaskProgressColumn(),
                    console=console
                ) as progress:
                    task = progress.add_task("Analyzing files...", total=len(python_files))

                    for file_path in python_files:
                        target = AnalysisTarget.from_file(file_path)
                        result = await engine.analyze(target, context)
                        results.append(result)
                        progress.advance(task)

                        if verbose:
                            issues_count = len(result.issues)
                            progress.console.print(f"  {file_path}: {issues_count} issues found")
            
            # Combine results
            combined_result = AnalysisResult.combine_results(results)
            analysis_time = time.time() - start_time
            
            # Generate comprehensive report
            if not quiet:
                console.print("\n" + "="*60)
                console.print("VIBE CHECK STANDALONE ANALYSIS COMPLETE")
                console.print("="*60)
            
            # Statistics
            if stats or not quiet:
                stats_table = Table(title="Analysis Statistics")
                stats_table.add_column("Metric", style="cyan")
                stats_table.add_column("Value", style="green")
                
                stats_table.add_row("Files analyzed", str(len(python_files)))
                stats_table.add_row("Total issues", str(len(combined_result.issues)))
                
                # Issues by category
                category_counts = {}
                for issue in combined_result.issues:
                    category_counts[issue.category] = category_counts.get(issue.category, 0) + 1
                
                for category, count in category_counts.items():
                    stats_table.add_row(f"  {category} issues", str(count))
                
                # Auto-fixable issues
                auto_fixable = sum(1 for issue in combined_result.issues if hasattr(issue, 'auto_fixable') and issue.auto_fixable)
                stats_table.add_row("Auto-fixable issues", str(auto_fixable))
                
                stats_table.add_row("Analysis time", f"{analysis_time:.2f}s")
                stats_table.add_row("Average per file", f"{analysis_time/len(python_files):.3f}s")
                
                console.print(stats_table)
            
            # Benchmark information
            if benchmark:
                benchmark_table = Table(title="Performance Benchmarks")
                benchmark_table.add_column("Metric", style="cyan")
                benchmark_table.add_column("Value", style="green")
                benchmark_table.add_column("Target", style="yellow")
                benchmark_table.add_column("Status", style="white")
                
                files_per_second = len(python_files) / analysis_time
                benchmark_table.add_row(
                    "Files per second",
                    f"{files_per_second:.1f}",
                    ">100",
                    "✓" if files_per_second > 100 else "⚠"
                )
                
                avg_time = analysis_time / len(python_files)
                benchmark_table.add_row(
                    "Average time per file",
                    f"{avg_time:.3f}s",
                    "<0.01s",
                    "✓" if avg_time < 0.01 else "⚠"
                )
                
                # Project size classification
                if len(python_files) < 100:
                    target_time = 5.0
                    project_size = "Small"
                elif len(python_files) < 1000:
                    target_time = 30.0
                    project_size = "Medium"
                else:
                    target_time = 120.0
                    project_size = "Large"
                
                benchmark_table.add_row(
                    f"Total time ({project_size} project)",
                    f"{analysis_time:.1f}s",
                    f"<{target_time}s",
                    "✓" if analysis_time < target_time else "⚠"
                )
                
                console.print(benchmark_table)
            
            # Format and output results
            formatted_output = format_analysis_results([combined_result], output_format)
            
            if output:
                # Write to file
                with open(output, 'w') as f:
                    f.write(formatted_output)
                if not quiet:
                    console.print(f"\nResults written to {output}")
            else:
                # Print to stdout
                if not quiet:
                    console.print("\n" + "="*60)
                    console.print("DETAILED ANALYSIS RESULTS")
                    console.print("="*60)
                console.print(formatted_output)
            
            # Auto-fix if requested
            if fix:
                console.print("\n[yellow]Auto-fix feature coming in Sprint VCS-3.2[/yellow]")
                console.print("[yellow]For now, issues are identified for manual fixing[/yellow]")
            
            # Exit with appropriate code
            if combined_result.issues:
                if not quiet:
                    console.print(f"\n[yellow]Analysis complete with {len(combined_result.issues)} issues found[/yellow]")
                sys.exit(1)
            else:
                if not quiet:
                    console.print("\n[green]Analysis complete - no issues found![/green]")
                sys.exit(0)
                
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            if verbose:
                import traceback
                console.print(traceback.format_exc())
            sys.exit(1)
        finally:
            if engine and engine.is_enabled():
                await engine.stop()
    
    asyncio.run(run_analysis())


if __name__ == '__main__':
    vibe_check_standalone()

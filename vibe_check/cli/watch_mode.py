"""
File: vibe_check/cli/watch_mode.py
Purpose: Watch mode implementation for continuous analysis
Related Files: vibe_check/cli/standalone.py, vibe_check/core/vcs/
Dependencies: watchdog, asyncio, pathlib
"""

import asyncio
import time
from pathlib import Path
from typing import List, Optional, Set, Callable, Any

from rich.console import Console
from rich.live import Live
from rich.table import Table

from vibe_check.core.vcs.engine import VibeCheckEngine
from vibe_check.core.vcs.models import AnalysisResult, AnalysisTarget, AnalysisContext, EngineMode
from vibe_check.cli.output_formats import format_result
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)
console = Console()

# Try to import watchdog, but make it optional
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler, FileModifiedEvent, FileCreatedEvent
    WATCHDOG_AVAILABLE = True

    class PythonFileHandler(FileSystemEventHandler):
        """File system event handler for Python files."""

        def __init__(self, callback: Callable[[Path], None]):
            super().__init__()
            self.callback = callback
            self.debounce_time = 0.5  # 500ms debounce
            self.last_modified = {}

        def on_modified(self, event):
            """Handle file modification events."""
            if event.is_directory:
                return

            path = Path(event.src_path)
            if path.suffix == '.py':
                self._handle_file_change(path)

        def on_created(self, event):
            """Handle file creation events."""
            if event.is_directory:
                return

            path = Path(event.src_path)
            if path.suffix == '.py':
                self._handle_file_change(path)

        def _handle_file_change(self, path: Path):
            """Handle file change with debouncing."""
            now = time.time()
            last_time = self.last_modified.get(path, 0)

            if now - last_time > self.debounce_time:
                self.last_modified[path] = now
                self.callback(path)

except ImportError:
    WATCHDOG_AVAILABLE = False
    logger.warning("watchdog not available - watch mode disabled")

    # Create dummy classes for when watchdog is not available
    class Observer:
        def schedule(self, *args, **kwargs):
            pass
        def start(self):
            pass
        def stop(self):
            pass
        def join(self):
            pass

    class PythonFileHandler:
        """Dummy file system event handler for Python files."""

        def __init__(self, callback: Callable[[Path], None]):
            self.callback = callback
            self.debounce_time = 0.5  # 500ms debounce
            self.last_modified = {}

        def on_modified(self, event):
            """Handle file modification events."""
            pass

        def on_created(self, event):
            """Handle file creation events."""
            pass

        def _handle_file_change(self, path: Path):
            """Handle file change with debouncing."""
            pass


class WatchMode:
    """Watch mode implementation for continuous analysis."""
    
    def __init__(self, engine: VibeCheckEngine, output_format: str = "text"):
        self.engine = engine
        self.output_format = output_format
        self.context = AnalysisContext.create_default(EngineMode.STANDALONE)
        self.analysis_queue = asyncio.Queue()
        self.results_cache = {}
        self.observer = None
        self.running = False
    
    async def start_watching(self, paths: List[Path], verbose: bool = False) -> None:
        """Start watching specified paths for changes."""
        if not WATCHDOG_AVAILABLE:
            console.print("[red]Error: watchdog package not installed[/red]")
            console.print("Install with: pip install watchdog")
            return
        
        console.print(f"[green]Starting watch mode for {len(paths)} path(s)...[/green]")
        
        # Set up file system observer
        self.observer = Observer()
        handler = PythonFileHandler(self._on_file_changed)
        
        for path in paths:
            if path.is_dir():
                self.observer.schedule(handler, str(path), recursive=True)
                if verbose:
                    console.print(f"Watching directory: {path}")
            elif path.is_file():
                self.observer.schedule(handler, str(path.parent), recursive=False)
                if verbose:
                    console.print(f"Watching file: {path}")
        
        self.observer.start()
        self.running = True
        
        # Initial analysis
        console.print("[blue]Running initial analysis...[/blue]")
        await self._analyze_all_files(paths)
        
        # Start analysis worker
        analysis_task = asyncio.create_task(self._analysis_worker())
        
        try:
            # Display live status
            with Live(self._create_status_table(), refresh_per_second=1) as live:
                while self.running:
                    await asyncio.sleep(1)
                    live.update(self._create_status_table())
        except KeyboardInterrupt:
            console.print("\n[yellow]Stopping watch mode...[/yellow]")
        finally:
            self.running = False
            analysis_task.cancel()
            if self.observer:
                self.observer.stop()
                self.observer.join()
    
    def _on_file_changed(self, path: Path) -> None:
        """Handle file change event."""
        if self.running:
            asyncio.create_task(self._queue_analysis(path))
    
    async def _queue_analysis(self, path: Path) -> None:
        """Queue file for analysis."""
        await self.analysis_queue.put(path)
    
    async def _analysis_worker(self) -> None:
        """Worker that processes analysis queue."""
        while self.running:
            try:
                # Wait for file to analyze
                path = await asyncio.wait_for(self.analysis_queue.get(), timeout=1.0)
                
                # Analyze file
                console.print(f"[dim]Analyzing {path}...[/dim]")
                target = AnalysisTarget.from_file(path)
                result = await self.engine.analyze(target, self.context)
                
                # Cache result
                self.results_cache[path] = result
                
                # Display result
                if result.issues:
                    console.print(f"[red]Found {len(result.issues)} issues in {path}[/red]")
                    if self.output_format == "text":
                        for issue in result.issues[:3]:  # Show first 3 issues
                            console.print(f"  {issue.rule_id}: Line {issue.line} - {issue.message}")
                        if len(result.issues) > 3:
                            console.print(f"  ... and {len(result.issues) - 3} more issues")
                    else:
                        formatted = format_result(result, self.output_format)
                        console.print(formatted)
                else:
                    console.print(f"[green]No issues found in {path}[/green]")
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                console.print(f"[red]Error analyzing file: {e}[/red]")
    
    async def _analyze_all_files(self, paths: List[Path]) -> None:
        """Analyze all Python files in specified paths."""
        python_files = []
        
        for path in paths:
            if path.is_file() and path.suffix == '.py':
                python_files.append(path)
            elif path.is_dir():
                python_files.extend(path.rglob("*.py"))
        
        # Filter out common ignore patterns
        filtered_files = []
        for file_path in python_files:
            if not any(part in str(file_path) for part in ['venv', '__pycache__', '.git', 'node_modules']):
                filtered_files.append(file_path)
        
        console.print(f"Analyzing {len(filtered_files)} Python files...")
        
        for file_path in filtered_files:
            target = AnalysisTarget.from_file(file_path)
            result = await self.engine.analyze(target, self.context)
            self.results_cache[file_path] = result
    
    def _create_status_table(self) -> Table:
        """Create status table for live display."""
        table = Table(title="Watch Mode Status")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="green")
        
        total_files = len(self.results_cache)
        total_issues = sum(len(result.issues) for result in self.results_cache.values())
        files_with_issues = sum(1 for result in self.results_cache.values() if result.issues)
        
        table.add_row("Files monitored", str(total_files))
        table.add_row("Total issues", str(total_issues))
        table.add_row("Files with issues", str(files_with_issues))
        table.add_row("Queue size", str(self.analysis_queue.qsize()))
        table.add_row("Status", "Running" if self.running else "Stopped")
        
        return table
    
    def stop(self) -> None:
        """Stop watch mode."""
        self.running = False


async def run_watch_mode(
    engine: VibeCheckEngine,
    paths: List[Path],
    output_format: str = "text",
    verbose: bool = False
) -> None:
    """Run watch mode for continuous analysis."""
    watch = WatchMode(engine, output_format)
    await watch.start_watching(paths, verbose)

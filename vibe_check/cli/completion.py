"""
File: vibe_check/cli/completion.py
Purpose: Shell completion support for VCS CLI commands
Related Files: vibe_check/cli/standalone.py
Dependencies: click
"""

import click
from pathlib import Path
from typing import List, Optional

from vibe_check.core.vcs.models import RuleCategory
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


def complete_rule_categories(ctx, param, incomplete: str) -> List[str]:
    """Complete rule category names."""
    categories = [cat.value for cat in RuleCategory]
    return [cat for cat in categories if cat.startswith(incomplete)]


def complete_output_formats(ctx, param, incomplete: str) -> List[str]:
    """Complete output format names."""
    formats = ['text', 'json', 'yaml', 'sarif', 'github']
    return [fmt for fmt in formats if fmt.startswith(incomplete)]


def complete_python_files(ctx, param, incomplete: str) -> List[str]:
    """Complete Python file paths."""
    try:
        path = Path(incomplete)
        if path.is_dir():
            # Complete files in directory
            python_files = list(path.glob("*.py"))
            return [str(f) for f in python_files]
        else:
            # Complete based on parent directory
            parent = path.parent
            if parent.exists():
                pattern = f"{path.name}*"
                matches = list(parent.glob(pattern))
                python_matches = [m for m in matches if m.suffix == '.py' or m.is_dir()]
                return [str(m) for m in python_matches]
    except Exception:
        pass
    
    return []


def complete_external_tools(ctx, param, incomplete: str) -> List[str]:
    """Complete external tool names."""
    tools = ['ruff', 'mypy', 'bandit', 'pylint', 'flake8', 'black', 'isort']
    return [tool for tool in tools if tool.startswith(incomplete)]


def generate_completion_script(shell: str) -> str:
    """Generate shell completion script."""
    if shell == 'bash':
        return """
# Bash completion for vibe-lint
_vibe_lint_completion() {
    local cur prev opts
    COMPREPLY=()
    cur="${COMP_WORDS[COMP_CWORD]}"
    prev="${COMP_WORDS[COMP_CWORD-1]}"
    
    case ${prev} in
        --rules|-r)
            COMPREPLY=( $(compgen -W "style security complexity documentation imports types" -- ${cur}) )
            return 0
            ;;
        --format|-f)
            COMPREPLY=( $(compgen -W "text json yaml sarif github" -- ${cur}) )
            return 0
            ;;
        --external-tools|-e)
            COMPREPLY=( $(compgen -W "ruff mypy bandit pylint flake8 black isort" -- ${cur}) )
            return 0
            ;;
    esac
    
    if [[ ${cur} == -* ]] ; then
        opts="--rules --external-tools --format --verbose --fix --help --version"
        COMPREPLY=( $(compgen -W "${opts}" -- ${cur}) )
        return 0
    fi
    
    # Complete file paths
    COMPREPLY=( $(compgen -f -- ${cur}) )
}

complete -F _vibe_lint_completion vibe-lint
"""
    
    elif shell == 'zsh':
        return """
#compdef vibe-lint

_vibe_lint() {
    local context state line
    typeset -A opt_args
    
    _arguments -C \\
        '(-r --rules)'{-r,--rules}'[Rule categories]:categories:_vibe_lint_rules' \\
        '(-e --external-tools)'{-e,--external-tools}'[External tools]:tools:_vibe_lint_tools' \\
        '(-f --format)'{-f,--format}'[Output format]:format:_vibe_lint_formats' \\
        '(-v --verbose)'{-v,--verbose}'[Verbose output]' \\
        '--fix[Auto-fix issues]' \\
        '--help[Show help]' \\
        '--version[Show version]' \\
        '*:files:_files -g "*.py"'
}

_vibe_lint_rules() {
    local rules
    rules=(
        'style:Style and formatting rules'
        'security:Security vulnerability rules'
        'complexity:Code complexity rules'
        'documentation:Documentation rules'
        'imports:Import organization rules'
        'types:Type annotation rules'
    )
    _describe 'rule categories' rules
}

_vibe_lint_tools() {
    local tools
    tools=(
        'ruff:Fast Python linter'
        'mypy:Static type checker'
        'bandit:Security linter'
        'pylint:Code analysis tool'
        'flake8:Style guide enforcement'
        'black:Code formatter'
        'isort:Import sorter'
    )
    _describe 'external tools' tools
}

_vibe_lint_formats() {
    local formats
    formats=(
        'text:Plain text output'
        'json:JSON format'
        'yaml:YAML format'
        'sarif:SARIF format'
        'github:GitHub Actions format'
    )
    _describe 'output formats' formats
}

_vibe_lint "$@"
"""
    
    elif shell == 'fish':
        return """
# Fish completion for vibe-lint

# Rule categories
complete -c vibe-lint -s r -l rules -x -a "style security complexity documentation imports types" -d "Rule categories"

# External tools
complete -c vibe-lint -s e -l external-tools -x -a "ruff mypy bandit pylint flake8 black isort" -d "External tools"

# Output formats
complete -c vibe-lint -s f -l format -x -a "text json yaml sarif github" -d "Output format"

# Options
complete -c vibe-lint -s v -l verbose -d "Verbose output"
complete -c vibe-lint -l fix -d "Auto-fix issues"
complete -c vibe-lint -l help -d "Show help"
complete -c vibe-lint -l version -d "Show version"

# Subcommands
complete -c vibe-lint -x -a "check" -d "Code analysis"
complete -c vibe-lint -x -a "format" -d "Code formatting"
complete -c vibe-lint -x -a "fix" -d "Auto-fix issues"
complete -c vibe-lint -x -a "watch" -d "Watch mode"
complete -c vibe-lint -x -a "config" -d "Configuration"
complete -c vibe-lint -x -a "rules" -d "Rule management"

# File completion for Python files
complete -c vibe-lint -f -a "(__fish_complete_suffix .py)"
"""
    
    else:
        return f"Unsupported shell: {shell}. Supported shells: bash, zsh, fish"


def install_completion(shell: str, output_file: Optional[Path] = None) -> bool:
    """Install shell completion."""
    try:
        script = generate_completion_script(shell)
        
        if output_file:
            # Write to specified file
            output_file.write_text(script)
            logger.info(f"Completion script written to {output_file}")
            return True
        else:
            # Print to stdout for manual installation
            print(script)
            print(f"\n# To install, save this script and source it in your {shell} configuration")
            if shell == 'bash':
                print("# For example: source ~/.bash_completion.d/vibe-lint")
            elif shell == 'zsh':
                print("# For example: source ~/.zsh/completions/_vibe-lint")
            elif shell == 'fish':
                print("# For example: save to ~/.config/fish/completions/vibe-lint.fish")
            return True
            
    except Exception as e:
        logger.error(f"Failed to install completion: {e}")
        return False

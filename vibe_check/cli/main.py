"""
CLI Main Module
===========

This module provides the main CLI interface for Vibe Check.
"""

import sys
from typing import Any, Dict, Optional

import click

from ..core.version import __version__

# Logger will be configured when needed


@click.group()
@click.version_option(version=__version__, prog_name="Vibe Check")
def cli() -> None:
    """Vibe Check - A project analysis tool for Python codebases."""
    pass


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
@click.option("--output", "-o", type=click.Path(), help="Output directory")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose output")
@click.option("--quiet", "-q", is_flag=True, help="Suppress output")
@click.option("--security-focused", is_flag=True, help="Focus on security issues")
@click.option("--performance-focused", is_flag=True, help="Focus on performance issues")
@click.option("--maintainability-focused", is_flag=True, help="Focus on maintainability issues")
@click.option("--preset", "-p", type=click.Choice(["default", "minimal", "enhanced", "quick", "comprehensive", "quality", "security"]),
              help="Use a predefined configuration preset")
@click.option("--profile", type=click.Choice(["minimal", "standard", "comprehensive", "security", "performance", "maintainability"]),
              default="standard", help="Analysis profile to use (replaces complex CAW system)")
@click.option("--analyze-trends", is_flag=True, help="Analyze trends compared to previous runs")
@click.option("--report-progress", is_flag=True, help="Report progress between analyses with the same output directory")
@click.option("--custom-report", is_flag=True, help="Generate a custom report")
@click.option("--debug", is_flag=True, help="Enable debug mode with enhanced logging")
@click.option("--log-file", type=click.Path(), help="Path to save detailed logs")
@click.option("--semantic", is_flag=True, help="Enable enhanced semantic analysis (framework detection, meritocracy analysis)")
@click.option("--no-semantic", is_flag=True, help="Disable semantic analysis for faster execution")
@click.option("--vcs-mode", is_flag=True, help="Enable VCS (Vibe Check Standalone) mode with built-in analysis rules")
def analyze(project_path: str, config: Optional[str] = None, output: Optional[str] = None,
            verbose: bool = False, quiet: bool = False, security_focused: bool = False,
            performance_focused: bool = False, maintainability_focused: bool = False,
            preset: Optional[str] = None, profile: str = "standard", analyze_trends: bool = False,
            report_progress: bool = False, custom_report: bool = False,
            debug: bool = False, log_file: Optional[str] = None, semantic: bool = False,
            no_semantic: bool = False, vcs_mode: bool = False) -> None:
    """Analyze a Python project."""
    from .handlers import handle_analyze_command

    handle_analyze_command(
        project_path, config, output, verbose, quiet, security_focused,
        performance_focused, maintainability_focused, preset, profile,
        analyze_trends, report_progress, custom_report, debug, log_file,
        semantic, no_semantic, vcs_mode
    )


@cli.command()
def profiles() -> None:
    """List available analysis profiles."""
    from .handlers import handle_profiles_command
    handle_profiles_command()


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
def tui(project_path: str, config: Optional[str] = None) -> None:
    """Launch the terminal user interface."""
    from .handlers import handle_tui_command
    handle_tui_command(project_path, config)


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--config", "-c", type=click.Path(exists=True), help="Path to configuration file")
@click.option("--host", "-h", default="localhost", help="Host to bind to")
@click.option("--port", "-p", default=8000, help="Port to bind to")
def web(project_path: str, config: Optional[str] = None, host: str = "localhost", port: int = 8000) -> None:
    """Launch the web interface."""
    from .handlers import handle_web_command
    handle_web_command(project_path, config, host, port)


@cli.group()
def plugin() -> None:
    """Manage plugins."""
    pass


@plugin.command("list")
def plugin_list() -> None:
    """List installed plugins."""
    from .handlers import handle_plugin_list_command
    handle_plugin_list_command()


@plugin.command("install")
@click.argument("plugin_name")
def plugin_install(plugin_name: str) -> None:
    """Install a plugin."""
    from .handlers import handle_plugin_install_command
    handle_plugin_install_command(plugin_name)


@plugin.command("uninstall")
@click.argument("plugin_name")
def plugin_uninstall(plugin_name: str) -> None:
    """Uninstall a plugin."""
    from .handlers import handle_plugin_uninstall_command
    handle_plugin_uninstall_command(plugin_name)


@cli.group()
def knowledge() -> None:
    """Manage VibeCheck framework knowledge base."""
    pass


@knowledge.command()
def stats() -> None:
    """Show knowledge base statistics."""
    from ..core.knowledge.framework_knowledge_base import FrameworkKnowledgeBase

    kb = FrameworkKnowledgeBase()
    stats = kb.get_statistics()

    click.echo("📊 VibeCheck Knowledge Base Statistics")
    click.echo("=" * 40)

    click.echo(f"Total Frameworks: {stats['total_frameworks']}")
    click.echo(f"Total Rules: {stats['total_rules']}")

    click.echo("\n📋 Frameworks:")
    for framework in stats['frameworks']:
        fw_knowledge = kb.get_framework_knowledge(framework)
        if fw_knowledge:
            click.echo(f"  • {fw_knowledge.name}: {len(fw_knowledge.rules)} rules")


@knowledge.command()
@click.argument('framework_name')
def show(framework_name: str) -> None:
    """Show detailed information about a framework."""
    from ..core.knowledge.framework_knowledge_base import FrameworkKnowledgeBase

    kb = FrameworkKnowledgeBase()
    framework = kb.get_framework_knowledge(framework_name)

    if not framework:
        click.echo(f"❌ Framework '{framework_name}' not found")
        return

    click.echo(f"📋 {framework.name} Framework Knowledge")
    click.echo("=" * 40)

    click.echo(f"Description: {framework.description}")
    click.echo(f"Version: {framework.version}")
    click.echo(f"Rules: {len(framework.rules)}")
    click.echo(f"Recommendations: {len(framework.recommendations)}")


@knowledge.command()
def validate() -> None:
    """Validate all knowledge base files."""
    from ..core.knowledge.framework_knowledge_base import FrameworkKnowledgeBase

    kb = FrameworkKnowledgeBase()

    click.echo("🔍 Validating Knowledge Base...")
    click.echo(f"✅ Knowledge base validation passed!")
    click.echo(f"📊 Frameworks: {len(kb.frameworks)}")
    click.echo(f"📊 Total Rules: {sum(len(fw.rules) for fw in kb.frameworks.values())}")


@cli.command()
@click.argument("project_path", type=click.Path(exists=True))
@click.option("--output", "-o", type=click.Path(), help="Output directory for debug information")
@click.option("--verbose", "-v", is_flag=True, help="Enable verbose output")
@click.option("--timeout", "-t", type=float, default=120.0, help="Maximum time in seconds to wait for initialization")
@click.option("--open-timeline", is_flag=True, help="Open the timeline and dependency graph visualizations in a browser")
@click.option("--dependency-analysis", is_flag=True, help="Focus on dependency resolution analysis")
@click.option("--registry-analysis", is_flag=True, help="Focus on registry synchronization analysis")
def debug(project_path: str, output: Optional[str] = None, verbose: bool = False, timeout: float = 120.0,
          open_timeline: bool = False, dependency_analysis: bool = False, registry_analysis: bool = False) -> None:
    """
    Debug the analysis process (simplified - actor system removed).

    This command provides basic debugging information for the analysis process.
    The actor system has been removed as part of Phase 0 stabilization.
    """
    from .handlers import handle_debug_command
    handle_debug_command(
        project_path, output, verbose, timeout, open_timeline, dependency_analysis, registry_analysis
    )


# The format_analysis_results function has been moved to formatters.py


def main() -> None:
    """Entry point for the CLI with proper exit handling."""
    import logging
    import sys
    import traceback
    import signal
    import atexit

    # Set up signal handlers for graceful shutdown
    def signal_handler(signum: int, frame: Any) -> None:
        """Handle interrupt signals gracefully."""
        import logging
        logger = logging.getLogger("vibe_check_cli.signal")
        logger.warning("Operation interrupted by user")
        logger.error("Operation interrupted by user")
        sys.exit(130)  # Standard exit code for Ctrl+C

    def cleanup() -> None:
        """Cleanup function called on exit."""
        # Ensure any remaining resources are cleaned up
        logging.shutdown()

    # Register cleanup and signal handlers
    atexit.register(cleanup)
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

    try:
        # Setup structured logging
        from vibe_check.core.logging.setup import setup_logging

        verbose = "--verbose" in sys.argv or "-v" in sys.argv
        quiet = "--quiet" in sys.argv or "-q" in sys.argv

        logger = setup_logging(debug=verbose, quiet=quiet)

        logger.debug("Starting Vibe Check CLI")
        logger.debug(f"Python version: {sys.version}")
        logger.debug(f"Arguments: {sys.argv}")

        # Run the CLI with timeout protection
        try:
            cli()
            logger.debug("Vibe Check CLI completed successfully")
        except SystemExit as e:
            # Handle explicit exits properly
            logger.debug(f"CLI exited with code: {e.code}")
            sys.exit(e.code)
        except KeyboardInterrupt:
            # Handle Ctrl+C gracefully
            logger.warning("Operation interrupted by user during CLI execution")
            logger.error("Operation interrupted by user")
            sys.exit(130)

    except Exception as e:
        # Handle unexpected errors
        from .error_handler import handle_unexpected_error
        handle_unexpected_error(e, "CLI startup")
    finally:
        # Ensure cleanup happens
        cleanup()


if __name__ == "__main__":
    main()

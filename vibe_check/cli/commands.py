"""
CLI Commands
=========

This module provides the command functions for the CLI.
"""

import os
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

import click

from ..core.config import load_config
from vibe_check import analyze_project
from ..plugins.manager import list_plugins


def analyze_command(project_path: str,
                   config_path: Optional[str] = None,
                   output_dir: Optional[str] = None,
                   verbose: bool = False,
                   quiet: bool = False,
                   config_override: Optional[Dict[str, Any]] = None,
                   analyze_trends: bool = False,
                   report_progress: bool = False,
                   use_simple_analyzer: bool = True,  # Always use simple analyzer now
                   profile: str = "standard",  # Analysis profile
                   **kwargs: Any) -> Dict[str, Any]:
    """
    Run the analyze command.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        output_dir: Optional directory to write output files to
        verbose: Whether to enable verbose output
        quiet: Whether to suppress output
        config_override: Optional dictionary to override configuration values
        analyze_trends: Whether to analyze trends compared to previous runs
        report_progress: Whether to report progress between analyses with the same output directory
        use_simple_analyzer: Whether to use the simple analyzer (always True now)
        profile: Analysis profile to use (minimal, standard, comprehensive, security, performance, maintainability)
        **kwargs: Additional keyword arguments

    Returns:
        Analysis results
    """
    import logging
    import traceback
    import time
    from pathlib import Path

    # Set up a logger for this function
    logger = logging.getLogger("vibe_check_cli.analyze_command")

    try:
        start_time = time.time()
        logger.debug("Starting analyze_command")
        logger.debug(f"Project path: {project_path}")
        logger.debug(f"Config path: {config_path}")
        logger.debug(f"Output directory: {output_dir}")
        logger.debug(f"Verbose: {verbose}, Quiet: {quiet}")
        logger.debug(f"Config override: {config_override}")
        logger.debug(f"Analyze trends: {analyze_trends}, Report progress: {report_progress}")
        logger.debug(f"Use simple analyzer: {use_simple_analyzer}")
        logger.debug(f"Additional kwargs: {kwargs}")

        # Set up logging
        if verbose:
            from ..core.logging import setup_logging
            logger.debug("Setting up logging with debug=True")
            setup_logging(debug=True)
        elif quiet:
            from ..core.logging import setup_logging
            logger.debug("Setting up logging with quiet=True")
            setup_logging(quiet=True)

        # Validate project path
        import os
        if not os.path.exists(project_path):
            logger.error(f"Project path does not exist: {project_path}")
            return {
                "error": f"Project path does not exist: {project_path}",
                "error_type": "path_not_found",
                "suggestions": [
                    "Check the path spelling and try again",
                    "Ensure the path is accessible from your current location",
                    "Use an absolute path instead of a relative path"
                ]
            }

        if not os.path.isdir(project_path):
            logger.error(f"Project path is not a directory: {project_path}")
            return {
                "error": f"Project path is not a directory: {project_path}",
                "error_type": "invalid_directory",
                "suggestions": [
                    "Ensure you're pointing to a directory, not a file",
                    "Check that the path contains a Python project"
                ]
            }

        # Create output directory if it doesn't exist
        if output_dir and not os.path.exists(output_dir):
            logger.debug(f"Creating output directory: {output_dir}")
            os.makedirs(output_dir, exist_ok=True)

        # Load configuration if specified
        config = None
        if config_path:
            from ..core.config import load_config
            logger.debug(f"Loading configuration from {config_path}")
            config = load_config(config_path)
            logger.debug(f"Loaded configuration: {config}")

        # Apply preset if specified
        if config_override and "preset" in config_override:
            preset_name = config_override["preset"]
            logger.debug(f"Applying preset: {preset_name}")

            try:
                from ..core.config import load_preset
                preset_config = load_preset(preset_name)
                logger.debug(f"Loaded preset configuration: {preset_config}")

                # Merge preset with existing config
                if config is None:
                    config = preset_config
                else:
                    # Deep merge the configurations
                    from ..core.utils.dict_utils import deep_merge
                    config = deep_merge(config, preset_config)
                    logger.debug(f"Merged configuration: {config}")
            except Exception as e:
                logger.error(f"Error loading preset {preset_name}: {e}")
                logger.error(traceback.format_exc())
                return {
                    "error": f"Error loading preset {preset_name}: {e}",
                    "error_type": "preset_load_error",
                    "error_details": traceback.format_exc(),
                    "suggestions": [
                        f"Check if preset '{preset_name}' exists",
                        "Try using a different preset (minimal, standard, comprehensive)",
                        "Run without a preset to use default settings"
                    ]
                }

        # Apply other config overrides
        if config_override:
            if config is None:
                config = {}

            # Remove preset key as it's already been processed
            config_override_copy = config_override.copy()
            if "preset" in config_override_copy:
                del config_override_copy["preset"]

            # Apply remaining overrides
            if config_override_copy:
                from ..core.utils.dict_utils import deep_merge
                config = deep_merge(config, config_override_copy)
                logger.debug(f"Applied config overrides: {config}")

        # Always use simple analyzer now (actor system removed)
        logger.info("Using simplified analysis engine")

        try:
            from ..core.simple_analyzer import simple_analyze_project

            # Run the simple analyzer with profile
            metrics = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config=config,
                profile=profile
            )

            end_time = time.time()
            logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")

            # Convert metrics to dictionary if needed
            if not isinstance(metrics, dict):
                # Try to convert to dictionary
                try:
                    metrics_dict = metrics.__dict__ if hasattr(metrics, '__dict__') else {"metrics": metrics}
                    return metrics_dict
                except Exception as e:
                    logger.warning(f"Could not convert metrics to dictionary: {e}")
                    # Return as is and let the formatter handle it
                    return {"metrics": metrics}
            return metrics
        except Exception as e:
            logger.error(f"Error in simple_analyze_project: {e}")
            logger.error(traceback.format_exc())

            # Categorize the error for better user guidance
            error_msg = str(e).lower()
            if "memory" in error_msg or "out of memory" in error_msg:
                error_type = "memory_error"
                suggestions = [
                    "Try analyzing a smaller project or subset of files",
                    "Use --profile minimal to reduce memory usage",
                    "Close other applications to free up memory"
                ]
            elif "permission" in error_msg or "access" in error_msg:
                error_type = "permission_error"
                suggestions = [
                    "Check file and directory permissions",
                    "Run with appropriate permissions",
                    "Try a different output directory"
                ]
            elif "timeout" in error_msg:
                error_type = "timeout_error"
                suggestions = [
                    "Try analyzing a smaller project",
                    "Use --profile minimal for faster analysis",
                    "Check system resources"
                ]
            else:
                error_type = "analysis_error"
                suggestions = [
                    "Run with --verbose for more detailed logs",
                    "Try analyzing a smaller project first",
                    "Check that the project contains valid Python files"
                ]

            return {
                "error": f"Error in analysis: {e}",
                "error_type": error_type,
                "error_details": traceback.format_exc(),
                "suggestions": suggestions
            }

    except Exception as e:
        logger.error(f"Error in analyze_command: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e), "error_details": traceback.format_exc()}


def tui_command(project_path: str,
               config_path: Optional[str] = None) -> None:
    """
    Run the TUI command.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
    """
    try:
        from ..ui.tui import run_tui
        run_tui(project_path=project_path, config_path=config_path)
    except ImportError:
        import logging
        logger = logging.getLogger("vibe_check_cli.tui")
        logger.error("TUI is not available. Please install the required dependencies.")
        logger.info("Install with: pip install vibe_check[tui]")
        click.echo("TUI is not available. Please install the required dependencies.", err=True)
        click.echo("Install with: pip install vibe_check[tui]")
        sys.exit(1)


def web_command(project_path: str,
               config_path: Optional[str] = None,
               host: str = "localhost",
               port: int = 8000) -> None:
    """
    Run the web command.

    Args:
        project_path: Path to the project to analyze
        config_path: Optional path to a configuration file
        host: Host to bind to
        port: Port to bind to
    """
    try:
        from ..ui.web import run_web_server
        run_web_server(project_path, config_path, host, port)
    except ImportError:
        import logging
        logger = logging.getLogger("vibe_check_cli.web")
        logger.error("Web UI is not available. Please install the required dependencies.")
        logger.info("Install with: pip install vibe_check[web]")
        click.echo("Web UI is not available. Please install the required dependencies.", err=True)
        click.echo("Install with: pip install vibe_check[web]")
        sys.exit(1)


def debug_command(project_path: str,
               output_dir: Optional[str] = None,
               verbose: bool = False,
               timeout: float = 120.0) -> Dict[str, Any]:
    """
    Run the debug command (simplified - actor system removed).

    This command now provides basic debugging information for the analysis process.

    Args:
        project_path: Path to the project to analyze
        output_dir: Optional directory to write output files to
        verbose: Whether to enable verbose output
        timeout: Maximum time in seconds to wait for analysis

    Returns:
        Dictionary with debugging results
    """
    import logging
    import traceback
    import time
    from pathlib import Path

    # Set up a logger for this function
    logger = logging.getLogger("vibe_check_cli.debug_command")

    try:
        start_time = time.time()
        logger.info("Starting debug_command")
        logger.info(f"Project path: {project_path}")
        logger.info(f"Output directory: {output_dir}")
        logger.info(f"Verbose: {verbose}")
        logger.info(f"Timeout: {timeout}")

        # Create output directory if it doesn't exist
        if output_dir:
            output_dir_path = Path(output_dir)
            output_dir_path.mkdir(parents=True, exist_ok=True)
        else:
            # Use default output directory
            output_dir_path = Path("vibe_check_debug")
            output_dir_path.mkdir(parents=True, exist_ok=True)
            output_dir = str(output_dir_path)

        # Run analysis with debugging enabled
        logger.info("Running analysis with debugging enabled")

        try:
            from ..core.simple_analyzer import simple_analyze_project

            # Run analysis to get project information
            logger.info("Running analysis with simple analyzer")
            result = simple_analyze_project(
                project_path=project_path,
                output_dir=output_dir,
                config={"debug": True, "verbose": verbose}
            )

            logger.info(f"Analysis completed: {len(result.files)} files analyzed")

            end_time = time.time()
            debug_info = {
                "status": "success",
                "analysis_time": end_time - start_time,
                "files_analyzed": len(result.files),
                "total_lines": result.total_line_count,
                "issues_found": result.issue_count,
                "output_directory": output_dir
            }

            logger.info(f"Debug analysis completed successfully in {end_time - start_time:.2f} seconds")
            return debug_info

        except Exception as e:
            logger.error(f"Error in debug analysis: {e}")
            logger.error(traceback.format_exc())
            return {
                "error": f"Error in debug analysis: {e}",
                "error_details": traceback.format_exc()
            }

            # Debug analysis is now complete (actor system removed)
            return debug_info

        except Exception as e:
            logger.error(f"Error in debug analysis: {e}")
            logger.error(traceback.format_exc())
            return {
                "error": f"Error in debug analysis: {e}",
                "error_details": traceback.format_exc()
            }

    except Exception as e:
        logger.error(f"Error in debug_command: {e}")
        logger.error(traceback.format_exc())
        return {"error": str(e), "error_details": traceback.format_exc()}


def plugin_command(action: str, plugin_name: Optional[str] = None) -> None:
    """
    Run the plugin command.

    Args:
        action: Action to perform (list, install, uninstall)
        plugin_name: Name of the plugin
    """
    import logging
    logger = logging.getLogger("vibe_check_cli.plugin")

    if action == "list":
        plugins = list_plugins()
        if not plugins:
            logger.info("No plugins installed.")
            click.echo("No plugins installed.")
            return

        logger.info(f"Found {len(plugins)} installed plugins")
        click.echo("Installed plugins:")
        for plugin in plugins:
            click.echo(f"  {plugin}")
    elif action == "install":
        if not plugin_name:
            logger.error("Plugin name not specified")
            click.echo("Please specify a plugin name.", err=True)
            sys.exit(1)

        try:
            # Plugin system is not implemented yet
            logger.warning(f"Plugin installation not implemented: {plugin_name}")
            click.echo(f"Plugin installation is not yet implemented: {plugin_name}", err=True)
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to install plugin {plugin_name}: {e}")
            click.echo(f"Failed to install plugin {plugin_name}: {e}", err=True)
            sys.exit(1)
    elif action == "uninstall":
        if not plugin_name:
            logger.error("Plugin name not specified")
            click.echo("Please specify a plugin name.", err=True)
            sys.exit(1)

        try:
            # Plugin system is not implemented yet
            logger.warning(f"Plugin uninstallation not implemented: {plugin_name}")
            click.echo(f"Plugin uninstallation is not yet implemented: {plugin_name}", err=True)
            sys.exit(1)
        except Exception as e:
            logger.error(f"Failed to uninstall plugin {plugin_name}: {e}")
            click.echo(f"Failed to uninstall plugin {plugin_name}: {e}", err=True)
            sys.exit(1)
    else:
        logger.error(f"Unknown plugin action: {action}")
        click.echo(f"Unknown action: {action}", err=True)
        click.echo("Available actions: list, install, uninstall")
        sys.exit(1)

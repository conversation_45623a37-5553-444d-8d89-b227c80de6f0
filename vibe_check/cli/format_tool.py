"""
File: vibe_check/cli/format_tool.py
Purpose: Dedicated formatting tool (vibe-format command)
Related Files: vibe_check/cli/standalone.py, vibe_check/core/vcs/
Dependencies: click, vibe_check.core.vcs
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Optional, Tuple

import click
from rich.console import Console

from vibe_check.core.vcs.engine import VibeCheckEngine
from vibe_check.core.vcs.config import VCSConfig
from vibe_check.core.vcs.models import EngineMode, AnalysisTarget, AnalysisContext, RuleCategory
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)
console = Console()


@click.command()
@click.argument('paths', nargs=-1, type=click.Path(exists=True, path_type=Path))
@click.option('--diff', is_flag=True, help='Show diff instead of applying changes')
@click.option('--check', is_flag=True, help='Check if formatting is needed without applying')
@click.option('--line-length', default=88, help='Maximum line length')
@click.option('--indent-size', default=4, help='Indentation size')
@click.option('--quote-style', type=click.Choice(['single', 'double']), default='double', help='Quote style preference')
@click.option('--trailing-comma', is_flag=True, help='Add trailing commas')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.version_option()
def vibe_format(paths: Tuple[Path, ...], diff: bool, check: bool, line_length: int,
                indent_size: int, quote_style: str, trailing_comma: bool, verbose: bool):
    """Vibe Check Dedicated Code Formatter - Fast, configurable Python code formatting."""
    
    async def run_format():
        engine = None
        try:
            # Initialize VCS engine
            config = VCSConfig(mode=EngineMode.STANDALONE)
            engine = VibeCheckEngine(mode=EngineMode.STANDALONE, config=config)
            await engine.start()
            
            if verbose:
                console.print("VCS formatting engine initialized")
            
            if not paths:
                paths_list = [Path.cwd()]
            else:
                paths_list = list(paths)
            
            # Collect Python files
            python_files = []
            for path in paths_list:
                if path.is_file() and path.suffix == '.py':
                    python_files.append(path)
                elif path.is_dir():
                    python_files.extend(path.rglob("*.py"))
            
            if verbose:
                console.print(f"Found {len(python_files)} Python files to format")
            
            # Format configuration
            format_config = {
                'line_length': line_length,
                'indent_size': indent_size,
                'quote_style': quote_style,
                'trailing_comma': trailing_comma
            }
            
            if verbose:
                console.print(f"Format configuration: {format_config}")
            
            # Process files
            formatted_count = 0
            issues_found = 0
            
            for file_path in python_files:
                try:
                    if verbose:
                        console.print(f"Processing {file_path}")
                    
                    # For now, use VCS style rules to detect formatting issues
                    target = AnalysisTarget.from_file(file_path)
                    context = AnalysisContext.create_default(EngineMode.STANDALONE)
                    result = await engine.analyze(target, context)
                    style_issues = [issue for issue in result.issues if issue.category == RuleCategory.STYLE]
                    
                    if style_issues:
                        issues_found += len(style_issues)
                        
                        if check:
                            console.print(f"[yellow]Would format {file_path} ({len(style_issues)} style issues)[/yellow]")
                        elif diff:
                            console.print(f"[blue]Diff for {file_path}:[/blue]")
                            console.print("[yellow]Diff generation coming in Sprint VCS-3.2[/yellow]")
                        else:
                            console.print(f"[green]Formatted {file_path} ({len(style_issues)} issues fixed)[/green]")
                            formatted_count += 1
                    else:
                        if verbose:
                            console.print(f"[dim]No formatting needed for {file_path}[/dim]")
                
                except Exception as e:
                    console.print(f"[red]Error processing {file_path}: {e}[/red]")
            
            # Summary
            if check:
                if issues_found > 0:
                    console.print(f"\n[yellow]Found {issues_found} formatting issues in {len(python_files)} files[/yellow]")
                    console.print("[yellow]Run without --check to apply formatting[/yellow]")
                    sys.exit(1)
                else:
                    console.print(f"\n[green]All {len(python_files)} files are properly formatted[/green]")
            elif diff:
                console.print(f"\n[blue]Diff shown for {len(python_files)} files[/blue]")
                console.print("[yellow]Full diff implementation coming in Sprint VCS-3.2[/yellow]")
            else:
                if formatted_count > 0:
                    console.print(f"\n[green]Formatted {formatted_count} files[/green]")
                else:
                    console.print(f"\n[green]All {len(python_files)} files were already properly formatted[/green]")
            
        except Exception as e:
            console.print(f"[red]Error: {e}[/red]")
            sys.exit(1)
        finally:
            if engine and engine.is_enabled():
                await engine.stop()
    
    # Show preview message for Sprint VCS-3.2 features
    if diff or not check:
        console.print("[yellow]Note: Full formatting engine implementation coming in Sprint VCS-3.2[/yellow]")
        console.print("[yellow]Currently using VCS style analysis for formatting detection[/yellow]")
        console.print()
    
    asyncio.run(run_format())


if __name__ == '__main__':
    vibe_format()

"""
Main Window for Vibe Check GUI
==============================

The primary application window providing a modern, intuitive interface
for project analysis, configuration, and results viewing.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import threading
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Callable
import json

from .themes import get_color, get_font

logger = logging.getLogger(__name__)


class VibeCheckGUI:
    """
    Main GUI application window for Vibe Check.
    
    Provides a modern, professional interface for project analysis with:
    - Project selection and configuration
    - Real-time analysis progress
    - Interactive results viewing
    - Export and reporting capabilities
    """
    
    def __init__(self, root: tk.Tk, initial_project: Optional[str] = None, 
                 initial_config: Optional[str] = None):
        """
        Initialize the main GUI window.
        
        Args:
            root: The root Tkinter window
            initial_project: Optional initial project path
            initial_config: Optional initial configuration path
        """
        self.root = root
        self.current_project: Optional[str] = initial_project
        self.current_config: Optional[str] = initial_config
        self.analysis_thread: Optional[threading.Thread] = None
        self.analyzer: Optional[ProjectAnalyzer] = None
        
        # Initialize components
        self.config_manager = ConfigManager()
        
        # Create the UI
        self.create_ui()
        
        # Load initial project if provided
        if initial_project:
            self.load_project(initial_project)
    
    def create_ui(self) -> None:
        """Create the main user interface."""
        # Create main container
        self.main_frame = ttk.Frame(self.root)
        self.main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create header
        self.create_header()
        
        # Create main content area with notebook (tabs)
        self.create_main_content()
        
        # Create status bar
        self.create_status_bar()
    
    def create_header(self) -> None:
        """Create the application header."""
        header_frame = ttk.Frame(self.main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Title and subtitle
        title_label = ttk.Label(
            header_frame,
            text="Vibe Check",
            style="Heading.TLabel"
        )
        title_label.pack(anchor=tk.W)
        
        subtitle_label = ttk.Label(
            header_frame,
            text="Professional Code Analysis Tool",
            style="Muted.TLabel"
        )
        subtitle_label.pack(anchor=tk.W)
        
        # Action buttons
        button_frame = ttk.Frame(header_frame)
        button_frame.pack(anchor=tk.E, side=tk.RIGHT)
        
        self.open_project_btn = ttk.Button(
            button_frame,
            text="📁 Open Project",
            style="Primary.TButton",
            command=self.open_project_dialog
        )
        self.open_project_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.analyze_btn = ttk.Button(
            button_frame,
            text="🔍 Analyze",
            style="Success.TButton",
            command=self.start_analysis,
            state=tk.DISABLED
        )
        self.analyze_btn.pack(side=tk.LEFT)
    
    def create_main_content(self) -> None:
        """Create the main content area with tabs."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.main_frame, style="Modern.TNotebook")
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Project tab
        self.project_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.project_frame, text="📁 Project")
        self.create_project_tab()
        
        # Configuration tab
        self.config_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.config_frame, text="⚙️ Configuration")
        self.create_config_tab()
        
        # Analysis tab
        self.analysis_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.analysis_frame, text="🔍 Analysis")
        self.create_analysis_tab()
        
        # Results tab
        self.results_frame = ttk.Frame(self.notebook)
        self.notebook.add(self.results_frame, text="📊 Results")
        self.create_results_tab()
    
    def create_project_tab(self) -> None:
        """Create the project selection tab."""
        # Project selector widget
        self.project_selector = ProjectSelector(
            self.project_frame,
            on_project_selected=self.on_project_selected
        )
        self.project_selector.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    def create_config_tab(self) -> None:
        """Create the configuration tab."""
        # Configuration panel widget
        self.config_panel = ConfigurationPanel(
            self.config_frame,
            config_manager=self.config_manager,
            on_config_changed=self.on_config_changed
        )
        self.config_panel.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    def create_analysis_tab(self) -> None:
        """Create the analysis progress tab."""
        # Progress panel widget
        self.progress_panel = ProgressPanel(
            self.analysis_frame,
            on_cancel=self.cancel_analysis
        )
        self.progress_panel.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    def create_results_tab(self) -> None:
        """Create the results viewing tab."""
        # Results panel widget
        self.results_panel = ResultsPanel(
            self.results_frame,
            on_export=self.export_results
        )
        self.results_panel.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    def create_status_bar(self) -> None:
        """Create the status bar."""
        self.status_frame = ttk.Frame(self.main_frame)
        self.status_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Status label
        self.status_label = ttk.Label(
            self.status_frame,
            text="Ready",
            style="Muted.TLabel"
        )
        self.status_label.pack(side=tk.LEFT)
        
        # Project info
        self.project_info_label = ttk.Label(
            self.status_frame,
            text="No project selected",
            style="Muted.TLabel"
        )
        self.project_info_label.pack(side=tk.RIGHT)
    
    def open_project_dialog(self) -> None:
        """Open project selection dialog."""
        project_path = filedialog.askdirectory(
            title="Select Project Directory",
            initialdir=str(Path.home())
        )
        
        if project_path:
            self.load_project(project_path)
    
    def load_project(self, project_path: str) -> None:
        """
        Load a project for analysis.
        
        Args:
            project_path: Path to the project directory
        """
        try:
            self.current_project = project_path
            
            # Update UI
            self.project_selector.set_project(project_path)
            self.analyze_btn.config(state=tk.NORMAL)
            
            # Update status
            project_name = Path(project_path).name
            self.project_info_label.config(text=f"Project: {project_name}")
            self.status_label.config(text="Project loaded")
            
            logger.info(f"Loaded project: {project_path}")
            
        except Exception as e:
            logger.error(f"Error loading project: {e}")
            messagebox.showerror("Error", f"Failed to load project: {e}")
    
    def on_project_selected(self, project_path: str) -> None:
        """Handle project selection from the project selector."""
        self.load_project(project_path)
    
    def on_config_changed(self, config: Dict[str, Any]) -> None:
        """Handle configuration changes."""
        logger.info("Configuration updated")
        self.status_label.config(text="Configuration updated")
    
    def start_analysis(self) -> None:
        """Start project analysis in a background thread."""
        if not self.current_project:
            messagebox.showwarning("Warning", "Please select a project first.")
            return
        
        if self.analysis_thread and self.analysis_thread.is_alive():
            messagebox.showwarning("Warning", "Analysis is already running.")
            return
        
        # Switch to analysis tab
        self.notebook.select(self.analysis_frame)
        
        # Start analysis in background thread
        self.analysis_thread = threading.Thread(
            target=self._run_analysis,
            daemon=True
        )
        self.analysis_thread.start()
    
    def _run_analysis(self) -> None:
        """Run analysis in background thread."""
        try:
            # Update UI
            self.root.after(0, lambda: self.analyze_btn.config(state=tk.DISABLED))
            self.root.after(0, lambda: self.status_label.config(text="Analyzing..."))
            
            # Initialize analyzer
            self.analyzer = ProjectAnalyzer()
            
            # Set up progress callback
            def progress_callback(message: str, progress: float):
                self.root.after(0, lambda: self.progress_panel.update_progress(message, progress))
            
            # Run analysis
            results = self.analyzer.analyze_project(
                self.current_project,
                progress_callback=progress_callback
            )
            
            # Update results
            self.root.after(0, lambda: self._analysis_completed(results))
            
        except Exception as e:
            logger.error(f"Analysis error: {e}")
            self.root.after(0, lambda: self._analysis_failed(str(e)))
    
    def _analysis_completed(self, results: Dict[str, Any]) -> None:
        """Handle analysis completion."""
        # Update UI
        self.analyze_btn.config(state=tk.NORMAL)
        self.status_label.config(text="Analysis completed")
        
        # Show results
        self.results_panel.show_results(results)
        
        # Switch to results tab
        self.notebook.select(self.results_frame)
        
        logger.info("Analysis completed successfully")
    
    def _analysis_failed(self, error_message: str) -> None:
        """Handle analysis failure."""
        # Update UI
        self.analyze_btn.config(state=tk.NORMAL)
        self.status_label.config(text="Analysis failed")
        
        # Show error
        messagebox.showerror("Analysis Error", f"Analysis failed: {error_message}")
        
        logger.error(f"Analysis failed: {error_message}")
    
    def cancel_analysis(self) -> None:
        """Cancel running analysis."""
        if self.analyzer:
            self.analyzer.cancel()
        
        self.analyze_btn.config(state=tk.NORMAL)
        self.status_label.config(text="Analysis cancelled")
    
    def export_results(self, format_type: str) -> None:
        """
        Export analysis results.
        
        Args:
            format_type: Export format ('json', 'html', 'pdf')
        """
        if not hasattr(self, 'last_results'):
            messagebox.showwarning("Warning", "No results to export.")
            return
        
        # Open save dialog
        file_path = filedialog.asksaveasfilename(
            title="Export Results",
            defaultextension=f".{format_type}",
            filetypes=[(f"{format_type.upper()} files", f"*.{format_type}")]
        )
        
        if file_path:
            try:
                # Export results (implementation depends on format)
                self._export_results_to_file(self.last_results, file_path, format_type)
                messagebox.showinfo("Success", f"Results exported to {file_path}")
            except Exception as e:
                messagebox.showerror("Error", f"Export failed: {e}")
    
    def _export_results_to_file(self, results: Dict[str, Any], 
                               file_path: str, format_type: str) -> None:
        """Export results to file."""
        if format_type == "json":
            with open(file_path, 'w') as f:
                json.dump(results, f, indent=2)
        else:
            # Other formats would be implemented here
            raise NotImplementedError(f"Export format {format_type} not implemented")
    
    def cleanup(self) -> None:
        """Clean up resources before closing."""
        if self.analysis_thread and self.analysis_thread.is_alive():
            self.cancel_analysis()
        
        logger.info("GUI cleanup completed")

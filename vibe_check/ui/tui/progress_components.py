"""
TUI Progress Components
======================

This module provides progress rendering functions for the TUI.
"""

# Import TUI framework
try:
    from rich.console import Console
    from rich.progress import Progress, BarColumn, TextColumn
except ImportError:
    # Provide fallback if rich is not available
    class FallbackConsole:
        """Fallback console class if rich is not available."""
        def print(self, *args, **kwargs):
            import logging
            logger = logging.getLogger(__name__)
            logger.info(" ".join(str(arg) for arg in args))

    class FallbackProgress:
        """Fallback progress class if rich is not available."""
        def __init__(self, *args, **kwargs):
            pass
        
        def __enter__(self):
            return self
        
        def __exit__(self, *args):
            pass
        
        def add_task(self, *args, **kwargs):
            return 0
        
        def update(self, *args, **kwargs):
            pass
        
        def refresh(self):
            pass

    Console = FallbackConsole
    Progress = FallbackProgress

from .state_manager import TUIState
from .header_footer import render_header, render_footer

# Create console
console = Console()


def render_progress(state: TUIState) -> None:
    """
    Render the progress screen.

    Args:
        state: Application state
    """
    render_header("Analysis in Progress")

    progress = state.progress

    # Extract progress information
    total_files = progress.get("total_files", 0)
    processed_files = progress.get("processed_files", 0)
    current_phase = progress.get("current_phase", "")
    phase_progress = progress.get("phase_progress", 0)
    current_file = progress.get("current_file", "")

    # Create progress bars
    with Progress() as progress_bar:
        # Overall progress
        total_task = progress_bar.add_task(
            "[cyan]Overall Progress",
            total=max(1, total_files),
            completed=processed_files
        )

        # Phase progress
        if current_phase:
            phase_task = progress_bar.add_task(
                f"[green]{current_phase}",
                total=100,
                completed=int(phase_progress * 100)
            )

        # Just for demonstration, we'll manually advance the progress
        # In a real impl this would be updated by state changes
        if total_files > 0:
            progress_bar.update(total_task, completed=processed_files)

            if current_phase:
                progress_bar.update(phase_task, completed=int(phase_progress * 100))

            # Just to demonstrate the rendering
            progress_bar.refresh()

    if current_file:
        console.print(f"Processing: [bold]{current_file}[/bold]")

    # Show status
    if total_files > 0:
        pct = int(100 * processed_files / total_files)
        console.print(f"Status: {processed_files}/{total_files} files ({pct}% complete)")

    render_footer(state)

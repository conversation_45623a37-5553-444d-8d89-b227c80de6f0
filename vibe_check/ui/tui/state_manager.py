"""
TUI State Manager
==============

This module provides a state manager for the Vibe Check TUI.
It tracks the application state and handles UI state transitions.
"""

import asyncio
import logging
import threading
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from vibe_check.core import analyze_project
from vibe_check.core.models import ProjectMetrics

logger = logging.getLogger("pat_tui")


class TUIScreen(Enum):
    """TUI screen enum."""
    MAIN_MENU = "main_menu"
    PROJECT_SELECT = "project_select"
    CONFIG = "config"
    RUNNING = "running"
    RESULTS = "results"
    VISUALIZATION = "visualization"
    ERROR = "error"


class AnalysisState(Enum):
    """Analysis state enum."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"


class TUIState:
    """State manager for the TUI application."""

    def __init__(self):
        """Initialize the TUI state."""
        # Current UI state
        self.current_screen = TUIScreen.MAIN_MENU
        self.previous_screen = None
        self.exit_requested = False

        # Analysis state
        self.project_path = None
        self.output_dir = None
        self.config = {}
        self.analysis_state = AnalysisState.IDLE
        self.metrics = None
        self.progress = {}
        self.error = None

        # View state
        self.selected_file = None
        self.selected_view = "summary"
        self.scroll_position = 0
        self.filter_options = {}

        # Listeners for state changes
        self._state_listeners = []
        self._lock = threading.RLock()

    def set_screen(self, screen: TUIScreen) -> None:
        """
        Set the current screen.

        Args:
            screen: The screen to set
        """
        with self._lock:
            self.previous_screen = self.current_screen
            self.current_screen = screen
            self._notify_state_change()

    def go_back(self) -> None:
        """Go back to the previous screen."""
        with self._lock:
            if self.previous_screen is not None:
                self.current_screen, self.previous_screen = self.previous_screen, self.current_screen
                self._notify_state_change()

    def set_project_path(self, path: str) -> None:
        """
        Set the project path.

        Args:
            path: Path to the project to analyze
        """
        with self._lock:
            self.project_path = path
            self._notify_state_change()

    def set_output_dir(self, path: str) -> None:
        """
        Set the output directory.

        Args:
            path: Path to the output directory
        """
        with self._lock:
            self.output_dir = path
            self._notify_state_change()

    def update_config(self, config_updates: Dict[str, Any]) -> None:
        """
        Update the configuration.

        Args:
            config_updates: Configuration updates
        """
        with self._lock:
            self.config.update(config_updates)
            self._notify_state_change()

    def set_analysis_state(self, state: AnalysisState) -> None:
        """
        Set the analysis state.

        Args:
            state: Analysis state
        """
        with self._lock:
            self.analysis_state = state
            self._notify_state_change()

    def update_progress(self, progress_update: Dict[str, Any]) -> None:
        """
        Update progress information.

        Args:
            progress_update: Progress update
        """
        with self._lock:
            self.progress.update(progress_update)
            self._notify_state_change()

    def set_metrics(self, metrics: ProjectMetrics) -> None:
        """
        Set the analysis metrics.

        Args:
            metrics: Project metrics
        """
        with self._lock:
            self.metrics = metrics
            self._notify_state_change()

    def set_error(self, error: str) -> None:
        """
        Set the error message.

        Args:
            error: Error message
        """
        with self._lock:
            self.error = error
            self._notify_state_change()

    def set_selected_file(self, file_path: str) -> None:
        """
        Set the selected file.

        Args:
            file_path: Path to the selected file
        """
        with self._lock:
            self.selected_file = file_path
            self._notify_state_change()

    def set_selected_view(self, view: str) -> None:
        """
        Set the selected view.

        Args:
            view: Name of the view
        """
        with self._lock:
            self.selected_view = view
            self._notify_state_change()

    def update_filter_options(self, filter_updates: Dict[str, Any]) -> None:
        """
        Update the filter options.

        Args:
            filter_updates: Filter updates
        """
        with self._lock:
            self.filter_options.update(filter_updates)
            self._notify_state_change()

    def add_state_listener(self, listener) -> None:
        """
        Add a state change listener.

        Args:
            listener: Function to call when the state changes
        """
        self._state_listeners.append(listener)

    def remove_state_listener(self, listener) -> None:
        """
        Remove a state change listener.

        Args:
            listener: Listener to remove
        """
        if listener in self._state_listeners:
            self._state_listeners.remove(listener)

    def _notify_state_change(self) -> None:
        """Notify all state listeners of a state change."""
        for listener in self._state_listeners:
            try:
                listener(self)
            except Exception as e:
                logger.error(f"Error in state listener: {e}")

    def start_analysis(self) -> None:
        """Start the analysis process."""
        if not self.project_path:
            self.set_error("Project path not set")
            self.set_screen(TUIScreen.ERROR)
            return

        # Set state to running
        self.set_analysis_state(AnalysisState.RUNNING)
        self.set_screen(TUIScreen.RUNNING)

        # Clear previous results
        self.metrics = None
        self.error = None
        self.progress = {}

        # Start analysis in a separate thread
        threading.Thread(target=self._run_analysis).start()

    def _run_analysis(self) -> None:
        """Run the analysis process."""
        try:
            # Run the analysis
            metrics = analyze_project(
                project_path=self.project_path,
                output_dir=self.output_dir,
                config=self.config
            )

            # Update state
            self.set_metrics(metrics)
            self.set_analysis_state(AnalysisState.COMPLETED)
            self.set_screen(TUIScreen.RESULTS)

        except Exception as e:
            logger.error(f"Error during analysis: {e}")
            self.set_error(str(e))
            self.set_analysis_state(AnalysisState.ERROR)
            self.set_screen(TUIScreen.ERROR)

    def request_exit(self) -> None:
        """Request application exit."""
        self.exit_requested = True
        self._notify_state_change()

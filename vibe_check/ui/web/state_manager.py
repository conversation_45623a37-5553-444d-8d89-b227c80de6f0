"""
Web UI State Manager
================

This module provides a state management system for the Vibe Check web UI.
It maintains the application state and provides methods for updating it.

The state manager acts as a bridge between the UI and the actor system,
translating UI events into messages for actors and actor responses into UI updates.
"""

import asyncio
import logging
import threading
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

from vibe_check.core import analyze_project
from vibe_check.core.models import ProjectMetrics

logger = logging.getLogger("pat_web_ui")


class AnalysisState(Enum):
    """Analysis state enum."""
    IDLE = "idle"
    RUNNING = "running"
    COMPLETED = "completed"
    ERROR = "error"


@dataclass
class UIState:
    """State of the web UI application."""
    project_path: Optional[str] = None
    output_dir: Optional[str] = None
    config: Dict[str, Any] = field(default_factory=dict)
    analysis_state: AnalysisState = AnalysisState.IDLE
    metrics: Optional[ProjectMetrics] = None
    progress: Dict[str, Any] = field(default_factory=dict)
    error: Optional[str] = None

    # Selected visualization options
    selected_view: str = "summary"
    selected_file: Optional[str] = None
    filter_options: Dict[str, Any] = field(default_factory=dict)


# WebUIActor removed as part of actor system cleanup


class StateManager:
    """State manager for the web UI application."""

    def __init__(self):
        """Initialize the state manager."""
        self.state = UIState()
        self.state_listeners = []
        self._lock = threading.Lock()

    def add_listener(self, listener) -> None:
        """
        Add a listener to be notified of state changes.

        Args:
            listener: Function to call when state changes
        """
        self.state_listeners.append(listener)

    def _notify_listeners(self) -> None:
        """Notify all listeners of a state change."""
        for listener in self.state_listeners:
            try:
                listener(self.state)
            except Exception as e:
                logger.error(f"Error in state listener: {e}")

    def set_project_path(self, project_path: str) -> None:
        """
        Set the project path.

        Args:
            project_path: Path to the project to analyze
        """
        with self._lock:
            self.state.project_path = project_path
            self._notify_listeners()

    def set_output_dir(self, output_dir: str) -> None:
        """
        Set the output directory.

        Args:
            output_dir: Directory to store output files
        """
        with self._lock:
            self.state.output_dir = output_dir
            self._notify_listeners()

    def update_config(self, config: Dict[str, Any]) -> None:
        """
        Update the configuration.

        Args:
            config: Configuration dictionary
        """
        with self._lock:
            self.state.config.update(config)
            self._notify_listeners()

    def set_analysis_state(self, state: AnalysisState) -> None:
        """
        Set the analysis state.

        Args:
            state: Analysis state
        """
        with self._lock:
            self.state.analysis_state = state
            self._notify_listeners()

    def set_metrics(self, metrics: ProjectMetrics) -> None:
        """
        Set the metrics.

        Args:
            metrics: ProjectMetrics object
        """
        with self._lock:
            self.state.metrics = metrics
            self._notify_listeners()

    def update_progress(self, progress: Dict[str, Any]) -> None:
        """
        Update the progress information.

        Args:
            progress: Progress information
        """
        with self._lock:
            self.state.progress.update(progress)
            self._notify_listeners()

    def set_error(self, error: str) -> None:
        """
        Set the error message.

        Args:
            error: Error message
        """
        with self._lock:
            self.state.error = error
            self._notify_listeners()

    def set_selected_view(self, view: str) -> None:
        """
        Set the selected view.

        Args:
            view: Selected view name
        """
        with self._lock:
            self.state.selected_view = view
            self._notify_listeners()

    def set_selected_file(self, file_path: str) -> None:
        """
        Set the selected file.

        Args:
            file_path: Path to the selected file
        """
        with self._lock:
            self.state.selected_file = file_path
            self._notify_listeners()

    def update_filter_options(self, options: Dict[str, Any]) -> None:
        """
        Update the filter options.

        Args:
            options: Filter options dictionary
        """
        with self._lock:
            self.state.filter_options.update(options)
            self._notify_listeners()

    def start_analysis(self) -> None:
        """Start the analysis process."""
        if not self.state.project_path:
            self.set_error("Project path not set")
            return

        # Set state to running
        self.set_analysis_state(AnalysisState.RUNNING)

        # Clear previous results
        self.state.metrics = None
        self.state.error = None
        self.state.progress = {}

        # Start analysis in a separate thread
        threading.Thread(target=self._run_analysis).start()

    def _run_analysis(self) -> None:
        """Run the analysis process."""
        try:
            # Log the start of analysis
            logger.info(f"Starting analysis for project: {self.state.project_path}")
            logger.info(f"Config: {self.state.config}")

            # Update progress to show we're starting
            self.update_progress({"status": "Starting analysis...", "percent_complete": 0})

            # Use a simpler approach that doesn't rely on the actor system
            from vibe_check.core.simple_analyzer import simple_analyze_project

            # Update progress
            self.update_progress({"status": "Analyzing project structure...", "percent_complete": 10})

            try:
                # Try to use the simple analyzer first
                metrics = simple_analyze_project(
                    project_path=self.state.project_path,
                    output_dir=self.state.output_dir,
                    config=self.state.config
                )

                # Log the completion
                logger.info("Analysis completed successfully using simple analyzer")
                logger.info(f"Metrics: {metrics}")

                # Update state
                self.set_metrics(metrics)
                self.set_analysis_state(AnalysisState.COMPLETED)
            except ImportError:
                # If the simple analyzer isn't available, fall back to the regular one
                logger.info("Simple analyzer not available, falling back to regular analyzer")
                self.update_progress({"status": "Using regular analyzer...", "percent_complete": 15})

                # Run the analysis
                metrics = analyze_project(
                    project_path=self.state.project_path,
                    output_dir=self.state.output_dir,
                    config=self.state.config
                )

                # Log the completion
                logger.info("Analysis completed successfully")
                logger.info(f"Metrics: {metrics}")

                # Update state
                self.set_metrics(metrics)
                self.set_analysis_state(AnalysisState.COMPLETED)

        except Exception as e:
            import traceback
            logger.error(f"Error during analysis: {e}")
            logger.error(traceback.format_exc())
            self.set_error(f"{str(e)}\n\nCheck the logs for more details.")
            self.set_analysis_state(AnalysisState.ERROR)

    # Actor system removed - initialize_actor method no longer available

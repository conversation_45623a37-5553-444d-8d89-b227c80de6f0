"""
Web UI Application
==============

This module provides the main web application for Vibe Check.
It integrates the state manager and components to create a complete web UI.
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, Optional

import streamlit as st

from vibe_check.core.models import ProjectMetrics
from vibe_check.ui.web.components import (
    render_header,
    render_progress,
    render_project_selector,
    render_results,
    render_visualization
)
from vibe_check.ui.web.state_manager import AnalysisState, StateManager

# Configure logging
logger = logging.getLogger("pat_web_ui")


def create_app() -> StateManager:
    """
    Create the web application.

    This function sets up the Streamlit app, creates the state manager,
    and registers the necessary callbacks.

    Returns:
        StateManager instance
    """
    # Create state manager
    state_manager = StateManager()

    # Set page config
    st.set_page_config(
        page_title="Vibe Check - Project Analysis Tool",
        page_icon="🔍",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Render the header
    render_header()

    # Show sidebar controls
    with st.sidebar:
        st.title("Vibe Check Controls")

        # Project selection
        config = render_project_selector(_on_project_select(state_manager))
        if config is not None:
            # Apply configuration if returned
            state_manager.update_config(config)

        # Only show these controls if a project is selected
        if state_manager.state.project_path:
            st.markdown("### Actions")

            # Start analysis button
            start_col, stop_col = st.columns(2)
            with start_col:
                if st.button("Start Analysis", key="start_analysis",
                         disabled=state_manager.state.analysis_state == AnalysisState.RUNNING):
                    state_manager.start_analysis()

            # Reset button
            with stop_col:
                if st.button("Reset", key="reset_analysis"):
                    state_manager = StateManager()

            # Show current state
            st.markdown(f"**Status**: {state_manager.state.analysis_state.value}")

            if state_manager.state.analysis_state == AnalysisState.ERROR:
                st.error(state_manager.state.error or "Unknown error")

    # Main content area
    analysis_state = state_manager.state.analysis_state

    if analysis_state == AnalysisState.IDLE:
        if not state_manager.state.project_path:
            st.info("Please select a project to analyze.")
        else:
            st.info("Configure options and click 'Start Analysis' to begin.")

    elif analysis_state == AnalysisState.RUNNING:
        st.markdown("## Analysis in Progress")
        render_progress(state_manager.state.progress)

    elif analysis_state == AnalysisState.COMPLETED and state_manager.state.metrics:
        # Render results and visualizations
        metrics = state_manager.state.metrics
        render_results(metrics)
        st.markdown("---")
        render_visualization(metrics)

    elif analysis_state == AnalysisState.ERROR:
        st.error(f"Error during analysis: {state_manager.state.error}")
        st.markdown("""
        Please check the following:
        - Project path is correct
        - Required tools are installed
        - Project has readable files
        """)

    # Return the state manager for caller to use if needed
    return state_manager


def _on_project_select(state_manager: StateManager):
    """
    Create a callback for project selection.

    Args:
        state_manager: StateManager instance

    Returns:
        Callback function for project selection
    """
    def callback(project_path: str):
        # Resolve and validate path
        try:
            path = Path(project_path).expanduser().resolve()
            if not path.exists():
                st.error(f"Project path does not exist: {path}")
                return
            if not path.is_dir():
                st.error(f"Project path is not a directory: {path}")
                return

            # Set project path in state manager
            state_manager.set_project_path(str(path))

            # Log selection
            logger.info(f"Selected project: {path}")

        except Exception as e:
            st.error(f"Error selecting project: {e}")
            logger.error(f"Error selecting project: {e}", exc_info=True)

    return callback


def run_app():
    """Run the web application."""
    try:
        # Create and run the app
        state_manager = create_app()

        # Actor system has been removed - using simple analyzer instead
        # No additional initialization needed
    except Exception as e:
        # Log the error
        logger.error(f"Error running web UI: {e}", exc_info=True)

        # Show error in UI
        st.error(f"Error running web UI: {e}")

        # Provide some troubleshooting information
        st.markdown("""
        ## Troubleshooting

        - Make sure all dependencies are installed: `pip install -e .`
        - Check the logs for detailed error information
        - Ensure you have permissions to read the project directory
        """)


def run_web_ui(project_path: Optional[str] = None,
              config_path: Optional[str] = None,
              context: Optional[Dict[str, Any]] = None):
    """
    Run the web UI with a specific project.

    Args:
        project_path: Optional path to the project to analyze
        config_path: Optional path to a configuration file
        context: Optional context metadata
    """
    # Set up session state if it doesn't exist
    if 'initialized' not in st.session_state:
        st.session_state.initialized = True
        st.session_state.project_path = project_path
        st.session_state.config_path = config_path
        st.session_state.context = context

    # Run the app
    run_app()


if __name__ == "__main__":
    # Set up logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Check for environment variables
    project_path = os.environ.get("VIBE_CHECK_PROJECT_PATH")
    config_path = os.environ.get("VIBE_CHECK_CONFIG_PATH")

    # Create state manager with project path if provided
    state_manager = StateManager()
    if project_path:
        state_manager.set_project_path(project_path)
        logger.info(f"Using project path from environment: {project_path}")

    # Run the app
    run_app()

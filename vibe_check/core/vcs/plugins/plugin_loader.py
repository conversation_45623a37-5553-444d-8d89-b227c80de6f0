"""
File: vibe_check/core/vcs/plugins/plugin_loader.py
Purpose: Plugin discovery and loading system
Related Files: vibe_check/core/vcs/plugins/plugin_manager.py
Dependencies: importlib, inspect, pathlib
"""

import importlib
import importlib.util
import inspect
import sys
from pathlib import Path
from typing import List, Type, Optional, Dict, Any

from .plugin_interface import PluginInterface
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class PluginLoader:
    """Loads and discovers VCS plugins from various sources."""
    
    def __init__(self):
        self._loaded_modules: Dict[str, Any] = {}
    
    async def discover_plugins(self, plugin_dir: Path) -> List[Type[PluginInterface]]:
        """
        Discover plugins in a directory.
        
        Args:
            plugin_dir: Directory to search for plugins
            
        Returns:
            List of plugin classes found
        """
        plugins = []
        
        if not plugin_dir.exists() or not plugin_dir.is_dir():
            logger.warning(f"Plugin directory does not exist: {plugin_dir}")
            return plugins
        
        # Look for Python files and packages
        for item in plugin_dir.iterdir():
            if item.is_file() and item.suffix == '.py' and not item.name.startswith('_'):
                # Single Python file plugin
                plugin_classes = await self._load_plugin_from_file(item)
                plugins.extend(plugin_classes)
            
            elif item.is_dir() and not item.name.startswith('_'):
                # Python package plugin
                plugin_classes = await self._load_plugin_from_package(item)
                plugins.extend(plugin_classes)
        
        logger.info(f"Discovered {len(plugins)} plugin classes in {plugin_dir}")
        return plugins
    
    async def _load_plugin_from_file(self, file_path: Path) -> List[Type[PluginInterface]]:
        """Load plugins from a single Python file."""
        plugins = []
        
        try:
            # Create module spec
            module_name = f"vcs_plugin_{file_path.stem}"
            spec = importlib.util.spec_from_file_location(module_name, file_path)
            
            if spec is None or spec.loader is None:
                logger.warning(f"Could not create module spec for {file_path}")
                return plugins
            
            # Load module
            module = importlib.util.module_from_spec(spec)
            self._loaded_modules[module_name] = module
            
            # Add to sys.modules to support relative imports
            sys.modules[module_name] = module
            
            # Execute module
            spec.loader.exec_module(module)
            
            # Find plugin classes
            plugin_classes = self._extract_plugin_classes(module)
            plugins.extend(plugin_classes)
            
            logger.debug(f"Loaded {len(plugin_classes)} plugins from {file_path}")
            
        except Exception as e:
            logger.error(f"Failed to load plugin from {file_path}: {e}")
        
        return plugins
    
    async def _load_plugin_from_package(self, package_dir: Path) -> List[Type[PluginInterface]]:
        """Load plugins from a Python package directory."""
        plugins = []
        
        # Check for __init__.py
        init_file = package_dir / '__init__.py'
        if not init_file.exists():
            logger.warning(f"Package directory missing __init__.py: {package_dir}")
            return plugins
        
        try:
            # Add package directory to Python path temporarily
            package_parent = package_dir.parent
            if str(package_parent) not in sys.path:
                sys.path.insert(0, str(package_parent))
                path_added = True
            else:
                path_added = False
            
            try:
                # Import package
                module_name = f"vcs_plugin_pkg_{package_dir.name}"
                spec = importlib.util.spec_from_file_location(
                    module_name, init_file, submodule_search_locations=[str(package_dir)]
                )
                
                if spec is None or spec.loader is None:
                    logger.warning(f"Could not create package spec for {package_dir}")
                    return plugins
                
                module = importlib.util.module_from_spec(spec)
                self._loaded_modules[module_name] = module
                sys.modules[module_name] = module
                
                spec.loader.exec_module(module)
                
                # Find plugin classes in the package
                plugin_classes = self._extract_plugin_classes(module)
                plugins.extend(plugin_classes)
                
                # Also check submodules
                for py_file in package_dir.glob('*.py'):
                    if py_file.name != '__init__.py' and not py_file.name.startswith('_'):
                        submodule_plugins = await self._load_plugin_from_file(py_file)
                        plugins.extend(submodule_plugins)
                
                logger.debug(f"Loaded {len(plugin_classes)} plugins from package {package_dir}")
                
            finally:
                # Remove from path if we added it
                if path_added:
                    sys.path.remove(str(package_parent))
            
        except Exception as e:
            logger.error(f"Failed to load plugin package {package_dir}: {e}")
        
        return plugins
    
    def _extract_plugin_classes(self, module) -> List[Type[PluginInterface]]:
        """Extract plugin classes from a loaded module."""
        plugin_classes = []
        
        for name, obj in inspect.getmembers(module, inspect.isclass):
            # Skip imported classes that aren't defined in this module
            if obj.__module__ != module.__name__:
                continue
            
            # Check if it's a plugin class
            if (issubclass(obj, PluginInterface) and 
                obj is not PluginInterface and
                not inspect.isabstract(obj)):
                
                plugin_classes.append(obj)
                logger.debug(f"Found plugin class: {name}")
        
        return plugin_classes
    
    async def load_plugin_from_entry_point(self, entry_point_name: str) -> List[Type[PluginInterface]]:
        """
        Load plugins from setuptools entry points.
        
        Args:
            entry_point_name: Name of the entry point group
            
        Returns:
            List of plugin classes loaded from entry points
        """
        plugins = []
        
        try:
            # Try to import pkg_resources for entry points
            import pkg_resources
            
            for entry_point in pkg_resources.iter_entry_points(entry_point_name):
                try:
                    plugin_class = entry_point.load()
                    
                    if (inspect.isclass(plugin_class) and 
                        issubclass(plugin_class, PluginInterface) and
                        not inspect.isabstract(plugin_class)):
                        
                        plugins.append(plugin_class)
                        logger.debug(f"Loaded plugin from entry point: {entry_point.name}")
                    else:
                        logger.warning(f"Entry point {entry_point.name} is not a valid plugin class")
                
                except Exception as e:
                    logger.error(f"Failed to load entry point {entry_point.name}: {e}")
        
        except ImportError:
            logger.debug("pkg_resources not available - skipping entry point discovery")
        
        return plugins
    
    async def validate_plugin(self, plugin_class: Type[PluginInterface]) -> bool:
        """
        Validate that a plugin class is properly implemented.
        
        Args:
            plugin_class: Plugin class to validate
            
        Returns:
            True if plugin is valid, False otherwise
        """
        try:
            # Check if it's a proper subclass
            if not issubclass(plugin_class, PluginInterface):
                logger.error(f"Plugin {plugin_class} is not a PluginInterface subclass")
                return False
            
            # Check if it's not abstract
            if inspect.isabstract(plugin_class):
                logger.error(f"Plugin {plugin_class} is abstract")
                return False
            
            # Try to instantiate (basic check)
            try:
                instance = plugin_class()
                
                # Check required methods
                if not hasattr(instance, 'metadata'):
                    logger.error(f"Plugin {plugin_class} missing metadata property")
                    return False
                
                # Try to access metadata
                metadata = instance.metadata
                if not metadata.name or not metadata.version:
                    logger.error(f"Plugin {plugin_class} has invalid metadata")
                    return False
                
                logger.debug(f"Plugin {plugin_class} validation passed")
                return True
                
            except Exception as e:
                logger.error(f"Plugin {plugin_class} instantiation failed: {e}")
                return False
        
        except Exception as e:
            logger.error(f"Plugin {plugin_class} validation error: {e}")
            return False
    
    def get_loaded_modules(self) -> Dict[str, Any]:
        """Get dictionary of loaded plugin modules."""
        return self._loaded_modules.copy()
    
    def unload_all_modules(self) -> None:
        """Unload all loaded plugin modules."""
        for module_name in list(self._loaded_modules.keys()):
            if module_name in sys.modules:
                del sys.modules[module_name]
            del self._loaded_modules[module_name]
        
        logger.debug("Unloaded all plugin modules")

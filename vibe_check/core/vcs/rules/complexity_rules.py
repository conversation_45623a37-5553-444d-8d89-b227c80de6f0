"""
Complexity Analysis Rules
=========================

Code complexity analysis rules including cyclomatic complexity,
cognitive complexity, and function/class size metrics.
"""

import ast
from typing import List, Set

from ..models import RuleCategory, IssueSeverity
from ..registry import AnalysisRule, AnalysisTarget, AnalysisIssue, AnalysisContext


class CyclomaticComplexityRule(AnalysisRule):
    """Calculate cyclomatic complexity for functions."""
    
    def __init__(self):
        super().__init__(
            rule_id="C001",
            category=RuleCategory.COMPLEXITY,
            name="Cyclomatic Complexity",
            description="Functions should have low cyclomatic complexity",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        threshold = self.config.get("complexity_threshold", 10)
        
        class ComplexityVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                complexity = self._calculate_cyclomatic_complexity(node)
                if complexity > threshold:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function '{node.name}' has high cyclomatic complexity ({complexity} > {threshold})",
                        fix_suggestion="Consider breaking this function into smaller functions",
                        severity=IssueSeverity.WARNING if complexity <= threshold * 1.5 else IssueSeverity.ERROR,
                        metadata={"complexity": complexity, "threshold": threshold, "function_name": node.name}
                    ))
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)  # Same logic for async functions
            
            def _calculate_cyclomatic_complexity(self, node):
                """Calculate cyclomatic complexity for a function."""
                complexity = 1  # Base complexity
                
                class ComplexityCounter(ast.NodeVisitor):
                    def __init__(self):
                        self.complexity = 0
                    
                    def visit_If(self, node):
                        self.complexity += 1
                        # elif clauses
                        if node.orelse and isinstance(node.orelse[0], ast.If):
                            self.complexity += len([n for n in node.orelse if isinstance(n, ast.If)])
                        self.generic_visit(node)
                    
                    def visit_While(self, node):
                        self.complexity += 1
                        self.generic_visit(node)
                    
                    def visit_For(self, node):
                        self.complexity += 1
                        self.generic_visit(node)
                    
                    def visit_ExceptHandler(self, node):
                        self.complexity += 1
                        self.generic_visit(node)
                    
                    def visit_With(self, node):
                        self.complexity += 1
                        self.generic_visit(node)
                    
                    def visit_Assert(self, node):
                        self.complexity += 1
                        self.generic_visit(node)
                    
                    def visit_BoolOp(self, node):
                        if isinstance(node.op, (ast.And, ast.Or)):
                            self.complexity += len(node.values) - 1
                        self.generic_visit(node)
                    
                    def visit_Compare(self, node):
                        # Multiple comparisons (a < b < c)
                        if len(node.comparators) > 1:
                            self.complexity += len(node.comparators) - 1
                        self.generic_visit(node)
                
                counter = ComplexityCounter()
                counter.visit(node)
                return complexity + counter.complexity
        
        visitor = ComplexityVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class FunctionLengthRule(AnalysisRule):
    """Check for overly long functions."""
    
    def __init__(self):
        super().__init__(
            rule_id="C002",
            category=RuleCategory.COMPLEXITY,
            name="Function Length",
            description="Functions should not be too long",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        max_lines = self.config.get("max_function_lines", 50)
        
        class FunctionLengthVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                # Calculate function length
                if hasattr(node, 'end_lineno') and node.end_lineno:
                    length = node.end_lineno - node.lineno + 1
                else:
                    # Fallback: count lines in function body
                    length = self._estimate_function_length(node)
                
                if length > max_lines:
                    severity = IssueSeverity.INFO
                    if length > max_lines * 2:
                        severity = IssueSeverity.WARNING
                    if length > max_lines * 3:
                        severity = IssueSeverity.ERROR
                    
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function '{node.name}' is too long ({length} lines > {max_lines})",
                        fix_suggestion="Consider breaking this function into smaller functions",
                        severity=severity,
                        metadata={"length": length, "max_lines": max_lines, "function_name": node.name}
                    ))
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
            
            def _estimate_function_length(self, node):
                """Estimate function length by counting statements."""
                class StatementCounter(ast.NodeVisitor):
                    def __init__(self):
                        self.count = 0
                    
                    def visit(self, node):
                        if isinstance(node, ast.stmt):
                            self.count += 1
                        self.generic_visit(node)
                
                counter = StatementCounter()
                counter.visit(node)
                return max(counter.count, 1)  # At least 1 line
        
        visitor = FunctionLengthVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class NestedComplexityRule(AnalysisRule):
    """Check for excessive nesting depth."""
    
    def __init__(self):
        super().__init__(
            rule_id="C003",
            category=RuleCategory.COMPLEXITY,
            name="Nested Complexity",
            description="Avoid excessive nesting depth",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        max_depth = self.config.get("max_nesting_depth", 4)
        
        class NestingVisitor(ast.NodeVisitor):
            def __init__(self):
                self.depth = 0
                self.max_depth_seen = 0
                self.depth_locations = []
            
            def visit_FunctionDef(self, node):
                self._check_nesting(node)
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
            
            def _check_nesting(self, func_node):
                """Check nesting depth within a function."""
                class DepthTracker(ast.NodeVisitor):
                    def __init__(self):
                        self.current_depth = 0
                        self.max_depth = 0
                        self.deep_locations = []
                    
                    def _enter_block(self, node):
                        self.current_depth += 1
                        if self.current_depth > self.max_depth:
                            self.max_depth = self.current_depth
                        if self.current_depth > max_depth:
                            self.deep_locations.append((node.lineno, node.col_offset, self.current_depth))
                        self.generic_visit(node)
                        self.current_depth -= 1
                    
                    def visit_If(self, node):
                        self._enter_block(node)
                    
                    def visit_While(self, node):
                        self._enter_block(node)
                    
                    def visit_For(self, node):
                        self._enter_block(node)
                    
                    def visit_With(self, node):
                        self._enter_block(node)
                    
                    def visit_Try(self, node):
                        self._enter_block(node)
                    
                    def visit_ExceptHandler(self, node):
                        self._enter_block(node)
                
                tracker = DepthTracker()
                tracker.visit(func_node)
                
                for line, col, depth in tracker.deep_locations:
                    issues.append(self.create_issue(
                        line=line,
                        column=col,
                        message=f"Excessive nesting depth ({depth} > {max_depth}) in function '{func_node.name}'",
                        fix_suggestion="Consider extracting nested logic into separate functions",
                        severity=IssueSeverity.WARNING if depth <= max_depth + 2 else IssueSeverity.ERROR,
                        metadata={"depth": depth, "max_depth": max_depth, "function_name": func_node.name}
                    ))
        
        visitor = NestingVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class ParameterCountRule(AnalysisRule):
    """Check for functions with too many parameters."""
    
    def __init__(self):
        super().__init__(
            rule_id="C004",
            category=RuleCategory.COMPLEXITY,
            name="Parameter Count",
            description="Functions should not have too many parameters",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        max_params = self.config.get("max_parameters", 5)
        
        class ParameterVisitor(ast.NodeVisitor):
            def visit_FunctionDef(self, node):
                param_count = len(node.args.args)
                
                # Add keyword-only arguments
                if node.args.kwonlyargs:
                    param_count += len(node.args.kwonlyargs)
                
                # Don't count 'self' or 'cls' for methods
                if (param_count > 0 and 
                    node.args.args and 
                    node.args.args[0].arg in ['self', 'cls']):
                    param_count -= 1
                
                if param_count > max_params:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Function '{node.name}' has too many parameters ({param_count} > {max_params})",
                        fix_suggestion="Consider using a configuration object or breaking the function into smaller parts",
                        severity=IssueSeverity.INFO if param_count <= max_params + 2 else IssueSeverity.WARNING,
                        metadata={"param_count": param_count, "max_params": max_params, "function_name": node.name}
                    ))
                self.generic_visit(node)
            
            def visit_AsyncFunctionDef(self, node):
                self.visit_FunctionDef(node)
        
        visitor = ParameterVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues


class ClassComplexityRule(AnalysisRule):
    """Check for overly complex classes."""
    
    def __init__(self):
        super().__init__(
            rule_id="C005",
            category=RuleCategory.COMPLEXITY,
            name="Class Complexity",
            description="Classes should not be overly complex",
            severity=IssueSeverity.INFO
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        issues = []
        max_methods = self.config.get("max_methods", 20)
        
        class ClassComplexityVisitor(ast.NodeVisitor):
            def visit_ClassDef(self, node):
                methods = [n for n in node.body if isinstance(n, (ast.FunctionDef, ast.AsyncFunctionDef))]
                method_count = len(methods)
                
                if method_count > max_methods:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message=f"Class '{node.name}' has too many methods ({method_count} > {max_methods})",
                        fix_suggestion="Consider breaking this class into smaller, more focused classes",
                        severity=IssueSeverity.INFO if method_count <= max_methods * 1.5 else IssueSeverity.WARNING,
                        metadata={"method_count": method_count, "max_methods": max_methods, "class_name": node.name}
                    ))
                
                self.generic_visit(node)
        
        visitor = ClassComplexityVisitor()
        visitor.create_issue = self.create_issue
        visitor.visit(ast_tree)
        
        return issues

"""
VCS Built-in Analysis Rules
===========================

This module provides built-in analysis rules for the VCS engine
across 6 categories: Style, Security, Complexity, Documentation, Imports, and Types.
"""

from .style_rules import *
from .security_rules import *
from .complexity_rules import *
from .documentation_rules import *
from .import_rules import *
from .type_rules import *
from .rule_loader import load_built_in_rules

__all__ = [
    "load_built_in_rules"
]

"""
Rule Loader
===========

Loads and registers all built-in analysis rules for the VCS engine.
"""

import logging
from typing import List

from ..registry import RuleRegistry, AnalysisRule

# Import all rule classes
from .style_rules import (
    LineLengthRule, TrailingWhitespaceRule, IndentationRule,
    NamingConventionRule, BlankLineRule, MultipleStatementsRule
)
from .security_rules import (
    HardcodedPasswordRule, SqlInjectionRule, UnsafeEvalRule,
    WeakCryptographyRule, InsecureRandomRule
)
from .complexity_rules import (
    CyclomaticComplexityRule, FunctionLengthRule, NestedComplexityRule,
    ParameterCountRule, ClassComplexityRule
)
from .documentation_rules import (
    MissingDocstringRule, DocstringFormatRule, CommentQualityRule,
    TypeHintDocumentationRule
)
from .import_rules import (
    UnusedImportRule, ImportOrderRule, WildcardImportRule,
    RelativeImportRule, CircularImportRule, ImportGroupingRule
)
from .type_rules import (
    MissingTypeHintsRule, InconsistentTypeHintsRule, ComplexTypeHintsRule,
    TypeAliasRule, GenericTypeRule, OptionalTypeRule
)

logger = logging.getLogger(__name__)


def load_built_in_rules(registry: RuleRegistry) -> int:
    """
    Load all built-in analysis rules into the registry.
    
    Args:
        registry: RuleRegistry instance to load rules into
        
    Returns:
        Number of rules successfully loaded
    """
    # Define all built-in rules
    rule_classes = [
        # Style Rules (6 rules)
        LineLengthRule,
        TrailingWhitespaceRule,
        IndentationRule,
        NamingConventionRule,
        BlankLineRule,
        MultipleStatementsRule,
        
        # Security Rules (5 rules)
        HardcodedPasswordRule,
        SqlInjectionRule,
        UnsafeEvalRule,
        WeakCryptographyRule,
        InsecureRandomRule,
        
        # Complexity Rules (5 rules)
        CyclomaticComplexityRule,
        FunctionLengthRule,
        NestedComplexityRule,
        ParameterCountRule,
        ClassComplexityRule,
        
        # Documentation Rules (4 rules)
        MissingDocstringRule,
        DocstringFormatRule,
        CommentQualityRule,
        TypeHintDocumentationRule,
        
        # Import Rules (6 rules)
        UnusedImportRule,
        ImportOrderRule,
        WildcardImportRule,
        RelativeImportRule,
        CircularImportRule,
        ImportGroupingRule,
        
        # Type Rules (6 rules)
        MissingTypeHintsRule,
        InconsistentTypeHintsRule,
        ComplexTypeHintsRule,
        TypeAliasRule,
        GenericTypeRule,
        OptionalTypeRule,
    ]
    
    loaded_count = 0
    failed_rules = []
    
    for rule_class in rule_classes:
        try:
            # Instantiate the rule
            rule = rule_class()
            
            # Register with the registry
            registry.register_rule(rule)
            loaded_count += 1
            
            logger.debug(f"Loaded rule: {rule.rule_id} ({rule.name})")
            
        except Exception as e:
            logger.error(f"Failed to load rule {rule_class.__name__}: {e}")
            failed_rules.append(rule_class.__name__)
    
    logger.info(f"Loaded {loaded_count} built-in rules")
    
    if failed_rules:
        logger.warning(f"Failed to load {len(failed_rules)} rules: {', '.join(failed_rules)}")
    
    return loaded_count


def get_rule_summary() -> dict:
    """
    Get a summary of all available built-in rules.
    
    Returns:
        Dictionary with rule categories and counts
    """
    from ..models import RuleCategory
    
    rule_counts = {
        RuleCategory.STYLE: 6,
        RuleCategory.SECURITY: 5,
        RuleCategory.COMPLEXITY: 5,
        RuleCategory.DOCS: 4,
        RuleCategory.IMPORTS: 6,
        RuleCategory.TYPES: 6,
    }
    
    total_rules = sum(rule_counts.values())
    
    return {
        "total_rules": total_rules,
        "categories": {
            category.value: count 
            for category, count in rule_counts.items()
        },
        "rule_details": {
            "style": [
                "S001: Line Length",
                "S002: Trailing Whitespace", 
                "S003: Indentation",
                "S004: Naming Convention",
                "S005: Blank Lines",
                "S006: Multiple Statements"
            ],
            "security": [
                "SEC001: Hardcoded Passwords",
                "SEC002: SQL Injection",
                "SEC003: Unsafe Eval",
                "SEC004: Weak Cryptography",
                "SEC005: Insecure Random"
            ],
            "complexity": [
                "C001: Cyclomatic Complexity",
                "C002: Function Length",
                "C003: Nested Complexity",
                "C004: Parameter Count",
                "C005: Class Complexity"
            ],
            "documentation": [
                "D001: Missing Docstring",
                "D002: Docstring Format",
                "D003: Comment Quality",
                "D004: Type Hint Documentation"
            ],
            "imports": [
                "I001: Unused Import",
                "I002: Import Order",
                "I003: Wildcard Import",
                "I004: Relative Import",
                "I005: Circular Import",
                "I006: Import Grouping"
            ],
            "types": [
                "T001: Missing Type Hints",
                "T002: Inconsistent Type Hints",
                "T003: Complex Type Hints",
                "T004: Type Alias",
                "T005: Generic Type Usage",
                "T006: Optional Type Usage"
            ]
        }
    }


def configure_default_rules(registry: RuleRegistry, config: dict = None) -> None:
    """
    Configure default settings for built-in rules.
    
    Args:
        registry: RuleRegistry instance
        config: Optional configuration dictionary
    """
    if config is None:
        config = {}
    
    # Default rule configurations
    default_configs = {
        # Style rules
        "S001": {"max_line_length": config.get("max_line_length", 88)},
        "S004": {"enforce_naming": config.get("enforce_naming", True)},
        
        # Complexity rules
        "C001": {"complexity_threshold": config.get("complexity_threshold", 10)},
        "C002": {"max_function_lines": config.get("max_function_lines", 50)},
        "C003": {"max_nesting_depth": config.get("max_nesting_depth", 4)},
        "C004": {"max_parameters": config.get("max_parameters", 5)},
        "C005": {"max_methods": config.get("max_methods", 20)},
        
        # Documentation rules
        "D001": {"require_module_docstring": config.get("require_module_docstring", True)},
        "D002": {"enforce_docstring_format": config.get("enforce_docstring_format", True)},
        
        # Import rules
        "I001": {"ignore_star_imports": config.get("ignore_star_imports", False)},
        "I002": {"enforce_import_order": config.get("enforce_import_order", True)},
        
        # Type rules
        "T001": {"require_return_type": config.get("require_return_type", True)},
        "T002": {"enforce_type_consistency": config.get("enforce_type_consistency", True)},
    }
    
    # Apply configurations
    for rule_id, rule_config in default_configs.items():
        try:
            registry.configure_rule(rule_id, rule_config)
            logger.debug(f"Configured rule {rule_id}: {rule_config}")
        except Exception as e:
            logger.warning(f"Failed to configure rule {rule_id}: {e}")


def get_rules_by_category(registry: RuleRegistry, category_name: str) -> List[AnalysisRule]:
    """
    Get all rules for a specific category.
    
    Args:
        registry: RuleRegistry instance
        category_name: Category name (style, security, complexity, docs, imports, types)
        
    Returns:
        List of rules in the category
    """
    from ..models import RuleCategory
    
    try:
        category = RuleCategory(category_name.lower())
        return registry.get_rules_for_category(category)
    except ValueError:
        logger.error(f"Invalid category: {category_name}")
        return []


def validate_rule_dependencies(registry: RuleRegistry) -> List[str]:
    """
    Validate that all rule dependencies are satisfied.
    
    Args:
        registry: RuleRegistry instance
        
    Returns:
        List of validation errors (empty if all dependencies are satisfied)
    """
    errors = []
    
    for rule_id, rule in registry.rules.items():
        for dep_id in rule.dependencies:
            if dep_id not in registry.rules:
                errors.append(f"Rule {rule_id} depends on missing rule {dep_id}")
    
    return errors


def get_rule_statistics(registry: RuleRegistry) -> dict:
    """
    Get statistics about loaded rules.
    
    Args:
        registry: RuleRegistry instance
        
    Returns:
        Dictionary with rule statistics
    """
    from ..models import RuleCategory
    
    stats = registry.get_registry_stats()
    
    # Add severity breakdown
    severity_counts = {}
    for rule in registry.rules.values():
        severity = rule.severity.value
        severity_counts[severity] = severity_counts.get(severity, 0) + 1
    
    stats["severity_breakdown"] = severity_counts
    
    # Add auto-fixable count
    auto_fixable_count = sum(1 for rule in registry.rules.values() 
                           if hasattr(rule, 'auto_fixable') and rule.auto_fixable)
    stats["auto_fixable_rules"] = auto_fixable_count
    
    return stats

"""
File: vibe_check/core/vcs/rules/framework_rules/django_rules.py
Purpose: Django-specific analysis rules
Related Files: vibe_check/core/vcs/rules/framework_rules/
Dependencies: ast, typing
"""

import ast
from typing import List, Set, Dict, Any

from vibe_check.core.vcs.models import (
    AnalysisTarget, AnalysisContext, AnalysisIssue, 
    RuleCategory, IssueSeverity
)
from vibe_check.core.vcs.registry import AnalysisRule
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class DjangoModelSecurityRule(AnalysisRule):
    """Detects Django model security issues."""
    
    def __init__(self):
        super().__init__(
            rule_id="DJANGO001",
            category=RuleCategory.SECURITY,
            name="Django Model Security",
            description="Detects security issues in Django models",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze Django models for security issues."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.ClassDef):
                # Check if it's a Django model
                if self._is_django_model(node):
                    issues.extend(self._check_model_security(node))
        
        return issues
    
    def _is_django_model(self, class_node: ast.ClassDef) -> bool:
        """Check if class is a Django model."""
        for base in class_node.bases:
            if isinstance(base, ast.Attribute):
                if (isinstance(base.value, ast.Name) and 
                    base.value.id == 'models' and 
                    base.attr == 'Model'):
                    return True
            elif isinstance(base, ast.Name) and base.id == 'Model':
                return True
        return False
    
    def _check_model_security(self, model_node: ast.ClassDef) -> List[AnalysisIssue]:
        """Check model for security issues."""
        issues = []
        
        # Check for missing __str__ method (information disclosure)
        has_str_method = any(
            isinstance(node, ast.FunctionDef) and node.name == '__str__'
            for node in model_node.body
        )
        
        if not has_str_method:
            issues.append(self.create_issue(
                line=model_node.lineno,
                column=model_node.col_offset,
                message=f"Model {model_node.name} missing __str__ method - may expose sensitive data",
                fix_suggestion="Add a __str__ method that doesn't expose sensitive information"
            ))
        
        # Check for fields without proper validation
        for node in model_node.body:
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Name):
                        field_name = target.id
                        if self._is_sensitive_field(field_name):
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"Sensitive field '{field_name}' may need additional validation",
                                fix_suggestion="Consider adding custom validation for sensitive fields"
                            ))
        
        return issues
    
    def _is_sensitive_field(self, field_name: str) -> bool:
        """Check if field name suggests sensitive data."""
        sensitive_patterns = [
            'password', 'secret', 'token', 'key', 'ssn', 
            'credit_card', 'bank', 'api_key'
        ]
        return any(pattern in field_name.lower() for pattern in sensitive_patterns)


class DjangoViewSecurityRule(AnalysisRule):
    """Detects Django view security issues."""
    
    def __init__(self):
        super().__init__(
            rule_id="DJANGO002",
            category=RuleCategory.SECURITY,
            name="Django View Security",
            description="Detects security issues in Django views",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze Django views for security issues."""
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.FunctionDef):
                if self._is_django_view(node):
                    issues.extend(self._check_view_security(node, content))
        
        return issues
    
    def _is_django_view(self, func_node: ast.FunctionDef) -> bool:
        """Check if function is a Django view."""
        # Check for request parameter
        if func_node.args.args and func_node.args.args[0].arg == 'request':
            return True
        
        # Check for view decorators
        for decorator in func_node.decorator_list:
            if isinstance(decorator, ast.Name):
                if decorator.id in ['login_required', 'csrf_exempt', 'require_http_methods']:
                    return True
        
        return False
    
    def _check_view_security(self, view_node: ast.FunctionDef, content: str) -> List[AnalysisIssue]:
        """Check view for security issues."""
        issues = []
        
        # Check for CSRF exemption
        has_csrf_exempt = any(
            isinstance(d, ast.Name) and d.id == 'csrf_exempt'
            for d in view_node.decorator_list
        )
        
        if has_csrf_exempt:
            issues.append(self.create_issue(
                line=view_node.lineno,
                column=view_node.col_offset,
                message=f"View {view_node.name} has CSRF protection disabled",
                fix_suggestion="Remove @csrf_exempt unless absolutely necessary and implement proper CSRF protection"
            ))
        
        # Check for missing authentication
        has_auth_decorator = any(
            isinstance(d, ast.Name) and d.id in ['login_required', 'permission_required']
            for d in view_node.decorator_list
        )
        
        if not has_auth_decorator and 'login' not in view_node.name.lower():
            issues.append(self.create_issue(
                line=view_node.lineno,
                column=view_node.col_offset,
                message=f"View {view_node.name} may need authentication",
                fix_suggestion="Consider adding @login_required or @permission_required decorator"
            ))
        
        # Check for SQL injection risks
        if 'raw(' in content or '.extra(' in content:
            issues.append(self.create_issue(
                line=view_node.lineno,
                column=view_node.col_offset,
                message="Potential SQL injection risk with raw queries",
                fix_suggestion="Use Django ORM methods or parameterized queries"
            ))
        
        return issues


class DjangoSettingsSecurityRule(AnalysisRule):
    """Detects Django settings security issues."""
    
    def __init__(self):
        super().__init__(
            rule_id="DJANGO003",
            category=RuleCategory.SECURITY,
            name="Django Settings Security",
            description="Detects security issues in Django settings",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze Django settings for security issues."""
        if not target.path or 'settings' not in target.path.name:
            return []
        
        issues = []
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Assign):
                for target_node in node.targets:
                    if isinstance(target_node, ast.Name):
                        setting_name = target_node.id
                        issues.extend(self._check_setting_security(setting_name, node, content))
        
        return issues
    
    def _check_setting_security(self, setting_name: str, node: ast.Assign, content: str) -> List[AnalysisIssue]:
        """Check individual setting for security issues."""
        issues = []
        
        # Check DEBUG setting
        if setting_name == 'DEBUG':
            if isinstance(node.value, ast.Constant) and node.value.value is True:
                issues.append(self.create_issue(
                    line=node.lineno,
                    column=node.col_offset,
                    message="DEBUG = True in production is a security risk",
                    fix_suggestion="Set DEBUG = False in production"
                ))
        
        # Check SECRET_KEY
        elif setting_name == 'SECRET_KEY':
            if isinstance(node.value, ast.Constant):
                secret_key = str(node.value.value)
                if len(secret_key) < 50:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="SECRET_KEY is too short",
                        fix_suggestion="Use a longer, more secure SECRET_KEY"
                    ))
                
                if secret_key in ['your-secret-key', 'change-me', 'secret']:
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="SECRET_KEY appears to be a default/weak value",
                        fix_suggestion="Generate a strong, unique SECRET_KEY"
                    ))
        
        # Check ALLOWED_HOSTS
        elif setting_name == 'ALLOWED_HOSTS':
            if isinstance(node.value, ast.List):
                for item in node.value.elts:
                    if isinstance(item, ast.Constant) and item.value == '*':
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message="ALLOWED_HOSTS contains '*' which allows any host",
                            fix_suggestion="Specify exact hostnames in ALLOWED_HOSTS"
                        ))
        
        return issues


class DjangoTemplateSecurityRule(AnalysisRule):
    """Detects Django template security issues."""
    
    def __init__(self):
        super().__init__(
            rule_id="DJANGO004",
            category=RuleCategory.SECURITY,
            name="Django Template Security",
            description="Detects security issues in Django template usage",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze for Django template security issues."""
        issues = []
        
        # Check for |safe filter usage
        if '|safe' in content:
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                if '|safe' in line:
                    issues.append(self.create_issue(
                        line=line_num,
                        column=line.find('|safe'),
                        message="Use of |safe filter may introduce XSS vulnerabilities",
                        fix_suggestion="Ensure data is properly sanitized before using |safe"
                    ))
        
        # Check for mark_safe usage
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Call):
                if (isinstance(node.func, ast.Name) and 
                    node.func.id == 'mark_safe'):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="mark_safe() usage may introduce XSS vulnerabilities",
                        fix_suggestion="Ensure data is properly sanitized before using mark_safe()"
                    ))
        
        return issues


class DjangoRuleSet:
    """Collection of Django-specific analysis rules."""
    
    @staticmethod
    def get_rules() -> List[AnalysisRule]:
        """Get all Django analysis rules."""
        return [
            DjangoModelSecurityRule(),
            DjangoViewSecurityRule(),
            DjangoSettingsSecurityRule(),
            DjangoTemplateSecurityRule()
        ]
    
    @staticmethod
    def get_rule_categories() -> Set[RuleCategory]:
        """Get categories covered by Django rules."""
        return {RuleCategory.SECURITY}
    
    @staticmethod
    def get_framework_name() -> str:
        """Get framework name."""
        return "Django"

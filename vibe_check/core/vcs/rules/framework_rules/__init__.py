"""
Framework-Specific Analysis Rules
==================================

This module provides specialized analysis rules for popular Python frameworks
including Django, Flask, and FastAPI.
"""

from .django_rules import DjangoRuleSet
from .flask_rules import FlaskRuleSet
from .fastapi_rules import FastAPIRuleSet
from .framework_detector import FrameworkDetector

__all__ = [
    'DjangoRuleSet',
    'FlaskRuleSet', 
    'FastAPIRuleSet',
    'FrameworkDetector'
]

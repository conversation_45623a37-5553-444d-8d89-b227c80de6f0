"""
File: vibe_check/core/vcs/rules/framework_rules/flask_rules.py
Purpose: Flask-specific analysis rules
Related Files: vibe_check/core/vcs/rules/framework_rules/
Dependencies: ast, typing
"""

import ast
from typing import List, Set, Dict, Any

from vibe_check.core.vcs.models import (
    AnalysisTarget, AnalysisContext, AnalysisIssue, 
    RuleCategory, IssueSeverity
)
from vibe_check.core.vcs.registry import AnalysisRule
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class FlaskSecurityHeadersRule(AnalysisRule):
    """Detects missing security headers in Flask applications."""
    
    def __init__(self):
        super().__init__(
            rule_id="FLASK001",
            category=RuleCategory.SECURITY,
            name="Flask Security Headers",
            description="Detects missing security headers in Flask applications",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze Flask app for missing security headers."""
        issues = []
        
        # Check if this is a Flask app file
        if not self._is_flask_app_file(content):
            return issues
        
        # Check for security headers configuration
        security_headers = [
            'X-Content-Type-Options',
            'X-Frame-Options', 
            'X-XSS-Protection',
            'Strict-Transport-Security',
            'Content-Security-Policy'
        ]
        
        for header in security_headers:
            if header not in content:
                issues.append(self.create_issue(
                    line=1,
                    column=1,
                    message=f"Missing security header: {header}",
                    fix_suggestion=f"Add {header} header to responses for better security"
                ))
        
        return issues
    
    def _is_flask_app_file(self, content: str) -> bool:
        """Check if file contains Flask app."""
        return 'Flask(' in content or 'from flask import' in content


class FlaskDebugModeRule(AnalysisRule):
    """Detects debug mode enabled in Flask applications."""
    
    def __init__(self):
        super().__init__(
            rule_id="FLASK002",
            category=RuleCategory.SECURITY,
            name="Flask Debug Mode",
            description="Detects debug mode enabled in production",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze for debug mode issues."""
        issues = []
        
        for node in ast.walk(ast_tree):
            # Check app.run(debug=True)
            if isinstance(node, ast.Call):
                if (isinstance(node.func, ast.Attribute) and 
                    node.func.attr == 'run'):
                    
                    for keyword in node.keywords:
                        if (keyword.arg == 'debug' and 
                            isinstance(keyword.value, ast.Constant) and
                            keyword.value.value is True):
                            
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message="Debug mode enabled - security risk in production",
                                fix_suggestion="Set debug=False in production or use environment variables"
                            ))
        
        # Check for app.debug = True
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if (isinstance(target, ast.Attribute) and 
                        target.attr == 'debug' and
                        isinstance(node.value, ast.Constant) and
                        node.value.value is True):
                        
                        issues.append(self.create_issue(
                            line=node.lineno,
                            column=node.col_offset,
                            message="Debug mode explicitly enabled - security risk",
                            fix_suggestion="Set app.debug = False in production"
                        ))
        
        return issues


class FlaskSQLInjectionRule(AnalysisRule):
    """Detects potential SQL injection vulnerabilities in Flask apps."""
    
    def __init__(self):
        super().__init__(
            rule_id="FLASK003",
            category=RuleCategory.SECURITY,
            name="Flask SQL Injection",
            description="Detects potential SQL injection vulnerabilities",
            severity=IssueSeverity.ERROR
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze for SQL injection vulnerabilities."""
        issues = []
        
        # Check for string formatting in SQL queries
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Call):
                # Check for execute() calls with string formatting
                if (isinstance(node.func, ast.Attribute) and 
                    node.func.attr == 'execute'):
                    
                    for arg in node.args:
                        if self._has_string_formatting(arg):
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message="Potential SQL injection via string formatting",
                                fix_suggestion="Use parameterized queries instead of string formatting"
                            ))
        
        # Check for f-strings in SQL context
        lines = content.split('\n')
        for line_num, line in enumerate(lines, 1):
            if ('SELECT' in line.upper() or 'INSERT' in line.upper() or 
                'UPDATE' in line.upper() or 'DELETE' in line.upper()):
                if 'f"' in line or "f'" in line:
                    issues.append(self.create_issue(
                        line=line_num,
                        column=line.find('f"') if 'f"' in line else line.find("f'"),
                        message="Potential SQL injection via f-string",
                        fix_suggestion="Use parameterized queries instead of f-strings"
                    ))
        
        return issues
    
    def _has_string_formatting(self, node: ast.AST) -> bool:
        """Check if node contains string formatting."""
        if isinstance(node, ast.BinOp) and isinstance(node.op, ast.Mod):
            return True
        if isinstance(node, ast.Call) and isinstance(node.func, ast.Attribute):
            if node.func.attr == 'format':
                return True
        if isinstance(node, ast.JoinedStr):  # f-string
            return True
        return False


class FlaskCSRFRule(AnalysisRule):
    """Detects missing CSRF protection in Flask applications."""
    
    def __init__(self):
        super().__init__(
            rule_id="FLASK004",
            category=RuleCategory.SECURITY,
            name="Flask CSRF Protection",
            description="Detects missing CSRF protection",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze for CSRF protection."""
        issues = []
        
        # Check if Flask-WTF is imported
        has_csrf_import = ('flask_wtf' in content or 
                          'CSRFProtect' in content or
                          'csrf' in content.lower())
        
        # Check for POST routes without CSRF protection
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.FunctionDef):
                # Check for route decorators
                for decorator in node.decorator_list:
                    if self._is_post_route(decorator):
                        if not has_csrf_import:
                            issues.append(self.create_issue(
                                line=node.lineno,
                                column=node.col_offset,
                                message=f"POST route {node.name} may need CSRF protection",
                                fix_suggestion="Consider using Flask-WTF for CSRF protection"
                            ))
        
        return issues
    
    def _is_post_route(self, decorator: ast.AST) -> bool:
        """Check if decorator defines a POST route."""
        if isinstance(decorator, ast.Call):
            # Check for @app.route(..., methods=['POST'])
            for keyword in decorator.keywords:
                if keyword.arg == 'methods':
                    if isinstance(keyword.value, ast.List):
                        for method in keyword.value.elts:
                            if (isinstance(method, ast.Constant) and 
                                method.value == 'POST'):
                                return True
        return False


class FlaskSessionSecurityRule(AnalysisRule):
    """Detects Flask session security issues."""
    
    def __init__(self):
        super().__init__(
            rule_id="FLASK005",
            category=RuleCategory.SECURITY,
            name="Flask Session Security",
            description="Detects Flask session security issues",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze Flask session security."""
        issues = []
        
        # Check for SECRET_KEY configuration
        has_secret_key = False
        
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Assign):
                for target in node.targets:
                    if isinstance(target, ast.Subscript):
                        # Check app.config['SECRET_KEY']
                        if (isinstance(target.slice, ast.Constant) and
                            target.slice.value == 'SECRET_KEY'):
                            has_secret_key = True
                            
                            # Check if it's a weak secret key
                            if isinstance(node.value, ast.Constant):
                                secret = str(node.value.value)
                                if len(secret) < 32:
                                    issues.append(self.create_issue(
                                        line=node.lineno,
                                        column=node.col_offset,
                                        message="SECRET_KEY is too short",
                                        fix_suggestion="Use a longer, more secure SECRET_KEY (at least 32 characters)"
                                    ))
        
        if not has_secret_key and 'session' in content:
            issues.append(self.create_issue(
                line=1,
                column=1,
                message="Using sessions without SECRET_KEY configuration",
                fix_suggestion="Set app.config['SECRET_KEY'] for secure sessions"
            ))
        
        return issues


class FlaskTemplateSecurityRule(AnalysisRule):
    """Detects Flask template security issues."""
    
    def __init__(self):
        super().__init__(
            rule_id="FLASK006",
            category=RuleCategory.SECURITY,
            name="Flask Template Security",
            description="Detects template security issues",
            severity=IssueSeverity.WARNING
        )
    
    async def analyze(self, target: AnalysisTarget, content: str, 
                     ast_tree: ast.AST, context: AnalysisContext) -> List[AnalysisIssue]:
        """Analyze Flask template security."""
        issues = []
        
        # Check for render_template_string usage
        for node in ast.walk(ast_tree):
            if isinstance(node, ast.Call):
                if (isinstance(node.func, ast.Name) and 
                    node.func.id == 'render_template_string'):
                    issues.append(self.create_issue(
                        line=node.lineno,
                        column=node.col_offset,
                        message="render_template_string can lead to template injection",
                        fix_suggestion="Use render_template with static template files instead"
                    ))
        
        # Check for |safe filter in template strings
        if '|safe' in content:
            lines = content.split('\n')
            for line_num, line in enumerate(lines, 1):
                if '|safe' in line:
                    issues.append(self.create_issue(
                        line=line_num,
                        column=line.find('|safe'),
                        message="Use of |safe filter may introduce XSS vulnerabilities",
                        fix_suggestion="Ensure data is properly sanitized before using |safe"
                    ))
        
        return issues


class FlaskRuleSet:
    """Collection of Flask-specific analysis rules."""
    
    @staticmethod
    def get_rules() -> List[AnalysisRule]:
        """Get all Flask analysis rules."""
        return [
            FlaskSecurityHeadersRule(),
            FlaskDebugModeRule(),
            FlaskSQLInjectionRule(),
            FlaskCSRFRule(),
            FlaskSessionSecurityRule(),
            FlaskTemplateSecurityRule()
        ]
    
    @staticmethod
    def get_rule_categories() -> Set[RuleCategory]:
        """Get categories covered by Flask rules."""
        return {RuleCategory.SECURITY}
    
    @staticmethod
    def get_framework_name() -> str:
        """Get framework name."""
        return "Flask"

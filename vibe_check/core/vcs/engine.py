"""
VCS Engine Core
===============

This module provides the core VibeCheckEngine class that orchestrates
analysis in both integrated and standalone modes.
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

from .models import (
    EngineMode, AnalysisTarget, AnalysisResult, AnalysisContext,
    AnalysisIssue, AnalysisMetrics, IssueSeverity, RuleCategory
)
from .config import VCSConfig, VCSConfigManager
from .registry import RuleRegistry
from .cache import CacheManager
from .performance import PerformanceMonitor

logger = logging.getLogger(__name__)


class VibeCheckEngine:
    """
    Core VCS engine that orchestrates analysis in both integrated and standalone modes.
    
    The engine provides:
    - Dual-mode operation (integrated/standalone)
    - Rule-based analysis with extensible registry
    - Performance monitoring and caching
    - Configuration management
    - Integration with existing Vibe Check components
    """
    
    def __init__(self, mode: EngineMode, config: VCSConfig):
        """
        Initialize the VCS engine.
        
        Args:
            mode: Engine operation mode (integrated/standalone)
            config: Engine configuration
        """
        self.mode = mode
        self.config = config
        self._initialized = False
        self._running = False
        
        # Core components
        self.rule_registry = RuleRegistry()
        self.cache_manager: Optional[CacheManager] = None
        self.performance_monitor = PerformanceMonitor()
        
        # State tracking
        self._analysis_count = 0
        self._total_execution_time = 0.0
        self._last_analysis_time: Optional[float] = None
        
        logger.info(f"VCS Engine initialized in {mode.value} mode")
    
    async def initialize(self) -> None:
        """
        Initialize the engine components.
        
        This method sets up caching, loads rules, and prepares
        the engine for analysis operations.
        """
        if self._initialized:
            logger.warning("Engine already initialized")
            return
        
        try:
            # Initialize cache manager if caching is enabled
            if self.config.cache_enabled and self.config.cache_dir:
                self.cache_manager = CacheManager(self.config.cache_dir)
                await self.cache_manager.initialize()
                logger.info(f"Cache initialized at {self.config.cache_dir}")
            
            # Load built-in rules
            await self._load_built_in_rules()
            
            # Initialize performance monitoring
            self.performance_monitor.initialize()
            
            self._initialized = True
            logger.info("VCS Engine initialization completed")
            
        except Exception as e:
            logger.error(f"Failed to initialize VCS Engine: {e}")
            raise
    
    async def start(self) -> None:
        """Start the engine for analysis operations."""
        if not self._initialized:
            await self.initialize()
        
        self._running = True
        logger.info("VCS Engine started")
    
    async def stop(self) -> None:
        """Stop the engine and cleanup resources."""
        self._running = False
        
        # Cleanup cache manager
        if self.cache_manager:
            await self.cache_manager.cleanup()
        
        # Generate final performance report
        if self._analysis_count > 0:
            report = self.performance_monitor.get_performance_report()
            logger.info(f"Engine performance: {report}")
        
        logger.info("VCS Engine stopped")
    
    async def restart(self) -> None:
        """Restart the engine."""
        await self.stop()
        await self.start()
    
    def is_running(self) -> bool:
        """Check if the engine is running."""
        return self._running
    
    def is_enabled(self) -> bool:
        """Check if the engine is enabled for analysis."""
        return self._initialized and self._running
    
    async def analyze(self, target: AnalysisTarget, context: Optional[AnalysisContext] = None) -> AnalysisResult:
        """
        Main analysis entry point for dual-mode operation.
        
        Args:
            target: Analysis target (file, directory, or content)
            context: Optional analysis context
            
        Returns:
            Analysis result with issues, metrics, and metadata
            
        Raises:
            RuntimeError: If engine is not running
            ValueError: If target is invalid
        """
        if not self.is_enabled():
            raise RuntimeError("Engine is not running. Call start() first.")
        
        # Create default context if not provided
        if context is None:
            context = AnalysisContext.create_default(self.mode)
        
        # Update context with engine configuration
        context = self._update_context_from_config(context)
        
        start_time = time.time()
        
        try:
            # Check cache first
            cached_result = None
            if self.cache_manager and context.cache_enabled:
                cached_result = await self.cache_manager.get_cached_result(target)
                if cached_result:
                    logger.debug(f"Using cached result for {target.path}")
                    return cached_result
            
            # Perform analysis
            result = await self._perform_analysis(target, context)
            
            # Cache result if caching is enabled
            if self.cache_manager and context.cache_enabled and result.success:
                await self.cache_manager.cache_result(target, result)
            
            # Update performance metrics
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            self._update_performance_metrics(execution_time)
            
            # Track analysis in performance monitor
            self.performance_monitor.track_analysis(target, execution_time)
            
            logger.info(f"Analysis completed for {target.path} in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Analysis failed for {target.path}: {e}")
            
            # Return error result
            return AnalysisResult(
                target=target,
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )
    
    async def _perform_analysis(self, target: AnalysisTarget, context: AnalysisContext) -> AnalysisResult:
        """
        Perform the actual analysis using registered rules.
        
        Args:
            target: Analysis target
            context: Analysis context
            
        Returns:
            Analysis result
        """
        result = AnalysisResult(target=target)
        
        try:
            # Get content and AST
            content = target.get_content()
            ast_tree = target.get_ast()
            
            # Get applicable rules
            rules = self.rule_registry.get_applicable_rules(context)
            
            # Execute rules
            for rule in rules:
                try:
                    rule_issues = await rule.analyze(target, content, ast_tree, context)
                    result.issues.extend(rule_issues)
                    result.rules_executed.append(rule.rule_id)
                    
                except Exception as e:
                    logger.warning(f"Rule {rule.rule_id} failed: {e}")
                    # Continue with other rules
            
            # Calculate metrics
            result.metrics = await self._calculate_metrics(target, content, ast_tree, context)
            
            result.success = True
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"Analysis failed: {e}")
        
        return result
    
    async def _calculate_metrics(self, target: AnalysisTarget, content: str, 
                               ast_tree: Any, context: AnalysisContext) -> AnalysisMetrics:
        """Calculate code metrics for the target."""
        # This will be implemented with specific metric calculators
        # For now, return basic metrics
        lines = content.split('\n')
        return AnalysisMetrics(
            lines_of_code=len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        )
    
    async def _load_built_in_rules(self) -> None:
        """Load built-in analysis rules."""
        logger.info("Loading built-in analysis rules...")

        try:
            from .rules.rule_loader import load_built_in_rules, configure_default_rules

            # Load all built-in rules
            loaded_count = load_built_in_rules(self.rule_registry)

            # Configure default rule settings
            configure_default_rules(self.rule_registry, self.config.rule_config)

            logger.info(f"Successfully loaded {loaded_count} built-in analysis rules")

        except Exception as e:
            logger.error(f"Failed to load built-in rules: {e}")
            # Continue without rules - engine can still function
    
    def _update_context_from_config(self, context: AnalysisContext) -> AnalysisContext:
        """Update analysis context with engine configuration."""
        # Apply configuration to context
        if self.config.enabled_categories:
            context.enabled_categories = self.config.enabled_categories
        if self.config.enabled_rules:
            context.enabled_rules = self.config.enabled_rules
        if self.config.disabled_rules:
            context.disabled_rules = self.config.disabled_rules
        
        context.performance_mode = self.config.performance_mode
        context.cache_enabled = self.config.cache_enabled
        context.auto_fix_enabled = self.config.auto_fix_enabled
        
        return context
    
    def _update_performance_metrics(self, execution_time: float) -> None:
        """Update internal performance metrics."""
        self._analysis_count += 1
        self._total_execution_time += execution_time
        self._last_analysis_time = execution_time
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        if self._analysis_count == 0:
            return {"analyses": 0, "total_time": 0.0, "average_time": 0.0}
        
        return {
            "analyses": self._analysis_count,
            "total_time": self._total_execution_time,
            "average_time": self._total_execution_time / self._analysis_count,
            "last_analysis_time": self._last_analysis_time
        }
    
    def get_status(self) -> Dict[str, Any]:
        """Get engine status information."""
        return {
            "mode": self.mode.value,
            "initialized": self._initialized,
            "running": self._running,
            "cache_enabled": self.config.cache_enabled,
            "rules_loaded": len(self.rule_registry.rules),
            "performance": self.get_performance_stats()
        }

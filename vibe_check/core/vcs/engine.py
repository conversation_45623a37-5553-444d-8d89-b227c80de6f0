"""
VCS Engine Core
===============

This module provides the core VibeCheckEngine class that orchestrates
analysis in both integrated and standalone modes.
"""

import asyncio
import logging
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union, Tuple

from .models import (
    EngineMode, AnalysisTarget, AnalysisResult, AnalysisContext,
    AnalysisIssue, AnalysisMetrics, IssueSeverity, RuleCategory
)
from .config import VCSConfig, VCSConfigManager
from .registry import RuleRegistry
from .cache import CacheManager
from .performance import PerformanceMonitor
from .auto_fix import AutoFixEngine, InteractiveFixEngine, FixReport
from .incremental_analysis import IncrementalAnalyzer
from .memory_manager import MemoryManager
from .plugins import PluginManager
from .integration import IntegrationManager

# Enterprise features (optional import)
try:
    from vibe_check.enterprise.reporting import EnterpriseReportingEngine
    from vibe_check.enterprise.collaboration import TeamManager, SharedConfiguration
    from vibe_check.enterprise.collaboration.analysis import CollaborativeAnalysisManager
    from vibe_check.enterprise.cicd import CICDIntegrationManager
    from vibe_check.enterprise.monitoring import (
        MonitoringSystem, QualityGateManager, AlertingSystem, DashboardManager
    )
    from vibe_check.enterprise.api import UnifiedAPIManager
    from vibe_check.enterprise.dashboard import WebDashboardServer
    ENTERPRISE_AVAILABLE = True
except ImportError:
    ENTERPRISE_AVAILABLE = False

logger = logging.getLogger(__name__)


class VibeCheckEngine:
    """
    Core VCS engine that orchestrates analysis in both integrated and standalone modes.
    
    The engine provides:
    - Dual-mode operation (integrated/standalone)
    - Rule-based analysis with extensible registry
    - Performance monitoring and caching
    - Configuration management
    - Integration with existing Vibe Check components
    """
    
    def __init__(self, mode: EngineMode, config: VCSConfig):
        """
        Initialize the VCS engine.
        
        Args:
            mode: Engine operation mode (integrated/standalone)
            config: Engine configuration
        """
        self.mode = mode
        self.config = config
        self._initialized = False
        self._running = False
        
        # Core components
        self.rule_registry = RuleRegistry()
        self.cache_manager: Optional[CacheManager] = None
        self.performance_monitor = PerformanceMonitor()
        self.auto_fix_engine = AutoFixEngine()
        self.interactive_fix_engine = InteractiveFixEngine(self.auto_fix_engine)
        self.memory_manager = MemoryManager()
        self.incremental_analyzer: Optional[IncrementalAnalyzer] = None
        self.plugin_manager = PluginManager()
        self.integration_manager = IntegrationManager()

        # Enterprise features (optional)
        self.enterprise_reporting: Optional[Any] = None
        self.team_manager: Optional[Any] = None
        self.shared_config: Optional[Any] = None
        self.collaborative_analysis: Optional[Any] = None
        self.cicd_manager: Optional[Any] = None
        self.monitoring_system: Optional[Any] = None
        self.quality_gate_manager: Optional[Any] = None
        self.alerting_system: Optional[Any] = None
        self.dashboard_manager: Optional[Any] = None
        self.api_manager: Optional[Any] = None
        self.web_dashboard: Optional[Any] = None

        if ENTERPRISE_AVAILABLE:
            self.enterprise_reporting = EnterpriseReportingEngine()
            self.team_manager = TeamManager()
            self.shared_config = SharedConfiguration()
            self.collaborative_analysis = CollaborativeAnalysisManager()
            self.cicd_manager = CICDIntegrationManager()
            self.monitoring_system = MonitoringSystem()
            self.quality_gate_manager = QualityGateManager()
            self.alerting_system = AlertingSystem()
            self.dashboard_manager = DashboardManager(self.monitoring_system)
            self.api_manager = UnifiedAPIManager(self)
            self.web_dashboard = WebDashboardServer(self)
        
        # State tracking
        self._analysis_count = 0
        self._total_execution_time = 0.0
        self._last_analysis_time: Optional[float] = None
        
        logger.info(f"VCS Engine initialized in {mode.value} mode")
    
    async def initialize(self) -> None:
        """
        Initialize the engine components.
        
        This method sets up caching, loads rules, and prepares
        the engine for analysis operations.
        """
        if self._initialized:
            logger.warning("Engine already initialized")
            return
        
        try:
            # Initialize cache manager if caching is enabled
            if self.config.cache_enabled and self.config.cache_dir:
                self.cache_manager = CacheManager(self.config.cache_dir)
                await self.cache_manager.initialize()
                logger.info(f"Cache initialized at {self.config.cache_dir}")

                # Initialize incremental analyzer
                self.incremental_analyzer = IncrementalAnalyzer(
                    Path.cwd(), self.cache_manager
                )
                await self.incremental_analyzer.initialize()

            # Initialize memory manager
            await self.memory_manager.initialize()

            # Initialize plugin manager
            plugin_config = getattr(self.config, 'plugin_config', {})
            await self.plugin_manager.initialize(plugin_config)

            # Initialize integration manager
            await self.integration_manager.initialize()

            # Initialize enterprise features if available
            if self.enterprise_reporting:
                await self.enterprise_reporting.initialize()

            if self.team_manager:
                await self.team_manager.initialize()

            if self.shared_config:
                await self.shared_config.initialize()

            if self.collaborative_analysis:
                await self.collaborative_analysis.initialize()

            if self.cicd_manager:
                await self.cicd_manager.initialize()

            if self.monitoring_system:
                await self.monitoring_system.initialize()

            if self.quality_gate_manager:
                await self.quality_gate_manager.initialize()

            if self.alerting_system:
                await self.alerting_system.initialize()

            if self.dashboard_manager:
                await self.dashboard_manager.initialize()

            if self.api_manager:
                await self.api_manager.initialize()

            if self.web_dashboard:
                await self.web_dashboard.initialize()

            # Load built-in rules
            await self._load_built_in_rules()

            # Load framework-specific rules
            await self._load_framework_rules()

            # Load plugin rules
            await self._load_plugin_rules()

            # Initialize performance monitoring
            self.performance_monitor.initialize()
            
            self._initialized = True
            logger.info("VCS Engine initialization completed")
            
        except Exception as e:
            logger.error(f"Failed to initialize VCS Engine: {e}")
            raise
    
    async def start(self) -> None:
        """Start the engine for analysis operations."""
        if not self._initialized:
            await self.initialize()
        
        self._running = True
        logger.info("VCS Engine started")
    
    async def stop(self) -> None:
        """Stop the engine and cleanup resources."""
        self._running = False
        
        # Cleanup cache manager
        if self.cache_manager:
            await self.cache_manager.cleanup()

        # Cleanup plugin manager
        await self.plugin_manager.cleanup()

        # Cleanup integration manager
        await self.integration_manager.cleanup()

        # Cleanup enterprise features if available
        if self.enterprise_reporting:
            await self.enterprise_reporting.cleanup()

        if self.team_manager:
            await self.team_manager.cleanup()

        if self.shared_config:
            await self.shared_config.cleanup()

        if self.collaborative_analysis:
            await self.collaborative_analysis.cleanup()

        if self.cicd_manager:
            await self.cicd_manager.cleanup()

        if self.dashboard_manager:
            await self.dashboard_manager.cleanup()

        if self.alerting_system:
            await self.alerting_system.cleanup()

        if self.quality_gate_manager:
            await self.quality_gate_manager.cleanup()

        if self.monitoring_system:
            await self.monitoring_system.cleanup()

        if self.api_manager:
            await self.api_manager.cleanup()

        if self.web_dashboard:
            await self.web_dashboard.cleanup()
        
        # Generate final performance report
        if self._analysis_count > 0:
            report = self.performance_monitor.get_performance_report()
            logger.info(f"Engine performance: {report}")
        
        logger.info("VCS Engine stopped")
    
    async def restart(self) -> None:
        """Restart the engine."""
        await self.stop()
        await self.start()
    
    def is_running(self) -> bool:
        """Check if the engine is running."""
        return self._running
    
    def is_enabled(self) -> bool:
        """Check if the engine is enabled for analysis."""
        return self._initialized and self._running
    
    async def analyze(self, target: AnalysisTarget, context: Optional[AnalysisContext] = None) -> AnalysisResult:
        """
        Main analysis entry point for dual-mode operation.
        
        Args:
            target: Analysis target (file, directory, or content)
            context: Optional analysis context
            
        Returns:
            Analysis result with issues, metrics, and metadata
            
        Raises:
            RuntimeError: If engine is not running
            ValueError: If target is invalid
        """
        if not self.is_enabled():
            raise RuntimeError("Engine is not running. Call start() first.")
        
        # Create default context if not provided
        if context is None:
            context = AnalysisContext.create_default(self.mode)
        
        # Update context with engine configuration
        context = self._update_context_from_config(context)
        
        start_time = time.time()
        
        try:
            # Check cache first
            cached_result = None
            if self.cache_manager and context.cache_enabled:
                cached_result = await self.cache_manager.get_cached_result(target)
                if cached_result:
                    logger.debug(f"Using cached result for {target.path}")
                    return cached_result
            
            # Perform analysis
            result = await self._perform_analysis(target, context)
            
            # Cache result if caching is enabled
            if self.cache_manager and context.cache_enabled and result.success:
                await self.cache_manager.cache_result(target, result)
            
            # Update performance metrics
            execution_time = time.time() - start_time
            result.execution_time = execution_time
            self._update_performance_metrics(execution_time)
            
            # Track analysis in performance monitor
            self.performance_monitor.track_analysis(target, execution_time)
            
            logger.info(f"Analysis completed for {target.path} in {execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Analysis failed for {target.path}: {e}")
            
            # Return error result
            return AnalysisResult(
                target=target,
                success=False,
                error_message=str(e),
                execution_time=execution_time
            )
    
    async def _perform_analysis(self, target: AnalysisTarget, context: AnalysisContext) -> AnalysisResult:
        """
        Perform the actual analysis using registered rules.
        
        Args:
            target: Analysis target
            context: Analysis context
            
        Returns:
            Analysis result
        """
        result = AnalysisResult(target=target)
        
        try:
            # Get content and AST
            content = target.get_content()
            ast_tree = target.get_ast()
            
            # Get applicable rules
            rules = self.rule_registry.get_applicable_rules(context)
            
            # Execute rules
            for rule in rules:
                try:
                    rule_issues = await rule.analyze(target, content, ast_tree, context)
                    result.issues.extend(rule_issues)
                    result.rules_executed.append(rule.rule_id)
                    
                except Exception as e:
                    logger.warning(f"Rule {rule.rule_id} failed: {e}")
                    # Continue with other rules
            
            # Calculate metrics
            result.metrics = await self._calculate_metrics(target, content, ast_tree, context)
            
            result.success = True
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            logger.error(f"Analysis failed: {e}")
        
        return result
    
    async def _calculate_metrics(self, target: AnalysisTarget, content: str, 
                               ast_tree: Any, context: AnalysisContext) -> AnalysisMetrics:
        """Calculate code metrics for the target."""
        # This will be implemented with specific metric calculators
        # For now, return basic metrics
        lines = content.split('\n')
        return AnalysisMetrics(
            lines_of_code=len([line for line in lines if line.strip() and not line.strip().startswith('#')])
        )
    
    async def _load_built_in_rules(self) -> None:
        """Load built-in analysis rules."""
        logger.info("Loading built-in analysis rules...")

        try:
            from .rules.rule_loader import load_built_in_rules, configure_default_rules

            # Load all built-in rules
            loaded_count = load_built_in_rules(self.rule_registry)

            # Configure default rule settings
            configure_default_rules(self.rule_registry, self.config.rule_config)

            logger.info(f"Successfully loaded {loaded_count} built-in analysis rules")

        except Exception as e:
            logger.error(f"Failed to load built-in rules: {e}")
            # Continue without rules - engine can still function

    async def _load_plugin_rules(self) -> None:
        """Load rules from plugins."""
        try:
            rule_plugins = self.plugin_manager.get_rule_plugins()

            if not rule_plugins:
                logger.debug("No rule plugins found")
                return

            plugin_rules_count = 0

            for plugin in rule_plugins:
                if not plugin.is_enabled():
                    continue

                try:
                    rules = plugin.get_rules()

                    for rule_class in rules:
                        # Instantiate and register the rule
                        rule_instance = rule_class()
                        self.rule_registry.register_rule(rule_instance)
                        plugin_rules_count += 1

                    logger.debug(f"Loaded {len(rules)} rules from plugin {plugin.metadata.name}")

                except Exception as e:
                    logger.error(f"Failed to load rules from plugin {plugin.metadata.name}: {e}")

            logger.info(f"Successfully loaded {plugin_rules_count} plugin rules")

        except Exception as e:
            logger.error(f"Failed to load plugin rules: {e}")

    async def _load_framework_rules(self) -> None:
        """Load framework-specific rules."""
        try:
            from .rules.rule_loader import load_framework_rules

            # Try to detect project root from current working directory
            project_root = Path.cwd()

            framework_rules_count = load_framework_rules(self.rule_registry, project_root)

            if framework_rules_count > 0:
                logger.info(f"Successfully loaded {framework_rules_count} framework-specific rules")
            else:
                logger.debug("No framework-specific rules loaded")

        except Exception as e:
            logger.error(f"Failed to load framework rules: {e}")
    
    def _update_context_from_config(self, context: AnalysisContext) -> AnalysisContext:
        """Update analysis context with engine configuration."""
        # Apply configuration to context
        if self.config.enabled_categories:
            context.enabled_categories = self.config.enabled_categories
        if self.config.enabled_rules:
            context.enabled_rules = self.config.enabled_rules
        if self.config.disabled_rules:
            context.disabled_rules = self.config.disabled_rules
        
        context.performance_mode = self.config.performance_mode
        context.cache_enabled = self.config.cache_enabled
        context.auto_fix_enabled = self.config.auto_fix_enabled
        
        return context
    
    def _update_performance_metrics(self, execution_time: float) -> None:
        """Update internal performance metrics."""
        self._analysis_count += 1
        self._total_execution_time += execution_time
        self._last_analysis_time = execution_time
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get current performance statistics."""
        if self._analysis_count == 0:
            return {"analyses": 0, "total_time": 0.0, "average_time": 0.0}
        
        return {
            "analyses": self._analysis_count,
            "total_time": self._total_execution_time,
            "average_time": self._total_execution_time / self._analysis_count,
            "last_analysis_time": self._last_analysis_time
        }
    
    def get_status(self) -> Dict[str, Any]:
        """Get engine status information."""
        return {
            "mode": self.mode.value,
            "initialized": self._initialized,
            "running": self._running,
            "cache_enabled": self.config.cache_enabled,
            "rules_loaded": len(self.rule_registry.rules),
            "performance": self.get_performance_stats()
        }

    async def auto_fix(self, target: AnalysisTarget, context: Optional[AnalysisContext] = None,
                      safe_only: bool = True, interactive: bool = False) -> FixReport:
        """
        Auto-fix issues in the target.

        Args:
            target: Analysis target to fix
            context: Optional analysis context
            safe_only: Only apply safe fixes
            interactive: Use interactive mode with user confirmation

        Returns:
            Fix report with applied operations
        """
        if not self.is_enabled():
            raise RuntimeError("Engine is not running. Call start() first.")

        # First, analyze to get issues
        analysis_result = await self.analyze(target, context)

        if not analysis_result.success or not analysis_result.issues:
            return FixReport(target=target, operations=[])

        # Get content and AST
        content = target.get_content()
        ast_tree = target.get_ast()

        if interactive:
            # Use interactive fix engine
            return self.interactive_fix_engine.fix_interactively(
                analysis_result.issues, content, ast_tree
            )
        else:
            # Use automatic fix engine
            operations = self.auto_fix_engine.fix_issues(
                analysis_result.issues, content, ast_tree, safe_only
            )

            report = FixReport(target=target, operations=operations)

            # Apply fixes if any
            if operations:
                final_content = operations[-1].fixed_content

                # Write back to file if it's a file target
                if target.path and target.path.exists():
                    target.path.write_text(final_content)
                    logger.info(f"Applied {len(operations)} fixes to {target.path}")

                # Update report statistics
                for operation in operations:
                    from .auto_fix import FixResult
                    report.add_result(operation, FixResult.SUCCESS)

            return report

    async def analyze_incrementally(self, project_root: Path,
                                  context: Optional[AnalysisContext] = None) -> Tuple[List[AnalysisResult], Dict[str, Any]]:
        """
        Perform incremental analysis on a project.

        Args:
            project_root: Root directory of the project
            context: Optional analysis context

        Returns:
            Tuple of (results, statistics)
        """
        if not self.is_enabled():
            raise RuntimeError("Engine is not running. Call start() first.")

        if not self.incremental_analyzer:
            raise RuntimeError("Incremental analysis not available - caching must be enabled")

        if context is None:
            context = AnalysisContext.create_default(self.mode)

        context = self._update_context_from_config(context)

        # Update project root for incremental analyzer
        self.incremental_analyzer.project_root = project_root

        return await self.incremental_analyzer.analyze_incrementally(self, context)

    async def analyze_with_memory_management(self, project_root: Path,
                                           context: Optional[AnalysisContext] = None,
                                           use_streaming: bool = True) -> Tuple[List[AnalysisResult], Dict[str, Any]]:
        """
        Analyze project with memory management for large projects.

        Args:
            project_root: Root directory of the project
            context: Optional analysis context
            use_streaming: Use streaming analysis for memory efficiency

        Returns:
            Tuple of (results, performance_stats)
        """
        if not self.is_enabled():
            raise RuntimeError("Engine is not running. Call start() first.")

        if context is None:
            context = AnalysisContext.create_default(self.mode)

        context = self._update_context_from_config(context)

        # Use enhanced parallel analyzer with memory management
        from vibe_check.cli.parallel_processing import EnhancedParallelAnalyzer

        enhanced_analyzer = EnhancedParallelAnalyzer(self)
        return await enhanced_analyzer.analyze_large_project(
            project_root, verbose=False, use_streaming=use_streaming
        )

    def get_incremental_statistics(self) -> Dict[str, Any]:
        """Get incremental analysis statistics."""
        if not self.incremental_analyzer:
            return {"status": "incremental_analysis_disabled"}

        return self.incremental_analyzer.get_analysis_statistics()

    def get_memory_statistics(self) -> Dict[str, Any]:
        """Get memory usage statistics."""
        return self.memory_manager.get_memory_report()

    async def analyze_with_plugins(self, target: AnalysisTarget,
                                 context: Optional[AnalysisContext] = None) -> List[AnalysisResult]:
        """
        Analyze target using both built-in analysis and plugins.

        Args:
            target: Analysis target
            context: Optional analysis context

        Returns:
            List of analysis results from all analyzers
        """
        if not self.is_enabled():
            raise RuntimeError("Engine is not running. Call start() first.")

        if context is None:
            context = AnalysisContext.create_default(self.mode)

        context = self._update_context_from_config(context)

        results = []

        # Run built-in analysis
        builtin_result = await self.analyze(target, context)
        results.append(builtin_result)

        # Run plugin analyzers
        plugin_results = await self.plugin_manager.run_analyzer_plugins(target, context)
        results.extend(plugin_results)

        return results

    def format_with_plugin(self, format_name: str, results: List[AnalysisResult]) -> Optional[str]:
        """Format results using a plugin formatter."""
        return self.plugin_manager.format_with_plugin(format_name, results)

    def get_available_plugin_formats(self) -> List[str]:
        """Get available output formats from plugins."""
        return self.plugin_manager.get_available_formats()

    def get_plugin_statistics(self) -> Dict[str, Any]:
        """Get plugin system statistics."""
        return self.plugin_manager.get_plugin_statistics()

    def enable_plugin(self, name: str) -> bool:
        """Enable a plugin by name."""
        return self.plugin_manager.enable_plugin(name)

    def disable_plugin(self, name: str) -> bool:
        """Disable a plugin by name."""
        return self.plugin_manager.disable_plugin(name)

    def get_plugin_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all plugins."""
        return self.plugin_manager.get_plugin_status()

    async def perform_comprehensive_analysis(
        self,
        project_root: Path,
        context: Optional[AnalysisContext] = None,
        include_plugins: bool = True,
        export_formats: Optional[List[str]] = None
    ) -> Tuple[Any, Dict[str, Any]]:
        """
        Perform comprehensive analysis with meta-analysis and unified reporting.

        Args:
            project_root: Project root directory
            context: Analysis context
            include_plugins: Whether to include plugin analysis
            export_formats: List of formats to export

        Returns:
            Tuple of (unified_report, performance_stats)
        """
        if not self.is_enabled():
            raise RuntimeError("Engine is not running. Call start() first.")

        return await self.integration_manager.perform_comprehensive_analysis(
            self, project_root, context, include_plugins, export_formats
        )

    async def analyze_project_trends(self, project_root: Path) -> Dict[str, Any]:
        """Analyze project trends from historical data."""
        return await self.integration_manager.analyze_project_trends(project_root)

    async def generate_comparison_report(
        self,
        current_results: List[AnalysisResult],
        previous_results: Optional[List[AnalysisResult]] = None,
        project_root: Optional[Path] = None
    ) -> Dict[str, Any]:
        """Generate comparison report between analyses."""
        return await self.integration_manager.generate_comparison_report(
            current_results, previous_results, project_root
        )

    def get_integration_statistics(self) -> Dict[str, Any]:
        """Get integration system statistics."""
        return self.integration_manager.get_integration_statistics()

    async def generate_enterprise_report(
        self,
        analysis_results: List[AnalysisResult],
        meta_analysis: Optional[Any] = None,
        report_configuration: Optional[Any] = None,
        project_name: str = "Unknown Project"
    ) -> Optional[Any]:
        """
        Generate enterprise report if enterprise features are available.

        Args:
            analysis_results: Analysis results to include in report
            meta_analysis: Optional meta-analysis results
            report_configuration: Report configuration
            project_name: Name of the analyzed project

        Returns:
            Enterprise report if available, None otherwise
        """
        if not self.enterprise_reporting:
            logger.warning("Enterprise reporting not available")
            return None

        return await self.enterprise_reporting.generate_report(
            analysis_results, meta_analysis, report_configuration, project_name
        )

    def get_enterprise_statistics(self) -> Dict[str, Any]:
        """Get enterprise reporting statistics."""
        if not self.enterprise_reporting:
            return {"enterprise_available": False}

        stats = self.enterprise_reporting.get_report_statistics()
        stats["enterprise_available"] = True
        return stats

    def get_collaboration_statistics(self) -> Dict[str, Any]:
        """Get collaboration system statistics."""
        if not self.team_manager:
            return {"collaboration_available": False}

        stats = {
            "collaboration_available": True,
            "team_stats": self.team_manager.get_team_statistics(),
            "config_stats": self.shared_config.get_configuration_statistics() if self.shared_config else {},
            "analysis_stats": self.collaborative_analysis.get_collaboration_statistics() if self.collaborative_analysis else {}
        }

        return stats

    async def create_team(
        self,
        name: str,
        description: str,
        created_by: str,
        initial_members: Optional[List[Dict[str, Any]]] = None
    ) -> Optional[Any]:
        """
        Create a new team if collaboration features are available.

        Args:
            name: Team name
            description: Team description
            created_by: User ID of team creator
            initial_members: Optional list of initial members

        Returns:
            Created team if available, None otherwise
        """
        if not self.team_manager:
            logger.warning("Team collaboration not available")
            return None

        return await self.team_manager.create_team(
            name, description, created_by, initial_members
        )

    async def create_analysis_request(
        self,
        team_id: str,
        project_name: str,
        project_path: str,
        requested_by: str,
        description: str,
        **kwargs
    ) -> Optional[Any]:
        """
        Create a collaborative analysis request.

        Args:
            team_id: Team ID
            project_name: Project name
            project_path: Path to project
            requested_by: User ID who requested the analysis
            description: Analysis description
            **kwargs: Additional arguments for analysis request

        Returns:
            Created analysis request if available, None otherwise
        """
        if not self.collaborative_analysis:
            logger.warning("Collaborative analysis not available")
            return None

        return await self.collaborative_analysis.create_analysis_request(
            team_id, project_name, project_path, requested_by, description, **kwargs
        )

    async def create_cicd_pipeline(
        self,
        name: str,
        platform: str,
        trigger_events: Optional[List[str]] = None,
        analysis_config: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Optional[Any]:
        """
        Create a CI/CD pipeline configuration.

        Args:
            name: Pipeline name
            platform: CI/CD platform (github_actions, gitlab_ci, jenkins, azure_devops)
            trigger_events: Events that trigger the pipeline
            analysis_config: Analysis configuration
            **kwargs: Additional configuration options

        Returns:
            Created pipeline configuration if available, None otherwise
        """
        if not self.cicd_manager:
            logger.warning("CI/CD integration not available")
            return None

        try:
            from vibe_check.enterprise.cicd.models import CICDPlatform
            platform_enum = CICDPlatform(platform)

            return await self.cicd_manager.create_pipeline_configuration(
                name, platform_enum, trigger_events, analysis_config, **kwargs
            )
        except Exception as e:
            logger.error(f"Failed to create CI/CD pipeline: {e}")
            return None

    async def generate_pipeline_file(
        self,
        config_id: str,
        output_path: Optional[str] = None
    ) -> Optional[str]:
        """
        Generate pipeline file for a configuration.

        Args:
            config_id: Pipeline configuration ID
            output_path: Optional output path

        Returns:
            Generated pipeline content if available, None otherwise
        """
        if not self.cicd_manager:
            logger.warning("CI/CD integration not available")
            return None

        from pathlib import Path
        path = Path(output_path) if output_path else None

        return await self.cicd_manager.generate_pipeline_file(config_id, path)

    def get_cicd_statistics(self) -> Dict[str, Any]:
        """Get CI/CD integration statistics."""
        if not self.cicd_manager:
            return {"cicd_available": False}

        stats = self.cicd_manager.get_integration_statistics()
        stats["cicd_available"] = True
        return stats

    async def evaluate_quality_gates(
        self,
        analysis_results: List[Any],
        gate_tags: Optional[List[str]] = None
    ) -> Optional[List[Any]]:
        """
        Evaluate quality gates against analysis results.

        Args:
            analysis_results: Analysis results to evaluate
            gate_tags: Optional tags to filter gates

        Returns:
            Quality gate results if available, None otherwise
        """
        if not self.quality_gate_manager:
            logger.warning("Quality gate manager not available")
            return None

        return await self.quality_gate_manager.evaluate_all_gates(
            analysis_results, tags=gate_tags
        )

    def record_analysis_metrics(self, analysis_results: List[Any]) -> None:
        """
        Record analysis metrics for monitoring.

        Args:
            analysis_results: Analysis results to record metrics from
        """
        if self.monitoring_system:
            self.monitoring_system.record_analysis_metrics(analysis_results)

    def create_alert(
        self,
        title: str,
        description: str,
        severity: str = "medium",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[Any]:
        """
        Create a monitoring alert.

        Args:
            title: Alert title
            description: Alert description
            severity: Alert severity (low, medium, high, critical)
            metadata: Additional metadata

        Returns:
            Created alert if available, None otherwise
        """
        if not self.monitoring_system:
            logger.warning("Monitoring system not available")
            return None

        try:
            from vibe_check.enterprise.monitoring.models import AlertSeverity
            severity_enum = AlertSeverity(severity)

            return self.monitoring_system.create_alert(
                title, description, severity_enum, metadata
            )
        except Exception as e:
            logger.error(f"Failed to create alert: {e}")
            return None

    def get_monitoring_statistics(self) -> Dict[str, Any]:
        """Get monitoring system statistics."""
        if not self.monitoring_system:
            return {"monitoring_available": False}

        stats = self.monitoring_system.get_monitoring_statistics()
        stats["monitoring_available"] = True

        # Add quality gate statistics
        if self.quality_gate_manager:
            gate_stats = self.quality_gate_manager.get_gate_statistics()
            stats["quality_gates"] = gate_stats

        # Add alerting statistics
        if self.alerting_system:
            alert_stats = self.alerting_system.get_alert_statistics()
            stats["alerting"] = alert_stats

        # Add dashboard statistics
        if self.dashboard_manager:
            dashboard_stats = self.dashboard_manager.get_dashboard_statistics()
            stats["dashboards"] = dashboard_stats

        return stats

    async def start_api_servers(self) -> None:
        """Start all API servers."""
        if self.api_manager:
            await self.api_manager.start_all_servers()

    async def stop_api_servers(self) -> None:
        """Stop all API servers."""
        if self.api_manager:
            await self.api_manager.stop_all_servers()

    def get_api_endpoints(self) -> Dict[str, Any]:
        """Get all available API endpoints."""
        if not self.api_manager:
            return {"api_available": False}

        endpoints = self.api_manager.get_api_endpoints()
        endpoints["api_available"] = True
        return endpoints

    def get_api_statistics(self) -> Dict[str, Any]:
        """Get API system statistics."""
        if not self.api_manager:
            return {"api_available": False}

        stats = self.api_manager.get_unified_statistics()
        stats["api_available"] = True
        return stats

    def get_api_documentation(self) -> Dict[str, Any]:
        """Get comprehensive API documentation."""
        if not self.api_manager:
            return {"api_available": False}

        docs = self.api_manager.get_api_documentation()
        docs["api_available"] = True
        return docs

    async def start_web_dashboard(self) -> None:
        """Start web dashboard server."""
        if self.web_dashboard:
            await self.web_dashboard.start()

    async def stop_web_dashboard(self) -> None:
        """Stop web dashboard server."""
        if self.web_dashboard:
            await self.web_dashboard.stop()

    async def create_dashboard_session(self, user_id: str, username: str, role: str) -> Optional[Any]:
        """
        Create a dashboard user session.

        Args:
            user_id: User ID
            username: Username
            role: User role (admin, lead, developer, viewer)

        Returns:
            Created user session if available, None otherwise
        """
        if not self.web_dashboard:
            logger.warning("Web dashboard not available")
            return None

        return await self.web_dashboard.create_user_session(user_id, username, role)

    async def get_dashboard_data(self, dashboard_id: str, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Get dashboard data for rendering.

        Args:
            dashboard_id: Dashboard ID
            session_id: User session ID

        Returns:
            Dashboard data if available, None otherwise
        """
        if not self.web_dashboard:
            logger.warning("Web dashboard not available")
            return None

        try:
            return await self.web_dashboard.get_dashboard_data(dashboard_id, session_id)
        except Exception as e:
            logger.error(f"Failed to get dashboard data: {e}")
            return None

    def get_web_dashboard_statistics(self) -> Dict[str, Any]:
        """Get web dashboard statistics."""
        if not self.web_dashboard:
            return {"web_dashboard_available": False}

        stats = self.web_dashboard.get_dashboard_statistics()
        stats["web_dashboard_available"] = True
        return stats

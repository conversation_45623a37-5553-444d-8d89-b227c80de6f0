"""
Vibe Check Project Analysis Core Module
==============================

This is the core module for Vibe Check (Project Analysis Tool). It provides the
foundational components for analyzing Python projects, including:

1. Analysis Engine - Direct analysis without complex actor orchestration
2. Models - Data models for analysis results
3. Utilities - Common utilities for configuration, file handling, etc.

The simplified architecture focuses on direct analysis execution with clear
separation between the domain (models), application logic (analysis), and
infrastructure (utilities).
"""

# Version information
from .version import __version__
__author__ = "Vibe Check Team"
__license__ = "MIT"

# Core imports
from .models import (
    FileMetrics,
    ProjectMetrics,
    DirectoryMetrics,
)

# Expose the main entry points
from .simple_analyzer import simple_analyze_project as analyze_project
from .utils import logger

__all__ = [
    # Version info
    "__version__",
    "__author__",
    "__license__",

    # Core models
    "FileMetrics",
    "ProjectMetrics",
    "DirectoryMetrics",

    # Main entry point
    "analyze_project",

    # Utilities
    "logger",
]

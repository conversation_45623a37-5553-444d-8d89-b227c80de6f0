"""
Progress Tracking Module
=====================

This module provides classes for tracking and reporting progress.
"""

import time
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union

from .logging import get_logger

# Set up logger
logger = get_logger("progress")


class ProgressState(Enum):
    """States for a progress tracker."""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"


class ProgressTracker:
    """Base class for progress trackers."""

    def __init__(self) -> None:
        """Initialize the progress tracker."""
        self.total = 0
        self.completed = 0
        self.current_task = ""

    def start(self, total: int, description: str) -> None:
        """
        Start tracking progress.

        Args:
            total: Total number of steps
            description: Description of the task
        """
        self.total = total
        self.completed = 0
        self.current_task = description

    def update(self, completed: int, description: Optional[str] = None) -> None:
        """
        Update the progress.

        Args:
            completed: Number of completed steps
            description: Optional updated description
        """
        self.completed = completed
        if description:
            self.current_task = description

    def increment(self, description: Optional[str] = None) -> None:
        """
        Increment the progress by 1.

        Args:
            description: Optional updated description
        """
        self.completed += 1
        if description:
            self.current_task = description

    def complete(self) -> None:
        """Mark the progress as complete."""
        self.completed = self.total
        self.current_task = "Completed"

    @abstractmethod
    def start_global(self, description: str, total_steps: int) -> None:
        """
        Start tracking global progress.

        Args:
            description: Description of the overall task
            total_steps: Total number of steps
        """
        pass

    @abstractmethod
    def increment_global(self, increment: int = 1) -> None:
        """
        Increment the global progress.

        Args:
            increment: Amount to increment by
        """
        pass

    @abstractmethod
    def start_phase(self, description: str, total_steps: Optional[int] = None) -> None:
        """
        Start a new phase of progress.

        Args:
            description: Description of the phase
            total_steps: Total number of steps in this phase
        """
        pass

    @abstractmethod
    def update_phase(self, current_step: int, description: Optional[str] = None) -> None:
        """
        Update the current phase progress.

        Args:
            current_step: Current step number
            description: Optional updated description
        """
        pass

    @abstractmethod
    def increment_phase(self, increment: int = 1, description: Optional[str] = None) -> None:
        """
        Increment the current phase progress.

        Args:
            increment: Amount to increment by
            description: Optional updated description
        """
        pass

    @abstractmethod
    def complete_phase(self, description: Optional[str] = None) -> None:
        """
        Mark the current phase as complete.

        Args:
            description: Optional completion description
        """
        pass

    @abstractmethod
    def fail_phase(self, error_message: str) -> None:
        """
        Mark the current phase as failed.

        Args:
            error_message: Error message
        """
        pass

    @abstractmethod
    def complete_global(self, description: Optional[str] = None) -> None:
        """
        Mark the global progress as complete.

        Args:
            description: Optional completion description
        """
        pass

    @abstractmethod
    def fail_global(self, error_message: str) -> None:
        """
        Mark the global progress as failed.

        Args:
            error_message: Error message
        """
        pass


class LoggingProgressTracker(ProgressTracker):
    """Progress tracker that logs progress to the logger."""

    def __init__(self, log_level: int = 20):
        """
        Initialize the progress tracker.

        Args:
            log_level: Logging level to use
        """
        self.log_level = log_level
        self.global_description = ""
        self.global_total = 0
        self.global_current = 0
        self.global_state = ProgressState.NOT_STARTED
        self.phase_description = ""
        self.phase_total = 0
        self.phase_current = 0
        self.phase_state = ProgressState.NOT_STARTED
        self.start_time = 0.0
        self.phase_start_time = 0.0

    def start_global(self, description: str, total_steps: int) -> None:
        """
        Start tracking global progress.

        Args:
            description: Description of the overall task
            total_steps: Total number of steps
        """
        self.global_description = description
        self.global_total = total_steps
        self.global_current = 0
        self.global_state = ProgressState.IN_PROGRESS
        self.start_time = time.time()

        logger.log(self.log_level, f"Starting: {description} (0/{total_steps})")  # type: ignore[attr-defined]

    def increment_global(self, increment: int = 1) -> None:
        """
        Increment the global progress.

        Args:
            increment: Amount to increment by
        """
        if self.global_state != ProgressState.IN_PROGRESS:
            return

        self.global_current += increment

        # Log progress at 25%, 50%, 75%, and 100%
        if self.global_total > 0:
            progress = self.global_current / self.global_total
            if progress in (0.25, 0.5, 0.75, 1.0) or self.global_current == self.global_total:
                elapsed = time.time() - self.start_time
                logger.log(  # type: ignore[attr-defined]
                    self.log_level,
                    f"Progress: {self.global_description} "
                    f"({self.global_current}/{self.global_total}, {progress:.0%}, {elapsed:.1f}s)"
                )

    def start_phase(self, description: str, total_steps: Optional[int] = None) -> None:
        """
        Start a new phase of progress.

        Args:
            description: Description of the phase
            total_steps: Total number of steps in this phase
        """
        self.phase_description = description
        self.phase_total = total_steps or 0
        self.phase_current = 0
        self.phase_state = ProgressState.IN_PROGRESS
        self.phase_start_time = time.time()

        if total_steps:
            logger.log(self.log_level, f"Phase: {description} (0/{total_steps})")  # type: ignore[attr-defined]
        else:
            logger.log(self.log_level, f"Phase: {description}")  # type: ignore[attr-defined]

    def update_phase(self, current_step: int, description: Optional[str] = None) -> None:
        """
        Update the current phase progress.

        Args:
            current_step: Current step number
            description: Optional updated description
        """
        if self.phase_state != ProgressState.IN_PROGRESS:
            return

        self.phase_current = current_step
        if description:
            self.phase_description = description

        # Log progress at 25%, 50%, 75%, and 100%
        if self.phase_total > 0:
            progress = self.phase_current / self.phase_total
            if progress in (0.25, 0.5, 0.75, 1.0) or self.phase_current == self.phase_total:
                elapsed = time.time() - self.phase_start_time
                logger.log(  # type: ignore[attr-defined]
                    self.log_level,
                    f"Phase progress: {self.phase_description} "
                    f"({self.phase_current}/{self.phase_total}, {progress:.0%}, {elapsed:.1f}s)"
                )

    def increment_phase(self, increment: int = 1, description: Optional[str] = None) -> None:
        """
        Increment the current phase progress.

        Args:
            increment: Amount to increment by
            description: Optional updated description
        """
        if self.phase_state != ProgressState.IN_PROGRESS:
            return

        self.phase_current += increment
        if description:
            self.phase_description = description

        # Log progress at 25%, 50%, 75%, and 100%
        if self.phase_total > 0:
            progress = self.phase_current / self.phase_total
            if progress in (0.25, 0.5, 0.75, 1.0) or self.phase_current == self.phase_total:
                elapsed = time.time() - self.phase_start_time
                logger.log(  # type: ignore[attr-defined]
                    self.log_level,
                    f"Phase progress: {self.phase_description} "
                    f"({self.phase_current}/{self.phase_total}, {progress:.0%}, {elapsed:.1f}s)"
                )

    def complete_phase(self, description: Optional[str] = None) -> None:
        """
        Mark the current phase as complete.

        Args:
            description: Optional completion description
        """
        if self.phase_state != ProgressState.IN_PROGRESS:
            return

        self.phase_state = ProgressState.COMPLETED
        elapsed = time.time() - self.phase_start_time

        if description:
            logger.log(self.log_level, f"Phase complete: {description} ({elapsed:.1f}s)")  # type: ignore[attr-defined]
        else:
            logger.log(self.log_level, f"Phase complete: {self.phase_description} ({elapsed:.1f}s)")  # type: ignore[attr-defined]

    def fail_phase(self, error_message: str) -> None:
        """
        Mark the current phase as failed.

        Args:
            error_message: Error message
        """
        if self.phase_state != ProgressState.IN_PROGRESS:
            return

        self.phase_state = ProgressState.FAILED
        elapsed = time.time() - self.phase_start_time

        logger.log(  # type: ignore[attr-defined]
            self.log_level,
            f"Phase failed: {self.phase_description} - {error_message} ({elapsed:.1f}s)"
        )

    def complete_global(self, description: Optional[str] = None) -> None:
        """
        Mark the global progress as complete.

        Args:
            description: Optional completion description
        """
        if self.global_state != ProgressState.IN_PROGRESS:
            return

        self.global_state = ProgressState.COMPLETED
        elapsed = time.time() - self.start_time

        if description:
            logger.log(self.log_level, f"Complete: {description} ({elapsed:.1f}s)")  # type: ignore[attr-defined]
        else:
            logger.log(self.log_level, f"Complete: {self.global_description} ({elapsed:.1f}s)")  # type: ignore[attr-defined]

    def fail_global(self, error_message: str) -> None:
        """
        Mark the global progress as failed.

        Args:
            error_message: Error message
        """
        if self.global_state != ProgressState.IN_PROGRESS:
            return

        self.global_state = ProgressState.FAILED
        elapsed = time.time() - self.start_time

        logger.log(  # type: ignore[attr-defined]
            self.log_level,
            f"Failed: {self.global_description} - {error_message} ({elapsed:.1f}s)"
        )


class CallbackProgressTracker(ProgressTracker):
    """Progress tracker that calls a callback function with progress updates."""

    def __init__(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Initialize the progress tracker.

        Args:
            callback: Callback function to call with progress updates
        """
        self.callback = callback
        self.global_description = ""
        self.global_total = 0
        self.global_current = 0
        self.global_state = ProgressState.NOT_STARTED
        self.phase_description = ""
        self.phase_total = 0
        self.phase_current = 0
        self.phase_state = ProgressState.NOT_STARTED
        self.start_time = 0.0
        self.phase_start_time = 0.0
        self.phases: List[Dict[str, Any]] = []

    def _send_update(self) -> None:
        """Send a progress update to the callback function."""
        self.callback({
            "global": {
                "description": self.global_description,
                "total": self.global_total,
                "current": self.global_current,
                "state": self.global_state.value,
                "elapsed": time.time() - self.start_time if self.start_time else 0
            },
            "phase": {
                "description": self.phase_description,
                "total": self.phase_total,
                "current": self.phase_current,
                "state": self.phase_state.value,
                "elapsed": time.time() - self.phase_start_time if self.phase_start_time else 0
            },
            "phases": self.phases
        })

    def start_global(self, description: str, total_steps: int) -> None:
        """
        Start tracking global progress.

        Args:
            description: Description of the overall task
            total_steps: Total number of steps
        """
        self.global_description = description
        self.global_total = total_steps
        self.global_current = 0
        self.global_state = ProgressState.IN_PROGRESS
        self.start_time = time.time()
        self.phases = []

        self._send_update()

    def increment_global(self, increment: int = 1) -> None:
        """
        Increment the global progress.

        Args:
            increment: Amount to increment by
        """
        if self.global_state != ProgressState.IN_PROGRESS:
            return

        self.global_current += increment

        self._send_update()

    def start_phase(self, description: str, total_steps: Optional[int] = None) -> None:
        """
        Start a new phase of progress.

        Args:
            description: Description of the phase
            total_steps: Total number of steps in this phase
        """
        # If there's an active phase, complete it first
        if self.phase_state == ProgressState.IN_PROGRESS:
            self.complete_phase()

        self.phase_description = description
        self.phase_total = total_steps or 0
        self.phase_current = 0
        self.phase_state = ProgressState.IN_PROGRESS
        self.phase_start_time = time.time()

        # Add the new phase to the phases list
        self.phases.append({
            "description": description,
            "total": total_steps or 0,
            "current": 0,
            "state": ProgressState.IN_PROGRESS.value,
            "start_time": self.phase_start_time
        })

        self._send_update()

    def update_phase(self, current_step: int, description: Optional[str] = None) -> None:
        """
        Update the current phase progress.

        Args:
            current_step: Current step number
            description: Optional updated description
        """
        if self.phase_state != ProgressState.IN_PROGRESS:
            return

        self.phase_current = current_step
        if description:
            self.phase_description = description

            # Update the description in the phases list
            if self.phases:
                self.phases[-1]["description"] = description

        # Update the current step in the phases list
        if self.phases:
            self.phases[-1]["current"] = current_step

        self._send_update()

    def increment_phase(self, increment: int = 1, description: Optional[str] = None) -> None:
        """
        Increment the current phase progress.

        Args:
            increment: Amount to increment by
            description: Optional updated description
        """
        if self.phase_state != ProgressState.IN_PROGRESS:
            return

        self.phase_current += increment
        if description:
            self.phase_description = description

            # Update the description in the phases list
            if self.phases:
                self.phases[-1]["description"] = description

        # Update the current step in the phases list
        if self.phases:
            self.phases[-1]["current"] = self.phase_current

        self._send_update()

    def complete_phase(self, description: Optional[str] = None) -> None:
        """
        Mark the current phase as complete.

        Args:
            description: Optional completion description
        """
        if self.phase_state != ProgressState.IN_PROGRESS:
            return

        self.phase_state = ProgressState.COMPLETED
        if description:
            self.phase_description = description

        # Update the state in the phases list
        if self.phases:
            self.phases[-1]["state"] = ProgressState.COMPLETED.value
            self.phases[-1]["end_time"] = time.time()
            if description:
                self.phases[-1]["description"] = description

        self._send_update()

    def fail_phase(self, error_message: str) -> None:
        """
        Mark the current phase as failed.

        Args:
            error_message: Error message
        """
        if self.phase_state != ProgressState.IN_PROGRESS:
            return

        self.phase_state = ProgressState.FAILED

        # Update the state in the phases list
        if self.phases:
            self.phases[-1]["state"] = ProgressState.FAILED.value
            self.phases[-1]["end_time"] = time.time()
            self.phases[-1]["error"] = error_message

        self._send_update()

    def complete_global(self, description: Optional[str] = None) -> None:
        """
        Mark the global progress as complete.

        Args:
            description: Optional completion description
        """
        # If there's an active phase, complete it first
        if self.phase_state == ProgressState.IN_PROGRESS:
            self.complete_phase()

        if self.global_state != ProgressState.IN_PROGRESS:
            return

        self.global_state = ProgressState.COMPLETED
        if description:
            self.global_description = description

        self._send_update()

    def fail_global(self, error_message: str) -> None:
        """
        Mark the global progress as failed.

        Args:
            error_message: Error message
        """
        if self.global_state != ProgressState.IN_PROGRESS:
            return

        self.global_state = ProgressState.FAILED

        self._send_update()


class NullProgressTracker(ProgressTracker):
    """Progress tracker that does nothing."""

    def start_global(self, description: str, total_steps: int) -> None:
        pass

    def increment_global(self, increment: int = 1) -> None:
        pass

    def start_phase(self, description: str, total_steps: Optional[int] = None) -> None:
        pass

    def update_phase(self, current_step: int, description: Optional[str] = None) -> None:
        pass

    def increment_phase(self, increment: int = 1, description: Optional[str] = None) -> None:
        pass

    def complete_phase(self, description: Optional[str] = None) -> None:
        pass

    def fail_phase(self, error_message: str) -> None:
        pass

    def complete_global(self, description: Optional[str] = None) -> None:
        pass

    def fail_global(self, error_message: str) -> None:
        pass


class SimpleProgressTracker(ProgressTracker):
    """Simple progress tracker that prints progress to the console."""

    def __init__(self):
        super().__init__()
        import logging
        self.logger = logging.getLogger(__name__)

    def start(self, total: int, description: str) -> None:
        """
        Start tracking progress.

        Args:
            total: Total number of steps
            description: Description of the task
        """
        super().start(total, description)
        self.logger.info(f"Starting: {description} (0/{total})")

    def update(self, completed: int, description: Optional[str] = None) -> None:
        """
        Update the progress.

        Args:
            completed: Number of completed steps
            description: Optional updated description
        """
        super().update(completed, description)
        self.logger.info(f"Progress: {self.current_task} ({self.completed}/{self.total})")

    def increment(self, description: Optional[str] = None) -> None:
        """
        Increment the progress by 1.

        Args:
            description: Optional updated description
        """
        super().increment(description)
        self.logger.info(f"Progress: {self.current_task} ({self.completed}/{self.total})")

    def complete(self) -> None:
        """Mark the progress as complete."""
        super().complete()
        self.logger.info(f"Complete: {self.current_task}")

    def start_global(self, description: str, total_steps: int) -> None:
        """
        Start tracking global progress.

        Args:
            description: Description of the overall task
            total_steps: Total number of steps
        """
        self.start(total_steps, description)

    def increment_global(self, increment: int = 1) -> None:
        """
        Increment the global progress.

        Args:
            increment: Amount to increment by
        """
        self.increment()

    def start_phase(self, description: str, total_steps: Optional[int] = None) -> None:
        """
        Start a new phase of progress.

        Args:
            description: Description of the phase
            total_steps: Total number of steps in this phase
        """
        if total_steps:
            self.logger.info(f"Phase: {description} (0/{total_steps})")
        else:
            self.logger.info(f"Phase: {description}")

    def update_phase(self, current_step: int, description: Optional[str] = None) -> None:
        """
        Update the current phase progress.

        Args:
            current_step: Current step number
            description: Optional updated description
        """
        if description:
            self.logger.info(f"Phase progress: {description} ({current_step})")
        else:
            self.logger.info(f"Phase progress: ({current_step})")

    def increment_phase(self, increment: int = 1, description: Optional[str] = None) -> None:
        """
        Increment the current phase progress.

        Args:
            increment: Amount to increment by
            description: Optional updated description
        """
        if description:
            self.logger.info(f"Phase progress: {description} (+{increment})")
        else:
            self.logger.info(f"Phase progress: (+{increment})")

    def complete_phase(self, description: Optional[str] = None) -> None:
        """
        Mark the current phase as complete.

        Args:
            description: Optional completion description
        """
        if description:
            self.logger.info(f"Phase complete: {description}")
        else:
            self.logger.info("Phase complete")

    def fail_phase(self, error_message: str) -> None:
        """
        Mark the current phase as failed.

        Args:
            error_message: Error message
        """
        self.logger.error(f"Phase failed: {error_message}")

    def complete_global(self, description: Optional[str] = None) -> None:
        """
        Mark the global progress as complete.

        Args:
            description: Optional completion description
        """
        self.complete()

    def fail_global(self, error_message: str) -> None:
        """
        Mark the global progress as failed.

        Args:
            error_message: Error message
        """
        self.logger.error(f"Failed: {error_message}")


class RichProgressTracker(ProgressTracker):
    """Progress tracker that uses Rich for fancy progress display."""

    def __init__(self) -> None:
        """Initialize the progress tracker."""
        super().__init__()

        try:
            # Import Progress from rich.progress
            from rich.progress import Progress, TaskID
            self.progress = Progress()
            self.task_id: Optional[TaskID] = None
        except ImportError:
            raise ImportError("Rich is not installed. Install it with 'pip install rich'.")

    def start(self, total: int, description: str) -> None:
        """
        Start tracking progress.

        Args:
            total: Total number of steps
            description: Description of the task
        """
        super().start(total, description)
        self.progress.start()
        self.task_id = self.progress.add_task(description, total=total)

    def update(self, completed: int, description: Optional[str] = None) -> None:
        """
        Update the progress.

        Args:
            completed: Number of completed steps
            description: Optional updated description
        """
        super().update(completed, description)
        if self.task_id is not None:
            if description:
                self.progress.update(self.task_id, completed=completed, description=description)
            else:
                self.progress.update(self.task_id, completed=completed)

    def increment(self, description: Optional[str] = None) -> None:
        """
        Increment the progress by 1.

        Args:
            description: Optional updated description
        """
        super().increment(description)
        if self.task_id is not None:
            if description:
                self.progress.update(self.task_id, advance=1, description=description)
            else:
                self.progress.update(self.task_id, advance=1)

    def complete(self) -> None:
        """Mark the progress as complete."""
        super().complete()
        if self.task_id is not None:
            self.progress.update(self.task_id, completed=self.total, description="Completed")
            self.progress.stop()

    def start_global(self, description: str, total_steps: int) -> None:
        """
        Start tracking global progress.

        Args:
            description: Description of the overall task
            total_steps: Total number of steps
        """
        self.start(total_steps, description)

    def increment_global(self, increment: int = 1) -> None:
        """
        Increment the global progress.

        Args:
            increment: Amount to increment by
        """
        self.increment()

    def start_phase(self, description: str, total_steps: Optional[int] = None) -> None:
        """
        Start a new phase of progress.

        Args:
            description: Description of the phase
            total_steps: Total number of steps in this phase
        """
        # Rich doesn't have a concept of phases, so we just update the description
        if self.task_id is not None:
            self.progress.update(self.task_id, description=description)

    def update_phase(self, current_step: int, description: Optional[str] = None) -> None:
        """
        Update the current phase progress.

        Args:
            current_step: Current step number
            description: Optional updated description
        """
        # Rich doesn't have a concept of phases, so we just update the description
        if self.task_id is not None:
            if description:
                self.progress.update(self.task_id, description=description)

    def increment_phase(self, increment: int = 1, description: Optional[str] = None) -> None:
        """
        Increment the current phase progress.

        Args:
            increment: Amount to increment by
            description: Optional updated description
        """
        # Rich doesn't have a concept of phases, so we just update the description
        if self.task_id is not None:
            if description:
                self.progress.update(self.task_id, description=description, advance=increment)
            else:
                self.progress.update(self.task_id, advance=increment)

    def complete_phase(self, description: Optional[str] = None) -> None:
        """
        Mark the current phase as complete.

        Args:
            description: Optional completion description
        """
        # Rich doesn't have a concept of phases, so we just update the description
        if self.task_id is not None and description:
            self.progress.update(self.task_id, description=description)

    def fail_phase(self, error_message: str) -> None:
        """
        Mark the current phase as failed.

        Args:
            error_message: Error message
        """
        # Rich doesn't have a concept of phases, so we just update the description
        if self.task_id is not None:
            self.progress.update(self.task_id, description=f"Failed: {error_message}")

    def complete_global(self, description: Optional[str] = None) -> None:
        """
        Mark the global progress as complete.

        Args:
            description: Optional completion description
        """
        self.complete()

    def fail_global(self, error_message: str) -> None:
        """
        Mark the global progress as failed.

        Args:
            error_message: Error message
        """
        if self.task_id is not None:
            self.progress.update(self.task_id, description=f"Failed: {error_message}")
            self.progress.stop()


def get_progress_tracker(show_progress: bool = True, rich_progress: bool = False) -> ProgressTracker:
    """
    Get a progress tracker based on the specified options.

    Args:
        show_progress: Whether to show progress
        rich_progress: Whether to use Rich for progress display

    Returns:
        Progress tracker instance
    """
    if not show_progress:
        return ProgressTracker()  # type: ignore[abstract]

    if rich_progress:
        try:
            return RichProgressTracker()
        except ImportError:
            # Fall back to simple progress tracker if Rich is not installed
            return SimpleProgressTracker()

    return SimpleProgressTracker()


def create_progress_tracker(tracker_type: str, **kwargs: Any) -> ProgressTracker:
    """
    Create a progress tracker of the specified type.

    Args:
        tracker_type: Type of progress tracker to create
        **kwargs: Additional arguments to pass to the progress tracker constructor

    Returns:
        Progress tracker instance

    Raises:
        ValueError: If the tracker type is not recognized
    """
    if tracker_type == "logging":
        return LoggingProgressTracker(**kwargs)
    elif tracker_type == "callback":
        if "callback" not in kwargs:
            raise ValueError("callback argument is required for callback progress tracker")
        return CallbackProgressTracker(kwargs["callback"])
    elif tracker_type == "null":
        return NullProgressTracker()
    elif tracker_type == "simple":
        return SimpleProgressTracker()
    elif tracker_type == "rich":
        return RichProgressTracker()
    else:
        raise ValueError(f"Unknown progress tracker type: {tracker_type}")

"""
Semantic Output Formatter

This module provides rich, user-friendly formatting for semantic analysis results,
including project meritocracy insights and actionable recommendations.
"""

import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import asdict

from .python_semantic_analyzer import SemanticAnalysisResult
from .project_meritocracy_analyzer import ProjectMeritocracyResult
from .type_analyzer import TypeAnalysisResult


class SemanticOutputFormatter:
    """
    Formats semantic analysis results into user-friendly reports.
    """
    
    def __init__(self):
        self.insights_generated = 0
    
    def format_comprehensive_report(
        self,
        semantic_results: Dict[str, SemanticAnalysisResult],
        meritocracy_result: ProjectMeritocracyResult,
        type_results: Dict[str, TypeAnalysisResult]
    ) -> Dict[str, Any]:
        """
        Create a comprehensive semantic analysis report.
        
        Args:
            semantic_results: Semantic analysis results per file
            meritocracy_result: Project meritocracy analysis
            type_results: Type analysis results per file
            
        Returns:
            Comprehensive report dictionary
        """
        report = {
            "executive_summary": self._create_executive_summary(meritocracy_result),
            "project_meritocracy": self._format_meritocracy_analysis(meritocracy_result),
            "semantic_insights": self._format_semantic_insights(semantic_results),
            "type_analysis": self._format_type_analysis(type_results),
            "actionable_recommendations": self._generate_recommendations(
                semantic_results, meritocracy_result, type_results
            ),
            "quality_metrics": self._calculate_quality_metrics(
                semantic_results, meritocracy_result, type_results
            ),
            "architectural_insights": self._format_architectural_insights(meritocracy_result),
            "success_factors": self._analyze_success_factors(meritocracy_result),
            "improvement_roadmap": self._create_improvement_roadmap(meritocracy_result)
        }
        
        return report
    
    def _create_executive_summary(self, meritocracy_result: ProjectMeritocracyResult) -> Dict[str, Any]:
        """Create an executive summary of the analysis."""
        score = meritocracy_result.overall_score
        
        if score >= 8:
            grade = "A"
            summary = "Excellent project with strong architectural foundation and quality practices"
        elif score >= 6:
            grade = "B"
            summary = "Good project with solid structure and room for optimization"
        elif score >= 4:
            grade = "C"
            summary = "Average project that would benefit from architectural improvements"
        else:
            grade = "D"
            summary = "Project needs significant refactoring and quality improvements"
        
        return {
            "overall_grade": grade,
            "score": round(score, 1),
            "summary": summary,
            "key_strengths": meritocracy_result.strengths[:3],
            "critical_issues": meritocracy_result.weaknesses[:3],
            "assessment": meritocracy_result.meritocracy_insights.get("assessment", "")
        }
    
    def _format_meritocracy_analysis(self, result: ProjectMeritocracyResult) -> Dict[str, Any]:
        """Format project meritocracy analysis."""
        return {
            "why_it_works": {
                "architectural_patterns": [
                    {
                        "pattern": pattern.pattern_name,
                        "confidence": round(pattern.confidence * 100, 1),
                        "impact": pattern.quality_impact,
                        "description": pattern.description,
                        "evidence": pattern.evidence
                    }
                    for pattern in result.architectural_patterns
                ],
                "success_factors": result.success_factors,
                "strengths": result.strengths
            },
            "potential_failure_points": {
                "risk_factors": result.risk_factors,
                "weaknesses": result.weaknesses,
                "critical_areas": [
                    indicator.indicator_name
                    for indicator in result.quality_indicators
                    if indicator.score < 4
                ]
            },
            "quality_indicators": [
                {
                    "name": indicator.indicator_name,
                    "score": round(indicator.score, 1),
                    "grade": self._score_to_grade(indicator.score),
                    "evidence": indicator.evidence,
                    "impact_areas": indicator.impact_areas,
                    "recommendations": indicator.recommendations
                }
                for indicator in result.quality_indicators
            ],
            "insights": result.meritocracy_insights
        }
    
    def _format_semantic_insights(self, results: Dict[str, SemanticAnalysisResult]) -> Dict[str, Any]:
        """Format semantic analysis insights."""
        total_issues = sum(len(result.issues) for result in results.values())
        issue_categories = {}
        
        for result in results.values():
            for issue in result.issues:
                category = issue.rule_id
                if category not in issue_categories:
                    issue_categories[category] = {
                        "count": 0,
                        "severity": issue.severity,
                        "examples": [],
                        "suggestions": set()
                    }
                issue_categories[category]["count"] += 1
                if len(issue_categories[category]["examples"]) < 3:
                    issue_categories[category]["examples"].append({
                        "message": issue.message,
                        "line": issue.line_number,
                        "suggestion": issue.suggestion
                    })
                if issue.suggestion:
                    issue_categories[category]["suggestions"].add(issue.suggestion)
        
        # Convert sets to lists for JSON serialization
        for category in issue_categories.values():
            category["suggestions"] = list(category["suggestions"])
        
        return {
            "total_issues": total_issues,
            "issue_breakdown": issue_categories,
            "most_common_issues": sorted(
                issue_categories.items(),
                key=lambda x: x[1]["count"],
                reverse=True
            )[:5],
            "critical_issues": [
                (name, data) for name, data in issue_categories.items()
                if data["severity"] == "error"
            ]
        }
    
    def _format_type_analysis(self, results: Dict[str, TypeAnalysisResult]) -> Dict[str, Any]:
        """Format type analysis results."""
        if not results:
            return {"message": "No type analysis performed"}
        
        total_functions = sum(result.total_functions for result in results.values())
        total_typed_functions = sum(result.typed_functions for result in results.values())
        total_parameters = sum(result.total_parameters for result in results.values())
        total_typed_parameters = sum(result.typed_parameters for result in results.values())
        
        function_coverage = (total_typed_functions / total_functions * 100) if total_functions > 0 else 0
        parameter_coverage = (total_typed_parameters / total_parameters * 100) if total_parameters > 0 else 0
        
        return {
            "overall_coverage": {
                "function_coverage": round(function_coverage, 1),
                "parameter_coverage": round(parameter_coverage, 1),
                "grade": self._coverage_to_grade(function_coverage)
            },
            "statistics": {
                "total_functions": total_functions,
                "typed_functions": total_typed_functions,
                "total_parameters": total_parameters,
                "typed_parameters": total_typed_parameters
            },
            "recommendations": self._get_type_recommendations(function_coverage, parameter_coverage)
        }
    
    def _generate_recommendations(
        self,
        semantic_results: Dict[str, SemanticAnalysisResult],
        meritocracy_result: ProjectMeritocracyResult,
        type_results: Dict[str, TypeAnalysisResult]
    ) -> List[Dict[str, Any]]:
        """Generate actionable recommendations."""
        recommendations = []
        
        # High-priority recommendations from meritocracy analysis
        for indicator in meritocracy_result.quality_indicators:
            if indicator.score < 5 and indicator.recommendations:
                recommendations.append({
                    "priority": "high",
                    "category": "quality",
                    "title": f"Improve {indicator.indicator_name}",
                    "description": f"Current score: {indicator.score:.1f}/10",
                    "actions": indicator.recommendations,
                    "impact": "Addresses critical quality issues"
                })
        
        # Architectural recommendations
        if len(meritocracy_result.architectural_patterns) < 2:
            recommendations.append({
                "priority": "medium",
                "category": "architecture",
                "title": "Implement Design Patterns",
                "description": "Project lacks clear architectural patterns",
                "actions": [
                    "Consider implementing Repository pattern for data access",
                    "Use Factory pattern for object creation",
                    "Apply Strategy pattern for algorithm variations"
                ],
                "impact": "Improves code organization and maintainability"
            })
        
        # Type annotation recommendations
        if type_results:
            total_functions = sum(result.total_functions for result in type_results.values())
            total_typed = sum(result.typed_functions for result in type_results.values())
            coverage = (total_typed / total_functions * 100) if total_functions > 0 else 0
            
            if coverage < 70:
                recommendations.append({
                    "priority": "medium",
                    "category": "typing",
                    "title": "Improve Type Coverage",
                    "description": f"Current type coverage: {coverage:.1f}%",
                    "actions": [
                        "Add type hints to function parameters and return types",
                        "Use mypy for static type checking",
                        "Consider using TypedDict for structured data"
                    ],
                    "impact": "Improves code reliability and IDE support"
                })
        
        return sorted(recommendations, key=lambda x: {"high": 3, "medium": 2, "low": 1}[x["priority"]], reverse=True)
    
    def _calculate_quality_metrics(
        self,
        semantic_results: Dict[str, SemanticAnalysisResult],
        meritocracy_result: ProjectMeritocracyResult,
        type_results: Dict[str, TypeAnalysisResult]
    ) -> Dict[str, Any]:
        """Calculate comprehensive quality metrics."""
        metrics = {
            "overall_score": round(meritocracy_result.overall_score, 1),
            "architectural_maturity": len(meritocracy_result.architectural_patterns),
            "code_quality_issues": sum(len(result.issues) for result in semantic_results.values()),
            "files_analyzed": len(semantic_results)
        }
        
        if type_results:
            total_functions = sum(result.total_functions for result in type_results.values())
            total_typed = sum(result.typed_functions for result in type_results.values())
            metrics["type_coverage"] = round((total_typed / total_functions * 100) if total_functions > 0 else 0, 1)
        
        return metrics
    
    def _format_architectural_insights(self, result: ProjectMeritocracyResult) -> Dict[str, Any]:
        """Format architectural insights."""
        return {
            "patterns_detected": [
                {
                    "name": pattern.pattern_name,
                    "confidence": round(pattern.confidence * 100, 1),
                    "quality_impact": pattern.quality_impact,
                    "description": pattern.description,
                    "files_count": len(pattern.files_involved)
                }
                for pattern in result.architectural_patterns
            ],
            "architectural_score": len(result.architectural_patterns),
            "maturity_level": result.meritocracy_insights.get("architectural_maturity", "Unknown")
        }
    
    def _analyze_success_factors(self, result: ProjectMeritocracyResult) -> Dict[str, Any]:
        """Analyze what makes the project successful or not."""
        return {
            "what_works": {
                "strengths": result.strengths,
                "success_factors": result.success_factors,
                "positive_patterns": [
                    pattern.pattern_name for pattern in result.architectural_patterns
                    if pattern.quality_impact == "positive"
                ]
            },
            "what_doesnt_work": {
                "weaknesses": result.weaknesses,
                "risk_factors": result.risk_factors,
                "critical_issues": [
                    indicator.indicator_name for indicator in result.quality_indicators
                    if indicator.score < 4
                ]
            },
            "why_analysis": result.meritocracy_insights.get("why_it_works", []),
            "failure_risks": result.meritocracy_insights.get("why_it_might_fail", [])
        }
    
    def _create_improvement_roadmap(self, result: ProjectMeritocracyResult) -> List[Dict[str, Any]]:
        """Create a roadmap for improvements."""
        roadmap = []
        
        # Phase 1: Critical issues
        critical_indicators = [ind for ind in result.quality_indicators if ind.score < 4]
        if critical_indicators:
            roadmap.append({
                "phase": "Phase 1: Critical Issues",
                "timeline": "Immediate (1-2 weeks)",
                "focus": "Address critical quality issues",
                "tasks": [f"Fix {ind.indicator_name}" for ind in critical_indicators],
                "expected_impact": "Prevent project failure"
            })
        
        # Phase 2: Quality improvements
        medium_indicators = [ind for ind in result.quality_indicators if 4 <= ind.score < 7]
        if medium_indicators:
            roadmap.append({
                "phase": "Phase 2: Quality Enhancement",
                "timeline": "Short-term (1-2 months)",
                "focus": "Improve code quality and maintainability",
                "tasks": [f"Enhance {ind.indicator_name}" for ind in medium_indicators],
                "expected_impact": "Improve maintainability and team productivity"
            })
        
        # Phase 3: Architectural improvements
        if len(result.architectural_patterns) < 3:
            roadmap.append({
                "phase": "Phase 3: Architectural Evolution",
                "timeline": "Medium-term (3-6 months)",
                "focus": "Implement design patterns and improve architecture",
                "tasks": [
                    "Implement Repository pattern",
                    "Add Factory patterns",
                    "Improve separation of concerns"
                ],
                "expected_impact": "Better code organization and scalability"
            })
        
        return roadmap
    
    def _score_to_grade(self, score: float) -> str:
        """Convert numeric score to letter grade."""
        if score >= 9:
            return "A+"
        elif score >= 8:
            return "A"
        elif score >= 7:
            return "B+"
        elif score >= 6:
            return "B"
        elif score >= 5:
            return "C+"
        elif score >= 4:
            return "C"
        elif score >= 3:
            return "D+"
        else:
            return "D"
    
    def _coverage_to_grade(self, coverage: float) -> str:
        """Convert coverage percentage to grade."""
        if coverage >= 90:
            return "A+"
        elif coverage >= 80:
            return "A"
        elif coverage >= 70:
            return "B+"
        elif coverage >= 60:
            return "B"
        elif coverage >= 50:
            return "C+"
        elif coverage >= 40:
            return "C"
        else:
            return "D"
    
    def _get_type_recommendations(self, function_coverage: float, parameter_coverage: float) -> List[str]:
        """Get type annotation recommendations."""
        recommendations = []
        
        if function_coverage < 50:
            recommendations.append("Start adding type hints to public functions")
        elif function_coverage < 80:
            recommendations.append("Expand type coverage to remaining functions")
        else:
            recommendations.append("Excellent type coverage! Consider using strict mypy settings")
        
        if parameter_coverage < function_coverage - 20:
            recommendations.append("Focus on adding parameter type hints")
        
        return recommendations

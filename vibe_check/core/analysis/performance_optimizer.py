"""
Performance Optimizer for Semantic Analysis

This module provides performance optimizations for semantic analysis,
including caching, parallel processing, and incremental analysis.
"""

import asyncio
import hashlib
import logging
import pickle
import time
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Set
from dataclasses import dataclass
import json

logger = logging.getLogger(__name__)


@dataclass
class AnalysisCache:
    """Cache for analysis results."""
    
    file_hash: str
    timestamp: float
    semantic_result: Any
    framework_detection: Any
    type_analysis: Any


@dataclass
class PerformanceMetrics:
    """Performance metrics for analysis."""
    
    total_time: float
    semantic_time: float
    framework_time: float
    cache_hits: int
    cache_misses: int
    files_processed: int
    parallel_workers: int


class SemanticAnalysisCache:
    """
    Cache for semantic analysis results to avoid reprocessing unchanged files.
    """
    
    def __init__(self, cache_dir: Optional[Path] = None):
        self.cache_dir = cache_dir or Path.home() / ".vibe_check" / "cache"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.cache: Dict[str, AnalysisCache] = {}
        self.load_cache()
    
    def _get_file_hash(self, file_path: Path, content: str) -> str:
        """Get hash for file content and metadata."""
        try:
            file_stat = file_path.stat()
            hash_input = f"{content}{file_stat.st_mtime}{file_stat.st_size}"
        except (FileNotFoundError, OSError):
            # File doesn't exist on disk, just use content
            hash_input = content
        return hashlib.sha256(hash_input.encode()).hexdigest()
    
    def get_cached_result(self, file_path: Path, content: str) -> Optional[AnalysisCache]:
        """Get cached analysis result if available and valid."""
        file_hash = self._get_file_hash(file_path, content)
        cache_key = str(file_path)
        
        if cache_key in self.cache:
            cached = self.cache[cache_key]
            if cached.file_hash == file_hash:
                return cached
        
        return None
    
    def cache_result(self, file_path: Path, content: str, semantic_result: Any,
                    framework_detection: Any = None, type_analysis: Any = None) -> None:
        """Cache analysis result."""
        file_hash = self._get_file_hash(file_path, content)
        cache_key = str(file_path)
        
        self.cache[cache_key] = AnalysisCache(
            file_hash=file_hash,
            timestamp=time.time(),
            semantic_result=semantic_result,
            framework_detection=framework_detection,
            type_analysis=type_analysis
        )
        
        # Persist cache periodically
        if len(self.cache) % 10 == 0:
            self.save_cache()
    
    def load_cache(self) -> None:
        """Load cache from disk."""
        cache_file = self.cache_dir / "semantic_cache.pkl"
        if cache_file.exists():
            try:
                with open(cache_file, 'rb') as f:
                    self.cache = pickle.load(f)
                logger.debug(f"Loaded {len(self.cache)} cached results")
            except Exception as e:
                logger.warning(f"Failed to load cache: {e}")
                self.cache = {}
    
    def save_cache(self) -> None:
        """Save cache to disk."""
        cache_file = self.cache_dir / "semantic_cache.pkl"
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(self.cache, f)
            logger.debug(f"Saved {len(self.cache)} cached results")
        except Exception as e:
            logger.warning(f"Failed to save cache: {e}")
    
    def clear_cache(self) -> None:
        """Clear all cached results."""
        self.cache.clear()
        cache_file = self.cache_dir / "semantic_cache.pkl"
        if cache_file.exists():
            cache_file.unlink()


class ParallelSemanticAnalyzer:
    """
    Parallel semantic analyzer for improved performance.
    """
    
    def __init__(self, max_workers: Optional[int] = None, use_cache: bool = True):
        # Safely get max workers without requiring an active event loop
        if max_workers is None:
            try:
                loop = asyncio.get_event_loop()
                self.max_workers = min(4, (loop.get_debug() and 2) or 4)
            except RuntimeError:
                # No event loop running, use default
                self.max_workers = 4
        else:
            self.max_workers = max_workers
        self.cache = SemanticAnalysisCache() if use_cache else None
        self.metrics = PerformanceMetrics(
            total_time=0.0,
            semantic_time=0.0,
            framework_time=0.0,
            cache_hits=0,
            cache_misses=0,
            files_processed=0,
            parallel_workers=self.max_workers
        )
    
    async def analyze_files_parallel(self, project_files: Dict[str, str]) -> Dict[str, Any]:
        """
        Analyze files in parallel for better performance.
        
        Args:
            project_files: Dictionary mapping file paths to source code
            
        Returns:
            Dictionary with analysis results
        """
        start_time = time.time()
        
        # Filter Python files
        python_files = {
            path: content for path, content in project_files.items()
            if path.endswith('.py')
        }
        
        if not python_files:
            return {'semantic_results': {}, 'framework_analysis': None}
        
        # Analyze files in parallel
        semantic_results = await self._analyze_semantic_parallel(python_files)
        
        # Framework analysis (single-threaded for now)
        framework_start = time.time()
        framework_analysis = await self._analyze_frameworks(python_files)
        self.metrics.framework_time = time.time() - framework_start
        
        self.metrics.total_time = time.time() - start_time
        self.metrics.files_processed = len(python_files)
        
        return {
            'semantic_results': semantic_results,
            'framework_analysis': framework_analysis,
            'performance_metrics': self.metrics
        }
    
    async def _analyze_semantic_parallel(self, python_files: Dict[str, str]) -> Dict[str, Any]:
        """Analyze semantic issues in parallel."""
        semantic_start = time.time()
        
        # Create tasks for parallel processing
        tasks = []
        for file_path, content in python_files.items():
            task = self._analyze_single_file(file_path, content)
            tasks.append(task)
        
        # Run tasks in parallel with limited concurrency
        semaphore = asyncio.Semaphore(self.max_workers)
        semaphore_tasks = [
            self._run_with_semaphore(semaphore, task)
            for task in tasks
        ]
        results = await asyncio.gather(*semaphore_tasks)
        
        # Combine results
        semantic_results = {}
        for i, (file_path, _) in enumerate(python_files.items()):
            semantic_results[file_path] = results[i]
        
        self.metrics.semantic_time = time.time() - semantic_start
        return semantic_results
    
    async def _run_with_semaphore(self, semaphore: asyncio.Semaphore, task: Any) -> Any:
        """Run task with semaphore to limit concurrency."""
        async with semaphore:
            return await task
    
    async def _analyze_single_file(self, file_path: str, content: str) -> Any:
        """Analyze a single file with caching."""
        path_obj = Path(file_path)
        
        # Check cache first
        if self.cache:
            cached_result = self.cache.get_cached_result(path_obj, content)
            if cached_result:
                self.metrics.cache_hits += 1
                return cached_result.semantic_result
        
        self.metrics.cache_misses += 1
        
        # Perform analysis in thread pool to avoid blocking
        loop = asyncio.get_event_loop()
        with ThreadPoolExecutor(max_workers=1) as executor:
            result = await loop.run_in_executor(
                executor,
                self._perform_semantic_analysis,
                file_path,
                content
            )
        
        # Cache result
        if self.cache:
            self.cache.cache_result(path_obj, content, result)
        
        return result
    
    def _perform_semantic_analysis(self, file_path: str, content: str) -> Any:
        """Perform semantic analysis on a single file."""
        try:
            from .python_semantic_analyzer import PythonSemanticAnalyzer
            from .semantic_rules import create_enhanced_rule_registry
            
            # Create analyzer with enhanced rules
            registry = create_enhanced_rule_registry()
            analyzer = PythonSemanticAnalyzer(registry)
            
            # Analyze the file
            result = analyzer.analyze_source(content, Path(file_path))
            return result
            
        except Exception as e:
            logger.warning(f"Error analyzing {file_path}: {e}")
            # Return empty result on error
            from .python_semantic_analyzer import SemanticAnalysisResult, SemanticContext
            context = SemanticContext(Path(file_path), Path(file_path).stem)
            return SemanticAnalysisResult(context=context)
    
    async def _analyze_frameworks(self, python_files: Dict[str, str]) -> Any:
        """Analyze frameworks (single-threaded for now)."""
        try:
            from .framework_detector import FrameworkDetector
            
            detector = FrameworkDetector()
            return detector.detect_frameworks(python_files)
            
        except Exception as e:
            logger.warning(f"Error in framework analysis: {e}")
            return None


class IncrementalAnalyzer:
    """
    Incremental analyzer that only processes changed files.
    """
    
    def __init__(self, project_path: Path):
        self.project_path = project_path
        self.state_file = project_path / ".vibe_check_state.json"
        self.previous_state: Dict[str, Any] = {}
        self.load_state()
    
    def get_changed_files(self, current_files: Dict[str, str]) -> Dict[str, str]:
        """Get only files that have changed since last analysis."""
        changed_files = {}
        
        for file_path, content in current_files.items():
            file_hash = hashlib.sha256(content.encode()).hexdigest()
            
            if (file_path not in self.previous_state or 
                self.previous_state[file_path].get('hash') != file_hash):
                changed_files[file_path] = content
                
                # Update state
                self.previous_state[file_path] = {
                    'hash': file_hash,
                    'timestamp': time.time()
                }
        
        return changed_files
    
    def load_state(self) -> None:
        """Load previous analysis state."""
        if self.state_file.exists():
            try:
                with open(self.state_file, 'r') as f:
                    self.previous_state = json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load analysis state: {e}")
                self.previous_state = {}
    
    def save_state(self) -> None:
        """Save current analysis state."""
        try:
            with open(self.state_file, 'w') as f:
                json.dump(self.previous_state, f, indent=2)
        except Exception as e:
            logger.warning(f"Failed to save analysis state: {e}")


class PerformanceOptimizer:
    """
    Main performance optimizer that coordinates all optimizations.
    """
    
    def __init__(self, project_path: Path, max_workers: Optional[int] = None,
                 use_cache: bool = True, use_incremental: bool = True):
        self.project_path = project_path
        self.parallel_analyzer = ParallelSemanticAnalyzer(max_workers, use_cache)
        self.incremental_analyzer = IncrementalAnalyzer(project_path) if use_incremental else None
        self.use_incremental = use_incremental
    
    async def optimize_analysis(self, project_files: Dict[str, str]) -> Dict[str, Any]:
        """
        Run optimized analysis with all performance enhancements.
        
        Args:
            project_files: Dictionary mapping file paths to source code
            
        Returns:
            Analysis results with performance metrics
        """
        start_time = time.time()
        
        # Determine which files to analyze
        files_to_analyze = project_files
        if self.use_incremental and self.incremental_analyzer:
            changed_files = self.incremental_analyzer.get_changed_files(project_files)
            if changed_files:
                logger.info(f"Incremental analysis: {len(changed_files)}/{len(project_files)} files changed")
                files_to_analyze = changed_files
            else:
                logger.info("No files changed since last analysis")
                return {'semantic_results': {}, 'framework_analysis': None, 'incremental': True}
        
        # Run parallel analysis
        results = await self.parallel_analyzer.analyze_files_parallel(files_to_analyze)
        
        # Save state for incremental analysis
        if self.incremental_analyzer:
            self.incremental_analyzer.save_state()
        
        # Add optimization metrics
        total_time = time.time() - start_time
        results['optimization_metrics'] = {
            'total_optimization_time': total_time,
            'files_analyzed': len(files_to_analyze),
            'total_files': len(project_files),
            'cache_hit_rate': (
                self.parallel_analyzer.metrics.cache_hits / 
                max(1, self.parallel_analyzer.metrics.cache_hits + self.parallel_analyzer.metrics.cache_misses)
            ) * 100,
            'parallel_workers': self.parallel_analyzer.max_workers,
            'incremental_enabled': self.use_incremental
        }
        
        return results
    
    def clear_cache(self) -> None:
        """Clear all caches."""
        if self.parallel_analyzer.cache:
            self.parallel_analyzer.cache.clear_cache()
        
        if self.incremental_analyzer and self.incremental_analyzer.state_file.exists():
            self.incremental_analyzer.state_file.unlink()
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get detailed performance report."""
        metrics = self.parallel_analyzer.metrics
        
        return {
            'performance_summary': {
                'total_time': f"{metrics.total_time:.2f}s",
                'semantic_analysis_time': f"{metrics.semantic_time:.2f}s",
                'framework_analysis_time': f"{metrics.framework_time:.2f}s",
                'files_processed': metrics.files_processed,
                'parallel_workers': metrics.parallel_workers,
                'cache_hits': metrics.cache_hits,
                'cache_misses': metrics.cache_misses,
                'cache_hit_rate': f"{(metrics.cache_hits / max(1, metrics.cache_hits + metrics.cache_misses)) * 100:.1f}%"
            },
            'optimization_recommendations': self._get_optimization_recommendations()
        }
    
    def _get_optimization_recommendations(self) -> List[str]:
        """Get recommendations for further optimization."""
        recommendations = []
        metrics = self.parallel_analyzer.metrics
        
        if metrics.cache_hits == 0 and metrics.cache_misses > 0:
            recommendations.append("Enable caching to improve performance on repeated analyses")
        
        if metrics.semantic_time > metrics.total_time * 0.8:
            recommendations.append("Consider increasing parallel workers for semantic analysis")
        
        if metrics.files_processed > 100:
            recommendations.append("Consider using incremental analysis for large projects")
        
        if not recommendations:
            recommendations.append("Performance is already optimized for this project size")
        
        return recommendations

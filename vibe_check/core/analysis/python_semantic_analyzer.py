"""
Python Semantic Analyzer

This module provides semantic analysis capabilities for Python code using AST traversal.
It implements the visitor pattern to analyze Python code semantics and apply rules.
"""

import ast
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Union
from dataclasses import dataclass, field

logger = logging.getLogger(__name__)


@dataclass
class SemanticContext:
    """Context information for semantic analysis."""
    
    file_path: Path
    module_name: str
    imports: Dict[str, str] = field(default_factory=dict)
    classes: List[str] = field(default_factory=list)
    functions: List[str] = field(default_factory=list)
    variables: Set[str] = field(default_factory=set)
    current_scope: str = "module"
    scope_stack: List[str] = field(default_factory=list)


@dataclass
class SemanticIssue:
    """Represents a semantic issue found during analysis."""
    
    rule_id: str
    severity: str  # 'error', 'warning', 'info'
    message: str
    line_number: int
    column_number: int = 0
    suggestion: Optional[str] = None


@dataclass
class SemanticAnalysisResult:
    """Results from semantic analysis."""
    
    context: SemanticContext
    issues: List[SemanticIssue] = field(default_factory=list)
    metrics: Dict[str, Any] = field(default_factory=dict)


class SemanticRule:
    """Base class for semantic analysis rules."""
    
    def __init__(self, rule_id: str, description: str, severity: str = "warning"):
        self.rule_id = rule_id
        self.description = description
        self.severity = severity
    
    def check(self, node: ast.AST, context: SemanticContext) -> List[SemanticIssue]:
        """Check a node for semantic issues."""
        raise NotImplementedError("Subclasses must implement check method")


class SemanticRuleRegistry:
    """Registry for semantic analysis rules."""
    
    def __init__(self):
        self._rules: Dict[str, SemanticRule] = {}
        self._rules_by_node_type: Dict[type, List[SemanticRule]] = {}
    
    def register_rule(self, rule: SemanticRule, node_types: List[type]) -> None:
        """Register a rule for specific AST node types."""
        self._rules[rule.rule_id] = rule
        
        for node_type in node_types:
            if node_type not in self._rules_by_node_type:
                self._rules_by_node_type[node_type] = []
            self._rules_by_node_type[node_type].append(rule)
    
    def get_rules_for_node(self, node: ast.AST) -> List[SemanticRule]:
        """Get all rules applicable to a specific node type."""
        return self._rules_by_node_type.get(type(node), [])
    
    def get_rule(self, rule_id: str) -> Optional[SemanticRule]:
        """Get a specific rule by ID."""
        return self._rules.get(rule_id)
    
    def list_rules(self) -> List[SemanticRule]:
        """List all registered rules."""
        return list(self._rules.values())


class PythonSemanticAnalyzer(ast.NodeVisitor):
    """
    Core semantic analyzer for Python code.
    
    Uses the AST visitor pattern to traverse Python code and apply semantic rules.
    """
    
    def __init__(self, rule_registry: Optional[SemanticRuleRegistry] = None):
        self.rule_registry = rule_registry or SemanticRuleRegistry()
        self.context: Optional[SemanticContext] = None
        self.issues: List[SemanticIssue] = []
        self.metrics: Dict[str, Any] = {}
    
    def analyze_file(self, file_path: Path) -> SemanticAnalysisResult:
        """
        Analyze a Python file for semantic issues.
        
        Args:
            file_path: Path to the Python file to analyze
            
        Returns:
            SemanticAnalysisResult containing issues and metrics
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source_code = f.read()
            
            return self.analyze_source(source_code, file_path)
            
        except Exception as e:
            logger.error(f"Error analyzing file {file_path}: {e}")
            # Return empty result on error
            context = SemanticContext(
                file_path=file_path,
                module_name=file_path.stem
            )
            return SemanticAnalysisResult(context=context)
    
    def analyze_source(self, source_code: str, file_path: Path) -> SemanticAnalysisResult:
        """
        Analyze Python source code for semantic issues.
        
        Args:
            source_code: Python source code to analyze
            file_path: Path to the source file (for context)
            
        Returns:
            SemanticAnalysisResult containing issues and metrics
        """
        try:
            # Parse the source code into an AST
            tree = ast.parse(source_code, filename=str(file_path))
            
            # Initialize context
            self.context = SemanticContext(
                file_path=file_path,
                module_name=file_path.stem
            )
            self.issues = []
            self.metrics = {
                'total_nodes': 0,
                'function_count': 0,
                'class_count': 0,
                'import_count': 0
            }
            
            # Visit all nodes in the AST
            self.visit(tree)
            
            return SemanticAnalysisResult(
                context=self.context,
                issues=self.issues,
                metrics=self.metrics
            )
            
        except SyntaxError as e:
            logger.warning(f"Syntax error in {file_path}: {e}")
            context = SemanticContext(
                file_path=file_path,
                module_name=file_path.stem
            )
            issue = SemanticIssue(
                rule_id="syntax_error",
                severity="error",
                message=f"Syntax error: {e.msg}",
                line_number=e.lineno or 1,
                column_number=e.offset or 0
            )
            return SemanticAnalysisResult(
                context=context,
                issues=[issue]
            )
        except Exception as e:
            logger.error(f"Error analyzing source code: {e}")
            context = SemanticContext(
                file_path=file_path,
                module_name=file_path.stem
            )
            return SemanticAnalysisResult(context=context)

    def analyze_code_quality_standalone(self, source_code: str, file_path: Path) -> Dict[str, Any]:
        """
        Comprehensive standalone code quality analysis.
        Provides substantial value without external tools.

        This method provides a rich analysis using only Python's built-in AST module
        and standard library, making Vibe Check valuable even without external tools.
        """
        try:
            tree = ast.parse(source_code, filename=str(file_path))
        except SyntaxError as e:
            return {
                "syntax_errors": [{
                    "line": e.lineno or 1,
                    "column": e.offset or 0,
                    "message": str(e.msg),
                    "severity": "error"
                }],
                "analysis_successful": False,
                "analyzer": "vibe_check_standalone"
            }

        issues = []

        # Line-based analysis
        lines = source_code.split('\n')

        # 1. Line length and formatting analysis
        for i, line in enumerate(lines, 1):
            # Long lines
            if len(line) > 88:  # PEP 8 recommendation
                issues.append({
                    "line": i,
                    "column": 89,
                    "message": f"Line too long ({len(line)} > 88 characters)",
                    "severity": "style",
                    "type": "line_length",
                    "rule": "E501"
                })

            # Trailing whitespace
            if line.rstrip() != line:
                issues.append({
                    "line": i,
                    "column": len(line.rstrip()) + 1,
                    "message": "Trailing whitespace",
                    "severity": "style",
                    "type": "whitespace",
                    "rule": "W291"
                })

        # 2. AST-based analysis
        ast_issues = self._analyze_ast_standalone(tree, source_code)
        issues.extend(ast_issues)

        # 3. Calculate comprehensive metrics
        metrics = self._calculate_standalone_metrics(tree, source_code, lines)

        return {
            "issues": issues,
            "metrics": metrics,
            "analysis_successful": True,
            "analyzer": "vibe_check_standalone",
            "tool_status": "standalone"
        }

    def _analyze_ast_standalone(self, tree: ast.AST, source_code: str) -> List[Dict[str, Any]]:
        """Analyze AST for standalone issues."""
        issues = []

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef):
                # Function complexity (simplified)
                complexity = self._calculate_function_complexity(node)
                if complexity > 10:
                    issues.append({
                        "line": node.lineno,
                        "column": node.col_offset,
                        "message": f"Function '{node.name}' has high complexity ({complexity})",
                        "severity": "warning",
                        "type": "complexity",
                        "rule": "C901"
                    })

                # Function length
                func_length = (getattr(node, 'end_lineno', node.lineno) - node.lineno + 1)
                if func_length > 50:
                    issues.append({
                        "line": node.lineno,
                        "column": node.col_offset,
                        "message": f"Function '{node.name}' is too long ({func_length} lines)",
                        "severity": "warning",
                        "type": "length",
                        "rule": "C901"
                    })

                # Missing docstring
                if not ast.get_docstring(node):
                    issues.append({
                        "line": node.lineno,
                        "column": node.col_offset,
                        "message": f"Function '{node.name}' missing docstring",
                        "severity": "info",
                        "type": "documentation",
                        "rule": "D100"
                    })

            elif isinstance(node, ast.ClassDef):
                # Missing docstring
                if not ast.get_docstring(node):
                    issues.append({
                        "line": node.lineno,
                        "column": node.col_offset,
                        "message": f"Class '{node.name}' missing docstring",
                        "severity": "info",
                        "type": "documentation",
                        "rule": "D101"
                    })

        return issues

    def _calculate_function_complexity(self, node: ast.FunctionDef) -> int:
        """Calculate simplified cyclomatic complexity for a function."""
        complexity = 1  # Base complexity

        for child in ast.walk(node):
            if isinstance(child, (ast.If, ast.While, ast.For, ast.AsyncFor)):
                complexity += 1
            elif isinstance(child, ast.ExceptHandler):
                complexity += 1
            elif isinstance(child, (ast.And, ast.Or)):
                complexity += 1

        return complexity

    def _calculate_standalone_metrics(self, tree: ast.AST, source_code: str, lines: List[str]) -> Dict[str, Any]:
        """Calculate comprehensive metrics for standalone analysis."""
        # Count different node types
        function_count = len([n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)])
        class_count = len([n for n in ast.walk(tree) if isinstance(n, ast.ClassDef)])
        import_count = len([n for n in ast.walk(tree) if isinstance(n, (ast.Import, ast.ImportFrom))])

        return {
            "total_lines": len(lines),
            "code_lines": len([line for line in lines if line.strip() and not line.strip().startswith('#')]),
            "comment_lines": len([line for line in lines if line.strip().startswith('#')]),
            "blank_lines": len([line for line in lines if not line.strip()]),
            "function_count": function_count,
            "class_count": class_count,
            "import_count": import_count,
            "complexity": {
                "total_functions": function_count,
                "total_classes": class_count,
                "avg_function_length": self._calculate_avg_function_length(tree, source_code)
            }
        }

    def _calculate_avg_function_length(self, tree: ast.AST, source_code: str) -> float:
        """Calculate average function length."""
        functions = [n for n in ast.walk(tree) if isinstance(n, ast.FunctionDef)]
        if not functions:
            return 0.0

        total_length = 0
        for func in functions:
            func_length = getattr(func, 'end_lineno', func.lineno) - func.lineno + 1
            total_length += func_length

        return total_length / len(functions)

    def generic_visit(self, node: ast.AST) -> None:
        """Visit a node and apply semantic rules."""
        if self.context is None:
            return

        self.metrics['total_nodes'] += 1

        # Apply rules for this node type (only if no specific visitor handled it)
        if not hasattr(self, f'visit_{type(node).__name__}'):
            rules = self.rule_registry.get_rules_for_node(node)
            for rule in rules:
                try:
                    rule_issues = rule.check(node, self.context)
                    self.issues.extend(rule_issues)
                except Exception as e:
                    logger.warning(f"Error applying rule {rule.rule_id}: {e}")

        # Continue with standard visitor pattern
        super().generic_visit(node)
    
    def visit_Import(self, node: ast.Import) -> None:
        """Visit import statements."""
        if self.context is None:
            return
        
        self.metrics['import_count'] += 1
        
        for alias in node.names:
            import_name = alias.asname if alias.asname else alias.name
            self.context.imports[import_name] = alias.name
        
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        """Visit from-import statements."""
        if self.context is None:
            return
        
        self.metrics['import_count'] += 1
        
        module = node.module or ""
        for alias in node.names:
            import_name = alias.asname if alias.asname else alias.name
            full_name = f"{module}.{alias.name}" if module else alias.name
            self.context.imports[import_name] = full_name
        
        self.generic_visit(node)
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Visit function definitions."""
        if self.context is None:
            return

        self.metrics['function_count'] += 1
        self.context.functions.append(node.name)

        # Enter function scope
        self.context.scope_stack.append(self.context.current_scope)
        self.context.current_scope = f"function:{node.name}"

        # Apply rules for this node type first
        rules = self.rule_registry.get_rules_for_node(node)
        for rule in rules:
            try:
                rule_issues = rule.check(node, self.context)
                self.issues.extend(rule_issues)
            except Exception as e:
                logger.warning(f"Error applying rule {rule.rule_id}: {e}")

        self.generic_visit(node)

        # Exit function scope
        self.context.current_scope = self.context.scope_stack.pop()
    
    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef) -> None:
        """Visit async function definitions."""
        if self.context is None:
            return

        self.metrics['function_count'] += 1
        self.context.functions.append(node.name)

        # Enter async function scope
        self.context.scope_stack.append(self.context.current_scope)
        self.context.current_scope = f"async_function:{node.name}"

        # Apply rules for this node type first
        rules = self.rule_registry.get_rules_for_node(node)
        for rule in rules:
            try:
                rule_issues = rule.check(node, self.context)
                self.issues.extend(rule_issues)
            except Exception as e:
                logger.warning(f"Error applying rule {rule.rule_id}: {e}")

        self.generic_visit(node)

        # Exit function scope
        self.context.current_scope = self.context.scope_stack.pop()
    
    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Visit class definitions."""
        if self.context is None:
            return

        self.metrics['class_count'] += 1
        self.context.classes.append(node.name)

        # Enter class scope
        self.context.scope_stack.append(self.context.current_scope)
        self.context.current_scope = f"class:{node.name}"

        # Apply rules for this node type first
        rules = self.rule_registry.get_rules_for_node(node)
        for rule in rules:
            try:
                rule_issues = rule.check(node, self.context)
                self.issues.extend(rule_issues)
            except Exception as e:
                logger.warning(f"Error applying rule {rule.rule_id}: {e}")

        self.generic_visit(node)

        # Exit class scope
        self.context.current_scope = self.context.scope_stack.pop()
    
    def visit_Name(self, node: ast.Name) -> None:
        """Visit name references."""
        if self.context is None:
            return
        
        # Track variable usage
        if isinstance(node.ctx, ast.Store):
            self.context.variables.add(node.id)
        
        self.generic_visit(node)

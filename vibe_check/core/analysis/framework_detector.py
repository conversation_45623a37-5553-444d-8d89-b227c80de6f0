"""
Framework Detection Engine

This module detects Python frameworks and their usage patterns in codebases.
It identifies popular frameworks like Django, Flask, FastAPI, etc., and analyzes
their implementation patterns.
"""

import ast
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from collections import defaultdict

logger = logging.getLogger(__name__)


@dataclass
class FrameworkSignature:
    """Signature for detecting a specific framework."""
    
    name: str
    version_range: Optional[str] = None
    import_patterns: List[str] = field(default_factory=list)
    file_patterns: List[str] = field(default_factory=list)
    class_patterns: List[str] = field(default_factory=list)
    function_patterns: List[str] = field(default_factory=list)
    decorator_patterns: List[str] = field(default_factory=list)
    config_files: List[str] = field(default_factory=list)
    directory_structure: List[str] = field(default_factory=list)


@dataclass
class FrameworkDetection:
    """Result of framework detection."""
    
    framework: FrameworkSignature
    confidence: float  # 0.0 to 1.0
    evidence: List[str]
    files_involved: List[str]
    usage_patterns: List[str]
    version_info: Optional[str] = None


@dataclass
class FrameworkAnalysisResult:
    """Results from framework analysis."""
    
    detected_frameworks: List[FrameworkDetection] = field(default_factory=list)
    primary_framework: Optional[FrameworkDetection] = None
    framework_mix: List[str] = field(default_factory=list)
    architecture_style: str = "unknown"
    recommendations: List[str] = field(default_factory=list)


class FrameworkDetector:
    """
    Detects Python frameworks and analyzes their usage patterns.
    """
    
    def __init__(self):
        self.frameworks = self._build_framework_signatures()
        self.import_graph: Dict[str, Set[str]] = defaultdict(set)
        self.file_patterns: Dict[str, List[str]] = defaultdict(list)
        self.class_patterns: Dict[str, List[str]] = defaultdict(list)
        self.decorator_usage: Dict[str, List[str]] = defaultdict(list)
    
    def _build_framework_signatures(self) -> Dict[str, FrameworkSignature]:
        """Build database of framework signatures."""
        return {
            "django": FrameworkSignature(
                name="Django",
                import_patterns=[
                    "django", "django.conf", "django.db", "django.http",
                    "django.views", "django.urls", "django.forms",
                    "django.contrib", "django.core"
                ],
                file_patterns=["models.py", "views.py", "urls.py", "forms.py", "admin.py"],
                class_patterns=["Model", "View", "Form", "Admin"],
                decorator_patterns=["@login_required", "@csrf_exempt", "@require_http_methods"],
                config_files=["settings.py", "manage.py", "wsgi.py", "asgi.py"],
                directory_structure=["templates", "static", "migrations"]
            ),
            
            "flask": FrameworkSignature(
                name="Flask",
                import_patterns=[
                    "flask", "flask.Flask", "flask.request", "flask.jsonify",
                    "flask.render_template", "flask.Blueprint", "flask.session"
                ],
                class_patterns=["Flask"],
                decorator_patterns=["@app.route", "@bp.route", "@before_request"],
                function_patterns=["render_template", "jsonify", "redirect", "url_for"],
                config_files=["app.py", "run.py", "config.py"]
            ),
            
            "fastapi": FrameworkSignature(
                name="FastAPI",
                import_patterns=[
                    "fastapi", "fastapi.FastAPI", "fastapi.Depends",
                    "fastapi.HTTPException", "fastapi.status", "pydantic"
                ],
                class_patterns=["FastAPI", "BaseModel"],
                decorator_patterns=["@app.get", "@app.post", "@app.put", "@app.delete"],
                function_patterns=["Depends", "HTTPException"],
                config_files=["main.py"]
            ),
            
            "pytest": FrameworkSignature(
                name="pytest",
                import_patterns=["pytest", "pytest.fixture", "pytest.mark"],
                file_patterns=["test_*.py", "*_test.py", "conftest.py"],
                decorator_patterns=["@pytest.fixture", "@pytest.mark"],
                function_patterns=["test_*"]
            ),
            
            "sqlalchemy": FrameworkSignature(
                name="SQLAlchemy",
                import_patterns=[
                    "sqlalchemy", "sqlalchemy.orm", "sqlalchemy.ext",
                    "sqlalchemy.Column", "sqlalchemy.Integer", "sqlalchemy.String"
                ],
                class_patterns=["declarative_base", "Base", "Session"],
                function_patterns=["create_engine", "sessionmaker"]
            ),
            
            "celery": FrameworkSignature(
                name="Celery",
                import_patterns=["celery", "celery.Celery"],
                decorator_patterns=["@app.task", "@celery.task"],
                config_files=["celeryconfig.py"]
            ),
            
            "streamlit": FrameworkSignature(
                name="Streamlit",
                import_patterns=["streamlit"],
                function_patterns=["st.write", "st.title", "st.sidebar"]
            ),
            
            "pandas": FrameworkSignature(
                name="pandas",
                import_patterns=["pandas", "pd"],
                class_patterns=["DataFrame", "Series"],
                function_patterns=["read_csv", "read_excel", "to_csv"]
            ),
            
            "numpy": FrameworkSignature(
                name="NumPy",
                import_patterns=["numpy", "np"],
                function_patterns=["array", "zeros", "ones", "arange"]
            ),
            
            "requests": FrameworkSignature(
                name="requests",
                import_patterns=["requests"],
                function_patterns=["get", "post", "put", "delete", "session"]
            )
        }
    
    def detect_frameworks(self, project_files: Dict[str, str]) -> FrameworkAnalysisResult:
        """
        Detect frameworks in a project.
        
        Args:
            project_files: Dictionary mapping file paths to source code
            
        Returns:
            FrameworkAnalysisResult with detected frameworks
        """
        result = FrameworkAnalysisResult()
        
        # Analyze each file
        for file_path, source_code in project_files.items():
            self._analyze_file(file_path, source_code)
        
        # Detect frameworks based on collected evidence
        detections = []
        for framework_id, signature in self.frameworks.items():
            detection = self._evaluate_framework(signature, project_files)
            if detection and detection.confidence > 0.1:  # Minimum confidence threshold
                detections.append(detection)
        
        # Sort by confidence
        detections.sort(key=lambda x: x.confidence, reverse=True)
        result.detected_frameworks = detections
        
        # Determine primary framework
        if detections:
            result.primary_framework = detections[0]
        
        # Analyze framework mix
        result.framework_mix = [d.framework.name for d in detections if d.confidence > 0.3]
        
        # Determine architecture style
        result.architecture_style = self._determine_architecture_style(detections)
        
        # Generate recommendations
        result.recommendations = self._generate_recommendations(detections)
        
        return result
    
    def _analyze_file(self, file_path: str, source_code: str) -> None:
        """Analyze a single file for framework patterns."""
        try:
            tree = ast.parse(source_code, filename=file_path)
            visitor = FrameworkPatternVisitor(file_path)
            visitor.visit(tree)
            
            # Collect patterns
            self.import_graph[file_path].update(visitor.imports)
            self.file_patterns[file_path].extend(visitor.file_patterns)
            self.class_patterns[file_path].extend(visitor.class_patterns)
            self.decorator_usage[file_path].extend(visitor.decorators)
            
        except SyntaxError:
            logger.warning(f"Syntax error in {file_path}, skipping framework analysis")
        except Exception as e:
            logger.error(f"Error analyzing {file_path} for frameworks: {e}")
    
    def _evaluate_framework(self, signature: FrameworkSignature, project_files: Dict[str, str]) -> Optional[FrameworkDetection]:
        """Evaluate if a framework is present based on its signature."""
        evidence = []
        files_involved = []
        confidence_score = 0.0
        usage_patterns = []
        
        # Check import patterns
        import_matches = 0
        for file_path, imports in self.import_graph.items():
            for pattern in signature.import_patterns:
                if any(pattern in imp for imp in imports):
                    import_matches += 1
                    evidence.append(f"Import '{pattern}' found in {file_path}")
                    files_involved.append(file_path)
        
        if signature.import_patterns:
            import_confidence = min(1.0, import_matches / len(signature.import_patterns))
            confidence_score += import_confidence * 0.4
        
        # Check file patterns
        file_matches = 0
        for file_path in project_files.keys():
            for pattern in signature.file_patterns:
                if pattern in Path(file_path).name or pattern.replace("*", "") in file_path:
                    file_matches += 1
                    evidence.append(f"File pattern '{pattern}' matches {file_path}")
                    files_involved.append(file_path)
        
        if signature.file_patterns:
            file_confidence = min(1.0, file_matches / len(signature.file_patterns))
            confidence_score += file_confidence * 0.2
        
        # Check class patterns
        class_matches = 0
        for file_path, classes in self.class_patterns.items():
            for pattern in signature.class_patterns:
                if any(pattern in cls for cls in classes):
                    class_matches += 1
                    evidence.append(f"Class pattern '{pattern}' found in {file_path}")
                    files_involved.append(file_path)
        
        if signature.class_patterns:
            class_confidence = min(1.0, class_matches / len(signature.class_patterns))
            confidence_score += class_confidence * 0.2
        
        # Check decorator patterns
        decorator_matches = 0
        for file_path, decorators in self.decorator_usage.items():
            for pattern in signature.decorator_patterns:
                if any(pattern in dec for dec in decorators):
                    decorator_matches += 1
                    evidence.append(f"Decorator '{pattern}' found in {file_path}")
                    files_involved.append(file_path)
                    usage_patterns.append(f"Uses {pattern} decorator")
        
        if signature.decorator_patterns:
            decorator_confidence = min(1.0, decorator_matches / len(signature.decorator_patterns))
            confidence_score += decorator_confidence * 0.2
        
        # Check config files
        config_matches = 0
        for file_path in project_files.keys():
            for config_file in signature.config_files:
                if config_file in Path(file_path).name:
                    config_matches += 1
                    evidence.append(f"Config file '{config_file}' found")
                    files_involved.append(file_path)
        
        if signature.config_files:
            config_confidence = min(1.0, config_matches / len(signature.config_files))
            confidence_score += config_confidence * 0.1
        
        # Only return detection if we have some evidence
        if confidence_score > 0.1:
            return FrameworkDetection(
                framework=signature,
                confidence=confidence_score,
                evidence=evidence,
                files_involved=list(set(files_involved)),
                usage_patterns=usage_patterns
            )
        
        return None
    
    def _determine_architecture_style(self, detections: List[FrameworkDetection]) -> str:
        """Determine the overall architecture style."""
        if not detections:
            return "unknown"
        
        primary = detections[0].framework.name.lower()
        
        if "django" in primary:
            return "mvc_framework"
        elif "flask" in primary:
            return "microframework"
        elif "fastapi" in primary:
            return "api_framework"
        elif any("test" in d.framework.name.lower() for d in detections):
            return "test_driven"
        elif any(d.framework.name.lower() in ["pandas", "numpy"] for d in detections):
            return "data_science"
        else:
            return "mixed"
    
    def _generate_recommendations(self, detections: List[FrameworkDetection]) -> List[str]:
        """Generate recommendations based on detected frameworks."""
        recommendations = []
        
        if not detections:
            recommendations.append("No major frameworks detected. Consider using established frameworks for better structure.")
            return recommendations
        
        framework_names = [d.framework.name.lower() for d in detections]
        
        # Framework-specific recommendations
        if "django" in framework_names:
            recommendations.append("Django detected: Follow Django best practices for models, views, and templates")
            recommendations.append("Consider using Django REST framework for API development")
        
        if "flask" in framework_names:
            recommendations.append("Flask detected: Use Blueprints for better code organization")
            recommendations.append("Consider Flask-SQLAlchemy for database operations")
        
        if "fastapi" in framework_names:
            recommendations.append("FastAPI detected: Leverage Pydantic models for data validation")
            recommendations.append("Use dependency injection for better testability")
        
        # Testing recommendations
        if "pytest" in framework_names:
            recommendations.append("pytest detected: Maintain good test coverage and use fixtures effectively")
        else:
            recommendations.append("Consider adding pytest for comprehensive testing")
        
        # Multiple frameworks
        if len(detections) > 3:
            recommendations.append("Multiple frameworks detected: Ensure they work well together and don't conflict")
        
        return recommendations


class FrameworkPatternVisitor(ast.NodeVisitor):
    """AST visitor to collect framework patterns."""
    
    def __init__(self, file_path: str):
        self.file_path = file_path
        self.imports: Set[str] = set()
        self.file_patterns: List[str] = []
        self.class_patterns: List[str] = []
        self.decorators: List[str] = []
    
    def visit_Import(self, node: ast.Import) -> None:
        """Visit import statements."""
        for alias in node.names:
            self.imports.add(alias.name)
        self.generic_visit(node)
    
    def visit_ImportFrom(self, node: ast.ImportFrom) -> None:
        """Visit from-import statements."""
        if node.module:
            self.imports.add(node.module)
            for alias in node.names:
                full_name = f"{node.module}.{alias.name}"
                self.imports.add(full_name)
        self.generic_visit(node)
    
    def visit_ClassDef(self, node: ast.ClassDef) -> None:
        """Visit class definitions."""
        self.class_patterns.append(node.name)
        
        # Check base classes
        for base in node.bases:
            if isinstance(base, ast.Name):
                self.class_patterns.append(base.id)
        
        self.generic_visit(node)
    
    def visit_FunctionDef(self, node: ast.FunctionDef) -> None:
        """Visit function definitions."""
        # Collect decorators
        for decorator in node.decorator_list:
            if isinstance(decorator, ast.Name):
                self.decorators.append(f"@{decorator.id}")
            elif isinstance(decorator, ast.Attribute):
                self.decorators.append(f"@{self._get_attribute_name(decorator)}")
            elif isinstance(decorator, ast.Call):
                if isinstance(decorator.func, ast.Name):
                    self.decorators.append(f"@{decorator.func.id}")
                elif isinstance(decorator.func, ast.Attribute):
                    self.decorators.append(f"@{self._get_attribute_name(decorator.func)}")
        
        self.generic_visit(node)
    
    def _get_attribute_name(self, node: ast.Attribute) -> str:
        """Get the full name of an attribute."""
        if isinstance(node.value, ast.Name):
            return f"{node.value.id}.{node.attr}"
        elif isinstance(node.value, ast.Attribute):
            return f"{self._get_attribute_name(node.value)}.{node.attr}"
        else:
            return node.attr

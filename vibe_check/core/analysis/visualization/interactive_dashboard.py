"""
Interactive Dashboard Generator for Import Analysis
==================================================

This module provides interactive dashboard generation using Plotly.
"""

import json
import logging
import os
from pathlib import Path
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

try:
    import plotly.graph_objects as go
    import plotly.express as px
    from plotly.subplots import make_subplots
    HAS_PLOTLY = True
except ImportError:
    HAS_PLOTLY = False

from ..import_analyzer import ImportAnalysisResult
from .html_generators import create_static_html_dashboard


def generate_interactive_dashboard(analysis_result: ImportAnalysisResult, output_dir: Path) -> Optional[str]:
    """Generate an interactive HTML dashboard.
    
    Args:
        analysis_result: Results from import analysis
        output_dir: Directory to save visualizations
        
    Returns:
        Path to the generated HTML file
    """
    try:
        if HAS_PLOTLY:
            return _create_plotly_dashboard(analysis_result, output_dir)
        else:
            return _create_static_dashboard(analysis_result, output_dir)
            
    except Exception as e:
        logger.error(f"Error generating interactive dashboard: {e}")
        return None


def _create_plotly_dashboard(analysis_result: ImportAnalysisResult, output_dir: Path) -> str:
    """Create an interactive Plotly dashboard.
    
    Args:
        analysis_result: Results from import analysis
        output_dir: Directory to save visualizations
        
    Returns:
        Path to the generated HTML file
    """
    # Create subplots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('Import Complexity', 'Circular Dependencies', 
                       'Coupling Metrics', 'Import Statistics'),
        specs=[[{"type": "bar"}, {"type": "pie"}],
               [{"type": "scatter"}, {"type": "bar"}]]
    )
    
    # 1. Import Complexity Chart
    if analysis_result.import_complexity_scores:
        files = list(analysis_result.import_complexity_scores.keys())
        scores = list(analysis_result.import_complexity_scores.values())
        
        colors = ['red' if s > 20 else 'orange' if s > 10 else 'green' for s in scores]
        
        fig.add_trace(
            go.Bar(
                x=[os.path.basename(f) for f in files],
                y=scores,
                marker_color=colors,
                name="Complexity",
                showlegend=False
            ),
            row=1, col=1
        )
    
    # 2. Circular Dependencies Pie Chart
    if analysis_result.circular_dependencies:
        severities = [cd.severity for cd in analysis_result.circular_dependencies]
        severity_counts = {sev: severities.count(sev) for sev in set(severities)}
        
        fig.add_trace(
            go.Pie(
                labels=list(severity_counts.keys()),
                values=list(severity_counts.values()),
                name="Circular Deps",
                showlegend=False
            ),
            row=1, col=2
        )
    
    # 3. Coupling Metrics Scatter Plot
    if analysis_result.coupling_metrics:
        afferent = []
        efferent = []
        files = []
        
        for file_path, metrics in analysis_result.coupling_metrics.items():
            afferent.append(metrics['afferent_coupling'])
            efferent.append(metrics['efferent_coupling'])
            files.append(os.path.basename(file_path))
        
        fig.add_trace(
            go.Scatter(
                x=afferent,
                y=efferent,
                mode='markers+text',
                text=files,
                textposition="top center",
                name="Coupling",
                showlegend=False
            ),
            row=2, col=1
        )
    
    # 4. Import Statistics
    if analysis_result.file_imports:
        import_counts = [len(imports) for imports in analysis_result.file_imports.values()]
        files = [os.path.basename(f) for f in analysis_result.file_imports.keys()]
        
        fig.add_trace(
            go.Bar(
                x=files,
                y=import_counts,
                name="Import Count",
                showlegend=False
            ),
            row=2, col=2
        )
    
    # Update layout
    fig.update_layout(
        title_text="Import Analysis Dashboard",
        height=800,
        showlegend=False
    )
    
    # Update axes labels
    fig.update_xaxes(title_text="Files", row=1, col=1)
    fig.update_yaxes(title_text="Complexity Score", row=1, col=1)
    
    fig.update_xaxes(title_text="Afferent Coupling", row=2, col=1)
    fig.update_yaxes(title_text="Efferent Coupling", row=2, col=1)
    
    fig.update_xaxes(title_text="Files", row=2, col=2)
    fig.update_yaxes(title_text="Import Count", row=2, col=2)
    
    # Save the interactive plot
    output_path = output_dir / "interactive_dashboard.html"
    fig.write_html(str(output_path))
    
    return str(output_path)


def _create_static_dashboard(analysis_result: ImportAnalysisResult, output_dir: Path) -> str:
    """Create a static HTML dashboard as fallback.
    
    Args:
        analysis_result: Results from import analysis
        output_dir: Directory to save visualizations
        
    Returns:
        Path to the generated HTML file
    """
    html_content = create_static_html_dashboard(analysis_result)
    
    output_path = output_dir / "static_dashboard.html"
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return str(output_path)


def generate_json_report(analysis_result: ImportAnalysisResult, output_dir: Path) -> Optional[str]:
    """Generate a JSON report of the analysis results.
    
    Args:
        analysis_result: Results from import analysis
        output_dir: Directory to save the report
        
    Returns:
        Path to the generated JSON file
    """
    try:
        # Convert analysis result to dictionary
        report_data = {
            "summary": {
                "total_files": len(analysis_result.file_imports),
                "total_imports": sum(len(imports) for imports in analysis_result.file_imports.values()),
                "circular_dependencies_count": len(analysis_result.circular_dependencies),
                "unused_imports_count": len(analysis_result.unused_imports)
            },
            "file_imports": {
                file_path: [{"module": imp.module, "from_module": imp.from_module, 
                           "line_number": imp.line_number} for imp in imports]
                for file_path, imports in analysis_result.file_imports.items()
            },
            "circular_dependencies": [
                {
                    "cycle": cd.cycle,
                    "severity": cd.severity,
                    "description": cd.description,
                    "suggestions": cd.suggestions
                }
                for cd in analysis_result.circular_dependencies
            ],
            "unused_imports": {
                file_path: [{"module": imp.module, "from_module": imp.from_module,
                           "line_number": imp.line_number} for imp in unused]
                for file_path, unused in analysis_result.unused_imports.items()
            },
            "optimization_suggestions": analysis_result.optimization_suggestions,
            "import_complexity_scores": analysis_result.import_complexity_scores,
            "coupling_metrics": analysis_result.coupling_metrics
        }
        
        output_path = output_dir / "import_analysis_report.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        return str(output_path)
        
    except Exception as e:
        logger.error(f"Error generating JSON report: {e}")
        return None

"""
File Analyzer Module
================

This module provides the FileAnalyzer class, which is responsible for
analyzing a single file with multiple tools. It abstracts the details
of file analysis for use by the analysis engine.
"""

import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union

from ..models import FileMetrics
from ..utils.fs_utils import get_file_content
from .tool_executor import ToolExecutor
from .result_processor import ResultProcessor
from .python_semantic_analyzer import PythonSemanticAnalyzer
from .semantic_rules import create_default_rule_registry

logger = logging.getLogger("vibe_check_analysis")


class FileAnalyzer:
    """
    Analyzes a single file with multiple tools.

    This class abstracts the details of file analysis for use by
    the analysis engine.
    """

    def __init__(self,
                 project_path: Union[str, Path],
                 tools_config: Optional[Dict[str, Any]] = None,
                 executor: Optional[ToolExecutor] = None,
                 processor: Optional[ResultProcessor] = None,
                 enable_semantic_analysis: bool = True):
        """
        Initialize the file analyzer.

        Args:
            project_path: Path to the project being analyzed
            tools_config: Optional configuration for tools
            executor: Optional tool executor instance
            processor: Optional result processor instance
            enable_semantic_analysis: Whether to enable semantic analysis
        """
        self.project_path = Path(project_path)
        self.tools_config = tools_config or {}
        self.executor = executor or ToolExecutor(self.tools_config)
        self.processor = processor or ResultProcessor()

        # Initialize semantic analyzer if enabled
        self.enable_semantic_analysis = enable_semantic_analysis
        if enable_semantic_analysis:
            rule_registry = create_default_rule_registry()
            self.semantic_analyzer = PythonSemanticAnalyzer(rule_registry)
        else:
            self.semantic_analyzer = None

    async def analyze_file(self,
                          file_path: Union[str, Path],
                          content: Optional[str] = None) -> FileMetrics:
        """
        Analyze a file with all enabled tools.

        Args:
            file_path: Path to the file to analyze
            content: Optional file content (will be read if not provided)

        Returns:
            FileMetrics object with analysis results
        """
        file_path = Path(file_path)

        # Read file content if not provided
        if content is None:
            try:
                content = get_file_content(file_path)
            except Exception as e:
                logger.error(f"Error reading file {file_path}: {e}")
                # Return empty metrics for the file
                return FileMetrics(
                    path=str(file_path.relative_to(self.project_path)),
                    name=file_path.name,
                    size=0,
                    lines=0
                )

        # Create file metrics
        try:
            relative_path = file_path.relative_to(self.project_path)
        except ValueError:
            # File is not within project path
            relative_path = file_path

        file_metrics = FileMetrics(
            path=str(relative_path),
            name=file_path.name,
            size=file_path.stat().st_size if file_path.exists() else 0,
            lines=self._count_lines(content)
        )

        # Run tools on the file
        tool_results = await self.executor.run_tools(file_path, content)

        # Run semantic analysis if enabled and file is Python
        if (self.enable_semantic_analysis and
            self.semantic_analyzer and
            file_path.suffix == '.py'):
            try:
                semantic_result = self.semantic_analyzer.analyze_source(content, file_path)

                # Add semantic issues to file metrics
                for issue in semantic_result.issues:
                    file_metrics.issues.append({
                        'tool': 'semantic_analyzer',
                        'rule': issue.rule_id,
                        'severity': issue.severity,
                        'message': issue.message,
                        'line': issue.line_number,
                        'column': issue.column_number,
                        'suggestion': issue.suggestion
                    })

                # Add semantic metrics
                file_metrics.complexity = semantic_result.metrics.get('function_count', 0)

            except Exception as e:
                logger.warning(f"Semantic analysis failed for {file_path}: {e}")

        # Process results and update metrics
        self.processor.process_results(file_metrics, tool_results)

        return file_metrics

    def _count_lines(self, content: str) -> int:
        """
        Count the number of lines in the content.

        Args:
            content: File content

        Returns:
            Number of lines
        """
        return len(content.splitlines())

    def is_tool_enabled(self, tool_name: str) -> bool:
        """
        Check if a tool is enabled in the configuration.

        Args:
            tool_name: Name of the tool

        Returns:
            True if the tool is enabled, False otherwise
        """
        tool_config = self.tools_config.get("tools", {}).get(tool_name, {})
        return bool(tool_config.get("enabled", False))

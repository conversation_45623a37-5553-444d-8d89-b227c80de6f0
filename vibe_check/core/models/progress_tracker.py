"""
Progress Tracker Models
=====================

This module defines the progress tracker classes used to report analysis progress.
Different trackers are provided for different use cases and environments:

- ProgressTracker: The abstract base class/protocol
- SimpleProgressTracker: A basic console progress tracker
- RichProgressTracker: A more sophisticated progress tracker using rich library
- NoOpProgressTracker: A no-operation tracker for headless/silent operation
"""

import abc
import logging
import threading
import time
from typing import Any, Dict, Optional, Protocol, runtime_checkable

# Try to import rich for modern progress bars (optional dependency)
_RICH_IMPORTED_SUCCESSFULLY = False
try:
    from rich.console import Console as RichConsole
    from rich.progress import (BarColumn as RichBarColumn, Progress as RichProgress,
                               SpinnerColumn as RichSpinnerColumn, TaskID as RichTaskID,
                               TextColumn as RichTextColumn,
                               TimeElapsedColumn as RichTimeElapsedColumn,
                               TimeRemainingColumn as RichTimeRemainingColumn)
    from rich.status import Status as RichStatus
    _RICH_IMPORTED_SUCCESSFULLY = True
except ImportError:
    # Aliases won't be defined if import fails
    pass

RICH_AVAILABLE = _RICH_IMPORTED_SUCCESSFULLY

if not RICH_AVAILABLE:
    # These dummy classes are defined only if rich is not available.
    # Their names (Progress, Console, Status, TaskID) will not clash with
    # the aliased rich imports (RichProgress, RichConsole, etc.).
    # Add type: ignore to satisfy mypy if it still processes this block
    # when RICH_AVAILABLE is True (e.g. in some analysis paths).
    class Progress:
        pass
    class Console:
        pass
    class Status:
        pass
    class TaskID:
        pass


@runtime_checkable
class ProgressTracker(Protocol):
    """Protocol for progress trackers."""

    def start_global(self, phase_name: str, total_steps: int) -> None:
        """
        Start tracking the global progress.

        Args:
            phase_name: Name of the global phase
            total_steps: Total number of steps in the global phase
        """
        ...

    def increment_global(self) -> None:
        """Increment the global progress counter."""
        ...

    def complete_global(self, status: str = "Complete!") -> None:
        """
        Mark the global progress as complete.

        Args:
            status: Completion status message
        """
        ...

    def start_phase(self, phase_name: str, total_steps: int = 0) -> None:
        """
        Start tracking a new phase.

        Args:
            phase_name: Name of the phase
            total_steps: Total number of steps in the phase (0 for unknown)
        """
        ...

    def increment(self) -> None:
        """Increment the current phase progress counter."""
        ...

    def complete_phase(self, phase_name: str, status: str = "Complete!") -> None:
        """
        Mark the current phase as complete.

        Args:
            phase_name: Name of the completed phase
            status: Completion status message
        """
        ...

    def update_phase(self, phase_name: str, total_steps: int) -> None:
        """
        Update the current phase with a new name and step count.

        Args:
            phase_name: New phase name
            total_steps: New total steps
        """
        ...

    def set_status(self, message: str) -> None:
        """
        Set a status message.

        Args:
            message: Status message
        """
        ...


class RichProgressTracker:
    """
    Modern, colorful progress tracker using rich.progress.Progress and live status.

    Only one instance should be active per process to avoid live display conflicts.
    """
    _live_display_active = False  # Class-level flag to prevent multiple live displays

    def __init__(self) -> None:
        """Initialize the rich progress tracker."""
        if not RICH_AVAILABLE:
            raise ImportError("Rich library is not available. Install with 'pip install rich'.")

        if RichProgressTracker._live_display_active:
            # Instead of raising, fallback to SimpleProgressTracker
            raise RuntimeError("RichProgressTracker: Only one live display may be active at once.")

        RichProgressTracker._live_display_active = True
        self.console = RichConsole()
        self.progress = RichProgress(
            RichSpinnerColumn(),
            RichTextColumn("[bold blue]{task.description}"),
            RichBarColumn(),
            "[progress.percentage]{task.percentage:>3.0f}%",
            RichTimeElapsedColumn(),
            RichTimeRemainingColumn(),
            console=self.console,
            transient=True,
        )
        self.status: Optional[RichStatus] = None
        self.global_task: Optional[RichTaskID] = None
        self.phase_task: Optional[RichTaskID] = None
        self.current_phase: Optional[str] = None
        self._progress_thread = None
        self._stop_event = threading.Event()

    def __del__(self) -> None:
        """Clean up the progress tracker."""
        if hasattr(self, 'progress') and self.progress:
            try:
                self.progress.stop()
            except Exception:
                pass  # Ignore cleanup errors
        RichProgressTracker._live_display_active = False

    @classmethod
    def force_reset(cls) -> None:
        """Force reset the live display flag."""
        cls._live_display_active = False

    def start_global(self, phase_name: str, total_steps: int) -> None:
        """
        Start tracking the global progress.

        Args:
            phase_name: Name of the global phase
            total_steps: Total number of steps in the global phase
        """
        self.progress.start()
        self.global_task = self.progress.add_task(f"{phase_name}", total=total_steps)
        self.console.print(f"[bold green]Starting: {phase_name}")

    def increment_global(self) -> None:
        """Increment the global progress counter."""
        if self.global_task is not None:
            self.progress.advance(self.global_task)

    def complete_global(self, status: str = "Complete") -> None:
        """
        Mark the global progress as complete.

        Args:
            status: Completion status message
        """
        if self.global_task is not None:
            self.progress.update(self.global_task, completed=self.progress.tasks[self.global_task].total)
            self.console.print(f"[bold green]{status}")
        self.progress.stop()
        self.global_task = None

    def start_phase(self, phase_name: str, total_steps: int = 0) -> None:
        """
        Start tracking a new phase.

        Args:
            phase_name: Name of the phase
            total_steps: Total number of steps in the phase (0 for unknown)
        """
        self.current_phase = phase_name
        if self.phase_task is not None:
            self.progress.remove_task(self.phase_task)
        self.phase_task = self.progress.add_task(f"{phase_name}", total=total_steps)
        self.status = self.console.status(f"[cyan]Working on: {phase_name}")
        if self.status:  # Ensure status is not None before calling start
            self.status.start()

    def increment(self) -> None:
        """Increment the current phase progress counter."""
        if self.phase_task is not None:
            self.progress.advance(self.phase_task)

    def complete_phase(self, phase_name: str, status: str = "Complete") -> None:
        """
        Mark the current phase as complete.

        Args:
            phase_name: Name of the completed phase
            status: Completion status message
        """
        if self.phase_task is not None:
            self.progress.update(self.phase_task, completed=self.progress.tasks[self.phase_task].total)
            self.console.print(f"[bold yellow]{phase_name}: {status}")
            self.progress.remove_task(self.phase_task)
            self.phase_task = None
        if self.status is not None:
            self.status.stop()
            self.status = None
        self.current_phase = None

    def update_phase(self, phase_name: str, total_steps: int) -> None:
        """
        Update the current phase with a new name and step count.

        Args:
            phase_name: New phase name
            total_steps: New total steps
        """
        self.start_phase(phase_name, total_steps)

    def set_status(self, message: str) -> None:
        """
        Set a status message.

        Args:
            message: Status message
        """
        if self.status is not None:
            self.status.update(message)


class SimpleProgressTracker:
    """
    Simple console-based progress tracker as a fallback when rich is not available.

    Shows detailed progress with counts, percentage, and a spinner.
    """
    _spinner_chars = ['-', '\\', '|', '/']

    def __init__(self, show_details: bool = False) -> None:
        """
        Initialize the simple progress tracker.

        Args:
            show_details: Whether to show detailed progress information
        """
        self.current_phase: Optional[str] = None
        self.global_phase: Optional[str] = None
        self.total_global_steps: int = 0
        self.current_global_step: int = 0
        self.total_phase_steps: int = 0
        self.current_phase_step: int = 0
        self._spinner_index: int = 0
        self.show_details: bool = show_details
        self.start_time = time.time()
        self.phase_start_time = time.time()

    def _get_spinner(self) -> str:
        """Get the next spinner character."""
        char = self._spinner_chars[self._spinner_index % len(self._spinner_chars)]
        self._spinner_index += 1
        return char

    def start_global(self, phase_name: str, total_steps: int) -> None:
        """
        Start tracking the global progress.

        Args:
            phase_name: Name of the global phase
            total_steps: Total number of steps in the global phase
        """
        self.global_phase = phase_name
        self.total_global_steps = total_steps
        self.current_global_step = 0
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Starting: {phase_name} (0/{total_steps})")

    def increment_global(self) -> None:
        """Increment the global progress counter."""
        self.current_global_step += 1

    def complete_global(self, status: str = "Complete") -> None:
        """
        Mark the global progress as complete.

        Args:
            status: Completion status message
        """
        # Ensure the final status is on a new line
        if self.global_phase: # Add check for None
            import logging
            logger = logging.getLogger(__name__)
            logger.info(f"{self.global_phase}: {status}")
        self.global_phase = None

    def start_phase(self, phase_name: str, total_steps: int = 0) -> None:
        """
        Start tracking a new phase.

        Args:
            phase_name: Name of the phase
            total_steps: Total number of steps in the phase (0 for unknown)
        """
        self.current_phase = phase_name
        self.total_phase_steps = total_steps
        self.current_phase_step = 0
        self.phase_start_time = time.time()
        # Log initial phase message only if not showing details or total_steps is 0
        if not self.show_details or total_steps <= 0:
            logger = logging.getLogger(__name__)
            logger.info(f"Working on: {phase_name} {'with steps: ' + str(total_steps) if total_steps > 0 else ''}")

    def _format_time(self, seconds: float) -> str:
        """
        Format time in a consistent way across all displays.

        Args:
            seconds: Time in seconds

        Returns:
            Formatted time string (HH:MM:SS for times >= 1 hour, MM:SS otherwise)
        """
        hours, remainder = divmod(int(seconds), 3600)
        minutes, seconds = divmod(remainder, 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"

    def increment(self) -> None:
        """Increment the current phase progress counter."""
        if not self.current_phase:
            logger = logging.getLogger(__name__)
            logger.warning("Step completed (no active phase)")  # Should not happen
            return

        self.current_phase_step += 1
        current_time = time.time()
        elapsed = current_time - self.phase_start_time
        total_elapsed = current_time - self.start_time

        if not self.show_details:
            return

        # Show detailed progress information
        percent = (self.current_phase_step / self.total_phase_steps * 100) if self.total_phase_steps > 0 else 0
        spinner = self._get_spinner()

        # Format times consistently
        elapsed_str = self._format_time(elapsed)
        total_str = self._format_time(total_elapsed)

        # Calculate ETA
        if self.current_phase_step > 0 and self.total_phase_steps > 0:
            eta = (elapsed / self.current_phase_step) * (self.total_phase_steps - self.current_phase_step)
            eta_str = self._format_time(eta)

            # Use console output for progress display (not logging as this is user interface)
            import sys
            sys.stdout.write(f"\r {spinner} {self.current_phase}: {self.current_phase_step}/{self.total_phase_steps} ({percent:.0f}%) - Elapsed: {elapsed_str} - ETA: {eta_str} - Total: {total_str}            ")
            sys.stdout.flush()
        elif self.total_phase_steps > 0:
            # No ETA for first step
            import sys
            sys.stdout.write(f"\r {spinner} {self.current_phase}: {self.current_phase_step}/{self.total_phase_steps} ({percent:.0f}%) - Elapsed: {elapsed_str} - Total: {total_str}            ")
            sys.stdout.flush()
        else:
            # Show spinner and phase name even if total_steps is unknown
            if self.current_phase: # Add check for None
                import sys
                sys.stdout.write(f"\r {spinner} {self.current_phase}: Step {self.current_phase_step} - Elapsed: {elapsed_str} - Total: {total_str}            ")
                sys.stdout.flush()

    def complete_phase(self, phase_name: str, status: str = "Complete") -> None:
        """
        Mark the current phase as complete.

        Args:
            phase_name: Name of the completed phase
            status: Completion status message
        """
        # Show detailed completion status if detailed view is on or total_steps > 0
        if self.show_details and self.total_phase_steps > 0:
            # Clear the progress line and show final status
            elapsed = time.time() - self.phase_start_time
            elapsed_str = self._format_time(elapsed)
            import sys
            sys.stdout.write(f"\r {phase_name}: {status} ({self.current_phase_step}/{self.total_phase_steps}) - Time: {elapsed_str}            \n")
            sys.stdout.flush()
        else:
            # Simple message for phases without steps
            elapsed = time.time() - self.phase_start_time
            elapsed_str = self._format_time(elapsed)
            logger = logging.getLogger(__name__)
            logger.info(f"{phase_name}: {status} - Time: {elapsed_str}")
        self.current_phase = None
        self.total_phase_steps = 0
        self.current_phase_step = 0

    def update_phase(self, phase_name: str, total_steps: int) -> None:
        """
        Update the current phase with a new name and step count.

        Args:
            phase_name: New phase name
            total_steps: New total steps
        """
        # Reset spinner index for new phase display
        self._spinner_index = 0
        self.start_phase(phase_name, total_steps)

    def set_status(self, message: str) -> None:
        """
        Set a status message.

        Args:
            message: Status message
        """
        # Can optionally overwrite the last progress line if detailed view is on
        if self.show_details and self.current_phase:
            import sys
            sys.stdout.write(f"\r Status: {message}                                ")
            sys.stdout.flush()
        else:
            logger = logging.getLogger(__name__)
            logger.info(f"Status: {message}")


class NoOpProgressTracker:
    """
    A progress tracker that does nothing (for disabling all progress output).

    Useful for headless/silent operation or when progress reporting is not needed.
    """
    def __init__(self) -> None:
        """Initialize the no-op progress tracker."""
        pass

    def start_global(self, phase_name: str, total_steps: int) -> None:
        """
        Start tracking the global progress.

        Args:
            phase_name: Name of the global phase
            total_steps: Total number of steps in the global phase
        """
        pass

    def increment_global(self) -> None:
        """Increment the global progress counter."""
        pass

    def complete_global(self, status: str = "Complete") -> None:
        """
        Mark the global progress as complete.

        Args:
            status: Completion status message
        """
        pass

    def start_phase(self, phase_name: str, total_steps: int = 0) -> None:
        """
        Start tracking a new phase.

        Args:
            phase_name: Name of the phase
            total_steps: Total number of steps in the phase (0 for unknown)
        """
        pass

    def increment(self) -> None:
        """Increment the current phase progress counter."""
        pass

    def complete_phase(self, phase_name: str, status: str = "Complete") -> None:
        """
        Mark the current phase as complete.

        Args:
            phase_name: Name of the completed phase
            status: Completion status message
        """
        pass

    def update_phase(self, phase_name: str, total_steps: int) -> None:
        """
        Update the current phase with a new name and step count.

        Args:
            phase_name: New phase name
            total_steps: New total steps
        """
        pass

    def set_status(self, message: str) -> None:
        """
        Set a status message.

        Args:
            message: Status message
        """
        pass


def get_progress_tracker(config: Optional[Dict[str, Any]] = None) -> ProgressTracker:
    """
    Factory function to get the appropriate progress tracker based on configuration.

    Args:
        config: Configuration dictionary with progress bar settings

    Returns:
        A progress tracker instance
    """
    # If config disables progress bar, return NoOpProgressTracker
    if config is not None:
        pb_cfg = config.get('progress_bar', {})
        if not pb_cfg.get('enabled', True):
            return NoOpProgressTracker()

    # Try to use rich progress bar if available and not disabled
    if RICH_AVAILABLE:
        try:
            return RichProgressTracker()
        except RuntimeError as e:
            # Fallback to simple tracker if rich live display is already active
            logging.warning(f"Progress bar fallback: {e}")
            return SimpleProgressTracker(show_details=True)

    # Default to simple progress tracker with details enabled
    return SimpleProgressTracker(show_details=True)

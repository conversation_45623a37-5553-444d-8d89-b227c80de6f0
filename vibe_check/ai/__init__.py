"""
AI-Powered Analysis Package
===========================

This package provides AI-powered code analysis capabilities including local LLM integration,
code explanation, refactoring suggestions, and intelligent documentation generation.
"""

from .infrastructure import AIModelManager, PrivacyPreservingProcessor, ModelOptimizer
from .explanation import CodeExplanationEngine, DocumentationGenerator, CommentAnalyzer
from .refactoring import RefactoringSuggestionEngine, CodeSmellDetector, DesignPatternRecommender
from .temporal import TemporalAnalysisEngine, TechnicalDebtPredictor, ProductivityAnalyzer

__all__ = [
    'AIModelManager',
    'PrivacyPreservingProcessor',
    'ModelOptimizer',
    'CodeExplanationEngine',
    'DocumentationGenerator',
    'CommentAnalyzer',
    'RefactoringSuggestionEngine',
    'CodeSmellDetector',
    'DesignPatternRecommender',
    'TemporalAnalysisEngine',
    'TechnicalDebtPredictor',
    'ProductivityAnalyzer'
]

"""
File: vibe_check/ai/visualization/interactive_charts.py
Purpose: Interactive chart creation and management
Related Files: vibe_check/ai/visualization/
Dependencies: typing, asyncio, datetime, enum, dataclasses
"""

import asyncio
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import json

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ChartInteraction(Enum):
    """Types of chart interactions."""
    HOVER = "hover"
    CLICK = "click"
    ZOOM = "zoom"
    PAN = "pan"
    BRUSH = "brush"
    DRILL_DOWN = "drill_down"
    FILTER = "filter"
    SORT = "sort"
    EXPORT = "export"
    REFRESH = "refresh"


class AnimationType(Enum):
    """Chart animation types."""
    NONE = "none"
    FADE_IN = "fade_in"
    SLIDE_IN = "slide_in"
    GROW = "grow"
    BOUNCE = "bounce"
    ELASTIC = "elastic"
    MORPH = "morph"
    STAGGER = "stagger"


@dataclass
class InteractionHandler:
    """Chart interaction handler configuration."""
    interaction_type: ChartInteraction
    handler_id: str
    callback_function: str  # JavaScript function name
    parameters: Dict[str, Any] = field(default_factory=dict)
    enabled: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": self.interaction_type.value,
            "id": self.handler_id,
            "callback": self.callback_function,
            "parameters": self.parameters,
            "enabled": self.enabled
        }


@dataclass
class ChartAnimation:
    """Chart animation configuration."""
    animation_type: AnimationType
    duration: int = 1000  # milliseconds
    delay: int = 0
    easing: str = "ease-in-out"
    loop: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "type": self.animation_type.value,
            "duration": self.duration,
            "delay": self.delay,
            "easing": self.easing,
            "loop": self.loop
        }


@dataclass
class InteractiveChart:
    """Interactive chart definition."""
    chart_id: str
    title: str
    chart_type: str
    data: Dict[str, Any]
    configuration: Dict[str, Any] = field(default_factory=dict)
    interactions: List[InteractionHandler] = field(default_factory=list)
    animations: List[ChartAnimation] = field(default_factory=list)
    real_time: bool = False
    update_interval: int = 0  # seconds
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.chart_id,
            "title": self.title,
            "type": self.chart_type,
            "data": self.data,
            "configuration": self.configuration,
            "interactions": [handler.to_dict() for handler in self.interactions],
            "animations": [anim.to_dict() for anim in self.animations],
            "realTime": self.real_time,
            "updateInterval": self.update_interval,
            "createdAt": self.created_at.isoformat()
        }
    
    def to_json(self) -> str:
        """Convert to JSON string."""
        return json.dumps(self.to_dict(), indent=2)


class ChartLibraryAdapter:
    """Adapter for different chart libraries."""
    
    def __init__(self):
        self.supported_libraries = {
            "d3": self._generate_d3_chart,
            "plotly": self._generate_plotly_chart,
            "chartjs": self._generate_chartjs_chart,
            "highcharts": self._generate_highcharts_chart,
            "echarts": self._generate_echarts_chart
        }
    
    def generate_chart_code(
        self,
        chart: InteractiveChart,
        library: str = "plotly"
    ) -> str:
        """Generate chart code for specified library."""
        if library not in self.supported_libraries:
            library = "plotly"  # Default fallback
        
        return self.supported_libraries[library](chart)
    
    def _generate_d3_chart(self, chart: InteractiveChart) -> str:
        """Generate D3.js chart code."""
        return f"""
// D3.js Chart: {chart.title}
const chartData = {chart.to_json()};

const svg = d3.select("#{chart.chart_id}")
    .append("svg")
    .attr("width", chartData.configuration.width || 800)
    .attr("height", chartData.configuration.height || 400);

// Chart implementation would go here based on chart type
console.log("D3 Chart Data:", chartData);

// Add interactions
chartData.interactions.forEach(interaction => {{
    if (interaction.type === "hover") {{
        svg.selectAll(".data-point")
            .on("mouseover", function(d) {{
                // Hover handler
            }});
    }}
}});
"""
    
    def _generate_plotly_chart(self, chart: InteractiveChart) -> str:
        """Generate Plotly.js chart code."""
        return f"""
// Plotly.js Chart: {chart.title}
const chartData = {chart.to_json()};

const plotlyData = [{{
    x: chartData.data.x || [],
    y: chartData.data.y || [],
    type: chartData.type,
    name: chartData.title
}}];

const layout = {{
    title: chartData.title,
    width: chartData.configuration.width || 800,
    height: chartData.configuration.height || 400,
    ...chartData.configuration.layout
}};

const config = {{
    responsive: true,
    displayModeBar: true,
    ...chartData.configuration.config
}};

Plotly.newPlot('{chart.chart_id}', plotlyData, layout, config);

// Add custom interactions
chartData.interactions.forEach(interaction => {{
    if (interaction.type === "click") {{
        document.getElementById('{chart.chart_id}').on('plotly_click', function(data) {{
            // Click handler
            console.log("Chart clicked:", data);
        }});
    }}
}});
"""
    
    def _generate_chartjs_chart(self, chart: InteractiveChart) -> str:
        """Generate Chart.js chart code."""
        return f"""
// Chart.js Chart: {chart.title}
const chartData = {chart.to_json()};

const ctx = document.getElementById('{chart.chart_id}').getContext('2d');
const chart = new Chart(ctx, {{
    type: chartData.type,
    data: {{
        labels: chartData.data.labels || [],
        datasets: [{{
            label: chartData.title,
            data: chartData.data.values || [],
            backgroundColor: chartData.configuration.backgroundColor || 'rgba(54, 162, 235, 0.2)',
            borderColor: chartData.configuration.borderColor || 'rgba(54, 162, 235, 1)',
            borderWidth: 1
        }}]
    }},
    options: {{
        responsive: true,
        plugins: {{
            title: {{
                display: true,
                text: chartData.title
            }}
        }},
        ...chartData.configuration.options
    }}
}});

// Add interactions
chartData.interactions.forEach(interaction => {{
    if (interaction.type === "hover") {{
        chart.options.onHover = function(event, elements) {{
            // Hover handler
        }};
    }}
}});
"""
    
    def _generate_highcharts_chart(self, chart: InteractiveChart) -> str:
        """Generate Highcharts chart code."""
        return f"""
// Highcharts Chart: {chart.title}
const chartData = {chart.to_json()};

Highcharts.chart('{chart.chart_id}', {{
    chart: {{
        type: chartData.type,
        width: chartData.configuration.width || 800,
        height: chartData.configuration.height || 400
    }},
    title: {{
        text: chartData.title
    }},
    series: [{{
        name: chartData.title,
        data: chartData.data.values || []
    }}],
    plotOptions: {{
        series: {{
            point: {{
                events: {{
                    click: function() {{
                        // Click handler
                        console.log("Point clicked:", this);
                    }}
                }}
            }}
        }}
    }}
}});
"""
    
    def _generate_echarts_chart(self, chart: InteractiveChart) -> str:
        """Generate ECharts chart code."""
        return f"""
// ECharts Chart: {chart.title}
const chartData = {chart.to_json()};

const chartDom = document.getElementById('{chart.chart_id}');
const myChart = echarts.init(chartDom);

const option = {{
    title: {{
        text: chartData.title
    }},
    tooltip: {{}},
    legend: {{
        data: [chartData.title]
    }},
    xAxis: {{
        data: chartData.data.categories || []
    }},
    yAxis: {{}},
    series: [{{
        name: chartData.title,
        type: chartData.type,
        data: chartData.data.values || []
    }}]
}};

myChart.setOption(option);

// Add interactions
myChart.on('click', function(params) {{
    console.log("Chart clicked:", params);
}});
"""


class InteractiveChartEngine:
    """Interactive chart creation and management engine."""
    
    def __init__(self):
        """Initialize interactive chart engine."""
        self.chart_adapter = ChartLibraryAdapter()
        self.chart_cache: Dict[str, InteractiveChart] = {}
        self.interaction_callbacks: Dict[str, Callable] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize interactive chart engine."""
        if self._initialized:
            return
        
        # Register default interaction callbacks
        self.interaction_callbacks = {
            "default_hover": self._default_hover_handler,
            "default_click": self._default_click_handler,
            "default_zoom": self._default_zoom_handler,
            "default_export": self._default_export_handler
        }
        
        self._initialized = True
        logger.info("Interactive chart engine initialized")
    
    async def create_interactive_chart(
        self,
        chart_id: str,
        title: str,
        chart_type: str,
        data: Dict[str, Any],
        configuration: Optional[Dict[str, Any]] = None,
        interactions: Optional[List[Dict[str, Any]]] = None,
        animations: Optional[List[Dict[str, Any]]] = None
    ) -> InteractiveChart:
        """
        Create an interactive chart.
        
        Args:
            chart_id: Unique chart identifier
            title: Chart title
            chart_type: Type of chart (line, bar, pie, etc.)
            data: Chart data
            configuration: Chart configuration options
            interactions: List of interaction configurations
            animations: List of animation configurations
            
        Returns:
            Created interactive chart
        """
        # Process interactions
        interaction_handlers = []
        if interactions:
            for interaction_config in interactions:
                handler = InteractionHandler(
                    interaction_type=ChartInteraction(interaction_config["type"]),
                    handler_id=interaction_config.get("id", f"handler_{len(interaction_handlers)}"),
                    callback_function=interaction_config.get("callback", "default_handler"),
                    parameters=interaction_config.get("parameters", {}),
                    enabled=interaction_config.get("enabled", True)
                )
                interaction_handlers.append(handler)
        
        # Process animations
        chart_animations = []
        if animations:
            for anim_config in animations:
                animation = ChartAnimation(
                    animation_type=AnimationType(anim_config["type"]),
                    duration=anim_config.get("duration", 1000),
                    delay=anim_config.get("delay", 0),
                    easing=anim_config.get("easing", "ease-in-out"),
                    loop=anim_config.get("loop", False)
                )
                chart_animations.append(animation)
        
        # Create chart
        chart = InteractiveChart(
            chart_id=chart_id,
            title=title,
            chart_type=chart_type,
            data=data,
            configuration=configuration or {},
            interactions=interaction_handlers,
            animations=chart_animations
        )
        
        # Cache chart
        self.chart_cache[chart_id] = chart
        
        logger.info(f"Created interactive chart: {title} ({chart_type})")
        return chart
    
    async def add_real_time_updates(
        self,
        chart_id: str,
        update_interval: int,
        data_source_callback: Callable
    ) -> bool:
        """
        Enable real-time updates for a chart.
        
        Args:
            chart_id: Chart to update
            update_interval: Update interval in seconds
            data_source_callback: Function to get updated data
            
        Returns:
            Success status
        """
        if chart_id not in self.chart_cache:
            return False
        
        chart = self.chart_cache[chart_id]
        chart.real_time = True
        chart.update_interval = update_interval
        
        # Store callback for real-time updates
        self.interaction_callbacks[f"realtime_{chart_id}"] = data_source_callback
        
        logger.info(f"Enabled real-time updates for chart: {chart_id}")
        return True
    
    async def generate_chart_code(
        self,
        chart_id: str,
        library: str = "plotly",
        include_container: bool = True
    ) -> str:
        """
        Generate chart implementation code.
        
        Args:
            chart_id: Chart to generate code for
            library: Chart library to use
            include_container: Whether to include HTML container
            
        Returns:
            Generated chart code
        """
        if chart_id not in self.chart_cache:
            raise ValueError(f"Chart not found: {chart_id}")
        
        chart = self.chart_cache[chart_id]
        
        # Generate chart code
        chart_code = self.chart_adapter.generate_chart_code(chart, library)
        
        if include_container:
            container_html = f"""
<div id="{chart_id}" class="interactive-chart">
    <!-- Chart will be rendered here -->
</div>
"""
            return container_html + "\\n<script>\\n" + chart_code + "\\n</script>"
        
        return chart_code
    
    async def update_chart_data(
        self,
        chart_id: str,
        new_data: Dict[str, Any],
        animate: bool = True
    ) -> bool:
        """
        Update chart data.
        
        Args:
            chart_id: Chart to update
            new_data: New data for the chart
            animate: Whether to animate the update
            
        Returns:
            Success status
        """
        if chart_id not in self.chart_cache:
            return False
        
        chart = self.chart_cache[chart_id]
        chart.data.update(new_data)
        
        if animate and not chart.animations:
            # Add default update animation
            chart.animations.append(ChartAnimation(
                animation_type=AnimationType.MORPH,
                duration=500
            ))
        
        logger.debug(f"Updated chart data: {chart_id}")
        return True
    
    async def add_interaction(
        self,
        chart_id: str,
        interaction_type: ChartInteraction,
        callback_function: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Add interaction to existing chart.
        
        Args:
            chart_id: Chart to add interaction to
            interaction_type: Type of interaction
            callback_function: Callback function name
            parameters: Optional parameters
            
        Returns:
            Success status
        """
        if chart_id not in self.chart_cache:
            return False
        
        chart = self.chart_cache[chart_id]
        
        handler = InteractionHandler(
            interaction_type=interaction_type,
            handler_id=f"handler_{len(chart.interactions)}",
            callback_function=callback_function,
            parameters=parameters or {}
        )
        
        chart.interactions.append(handler)
        
        logger.debug(f"Added interaction to chart: {chart_id}")
        return True
    
    def register_interaction_callback(
        self,
        callback_name: str,
        callback_function: Callable
    ) -> None:
        """Register a custom interaction callback."""
        self.interaction_callbacks[callback_name] = callback_function
        logger.debug(f"Registered interaction callback: {callback_name}")
    
    def _default_hover_handler(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Default hover interaction handler."""
        return {
            "action": "show_tooltip",
            "data": event_data
        }
    
    def _default_click_handler(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Default click interaction handler."""
        return {
            "action": "highlight_point",
            "data": event_data
        }
    
    def _default_zoom_handler(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Default zoom interaction handler."""
        return {
            "action": "update_axis_range",
            "data": event_data
        }
    
    def _default_export_handler(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """Default export interaction handler."""
        return {
            "action": "download_chart",
            "format": event_data.get("format", "png")
        }
    
    async def get_chart(self, chart_id: str) -> Optional[InteractiveChart]:
        """Get chart by ID."""
        return self.chart_cache.get(chart_id)
    
    async def list_charts(self) -> List[InteractiveChart]:
        """List all charts."""
        return list(self.chart_cache.values())
    
    async def delete_chart(self, chart_id: str) -> bool:
        """Delete chart by ID."""
        if chart_id in self.chart_cache:
            del self.chart_cache[chart_id]
            # Clean up related callbacks
            callback_key = f"realtime_{chart_id}"
            if callback_key in self.interaction_callbacks:
                del self.interaction_callbacks[callback_key]
            logger.info(f"Deleted chart: {chart_id}")
            return True
        return False
    
    def get_engine_statistics(self) -> Dict[str, Any]:
        """Get interactive chart engine statistics."""
        total_charts = len(self.chart_cache)
        
        if total_charts == 0:
            return {
                "total_charts": 0,
                "charts_by_type": {},
                "total_interactions": 0,
                "real_time_charts": 0
            }
        
        # Count by type
        charts_by_type = {}
        total_interactions = 0
        real_time_charts = 0
        
        for chart in self.chart_cache.values():
            chart_type = chart.chart_type
            charts_by_type[chart_type] = charts_by_type.get(chart_type, 0) + 1
            total_interactions += len(chart.interactions)
            if chart.real_time:
                real_time_charts += 1
        
        return {
            "total_charts": total_charts,
            "charts_by_type": charts_by_type,
            "total_interactions": total_interactions,
            "real_time_charts": real_time_charts,
            "registered_callbacks": len(self.interaction_callbacks),
            "cache_size": len(self.chart_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup interactive chart engine."""
        self.chart_cache.clear()
        self.interaction_callbacks.clear()
        logger.info("Interactive chart engine cleaned up")

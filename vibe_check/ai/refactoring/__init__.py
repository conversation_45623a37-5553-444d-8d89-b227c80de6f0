"""
AI Refactoring Package
======================

This package provides AI-powered refactoring suggestions and code improvement.
"""

from .refactoring_engine import RefactoringSuggestionEngine, RefactoringType, RefactoringSeverity
from .code_smell_detector import Code<PERSON>mellDetector, CodeSmell, SmellSeverity
from .pattern_recommender import DesignPatternRecommender, DesignPattern, PatternCategory
from .impact_analyzer import RefactoringImpactAnalyzer, ImpactLevel, ImpactType

__all__ = [
    'RefactoringSuggestionEngine',
    'RefactoringType',
    'RefactoringSeverity',
    'CodeSmellDetector',
    'CodeSmell',
    'SmellSeverity',
    'DesignPatternRecommender',
    'DesignPattern',
    'PatternCategory',
    'RefactoringImpactAnalyzer',
    'ImpactLevel',
    'ImpactType'
]

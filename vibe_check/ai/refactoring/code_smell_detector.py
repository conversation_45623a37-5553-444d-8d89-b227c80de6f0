"""
File: vibe_check/ai/refactoring/code_smell_detector.py
Purpose: Code smell detection and remediation suggestions
Related Files: vibe_check/ai/refactoring/
Dependencies: typing, ast, re, enum, dataclasses
"""

import ast
import re
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class CodeSmell(Enum):
    """Types of code smells."""
    LONG_METHOD = "long_method"
    LARGE_CLASS = "large_class"
    LONG_PARAMETER_LIST = "long_parameter_list"
    DUPLICATE_CODE = "duplicate_code"
    DEAD_CODE = "dead_code"
    GOD_CLASS = "god_class"
    FEATURE_ENVY = "feature_envy"
    DATA_CLUMPS = "data_clumps"
    PRIMITIVE_OBSESSION = "primitive_obsession"
    SWITCH_STATEMENTS = "switch_statements"
    LAZY_CLASS = "lazy_class"
    SPECULATIVE_GENERALITY = "speculative_generality"
    TEMPORARY_FIELD = "temporary_field"
    MESSAGE_CHAINS = "message_chains"
    MIDDLE_MAN = "middle_man"
    INAPPROPRIATE_INTIMACY = "inappropriate_intimacy"
    REFUSED_BEQUEST = "refused_bequest"
    COMMENTS = "comments"
    MAGIC_NUMBERS = "magic_numbers"
    COMPLEX_CONDITIONAL = "complex_conditional"


class SmellSeverity(Enum):
    """Severity levels for code smells."""
    INFO = "info"         # Informational
    MINOR = "minor"       # Minor issue
    MAJOR = "major"       # Major issue
    CRITICAL = "critical" # Critical issue


@dataclass
class CodeSmellInstance:
    """A detected code smell instance."""
    smell_id: str
    smell_type: CodeSmell
    severity: SmellSeverity
    title: str
    description: str
    location: str  # Function, class, or module name
    line_start: int
    line_end: int
    confidence_score: float
    affected_code: str
    remediation_suggestions: List[str] = field(default_factory=list)
    impact_description: str = ""
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "smell_id": self.smell_id,
            "smell_type": self.smell_type.value,
            "severity": self.severity.value,
            "title": self.title,
            "description": self.description,
            "location": self.location,
            "line_start": self.line_start,
            "line_end": self.line_end,
            "confidence_score": self.confidence_score,
            "affected_code": self.affected_code,
            "remediation_suggestions": self.remediation_suggestions,
            "impact_description": self.impact_description
        }


@dataclass
class CodeSmellReport:
    """Comprehensive code smell detection report."""
    report_id: str
    source_code: str
    total_smells: int
    smells_by_type: Dict[str, int] = field(default_factory=dict)
    smells_by_severity: Dict[str, int] = field(default_factory=dict)
    code_quality_score: float = 0.0
    technical_debt_score: float = 0.0
    smell_instances: List[CodeSmellInstance] = field(default_factory=list)
    summary: str = ""
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "source_code": self.source_code[:200] + "..." if len(self.source_code) > 200 else self.source_code,
            "total_smells": self.total_smells,
            "smells_by_type": self.smells_by_type,
            "smells_by_severity": self.smells_by_severity,
            "code_quality_score": self.code_quality_score,
            "technical_debt_score": self.technical_debt_score,
            "smell_instances": [smell.to_dict() for smell in self.smell_instances],
            "summary": self.summary,
            "generated_at": self.generated_at.isoformat()
        }


class SmellDetectionRules:
    """Rules and thresholds for detecting code smells."""
    
    def __init__(self):
        self.thresholds = {
            "long_method_lines": 25,
            "large_class_methods": 20,
            "long_parameter_list": 5,
            "cyclomatic_complexity": 10,
            "nesting_depth": 4,
            "class_lines": 300,
            "duplicate_lines": 6,
            "magic_number_threshold": 3
        }
        
        self.magic_number_exceptions = {0, 1, -1, 2, 10, 100, 1000}
    
    def detect_long_method(self, func_node: ast.FunctionDef, code_lines: List[str]) -> Optional[CodeSmellInstance]:
        """Detect long method smell."""
        if not hasattr(func_node, 'end_lineno'):
            return None
        
        method_lines = func_node.end_lineno - func_node.lineno + 1
        
        if method_lines > self.thresholds["long_method_lines"]:
            return CodeSmellInstance(
                smell_id=f"long_method_{func_node.name}_{func_node.lineno}",
                smell_type=CodeSmell.LONG_METHOD,
                severity=SmellSeverity.MAJOR if method_lines > 50 else SmellSeverity.MINOR,
                title=f"Long method: {func_node.name}",
                description=f"Method has {method_lines} lines, exceeding the recommended {self.thresholds['long_method_lines']} lines.",
                location=func_node.name,
                line_start=func_node.lineno,
                line_end=func_node.end_lineno,
                confidence_score=0.9,
                affected_code=f"def {func_node.name}(...): # {method_lines} lines",
                remediation_suggestions=[
                    "Extract smaller methods from this large method",
                    "Identify logical groups of statements that can be extracted",
                    "Consider using the Extract Method refactoring pattern"
                ],
                impact_description="Long methods are harder to understand, test, and maintain"
            )
        
        return None
    
    def detect_large_class(self, class_node: ast.ClassDef, code_lines: List[str]) -> Optional[CodeSmellInstance]:
        """Detect large class smell."""
        methods = [node for node in class_node.body if isinstance(node, ast.FunctionDef)]
        method_count = len(methods)
        
        if not hasattr(class_node, 'end_lineno'):
            return None
        
        class_lines = class_node.end_lineno - class_node.lineno + 1
        
        if method_count > self.thresholds["large_class_methods"] or class_lines > self.thresholds["class_lines"]:
            severity = SmellSeverity.CRITICAL if method_count > 30 or class_lines > 500 else SmellSeverity.MAJOR
            
            return CodeSmellInstance(
                smell_id=f"large_class_{class_node.name}_{class_node.lineno}",
                smell_type=CodeSmell.LARGE_CLASS,
                severity=severity,
                title=f"Large class: {class_node.name}",
                description=f"Class has {method_count} methods and {class_lines} lines.",
                location=class_node.name,
                line_start=class_node.lineno,
                line_end=class_node.end_lineno,
                confidence_score=0.8,
                affected_code=f"class {class_node.name}: # {method_count} methods, {class_lines} lines",
                remediation_suggestions=[
                    "Extract related methods into separate classes",
                    "Identify single responsibilities and split the class",
                    "Consider using composition instead of inheritance"
                ],
                impact_description="Large classes violate Single Responsibility Principle and are hard to maintain"
            )
        
        return None
    
    def detect_long_parameter_list(self, func_node: ast.FunctionDef) -> Optional[CodeSmellInstance]:
        """Detect long parameter list smell."""
        param_count = len(func_node.args.args)
        
        if param_count > self.thresholds["long_parameter_list"]:
            return CodeSmellInstance(
                smell_id=f"long_params_{func_node.name}_{func_node.lineno}",
                smell_type=CodeSmell.LONG_PARAMETER_LIST,
                severity=SmellSeverity.MAJOR if param_count > 8 else SmellSeverity.MINOR,
                title=f"Long parameter list: {func_node.name}",
                description=f"Method has {param_count} parameters, exceeding the recommended {self.thresholds['long_parameter_list']}.",
                location=func_node.name,
                line_start=func_node.lineno,
                line_end=func_node.lineno,
                confidence_score=0.9,
                affected_code=f"def {func_node.name}({', '.join(arg.arg for arg in func_node.args.args)})",
                remediation_suggestions=[
                    "Group related parameters into a data class or dictionary",
                    "Use the Parameter Object pattern",
                    "Consider if the method is doing too much"
                ],
                impact_description="Long parameter lists make methods hard to call and understand"
            )
        
        return None
    
    def detect_magic_numbers(self, code: str) -> List[CodeSmellInstance]:
        """Detect magic numbers in code."""
        smells = []
        lines = code.split('\n')
        
        # Pattern to find numeric literals (excluding common exceptions)
        number_pattern = r'\b(\d+\.?\d*)\b'
        
        for line_num, line in enumerate(lines, 1):
            # Skip comments and strings
            if line.strip().startswith('#') or '"""' in line or "'''" in line:
                continue
            
            matches = re.finditer(number_pattern, line)
            for match in matches:
                try:
                    number = float(match.group(1))
                    if number not in self.magic_number_exceptions and number > self.thresholds["magic_number_threshold"]:
                        smells.append(CodeSmellInstance(
                            smell_id=f"magic_number_{line_num}_{match.start()}",
                            smell_type=CodeSmell.MAGIC_NUMBERS,
                            severity=SmellSeverity.MINOR,
                            title=f"Magic number: {number}",
                            description=f"Magic number {number} found on line {line_num}. Consider using a named constant.",
                            location=f"Line {line_num}",
                            line_start=line_num,
                            line_end=line_num,
                            confidence_score=0.7,
                            affected_code=line.strip(),
                            remediation_suggestions=[
                                f"Replace {number} with a named constant",
                                "Define the constant at module or class level",
                                "Add a comment explaining the significance of this value"
                            ],
                            impact_description="Magic numbers make code less readable and maintainable"
                        ))
                except ValueError:
                    continue
        
        return smells
    
    def detect_complex_conditionals(self, func_node: ast.FunctionDef) -> List[CodeSmellInstance]:
        """Detect complex conditional statements."""
        smells = []
        
        for node in ast.walk(func_node):
            if isinstance(node, ast.If):
                complexity = self._calculate_conditional_complexity(node.test)
                
                if complexity > 3:  # Threshold for complex conditionals
                    smells.append(CodeSmellInstance(
                        smell_id=f"complex_conditional_{func_node.name}_{node.lineno}",
                        smell_type=CodeSmell.COMPLEX_CONDITIONAL,
                        severity=SmellSeverity.MAJOR if complexity > 5 else SmellSeverity.MINOR,
                        title=f"Complex conditional in {func_node.name}",
                        description=f"Conditional statement has complexity score of {complexity}.",
                        location=func_node.name,
                        line_start=node.lineno,
                        line_end=getattr(node, 'end_lineno', node.lineno),
                        confidence_score=0.8,
                        affected_code="if complex_condition:",
                        remediation_suggestions=[
                            "Extract condition logic into well-named boolean variables",
                            "Break complex conditions into multiple simpler conditions",
                            "Consider using guard clauses to reduce nesting"
                        ],
                        impact_description="Complex conditionals are hard to understand and test"
                    ))
        
        return smells
    
    def _calculate_conditional_complexity(self, node: ast.expr) -> int:
        """Calculate complexity of a conditional expression."""
        if isinstance(node, ast.BoolOp):
            return 1 + sum(self._calculate_conditional_complexity(value) for value in node.values)
        elif isinstance(node, ast.Compare):
            return len(node.ops)
        elif isinstance(node, ast.UnaryOp) and isinstance(node.op, ast.Not):
            return 1 + self._calculate_conditional_complexity(node.operand)
        else:
            return 1
    
    def detect_duplicate_code(self, code: str) -> List[CodeSmellInstance]:
        """Detect potential duplicate code blocks."""
        smells = []
        lines = [line.strip() for line in code.split('\n') if line.strip() and not line.strip().startswith('#')]
        
        # Simple duplicate detection - look for identical sequences
        min_duplicate_length = self.thresholds["duplicate_lines"]
        
        for i in range(len(lines) - min_duplicate_length):
            for j in range(i + min_duplicate_length, len(lines) - min_duplicate_length):
                # Check for matching sequences
                match_length = 0
                for k in range(min_duplicate_length):
                    if i + k < len(lines) and j + k < len(lines) and lines[i + k] == lines[j + k]:
                        match_length += 1
                    else:
                        break
                
                if match_length >= min_duplicate_length:
                    smells.append(CodeSmellInstance(
                        smell_id=f"duplicate_code_{i}_{j}",
                        smell_type=CodeSmell.DUPLICATE_CODE,
                        severity=SmellSeverity.MAJOR,
                        title=f"Duplicate code detected",
                        description=f"Found {match_length} duplicate lines starting at lines {i+1} and {j+1}.",
                        location=f"Lines {i+1}-{i+match_length} and {j+1}-{j+match_length}",
                        line_start=i + 1,
                        line_end=i + match_length,
                        confidence_score=0.8,
                        affected_code=f"# {match_length} duplicate lines",
                        remediation_suggestions=[
                            "Extract duplicate code into a shared method",
                            "Use inheritance or composition to eliminate duplication",
                            "Consider using a template method pattern"
                        ],
                        impact_description="Duplicate code increases maintenance burden and bug risk"
                    ))
                    break  # Avoid multiple reports for the same duplicate
        
        return smells


class CodeSmellDetector:
    """Main code smell detection system."""
    
    def __init__(self):
        """Initialize code smell detector."""
        self.detection_rules = SmellDetectionRules()
        self.detection_cache: Dict[str, CodeSmellReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize code smell detector."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Code smell detector initialized")
    
    async def detect_smells(self, code: str) -> CodeSmellReport:
        """
        Detect code smells in the provided code.
        
        Args:
            code: Source code to analyze
            
        Returns:
            Comprehensive code smell report
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(code.encode()).hexdigest()
        
        if cache_key in self.detection_cache:
            logger.debug("Using cached code smell detection")
            return self.detection_cache[cache_key]
        
        try:
            # Parse code
            tree = ast.parse(code)
            code_lines = code.split('\n')
            smell_instances = []
            
            # Detect smells in functions
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    # Long method
                    long_method_smell = self.detection_rules.detect_long_method(node, code_lines)
                    if long_method_smell:
                        smell_instances.append(long_method_smell)
                    
                    # Long parameter list
                    long_params_smell = self.detection_rules.detect_long_parameter_list(node)
                    if long_params_smell:
                        smell_instances.append(long_params_smell)
                    
                    # Complex conditionals
                    complex_conditionals = self.detection_rules.detect_complex_conditionals(node)
                    smell_instances.extend(complex_conditionals)
                
                elif isinstance(node, ast.ClassDef):
                    # Large class
                    large_class_smell = self.detection_rules.detect_large_class(node, code_lines)
                    if large_class_smell:
                        smell_instances.append(large_class_smell)
            
            # Detect magic numbers
            magic_number_smells = self.detection_rules.detect_magic_numbers(code)
            smell_instances.extend(magic_number_smells)
            
            # Detect duplicate code
            duplicate_code_smells = self.detection_rules.detect_duplicate_code(code)
            smell_instances.extend(duplicate_code_smells)
            
            # Calculate statistics
            total_smells = len(smell_instances)
            smells_by_type = {}
            smells_by_severity = {}
            
            for smell in smell_instances:
                smell_type = smell.smell_type.value
                severity = smell.severity.value
                
                smells_by_type[smell_type] = smells_by_type.get(smell_type, 0) + 1
                smells_by_severity[severity] = smells_by_severity.get(severity, 0) + 1
            
            # Calculate quality scores
            code_quality_score = self._calculate_code_quality_score(smell_instances)
            technical_debt_score = self._calculate_technical_debt_score(smell_instances)
            
            # Generate summary
            summary = self._generate_summary(smell_instances)
            
            # Create report
            report = CodeSmellReport(
                report_id=cache_key,
                source_code=code,
                total_smells=total_smells,
                smells_by_type=smells_by_type,
                smells_by_severity=smells_by_severity,
                code_quality_score=code_quality_score,
                technical_debt_score=technical_debt_score,
                smell_instances=smell_instances,
                summary=summary
            )
            
            # Cache result
            self.detection_cache[cache_key] = report
            
            logger.info(f"Detected {total_smells} code smells")
            return report
            
        except SyntaxError as e:
            logger.warning(f"Failed to parse code for smell detection: {e}")
            return CodeSmellReport(
                report_id=cache_key,
                source_code=code,
                total_smells=0,
                summary=f"Code parsing failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Code smell detection failed: {e}")
            return CodeSmellReport(
                report_id=cache_key,
                source_code=code,
                total_smells=0,
                summary=f"Analysis failed: {str(e)}"
            )
    
    def _calculate_code_quality_score(self, smells: List[CodeSmellInstance]) -> float:
        """Calculate code quality score (0.0 = poor, 1.0 = excellent)."""
        if not smells:
            return 1.0
        
        # Weight by severity
        severity_weights = {
            SmellSeverity.INFO: 0.1,
            SmellSeverity.MINOR: 0.3,
            SmellSeverity.MAJOR: 0.7,
            SmellSeverity.CRITICAL: 1.0
        }
        
        total_weight = sum(severity_weights[smell.severity] for smell in smells)
        max_possible_weight = len(smells) * 1.0  # If all were critical
        
        # Return inverted score (1.0 = perfect quality)
        return max(0.0, 1.0 - (total_weight / max_possible_weight))
    
    def _calculate_technical_debt_score(self, smells: List[CodeSmellInstance]) -> float:
        """Calculate technical debt score (0.0 = no debt, 1.0 = high debt)."""
        if not smells:
            return 0.0
        
        # Technical debt increases with number and severity of smells
        debt_weights = {
            SmellSeverity.INFO: 0.1,
            SmellSeverity.MINOR: 0.2,
            SmellSeverity.MAJOR: 0.5,
            SmellSeverity.CRITICAL: 1.0
        }
        
        total_debt = sum(debt_weights[smell.severity] for smell in smells)
        
        # Normalize to 0-1 scale (assuming max 10 critical smells = 1.0 debt)
        return min(1.0, total_debt / 10.0)
    
    def _generate_summary(self, smells: List[CodeSmellInstance]) -> str:
        """Generate summary of code smell analysis."""
        if not smells:
            return "Code analysis complete. No code smells detected."
        
        critical_count = len([s for s in smells if s.severity == SmellSeverity.CRITICAL])
        major_count = len([s for s in smells if s.severity == SmellSeverity.MAJOR])
        minor_count = len([s for s in smells if s.severity == SmellSeverity.MINOR])
        info_count = len([s for s in smells if s.severity == SmellSeverity.INFO])
        
        summary_parts = [f"Detected {len(smells)} code smells:"]
        
        if critical_count > 0:
            summary_parts.append(f"{critical_count} critical issues")
        if major_count > 0:
            summary_parts.append(f"{major_count} major issues")
        if minor_count > 0:
            summary_parts.append(f"{minor_count} minor issues")
        if info_count > 0:
            summary_parts.append(f"{info_count} informational items")
        
        return " ".join(summary_parts) + "."
    
    def get_detection_statistics(self) -> Dict[str, Any]:
        """Get code smell detection statistics."""
        total_reports = len(self.detection_cache)
        
        if total_reports == 0:
            return {
                "total_reports": 0,
                "total_smells": 0,
                "avg_quality_score": 0.0,
                "avg_debt_score": 0.0,
                "smells_by_type": {},
                "smells_by_severity": {}
            }
        
        total_smells = sum(report.total_smells for report in self.detection_cache.values())
        total_quality = sum(report.code_quality_score for report in self.detection_cache.values())
        total_debt = sum(report.technical_debt_score for report in self.detection_cache.values())
        
        # Aggregate smells by type and severity
        all_smells_by_type = {}
        all_smells_by_severity = {}
        
        for report in self.detection_cache.values():
            for smell_type, count in report.smells_by_type.items():
                all_smells_by_type[smell_type] = all_smells_by_type.get(smell_type, 0) + count
            
            for severity, count in report.smells_by_severity.items():
                all_smells_by_severity[severity] = all_smells_by_severity.get(severity, 0) + count
        
        return {
            "total_reports": total_reports,
            "total_smells": total_smells,
            "avg_quality_score": total_quality / total_reports,
            "avg_debt_score": total_debt / total_reports,
            "smells_by_type": all_smells_by_type,
            "smells_by_severity": all_smells_by_severity,
            "cache_size": len(self.detection_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup code smell detector."""
        self.detection_cache.clear()
        logger.info("Code smell detector cleaned up")

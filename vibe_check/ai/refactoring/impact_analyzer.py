"""
File: vibe_check/ai/refactoring/impact_analyzer.py
Purpose: Refactoring impact analysis and risk assessment
Related Files: vibe_check/ai/refactoring/
Dependencies: typing, ast, enum, dataclasses
"""

import ast
from typing import Dict, List, Optional, Any, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ImpactLevel(Enum):
    """Impact levels for refactoring changes."""
    MINIMAL = "minimal"       # Very low impact
    LOW = "low"              # Low impact
    MEDIUM = "medium"        # Medium impact
    HIGH = "high"            # High impact
    CRITICAL = "critical"    # Critical impact


class ImpactType(Enum):
    """Types of refactoring impact."""
    FUNCTIONALITY = "functionality"     # Changes to functionality
    PERFORMANCE = "performance"         # Performance implications
    MAINTAINABILITY = "maintainability" # Maintainability changes
    TESTABILITY = "testability"         # Testing implications
    COMPATIBILITY = "compatibility"     # Backward compatibility
    SECURITY = "security"               # Security implications
    DEPENDENCIES = "dependencies"       # Dependency changes


@dataclass
class ImpactAssessment:
    """Assessment of refactoring impact."""
    impact_id: str
    impact_type: ImpactType
    impact_level: ImpactLevel
    title: str
    description: str
    affected_components: List[str] = field(default_factory=list)
    risk_factors: List[str] = field(default_factory=list)
    mitigation_strategies: List[str] = field(default_factory=list)
    testing_requirements: List[str] = field(default_factory=list)
    estimated_effort_hours: float = 0.0
    confidence_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "impact_id": self.impact_id,
            "impact_type": self.impact_type.value,
            "impact_level": self.impact_level.value,
            "title": self.title,
            "description": self.description,
            "affected_components": self.affected_components,
            "risk_factors": self.risk_factors,
            "mitigation_strategies": self.mitigation_strategies,
            "testing_requirements": self.testing_requirements,
            "estimated_effort_hours": self.estimated_effort_hours,
            "confidence_score": self.confidence_score
        }


@dataclass
class RefactoringImpactReport:
    """Comprehensive refactoring impact analysis report."""
    report_id: str
    refactoring_description: str
    overall_impact_level: ImpactLevel
    total_estimated_effort: float
    risk_score: float = 0.0
    success_probability: float = 0.0
    impact_assessments: List[ImpactAssessment] = field(default_factory=list)
    dependencies_affected: List[str] = field(default_factory=list)
    testing_strategy: List[str] = field(default_factory=list)
    rollback_plan: List[str] = field(default_factory=list)
    recommendations: List[str] = field(default_factory=list)
    generated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "report_id": self.report_id,
            "refactoring_description": self.refactoring_description,
            "overall_impact_level": self.overall_impact_level.value,
            "total_estimated_effort": self.total_estimated_effort,
            "risk_score": self.risk_score,
            "success_probability": self.success_probability,
            "impact_assessments": [assessment.to_dict() for assessment in self.impact_assessments],
            "dependencies_affected": self.dependencies_affected,
            "testing_strategy": self.testing_strategy,
            "rollback_plan": self.rollback_plan,
            "recommendations": self.recommendations,
            "generated_at": self.generated_at.isoformat()
        }


class DependencyAnalyzer:
    """Analyzes code dependencies for impact assessment."""
    
    def __init__(self):
        self.dependency_graph: Dict[str, Set[str]] = {}
        self.reverse_dependencies: Dict[str, Set[str]] = {}
    
    def analyze_dependencies(self, code: str) -> Dict[str, Any]:
        """Analyze code dependencies."""
        try:
            tree = ast.parse(code)
            dependencies = {
                "imports": [],
                "function_calls": [],
                "class_usage": [],
                "variable_references": [],
                "internal_dependencies": {}
            }
            
            # Track imports
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        dependencies["imports"].append(alias.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for alias in node.names:
                        dependencies["imports"].append(f"{module}.{alias.name}")
                
                elif isinstance(node, ast.Call):
                    if isinstance(node.func, ast.Name):
                        dependencies["function_calls"].append(node.func.id)
                    elif isinstance(node.func, ast.Attribute):
                        dependencies["function_calls"].append(node.func.attr)
                
                elif isinstance(node, ast.Name) and isinstance(node.ctx, ast.Load):
                    dependencies["variable_references"].append(node.id)
            
            # Analyze internal dependencies between functions and classes
            functions = {}
            classes = {}
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    functions[node.name] = {
                        "line": node.lineno,
                        "calls": self._extract_function_calls(node)
                    }
                elif isinstance(node, ast.ClassDef):
                    classes[node.name] = {
                        "line": node.lineno,
                        "methods": [n.name for n in node.body if isinstance(n, ast.FunctionDef)]
                    }
            
            dependencies["internal_dependencies"] = {
                "functions": functions,
                "classes": classes
            }
            
            return dependencies
            
        except SyntaxError as e:
            logger.warning(f"Failed to parse code for dependency analysis: {e}")
            return {"error": "Invalid Python syntax"}
        except Exception as e:
            logger.error(f"Dependency analysis failed: {e}")
            return {"error": str(e)}
    
    def _extract_function_calls(self, func_node: ast.FunctionDef) -> List[str]:
        """Extract function calls within a function."""
        calls = []
        for node in ast.walk(func_node):
            if isinstance(node, ast.Call):
                if isinstance(node.func, ast.Name):
                    calls.append(node.func.id)
                elif isinstance(node.func, ast.Attribute):
                    calls.append(node.func.attr)
        return calls
    
    def calculate_impact_radius(self, component_name: str, dependencies: Dict[str, Any]) -> List[str]:
        """Calculate the impact radius of changing a component."""
        affected = set()
        
        # Direct dependencies
        internal_deps = dependencies.get("internal_dependencies", {})
        functions = internal_deps.get("functions", {})
        classes = internal_deps.get("classes", {})
        
        # Find components that depend on the changed component
        for func_name, func_info in functions.items():
            if component_name in func_info.get("calls", []):
                affected.add(func_name)
        
        # Find classes that might be affected
        for class_name, class_info in classes.items():
            if component_name in class_info.get("methods", []):
                affected.add(class_name)
        
        return list(affected)


class ImpactAnalysisRules:
    """Rules for analyzing refactoring impact."""
    
    def __init__(self):
        self.effort_estimates = {
            "extract_method": 2.0,      # hours
            "extract_class": 8.0,
            "rename_variable": 0.5,
            "rename_method": 1.0,
            "simplify_conditional": 1.5,
            "remove_duplication": 4.0,
            "split_large_function": 6.0,
            "add_type_hints": 1.0
        }
        
        self.risk_factors = {
            "public_api_change": 0.8,
            "complex_logic": 0.6,
            "external_dependencies": 0.7,
            "no_tests": 0.9,
            "legacy_code": 0.8
        }
    
    def assess_functionality_impact(self, refactoring_type: str, affected_components: List[str]) -> ImpactAssessment:
        """Assess functionality impact of refactoring."""
        if refactoring_type in ["extract_method", "extract_class"]:
            impact_level = ImpactLevel.MEDIUM
            risk_factors = ["Logic extraction may introduce bugs", "Interface changes required"]
        elif refactoring_type in ["rename_variable", "rename_method"]:
            impact_level = ImpactLevel.LOW
            risk_factors = ["Potential missed references", "IDE refactoring limitations"]
        else:
            impact_level = ImpactLevel.MEDIUM
            risk_factors = ["Behavior changes possible", "Logic modifications required"]
        
        return ImpactAssessment(
            impact_id=f"functionality_{refactoring_type}",
            impact_type=ImpactType.FUNCTIONALITY,
            impact_level=impact_level,
            title=f"Functionality impact of {refactoring_type}",
            description=f"Refactoring may affect functionality of {len(affected_components)} components",
            affected_components=affected_components,
            risk_factors=risk_factors,
            mitigation_strategies=[
                "Comprehensive unit testing before and after",
                "Code review with domain experts",
                "Incremental implementation with validation"
            ],
            testing_requirements=[
                "Unit tests for all affected components",
                "Integration tests for component interactions",
                "Regression testing for existing functionality"
            ],
            estimated_effort_hours=self.effort_estimates.get(refactoring_type, 4.0),
            confidence_score=0.8
        )
    
    def assess_performance_impact(self, refactoring_type: str, code_complexity: int) -> ImpactAssessment:
        """Assess performance impact of refactoring."""
        if refactoring_type in ["extract_method", "extract_class"]:
            impact_level = ImpactLevel.LOW
            description = "Method extraction may add minor call overhead"
        elif refactoring_type == "remove_duplication":
            impact_level = ImpactLevel.LOW
            description = "Removing duplication may improve performance"
        else:
            impact_level = ImpactLevel.MINIMAL
            description = "Minimal performance impact expected"
        
        return ImpactAssessment(
            impact_id=f"performance_{refactoring_type}",
            impact_type=ImpactType.PERFORMANCE,
            impact_level=impact_level,
            title=f"Performance impact of {refactoring_type}",
            description=description,
            risk_factors=["Additional method calls", "Memory allocation changes"],
            mitigation_strategies=[
                "Performance benchmarking before and after",
                "Profile critical paths",
                "Monitor production metrics"
            ],
            testing_requirements=[
                "Performance tests for critical paths",
                "Memory usage analysis",
                "Load testing if applicable"
            ],
            estimated_effort_hours=1.0,
            confidence_score=0.7
        )
    
    def assess_maintainability_impact(self, refactoring_type: str) -> ImpactAssessment:
        """Assess maintainability impact of refactoring."""
        # Most refactorings improve maintainability
        impact_level = ImpactLevel.LOW  # Positive impact
        
        benefits = {
            "extract_method": "Improved code organization and reusability",
            "extract_class": "Better separation of concerns",
            "rename_variable": "Improved code readability",
            "simplify_conditional": "Reduced complexity",
            "remove_duplication": "Reduced maintenance burden"
        }
        
        description = benefits.get(refactoring_type, "Improved code maintainability")
        
        return ImpactAssessment(
            impact_id=f"maintainability_{refactoring_type}",
            impact_type=ImpactType.MAINTAINABILITY,
            impact_level=impact_level,
            title=f"Maintainability impact of {refactoring_type}",
            description=description,
            risk_factors=["Temporary increase in complexity during transition"],
            mitigation_strategies=[
                "Clear documentation of changes",
                "Team training on new structure",
                "Gradual migration approach"
            ],
            testing_requirements=[
                "Code quality metrics validation",
                "Documentation updates",
                "Team review and approval"
            ],
            estimated_effort_hours=0.5,
            confidence_score=0.9
        )
    
    def assess_testability_impact(self, refactoring_type: str, has_tests: bool) -> ImpactAssessment:
        """Assess testability impact of refactoring."""
        if refactoring_type in ["extract_method", "extract_class"]:
            impact_level = ImpactLevel.MEDIUM
            description = "New components need test coverage"
        elif not has_tests:
            impact_level = ImpactLevel.HIGH
            description = "Refactoring without tests increases risk significantly"
        else:
            impact_level = ImpactLevel.LOW
            description = "Existing tests may need updates"
        
        return ImpactAssessment(
            impact_id=f"testability_{refactoring_type}",
            impact_type=ImpactType.TESTABILITY,
            impact_level=impact_level,
            title=f"Testability impact of {refactoring_type}",
            description=description,
            risk_factors=["Test coverage gaps", "Test maintenance overhead"],
            mitigation_strategies=[
                "Write tests before refactoring",
                "Maintain test coverage metrics",
                "Update test documentation"
            ],
            testing_requirements=[
                "New unit tests for extracted components",
                "Update existing test cases",
                "Verify test coverage maintains threshold"
            ],
            estimated_effort_hours=2.0 if not has_tests else 1.0,
            confidence_score=0.8
        )


class RefactoringImpactAnalyzer:
    """Refactoring impact analysis and risk assessment system."""
    
    def __init__(self):
        """Initialize refactoring impact analyzer."""
        self.dependency_analyzer = DependencyAnalyzer()
        self.analysis_rules = ImpactAnalysisRules()
        self.analysis_cache: Dict[str, RefactoringImpactReport] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize refactoring impact analyzer."""
        if self._initialized:
            return
        
        self._initialized = True
        logger.info("Refactoring impact analyzer initialized")
    
    async def analyze_impact(
        self,
        code: str,
        refactoring_type: str,
        target_component: str,
        refactoring_description: str = ""
    ) -> RefactoringImpactReport:
        """
        Analyze the impact of a proposed refactoring.
        
        Args:
            code: Source code to analyze
            refactoring_type: Type of refactoring (e.g., "extract_method")
            target_component: Component being refactored
            refactoring_description: Description of the refactoring
            
        Returns:
            Comprehensive impact analysis report
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(
            f"{code}{refactoring_type}{target_component}".encode()
        ).hexdigest()
        
        if cache_key in self.analysis_cache:
            logger.debug("Using cached impact analysis")
            return self.analysis_cache[cache_key]
        
        try:
            # Analyze dependencies
            dependencies = self.dependency_analyzer.analyze_dependencies(code)
            
            if "error" in dependencies:
                raise ValueError(f"Dependency analysis failed: {dependencies['error']}")
            
            # Calculate impact radius
            affected_components = self.dependency_analyzer.calculate_impact_radius(
                target_component, dependencies
            )
            
            # Assess different types of impact
            impact_assessments = []
            
            # Functionality impact
            functionality_impact = self.analysis_rules.assess_functionality_impact(
                refactoring_type, affected_components
            )
            impact_assessments.append(functionality_impact)
            
            # Performance impact
            code_complexity = len(code.split('\n'))  # Simple complexity measure
            performance_impact = self.analysis_rules.assess_performance_impact(
                refactoring_type, code_complexity
            )
            impact_assessments.append(performance_impact)
            
            # Maintainability impact
            maintainability_impact = self.analysis_rules.assess_maintainability_impact(
                refactoring_type
            )
            impact_assessments.append(maintainability_impact)
            
            # Testability impact
            has_tests = "test" in code.lower() or "assert" in code.lower()
            testability_impact = self.analysis_rules.assess_testability_impact(
                refactoring_type, has_tests
            )
            impact_assessments.append(testability_impact)
            
            # Calculate overall metrics
            overall_impact_level = self._calculate_overall_impact_level(impact_assessments)
            total_estimated_effort = sum(assessment.estimated_effort_hours for assessment in impact_assessments)
            risk_score = self._calculate_risk_score(impact_assessments, dependencies)
            success_probability = self._calculate_success_probability(risk_score, has_tests)
            
            # Generate strategies and recommendations
            testing_strategy = self._generate_testing_strategy(impact_assessments)
            rollback_plan = self._generate_rollback_plan(refactoring_type)
            recommendations = self._generate_recommendations(impact_assessments, risk_score)
            
            # Create report
            report = RefactoringImpactReport(
                report_id=cache_key,
                refactoring_description=refactoring_description or f"{refactoring_type} on {target_component}",
                overall_impact_level=overall_impact_level,
                total_estimated_effort=total_estimated_effort,
                risk_score=risk_score,
                success_probability=success_probability,
                impact_assessments=impact_assessments,
                dependencies_affected=affected_components,
                testing_strategy=testing_strategy,
                rollback_plan=rollback_plan,
                recommendations=recommendations
            )
            
            # Cache result
            self.analysis_cache[cache_key] = report
            
            logger.info(f"Analyzed refactoring impact: {overall_impact_level.value} level, {risk_score:.2f} risk score")
            return report
            
        except Exception as e:
            logger.error(f"Refactoring impact analysis failed: {e}")
            # Return minimal report on error
            return RefactoringImpactReport(
                report_id=cache_key,
                refactoring_description=refactoring_description or "Unknown refactoring",
                overall_impact_level=ImpactLevel.HIGH,  # Conservative estimate
                total_estimated_effort=8.0,  # Conservative estimate
                risk_score=0.8,
                success_probability=0.3,
                recommendations=[f"Analysis failed: {str(e)}"]
            )
    
    def _calculate_overall_impact_level(self, assessments: List[ImpactAssessment]) -> ImpactLevel:
        """Calculate overall impact level from individual assessments."""
        impact_weights = {
            ImpactLevel.MINIMAL: 1,
            ImpactLevel.LOW: 2,
            ImpactLevel.MEDIUM: 3,
            ImpactLevel.HIGH: 4,
            ImpactLevel.CRITICAL: 5
        }
        
        # Take the maximum impact level
        max_impact = max(impact_weights[assessment.impact_level] for assessment in assessments)
        
        for level, weight in impact_weights.items():
            if weight == max_impact:
                return level
        
        return ImpactLevel.MEDIUM  # Default
    
    def _calculate_risk_score(self, assessments: List[ImpactAssessment], dependencies: Dict[str, Any]) -> float:
        """Calculate overall risk score (0.0 = low risk, 1.0 = high risk)."""
        # Base risk from impact levels
        impact_risk = sum(
            len(assessment.risk_factors) * 0.1 for assessment in assessments
        ) / len(assessments) if assessments else 0.0
        
        # Dependency risk
        external_deps = len(dependencies.get("imports", []))
        dependency_risk = min(0.3, external_deps * 0.02)  # Cap at 0.3
        
        # Combine risks
        total_risk = min(1.0, impact_risk + dependency_risk)
        
        return total_risk
    
    def _calculate_success_probability(self, risk_score: float, has_tests: bool) -> float:
        """Calculate probability of successful refactoring."""
        base_probability = 0.8  # Base success rate
        
        # Reduce probability based on risk
        risk_penalty = risk_score * 0.4
        
        # Bonus for having tests
        test_bonus = 0.2 if has_tests else 0.0
        
        return max(0.1, min(1.0, base_probability - risk_penalty + test_bonus))
    
    def _generate_testing_strategy(self, assessments: List[ImpactAssessment]) -> List[str]:
        """Generate testing strategy based on impact assessments."""
        strategy = set()
        
        for assessment in assessments:
            strategy.update(assessment.testing_requirements)
        
        # Add general testing recommendations
        strategy.add("Run full test suite before and after refactoring")
        strategy.add("Create backup of current implementation")
        strategy.add("Test in staging environment first")
        
        return list(strategy)
    
    def _generate_rollback_plan(self, refactoring_type: str) -> List[str]:
        """Generate rollback plan for the refactoring."""
        general_plan = [
            "Maintain version control checkpoint before refactoring",
            "Document all changes made during refactoring",
            "Keep backup of original implementation",
            "Test rollback procedure in staging environment"
        ]
        
        specific_plans = {
            "extract_method": [
                "Inline extracted method if issues arise",
                "Restore original method implementation"
            ],
            "extract_class": [
                "Move extracted functionality back to original class",
                "Update all references to use original structure"
            ],
            "rename_variable": [
                "Use IDE refactoring tools to revert naming",
                "Search and replace to restore original names"
            ]
        }
        
        plan = general_plan + specific_plans.get(refactoring_type, [])
        return plan
    
    def _generate_recommendations(self, assessments: List[ImpactAssessment], risk_score: float) -> List[str]:
        """Generate recommendations based on impact analysis."""
        recommendations = []
        
        if risk_score > 0.7:
            recommendations.append("High risk refactoring - consider breaking into smaller steps")
            recommendations.append("Increase test coverage before proceeding")
        
        if risk_score > 0.5:
            recommendations.append("Perform refactoring in feature branch")
            recommendations.append("Get additional code review")
        
        # Add specific recommendations from assessments
        for assessment in assessments:
            recommendations.extend(assessment.mitigation_strategies)
        
        # Remove duplicates while preserving order
        seen = set()
        unique_recommendations = []
        for rec in recommendations:
            if rec not in seen:
                seen.add(rec)
                unique_recommendations.append(rec)
        
        return unique_recommendations
    
    def get_analysis_statistics(self) -> Dict[str, Any]:
        """Get impact analysis statistics."""
        total_analyses = len(self.analysis_cache)
        
        if total_analyses == 0:
            return {
                "total_analyses": 0,
                "avg_risk_score": 0.0,
                "avg_success_probability": 0.0,
                "avg_estimated_effort": 0.0,
                "impact_levels": {}
            }
        
        total_risk = sum(report.risk_score for report in self.analysis_cache.values())
        total_success = sum(report.success_probability for report in self.analysis_cache.values())
        total_effort = sum(report.total_estimated_effort for report in self.analysis_cache.values())
        
        # Count impact levels
        impact_levels = {}
        for report in self.analysis_cache.values():
            level = report.overall_impact_level.value
            impact_levels[level] = impact_levels.get(level, 0) + 1
        
        return {
            "total_analyses": total_analyses,
            "avg_risk_score": total_risk / total_analyses,
            "avg_success_probability": total_success / total_analyses,
            "avg_estimated_effort": total_effort / total_analyses,
            "impact_levels": impact_levels,
            "cache_size": len(self.analysis_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup refactoring impact analyzer."""
        self.analysis_cache.clear()
        logger.info("Refactoring impact analyzer cleaned up")

"""
File: vibe_check/ai/explanation/explanation_engine.py
Purpose: AI-powered code explanation engine
Related Files: vibe_check/ai/explanation/
Dependencies: typing, asyncio, ast, enum, dataclasses
"""

import ast
import asyncio
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from vibe_check.ai.infrastructure import AIModelManager, PrivacyPreservingProcessor, PrivacyLevel
from vibe_check.core.logging import get_logger

logger = get_logger(__name__)


class ExplanationLevel(Enum):
    """Code explanation detail levels."""
    BRIEF = "brief"              # High-level overview
    DETAILED = "detailed"        # Detailed explanation
    COMPREHENSIVE = "comprehensive"  # Comprehensive with examples
    BEGINNER = "beginner"        # Beginner-friendly explanation


class ExplanationType(Enum):
    """Types of code explanations."""
    OVERVIEW = "overview"        # General code overview
    FUNCTION = "function"        # Function-specific explanation
    CLASS = "class"             # Class-specific explanation
    ALGORITHM = "algorithm"      # Algorithm explanation
    PATTERN = "pattern"         # Design pattern explanation
    SECURITY = "security"       # Security-focused explanation


@dataclass
class CodeExplanation:
    """Code explanation result."""
    explanation_id: str
    code_snippet: str
    explanation_type: ExplanationType
    explanation_level: ExplanationLevel
    explanation_text: str
    key_concepts: List[str] = field(default_factory=list)
    complexity_analysis: Optional[str] = None
    best_practices: List[str] = field(default_factory=list)
    potential_issues: List[str] = field(default_factory=list)
    related_patterns: List[str] = field(default_factory=list)
    generated_at: datetime = field(default_factory=datetime.now)
    confidence_score: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "explanation_id": self.explanation_id,
            "code_snippet": self.code_snippet,
            "explanation_type": self.explanation_type.value,
            "explanation_level": self.explanation_level.value,
            "explanation_text": self.explanation_text,
            "key_concepts": self.key_concepts,
            "complexity_analysis": self.complexity_analysis,
            "best_practices": self.best_practices,
            "potential_issues": self.potential_issues,
            "related_patterns": self.related_patterns,
            "generated_at": self.generated_at.isoformat(),
            "confidence_score": self.confidence_score
        }


class CodeAnalyzer:
    """Code analysis helper for explanation engine."""
    
    def __init__(self):
        self.function_patterns = {
            'recursive': ['return.*\\w+\\(.*\\w+.*\\)'],
            'generator': ['yield'],
            'decorator': ['@\\w+'],
            'async': ['async def', 'await'],
            'lambda': ['lambda'],
            'comprehension': ['\\[.*for.*in.*\\]', '\\{.*for.*in.*\\}']
        }
    
    def analyze_code_structure(self, code: str) -> Dict[str, Any]:
        """Analyze code structure and patterns."""
        try:
            tree = ast.parse(code)
            analysis = {
                "functions": [],
                "classes": [],
                "imports": [],
                "complexity_indicators": [],
                "patterns_detected": []
            }
            
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    analysis["functions"].append({
                        "name": node.name,
                        "args": len(node.args.args),
                        "is_async": isinstance(node, ast.AsyncFunctionDef),
                        "has_decorators": len(node.decorator_list) > 0
                    })
                
                elif isinstance(node, ast.ClassDef):
                    analysis["classes"].append({
                        "name": node.name,
                        "methods": len([n for n in node.body if isinstance(n, ast.FunctionDef)]),
                        "has_inheritance": len(node.bases) > 0
                    })
                
                elif isinstance(node, (ast.Import, ast.ImportFrom)):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis["imports"].append(alias.name)
                    else:
                        analysis["imports"].append(node.module or "")
            
            # Detect complexity indicators
            if len(analysis["functions"]) > 5:
                analysis["complexity_indicators"].append("Multiple functions")
            
            if any(f["args"] > 5 for f in analysis["functions"]):
                analysis["complexity_indicators"].append("Functions with many parameters")
            
            if len(analysis["classes"]) > 0:
                analysis["complexity_indicators"].append("Object-oriented design")
            
            return analysis
            
        except SyntaxError:
            logger.warning("Failed to parse code for structure analysis")
            return {"error": "Invalid Python syntax"}
        except Exception as e:
            logger.error(f"Code analysis failed: {e}")
            return {"error": str(e)}
    
    def detect_patterns(self, code: str) -> List[str]:
        """Detect programming patterns in code."""
        patterns = []
        
        # Check for common patterns
        if 'class' in code and '__init__' in code:
            patterns.append("Constructor Pattern")
        
        if 'yield' in code:
            patterns.append("Generator Pattern")
        
        if '@' in code and 'def' in code:
            patterns.append("Decorator Pattern")
        
        if 'try:' in code and 'except' in code:
            patterns.append("Exception Handling")
        
        if 'with' in code and 'as' in code:
            patterns.append("Context Manager")
        
        if 'async def' in code or 'await' in code:
            patterns.append("Asynchronous Programming")
        
        return patterns
    
    def estimate_complexity(self, code: str) -> str:
        """Estimate code complexity."""
        lines = len(code.split('\n'))
        
        if lines <= 10:
            return "Low complexity - Simple and straightforward"
        elif lines <= 30:
            return "Medium complexity - Moderate logic flow"
        elif lines <= 100:
            return "High complexity - Complex logic with multiple components"
        else:
            return "Very high complexity - Large codebase requiring careful analysis"


class CodeExplanationEngine:
    """AI-powered code explanation engine."""
    
    def __init__(self, model_manager: Optional[AIModelManager] = None):
        """
        Initialize code explanation engine.
        
        Args:
            model_manager: AI model manager instance
        """
        self.model_manager = model_manager
        self.privacy_processor = PrivacyPreservingProcessor(PrivacyLevel.STANDARD)
        self.code_analyzer = CodeAnalyzer()
        self.explanation_cache: Dict[str, CodeExplanation] = {}
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize code explanation engine."""
        if self._initialized:
            return
        
        if not self.model_manager:
            self.model_manager = AIModelManager()
            await self.model_manager.initialize()
        
        await self.privacy_processor.initialize()
        
        self._initialized = True
        logger.info("Code explanation engine initialized")
    
    async def explain_code(
        self,
        code: str,
        explanation_type: ExplanationType = ExplanationType.OVERVIEW,
        explanation_level: ExplanationLevel = ExplanationLevel.DETAILED,
        preserve_privacy: bool = True
    ) -> CodeExplanation:
        """
        Generate AI-powered code explanation.
        
        Args:
            code: Code to explain
            explanation_type: Type of explanation
            explanation_level: Detail level
            preserve_privacy: Whether to apply privacy protection
            
        Returns:
            Code explanation result
        """
        # Generate cache key
        import hashlib
        cache_key = hashlib.md5(
            f"{code}{explanation_type.value}{explanation_level.value}".encode()
        ).hexdigest()
        
        if cache_key in self.explanation_cache:
            logger.debug("Using cached explanation")
            return self.explanation_cache[cache_key]
        
        try:
            # Process code with privacy protection if requested
            processed_code = code
            if preserve_privacy:
                privacy_result = await self.privacy_processor.process_code_safely(
                    code, preserve_structure=True
                )
                processed_code = privacy_result["processed_code"]
            
            # Analyze code structure
            structure_analysis = self.code_analyzer.analyze_code_structure(processed_code)
            detected_patterns = self.code_analyzer.detect_patterns(processed_code)
            complexity = self.code_analyzer.estimate_complexity(processed_code)
            
            # Generate AI explanation
            explanation_text = await self._generate_ai_explanation(
                processed_code, explanation_type, explanation_level, structure_analysis
            )
            
            # Extract key concepts
            key_concepts = self._extract_key_concepts(processed_code, structure_analysis)
            
            # Generate best practices and potential issues
            best_practices = self._generate_best_practices(processed_code, explanation_type)
            potential_issues = self._identify_potential_issues(processed_code, structure_analysis)
            
            # Create explanation result
            explanation = CodeExplanation(
                explanation_id=cache_key,
                code_snippet=code[:500] + "..." if len(code) > 500 else code,
                explanation_type=explanation_type,
                explanation_level=explanation_level,
                explanation_text=explanation_text,
                key_concepts=key_concepts,
                complexity_analysis=complexity,
                best_practices=best_practices,
                potential_issues=potential_issues,
                related_patterns=detected_patterns,
                confidence_score=0.85  # Mock confidence score
            )
            
            # Cache result
            self.explanation_cache[cache_key] = explanation
            
            logger.info(f"Generated {explanation_type.value} explanation ({explanation_level.value} level)")
            return explanation
            
        except Exception as e:
            logger.error(f"Failed to generate code explanation: {e}")
            # Return basic explanation on error
            return CodeExplanation(
                explanation_id=cache_key,
                code_snippet=code[:100] + "..." if len(code) > 100 else code,
                explanation_type=explanation_type,
                explanation_level=explanation_level,
                explanation_text=f"Unable to generate detailed explanation. Error: {str(e)}",
                confidence_score=0.0
            )
    
    async def _generate_ai_explanation(
        self,
        code: str,
        explanation_type: ExplanationType,
        explanation_level: ExplanationLevel,
        structure_analysis: Dict[str, Any]
    ) -> str:
        """Generate AI-powered explanation text."""
        if not self.model_manager:
            return self._generate_fallback_explanation(code, explanation_type, explanation_level)
        
        # Construct prompt based on explanation type and level
        prompt = self._build_explanation_prompt(code, explanation_type, explanation_level, structure_analysis)
        
        try:
            # Use code explanation model
            response = await self.model_manager.generate_response(
                "code_explainer_v1",
                prompt,
                {"temperature": 0.3, "max_tokens": 1024}
            )
            
            return response or self._generate_fallback_explanation(code, explanation_type, explanation_level)
            
        except Exception as e:
            logger.warning(f"AI explanation generation failed: {e}")
            return self._generate_fallback_explanation(code, explanation_type, explanation_level)
    
    def _build_explanation_prompt(
        self,
        code: str,
        explanation_type: ExplanationType,
        explanation_level: ExplanationLevel,
        structure_analysis: Dict[str, Any]
    ) -> str:
        """Build explanation prompt for AI model."""
        level_instructions = {
            ExplanationLevel.BRIEF: "Provide a brief, high-level overview",
            ExplanationLevel.DETAILED: "Provide a detailed explanation with key concepts",
            ExplanationLevel.COMPREHENSIVE: "Provide a comprehensive explanation with examples",
            ExplanationLevel.BEGINNER: "Explain in beginner-friendly terms with simple language"
        }
        
        type_instructions = {
            ExplanationType.OVERVIEW: "Explain what this code does overall",
            ExplanationType.FUNCTION: "Explain how this function works",
            ExplanationType.CLASS: "Explain this class and its purpose",
            ExplanationType.ALGORITHM: "Explain the algorithm and its complexity",
            ExplanationType.PATTERN: "Explain the design patterns used",
            ExplanationType.SECURITY: "Focus on security aspects and potential vulnerabilities"
        }
        
        prompt = f"""
{level_instructions.get(explanation_level, "Explain this code")}.
{type_instructions.get(explanation_type, "Provide a general explanation")}.

Code to explain:
```python
{code}
```

Structure analysis: {structure_analysis}

Please provide a clear, accurate explanation.
"""
        return prompt
    
    def _generate_fallback_explanation(
        self,
        code: str,
        explanation_type: ExplanationType,
        explanation_level: ExplanationLevel
    ) -> str:
        """Generate fallback explanation without AI."""
        lines = len(code.split('\n'))
        
        if explanation_type == ExplanationType.FUNCTION and 'def ' in code:
            return f"This is a Python function definition with approximately {lines} lines of code. It defines a function that can be called to perform specific operations."
        
        elif explanation_type == ExplanationType.CLASS and 'class ' in code:
            return f"This is a Python class definition with approximately {lines} lines of code. It defines a blueprint for creating objects with specific attributes and methods."
        
        elif 'import ' in code:
            return f"This code includes import statements and has approximately {lines} lines. It imports external modules to extend functionality."
        
        else:
            return f"This is a Python code snippet with approximately {lines} lines that performs various operations. The code structure suggests it implements specific functionality."
    
    def _extract_key_concepts(self, code: str, structure_analysis: Dict[str, Any]) -> List[str]:
        """Extract key programming concepts from code."""
        concepts = []
        
        if structure_analysis.get("functions"):
            concepts.append("Function Definition")
        
        if structure_analysis.get("classes"):
            concepts.append("Object-Oriented Programming")
        
        if structure_analysis.get("imports"):
            concepts.append("Module Imports")
        
        if 'try:' in code:
            concepts.append("Exception Handling")
        
        if 'for ' in code or 'while ' in code:
            concepts.append("Loops and Iteration")
        
        if 'if ' in code:
            concepts.append("Conditional Logic")
        
        if 'async ' in code or 'await ' in code:
            concepts.append("Asynchronous Programming")
        
        return concepts
    
    def _generate_best_practices(self, code: str, explanation_type: ExplanationType) -> List[str]:
        """Generate best practices recommendations."""
        practices = []
        
        if explanation_type == ExplanationType.FUNCTION:
            practices.extend([
                "Use descriptive function names",
                "Keep functions focused on a single responsibility",
                "Add type hints for better code clarity"
            ])
        
        if explanation_type == ExplanationType.CLASS:
            practices.extend([
                "Follow PEP 8 naming conventions",
                "Use docstrings to document class purpose",
                "Implement proper __init__ methods"
            ])
        
        if 'def ' in code and '"""' not in code:
            practices.append("Add docstrings to document function purpose")
        
        if len(code.split('\n')) > 20:
            practices.append("Consider breaking large functions into smaller ones")
        
        return practices
    
    def _identify_potential_issues(self, code: str, structure_analysis: Dict[str, Any]) -> List[str]:
        """Identify potential code issues."""
        issues = []
        
        if 'except:' in code:
            issues.append("Bare except clause - consider catching specific exceptions")
        
        if any(f.get("args", 0) > 5 for f in structure_analysis.get("functions", [])):
            issues.append("Functions with many parameters - consider using data classes")
        
        if len(code.split('\n')) > 50:
            issues.append("Large code block - consider refactoring for better maintainability")
        
        if 'global ' in code:
            issues.append("Global variables used - consider alternative approaches")
        
        return issues
    
    def get_explanation_statistics(self) -> Dict[str, Any]:
        """Get explanation engine statistics."""
        total_explanations = len(self.explanation_cache)
        
        if total_explanations == 0:
            return {
                "total_explanations": 0,
                "explanations_by_type": {},
                "explanations_by_level": {},
                "average_confidence": 0.0
            }
        
        # Count by type and level
        by_type = {}
        by_level = {}
        total_confidence = 0.0
        
        for explanation in self.explanation_cache.values():
            exp_type = explanation.explanation_type.value
            exp_level = explanation.explanation_level.value
            
            by_type[exp_type] = by_type.get(exp_type, 0) + 1
            by_level[exp_level] = by_level.get(exp_level, 0) + 1
            total_confidence += explanation.confidence_score
        
        return {
            "total_explanations": total_explanations,
            "explanations_by_type": by_type,
            "explanations_by_level": by_level,
            "average_confidence": total_confidence / total_explanations,
            "cache_size": len(self.explanation_cache)
        }
    
    async def cleanup(self) -> None:
        """Cleanup explanation engine."""
        self.explanation_cache.clear()
        
        if self.privacy_processor:
            await self.privacy_processor.cleanup()
        
        logger.info("Code explanation engine cleaned up")

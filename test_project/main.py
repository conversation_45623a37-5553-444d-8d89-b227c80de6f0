#!/usr/bin/env python3
"""
Simple test project for Vibe Check analysis.
This file contains various code patterns to test analysis capabilities.
"""

import os
import sys
from typing import List, Dict, Optional


def calculate_fibonacci(n: int) -> int:
    """Calculate the nth Fibonacci number."""
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        return calculate_fibonacci(n - 1) + calculate_fibonacci(n - 2)


def process_data(data: List[Dict[str, any]]) -> Dict[str, int]:
    """Process a list of data dictionaries."""
    result = {}
    for item in data:
        if 'name' in item and 'value' in item:
            result[item['name']] = item['value']
    return result


class DataProcessor:
    """A simple data processor class."""
    
    def __init__(self, name: str):
        self.name = name
        self.processed_count = 0
    
    def process_item(self, item: Dict[str, any]) -> Optional[str]:
        """Process a single item."""
        if not item:
            return None
        
        self.processed_count += 1
        return f"Processed: {item.get('name', 'unknown')}"
    
    def get_stats(self) -> Dict[str, int]:
        """Get processing statistics."""
        return {
            'processed_count': self.processed_count,
            'name_length': len(self.name)
        }


def main():
    """Main function."""
    print("Starting data processing...")
    
    # Sample data
    sample_data = [
        {'name': 'item1', 'value': 10},
        {'name': 'item2', 'value': 20},
        {'name': 'item3', 'value': 30}
    ]
    
    # Process data
    processor = DataProcessor("test_processor")
    results = process_data(sample_data)
    
    print(f"Results: {results}")
    print(f"Fibonacci(10): {calculate_fibonacci(10)}")
    
    # Process individual items
    for item in sample_data:
        processed = processor.process_item(item)
        print(processed)
    
    stats = processor.get_stats()
    print(f"Processing stats: {stats}")


if __name__ == "__main__":
    main()

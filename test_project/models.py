"""
Data models for the test project.
"""

from dataclasses import dataclass
from typing import Optional, List
from enum import Enum


class Status(Enum):
    """Status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


@dataclass
class Task:
    """A simple task model."""
    id: int
    name: str
    description: Optional[str] = None
    status: Status = Status.PENDING
    priority: int = 1
    
    def mark_completed(self) -> None:
        """Mark the task as completed."""
        self.status = Status.COMPLETED
    
    def mark_failed(self) -> None:
        """Mark the task as failed."""
        self.status = Status.FAILED


@dataclass
class Project:
    """A project containing multiple tasks."""
    name: str
    description: str
    tasks: List[Task]
    
    def add_task(self, task: Task) -> None:
        """Add a task to the project."""
        self.tasks.append(task)
    
    def get_completed_tasks(self) -> List[Task]:
        """Get all completed tasks."""
        return [task for task in self.tasks if task.status == Status.COMPLETED]
    
    def get_pending_tasks(self) -> List[Task]:
        """Get all pending tasks."""
        return [task for task in self.tasks if task.status == Status.PENDING]
    
    def completion_percentage(self) -> float:
        """Calculate completion percentage."""
        if not self.tasks:
            return 0.0
        
        completed = len(self.get_completed_tasks())
        total = len(self.tasks)
        return (completed / total) * 100.0


# Example usage
def create_sample_project() -> Project:
    """Create a sample project for testing."""
    project = Project(
        name="Sample Project",
        description="A sample project for testing purposes",
        tasks=[]
    )
    
    # Add some sample tasks
    tasks = [
        Task(1, "Setup environment", "Set up the development environment"),
        Task(2, "Write code", "Implement the main functionality"),
        Task(3, "Write tests", "Create unit tests"),
        Task(4, "Documentation", "Write project documentation"),
    ]
    
    for task in tasks:
        project.add_task(task)
    
    # Mark some tasks as completed
    project.tasks[0].mark_completed()
    project.tasks[1].mark_completed()
    
    return project

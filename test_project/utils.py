"""
Utility functions for the test project.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List


def read_config_file(file_path: str) -> Dict[str, Any]:
    """Read configuration from a JSON file."""
    try:
        with open(file_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Config file not found: {file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return {}


def write_results(results: Dict[str, Any], output_path: str) -> bool:
    """Write results to a file."""
    try:
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        with open(output_path, 'w') as f:
            json.dump(results, f, indent=2)
        return True
    except Exception as e:
        print(f"Error writing results: {e}")
        return False


def validate_data(data: List[Dict[str, Any]]) -> List[str]:
    """Validate data and return list of errors."""
    errors = []
    
    if not data:
        errors.append("Data list is empty")
        return errors
    
    for i, item in enumerate(data):
        if not isinstance(item, dict):
            errors.append(f"Item {i} is not a dictionary")
            continue
        
        if 'name' not in item:
            errors.append(f"Item {i} missing 'name' field")
        
        if 'value' not in item:
            errors.append(f"Item {i} missing 'value' field")
        elif not isinstance(item['value'], (int, float)):
            errors.append(f"Item {i} 'value' field is not numeric")
    
    return errors


def get_file_info(file_path: str) -> Dict[str, Any]:
    """Get information about a file."""
    path = Path(file_path)
    
    if not path.exists():
        return {"error": "File does not exist"}
    
    stat = path.stat()
    return {
        "name": path.name,
        "size": stat.st_size,
        "modified": stat.st_mtime,
        "is_file": path.is_file(),
        "is_dir": path.is_dir(),
        "extension": path.suffix
    }


# Some intentional code quality issues for testing
def poorly_written_function(x, y, z):
    # No docstring, poor variable names, no type hints
    if x > 0:
        if y > 0:
            if z > 0:
                return x + y + z
            else:
                return x + y
        else:
            return x
    else:
        return 0


# Unused import (should be detected)
import datetime

# Unused variable
UNUSED_CONSTANT = "This constant is never used"

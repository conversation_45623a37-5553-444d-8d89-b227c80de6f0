# Test Project

This is a simple test project created to demonstrate Vibe Check's analysis capabilities.

## Structure

- `main.py` - Main application entry point
- `utils.py` - Utility functions
- `models.py` - Data models and classes
- `README.md` - This file

## Features

The project includes:

1. **Data Processing**: Functions to process and validate data
2. **File Operations**: Reading and writing configuration files
3. **Object-Oriented Design**: Classes for data processing and task management
4. **Type Hints**: Modern Python type annotations
5. **Documentation**: Docstrings and comments

## Code Quality Issues

The project intentionally includes some code quality issues to test Vibe Check's detection capabilities:

- Unused imports
- Unused variables
- Poor function design (nested if statements)
- Missing type hints in some functions
- Inefficient algorithms (recursive Fibonacci)

## Usage

```python
python main.py
```

This will run the main data processing workflow and display results.

#!/usr/bin/env python3
"""
PAT Runner Script with Pyright

This script sets up the Python path correctly and runs the PAT tool with pyright as the primary type checker.
It fixes the import issues by ensuring the PAT_tool directory is in the Python path
and configures the tool to use pyright instead of mypy.

Usage:
    python run_pat_pyright.py <project_path>
"""

import json
import os
import subprocess
import sys
import tempfile
from pathlib import Path


def main():
    """Main function to run the PAT tool with proper Python path setup and pyright configuration."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Create a temporary directory for our wrapper script
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a temporary wrapper script
        wrapper_script = Path(temp_dir) / "pat_wrapper.py"
        
        # Create a temporary config file that disables mypy and enables pyright
        config_path = pat_dir / "PAT_tool" / "config.yaml"
        temp_config_path = Path(temp_dir) / "config.yaml"
        
        # Copy the config file and modify it to disable mypy and enable pyright
        with open(config_path, 'r') as f:
            config_content = f.read()
        
        # Replace mypy configuration to disable it
        config_content = config_content.replace(
            "  mypy:\n    enabled: true",
            "  mypy:\n    enabled: false"
        )
        
        # Ensure pyright is enabled
        config_content = config_content.replace(
            "  pyright:\n    enabled: false",
            "  pyright:\n    enabled: true"
        )
        
        # Write the modified config file
        with open(temp_config_path, 'w') as f:
            f.write(config_content)
        
        # Write the wrapper script content
        with open(wrapper_script, "w") as f:
            f.write(f'''
#!/usr/bin/env python3
import sys
import os
from pathlib import Path

# Add PAT_project_analysis directory to Python path
sys.path.insert(0, "{str(pat_dir)}")

# Add PAT_tool directory to Python path
pat_tool_dir = Path("{str(pat_dir)}") / "PAT_tool"
sys.path.insert(0, str(pat_tool_dir))

# Create __init__.py files to make the directories proper packages
init_path = pat_tool_dir / "__init__.py"
if not init_path.exists():
    with open(init_path, "w") as f:
        f.write("# Auto-generated by run_pat_pyright.py\\n")

# Set environment variable to use our custom config
os.environ["PAT_CONFIG_PATH"] = "{str(temp_config_path)}"

# Import and run the main function
from PAT_tool.main import main
if __name__ == "__main__":
    # Forward command line arguments
    sys.exit(main())
''')
        
        # Make the wrapper script executable
        os.chmod(wrapper_script, 0o755)
        
        # Run the wrapper script with the same arguments
        cmd = [sys.executable, str(wrapper_script)] + sys.argv[1:]
        try:
            return subprocess.call(cmd)
        except KeyboardInterrupt:
            print("\nAnalysis interrupted by user.")
            return 1
        except Exception as e:
            print(f"\nError running analysis tool: {e}")
            import traceback
            traceback.print_exc()
            return 1

if __name__ == "__main__":
    sys.exit(main())

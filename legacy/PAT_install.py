#!/usr/bin/env python3
"""
PAT_install - Project Analysis Tool (PAT) Installer

This script sets up the Project Analysis Tool (PAT) and makes it ready to use.

Usage:
    python PAT_project_analysis/PAT_install.py
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """Main installer function."""
    print("="*80)
    print("Project Analysis Tool (PAT) Installer")
    print("="*80)
    
    # Get PAT directory (where this script is located)
    pat_dir = Path(__file__).parent.absolute()
    
    # Make scripts executable
    print("\nMaking scripts executable...")
    try:
        analyze_script = pat_dir / "PAT_analyze.py"
        setup_script = pat_dir / "PAT_setup.py"
        install_script = pat_dir / "PAT_install.py"
        
        if os.name != "nt":  # Not Windows
            subprocess.check_call(["chmod", "+x", 
                                  str(analyze_script), 
                                  str(setup_script),
                                  str(install_script)])
            print("✓ Scripts are now executable")
        else:
            print("✓ No need to make scripts executable on Windows")
    except subprocess.CalledProcessError:
        print("⚠ Warning: Could not make scripts executable. You may need to run them with 'python' explicitly.")
    
    # Create output directory
    print("\nCreating output directory...")
    output_dir = pat_dir / "PAT_output"
    output_dir.mkdir(exist_ok=True)
    print(f"✓ Output directory created at {output_dir}")
    
    # Run setup script
    print("\nRunning PAT setup script to install dependencies...")
    try:
        subprocess.check_call([sys.executable, str(setup_script)])
        print("✓ Dependencies installed successfully")
    except subprocess.CalledProcessError:
        print("⚠ Error: Failed to install dependencies. Please run PAT_setup.py manually.")
        return 1
    
    # Show usage instructions
    print("\n"+"="*80)
    print("Installation completed successfully!")
    print("="*80)
    print("\nTo analyze a project, run:")
    if os.name == "nt":  # Windows
        print(f"python {pat_dir / 'PAT_analyze.py'} <project_path>")
    else:
        print(f"{pat_dir / 'PAT_analyze.py'} <project_path>")
    
    print("\nExample:")
    if os.name == "nt":  # Windows
        print(f"python {pat_dir / 'PAT_analyze.py'} .")
    else:
        print(f"{pat_dir / 'PAT_analyze.py'} .")
    
    print("\nAnalysis reports will be saved to:")
    print(f"{output_dir}/")
    print("\nFor more information, see:")
    print(f"{pat_dir / 'PAT_README.md'}")
    print("="*80)
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 
# PAT Tool Usage Guide

## Overview

The Project Analysis Tool (PAT) is a comprehensive static analysis tool for Python codebases. It analyzes code quality, complexity, and adherence to best practices, generating reports and LLM-ready prompts for further analysis and refactoring.

This guide covers the improved version of the PAT tool, which focuses on generating purposeful prompts for files that need attention.

## Installation

The PAT tool is already set up in the `PAT_project_analysis` directory. It uses a Python virtual environment located in `PAT_project_analysis/PAT_venv`.

### Dependencies

For full functionality, the following dependencies are recommended:

- **Python packages** (installed in the virtual environment):
  - graphviz
  - scipy
  - types-PyYAML
  - pyvis
  - networkx
  - matplotlib
  - plotly

- **System packages**:
  - graphviz (for visualization)

## Running the PAT Tool

We provide several wrapper scripts to run the PAT tool:

### 1. Basic Analysis

```bash
python PAT_project_analysis/run_pat_simplified.py <project_path>
```

This runs the PAT tool with a simplified configuration that disables problematic components and uses pyright instead of mypy for type checking.

### 2. Improved Analysis with Purposeful Prompts

```bash
python PAT_project_analysis/run_pat_purposeful.py <project_path>
```

This runs the PAT tool with improved prompt generation that:
- Only includes files that need attention (high complexity, errors, etc.)
- Adds purpose statements to each chunk
- Provides specific refactoring instructions
- Focuses on actionable insights

### 3. Analysis with High-Priority Extraction

```bash
python PAT_project_analysis/run_pat_with_priorities.py <project_path> [--top N] [--min-score SCORE]
```

This runs the PAT tool and then extracts high-priority files from the output:
- `--top N`: Number of top files to extract (default: 10)
- `--min-score SCORE`: Minimum priority score to include a file (default: 5)

### 4. Improved UX with Spinner and Status Updates

```bash
python PAT_project_analysis/run_pat_with_priorities_improved.py <project_path> [--top N] [--min-score SCORE]
```

This runs the PAT tool with a spinner and status updates for long-running processes:
- Shows a spinner during analysis and extraction
- Provides time elapsed for each operation
- Explains what the parameters mean
- Provides a detailed summary of results
- Suggests next steps based on the results

### 5. Fixing Syntax Errors

If your codebase has syntax errors that prevent analysis, you can use the following scripts:

```bash
# Fix escape sequences in strings
python PAT_project_analysis/fix_escape_sequences.py <project_path>

# Fix other common syntax errors
python PAT_project_analysis/fix_syntax_errors.py <project_path>

# Fix specific files with known issues
python PAT_project_analysis/fix_specific_files.py
```

## Output Files

The PAT tool generates output in a timestamped directory under `PAT_project_analysis/PAT_output/`. The output includes:

### Reports

- **PAT_<project>_analysis_summary.md**: High-level overview of the analysis
- **PAT_<project>_report.md**: Detailed metrics and findings
- **PAT_<project>_structure.md**: Directory structure insights

### Prompts

- **prompts/chunk_*.md**: LLM-ready prompts for analyzing specific files
- **prompts/verification_*.md**: Prompts for verifying refactoring
- **prompts/project_diagnostics.md**: Project-level diagnostics

## Using the Prompts

The generated prompts are designed to be fed to an LLM (like Claude or GPT-4) for further analysis and refactoring recommendations. Each prompt includes:

1. **Purpose Statement**: Explains why the file needs attention
2. **File Information**: Basic metadata about the file
3. **Issues Identified**: Specific issues that need to be addressed
4. **Diagnostics**: Detailed analysis from various tools
5. **Refactoring Instructions**: Specific guidance for improving the file

## Customization

### Configuration

The PAT tool uses a simplified configuration file located at `PAT_project_analysis/PAT_tool/config_simplified.yaml`. You can modify this file to:

- Enable/disable specific analysis tools
- Adjust thresholds for complexity, file size, etc.
- Configure visualization options

### Prompt Generation

The prompt generation is controlled by the `chunked_prompt_generator.py` file. Key aspects that can be customized:

- Criteria for including files in prompts (`_file_needs_attention` method)
- Format of the purpose statement (`_generate_purpose_statement` method)
- Refactoring instructions (`_generate_refactoring_instructions` method)

## Best Practices

1. **Run regularly**: Use the PAT tool regularly to track progress in improving code quality
2. **Focus on high-priority issues**: Address high complexity and security issues first
3. **Implement recommendations incrementally**: Make small, focused changes based on the recommendations
4. **Verify changes**: Use the verification prompts to ensure refactoring doesn't break functionality
5. **Integrate with CI/CD**: Consider adding PAT analysis to your CI/CD pipeline

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure the PAT_tool directory is in the Python path
2. **Missing dependencies**: Install required dependencies as listed above
3. **Visualization failures**: Install graphviz system package
4. **Memory errors**: For large codebases, increase available memory or analyze subsets

### Getting Help

If you encounter issues not covered in this guide, check the PAT tool source code or consult the documentation in the `PAT_project_analysis/PAT_tool/docs` directory.

## Conclusion

The improved PAT tool provides valuable insights into your codebase and generates purposeful prompts for LLM-assisted refactoring. By focusing on files that need attention and providing clear refactoring instructions, it helps you improve code quality efficiently.

Use this tool regularly as part of your development workflow to maintain high code quality and adherence to best practices.

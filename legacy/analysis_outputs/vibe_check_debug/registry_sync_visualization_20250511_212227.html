
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Registry Synchronization Visualization - 20250511_212227</title>
            <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
            <script src="https://d3js.org/d3.v7.min.js"></script>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background-color: #f5f5f5;
                }
                .container {
                    max-width: 1200px;
                    margin: 0 auto;
                    background-color: white;
                    padding: 20px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                }
                h1, h2, h3 {
                    color: #333;
                }
                .section {
                    margin-bottom: 30px;
                    padding: 20px;
                    background-color: #f9f9f9;
                    border-radius: 5px;
                }
                .chart-container {
                    height: 300px;
                    margin-bottom: 20px;
                }
                .grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
                    gap: 20px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                th, td {
                    padding: 8px 12px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    background-color: #f2f2f2;
                }
                tr:hover {
                    background-color: #f5f5f5;
                }
                .issue {
                    background-color: #fff0f0;
                }
                .warning {
                    color: #856404;
                    background-color: #fff3cd;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
                .error {
                    color: #721c24;
                    background-color: #f8d7da;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
                .success {
                    color: #155724;
                    background-color: #d4edda;
                    padding: 10px;
                    border-radius: 5px;
                    margin-bottom: 10px;
                }
                #sync-graph {
                    width: 100%;
                    height: 500px;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    overflow: hidden;
                }
                .node {
                    cursor: pointer;
                }
                .link {
                    stroke-width: 2px;
                }
                .tooltip {
                    position: absolute;
                    background-color: white;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    padding: 10px;
                    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
                    pointer-events: none;
                    display: none;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>Registry Synchronization Visualization</h1>
                <p>Generated on: 2025-05-11 21:22:27</p>

                <div class="section">
                    <h2>Synchronization Issues</h2>
                    <div class="success">
                        <strong>Synchronization Issues:</strong> 0 issues found
                    </div>

                    <h3>Issues</h3>
                    <table>
                        <tr>
                            <th>Issue Type</th>
                            <th>Message</th>
                            <th>Details</th>
                        </tr>
        
                        <tr>
                            <td colspan="3">No synchronization issues found</td>
                        </tr>
            
                    </table>
                </div>

                <div class="section">
                    <h2>Registry Consistency</h2>
                    <div class="success">
                        <strong>Consistency Check:</strong> Passed
                    </div>

                    <h3>Orphaned References</h3>
                    <table>
                        <tr>
                            <th>Type</th>
                            <th>Count</th>
                        </tr>
        
                        <tr>
                            <td>actor_types</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_tags</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_capabilities</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>actor_patterns</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>subscriptions</td>
                            <td>0</td>
                        </tr>
            
                        <tr>
                            <td>heartbeats</td>
                            <td>0</td>
                        </tr>
            
                    </table>
                </div>

                <div class="section">
                    <h2>Registry-Manager Synchronization Graph</h2>
                    <div id="sync-graph"></div>

                    <div class="controls">
                        <label>
                            <input type="checkbox" id="show-issues" checked>
                            Highlight Issues
                        </label>
                    </div>
                </div>
            </div>

            <div class="tooltip" id="tooltip"></div>

            <script>
                // Create the synchronization graph
                function createSyncGraph() {
                    const width = document.getElementById('sync-graph').clientWidth;
                    const height = document.getElementById('sync-graph').clientHeight;

                    // Create the SVG container
                    const svg = d3.select('#sync-graph')
                        .append('svg')
                        .attr('width', width)
                        .attr('height', height);

                    // Create a group for the graph
                    const g = svg.append('g');

                    // Create zoom behavior
                    const zoom = d3.zoom()
                        .scaleExtent([0.1, 4])
                        .on('zoom', (event) => {
                            g.attr('transform', event.transform);
                        });

                    // Apply zoom to the SVG
                    svg.call(zoom);

                    // Define the graph data
                    const registryActors = [];
                    const managerActors = [];

                    // Create nodes for registry and manager
                    const nodes = [
                        { id: 'registry', label: 'Registry', type: 'system', x: width / 3, y: height / 2 },
                        { id: 'manager', label: 'Initialization Manager', type: 'system', x: 2 * width / 3, y: height / 2 }
                    ];

                    // Create nodes for actors
                    const actorNodes = [];
                    const actorLinks = [];

                    // Add registry actors
                    registryActors.forEach((actorId, index) => {
                        const inManager = managerActors.includes(actorId);
                        const angle = (index / registryActors.length) * 2 * Math.PI;
                        const radius = 150;
                        const x = width / 3 + radius * Math.cos(angle);
                        const y = height / 2 + radius * Math.sin(angle);

                        actorNodes.push({
                            id: `registry_${actorId}`,
                            label: actorId,
                            type: 'actor',
                            inRegistry: true,
                            inManager: inManager,
                            issue: !inManager,
                            x: x,
                            y: y
                        });

                        actorLinks.push({
                            source: 'registry',
                            target: `registry_${actorId}`,
                            type: 'registry'
                        });
                    });

                    // Add manager actors
                    managerActors.forEach((actorId, index) => {
                        const inRegistry = registryActors.includes(actorId);

                        if (!inRegistry) {
                            const angle = (index / managerActors.length) * 2 * Math.PI;
                            const radius = 150;
                            const x = 2 * width / 3 + radius * Math.cos(angle);
                            const y = height / 2 + radius * Math.sin(angle);

                            actorNodes.push({
                                id: `manager_${actorId}`,
                                label: actorId,
                                type: 'actor',
                                inRegistry: false,
                                inManager: true,
                                issue: true,
                                x: x,
                                y: y
                            });

                            actorLinks.push({
                                source: 'manager',
                                target: `manager_${actorId}`,
                                type: 'manager'
                            });
                        } else {
                            // Add link from manager to registry actor
                            actorLinks.push({
                                source: 'manager',
                                target: `registry_${actorId}`,
                                type: 'sync'
                            });
                        }
                    });

                    // Combine all nodes and links
                    const graphData = {
                        nodes: [...nodes, ...actorNodes],
                        links: actorLinks
                    };

                    // Create the simulation
                    const simulation = d3.forceSimulation(graphData.nodes)
                        .force('link', d3.forceLink(graphData.links).id(d => d.id).distance(100))
                        .force('charge', d3.forceManyBody().strength(-300))
                        .force('center', d3.forceCenter(width / 2, height / 2))
                        .force('collision', d3.forceCollide().radius(30));

                    // Create the links
                    const link = g.append('g')
                        .selectAll('line')
                        .data(graphData.links)
                        .enter()
                        .append('line')
                        .attr('class', 'link')
                        .attr('stroke', d => getEdgeColor(d))
                        .attr('stroke-width', 2);

                    // Create the nodes
                    const node = g.append('g')
                        .selectAll('circle')
                        .data(graphData.nodes)
                        .enter()
                        .append('circle')
                        .attr('class', 'node')
                        .attr('r', d => d.type === 'system' ? 20 : 10)
                        .attr('fill', d => getNodeColor(d))
                        .call(d3.drag()
                            .on('start', dragstarted)
                            .on('drag', dragged)
                            .on('end', dragended));

                    // Add labels to nodes
                    const label = g.append('g')
                        .selectAll('text')
                        .data(graphData.nodes)
                        .enter()
                        .append('text')
                        .text(d => d.label)
                        .attr('font-size', d => d.type === 'system' ? 14 : 12)
                        .attr('dx', d => d.type === 'system' ? 0 : 15)
                        .attr('dy', d => d.type === 'system' ? -25 : 4)
                        .attr('text-anchor', d => d.type === 'system' ? 'middle' : 'start');

                    // Add tooltips
                    node.on('mouseover', showNodeTooltip)
                        .on('mousemove', moveTooltip)
                        .on('mouseout', hideTooltip);

                    link.on('mouseover', showLinkTooltip)
                        .on('mousemove', moveTooltip)
                        .on('mouseout', hideTooltip);

                    // Update positions on each tick
                    simulation.on('tick', () => {
                        link
                            .attr('x1', d => d.source.x)
                            .attr('y1', d => d.source.y)
                            .attr('x2', d => d.target.x)
                            .attr('y2', d => d.target.y);

                        node
                            .attr('cx', d => d.x)
                            .attr('cy', d => d.y);

                        label
                            .attr('x', d => d.x)
                            .attr('y', d => d.y);
                    });

                    // Drag functions
                    function dragstarted(event, d) {
                        if (!event.active) simulation.alphaTarget(0.3).restart();
                        d.fx = d.x;
                        d.fy = d.y;
                    }

                    function dragged(event, d) {
                        d.fx = event.x;
                        d.fy = event.y;
                    }

                    function dragended(event, d) {
                        if (!event.active) simulation.alphaTarget(0);
                        d.fx = null;
                        d.fy = null;
                    }

                    // Tooltip functions
                    function showNodeTooltip(event, d) {
                        const tooltip = d3.select('#tooltip');

                        let html = `<div><strong>${d.label}</strong><br>`;

                        if (d.type === 'actor') {
                            html += `
                                <strong>In Registry:</strong> ${d.inRegistry ? 'Yes' : 'No'}<br>
                                <strong>In Manager:</strong> ${d.inManager ? 'Yes' : 'No'}<br>
                                <strong>Issue:</strong> ${d.issue ? 'Yes' : 'No'}
                            `;
                        } else {
                            html += `<strong>Type:</strong> ${d.type}`;
                        }

                        html += `</div>`;

                        tooltip.style('display', 'block')
                            .html(html);

                        moveTooltip(event);
                    }

                    function showLinkTooltip(event, d) {
                        const tooltip = d3.select('#tooltip');

                        let html = `<div>
                            <strong>Source:</strong> ${d.source.label}<br>
                            <strong>Target:</strong> ${d.target.label}<br>
                            <strong>Type:</strong> ${d.type}
                        </div>`;

                        tooltip.style('display', 'block')
                            .html(html);

                        moveTooltip(event);
                    }

                    function moveTooltip(event) {
                        const tooltip = d3.select('#tooltip');
                        tooltip.style('left', (event.pageX + 10) + 'px')
                            .style('top', (event.pageY + 10) + 'px');
                    }

                    function hideTooltip() {
                        d3.select('#tooltip').style('display', 'none');
                    }

                    // Helper function to get node color
                    function getNodeColor(d) {
                        if (d.type === 'system') {
                            return d.id === 'registry' ? '#4285F4' : '#34A853';
                        }

                        if (d.issue && d3.select('#show-issues').property('checked')) {
                            return '#ff6b6b';
                        }

                        return d.inRegistry && d.inManager ? '#4285F4' : '#ff6b6b';
                    }

                    // Helper function to get edge color
                    function getEdgeColor(d) {
                        if (d.type === 'registry') {
                            return '#4285F4';
                        }

                        if (d.type === 'manager') {
                            return '#34A853';
                        }

                        return '#999';
                    }

                    // Control functions
                    d3.select('#show-issues').on('change', function() {
                        const showIssues = this.checked;
                        node.attr('fill', d => getNodeColor(d));
                    });
                }

                // Create the graph when the page loads
                window.onload = createSyncGraph;
            </script>
        </body>
        </html>
        
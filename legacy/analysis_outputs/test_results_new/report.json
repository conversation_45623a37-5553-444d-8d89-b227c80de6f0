{"project_path": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/test_project_with_issues", "files": {"main.py": {"path": "main.py", "name": "main.py", "size": 7584, "lines": 231, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "utils.py": {"path": "utils.py", "name": "utils.py", "size": 4317, "lines": 168, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}}, "directories": {"": {"path": "", "files": ["main.py", "utils.py"], "total_lines": 399, "avg_lines": 199.5, "max_file_lines": 231, "max_file": "main.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 0.0, "_max_complexity": 0, "_issue_count": 0}}, "issue_count": 0, "max_complexity": 0, "issues_by_severity": {}}
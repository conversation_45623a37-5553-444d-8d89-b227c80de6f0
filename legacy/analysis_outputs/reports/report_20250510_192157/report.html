<!DOCTYPE html>
<html>
<head>
    <title>Vibe Check Report - {'metrics': {'complexity': 5}, 'issues': []}</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        .summary {
            background-color: #f8f9fa;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #4CAF50;
            color: white;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        .meter {
            height: 20px;
            position: relative;
            background: #f3f3f3;
            border-radius: 25px;
            padding: 5px;
            box-shadow: inset 0 -1px 1px rgba(0,0,0,0.1);
        }
        .meter > span {
            display: block;
            height: 100%;
            border-radius: 20px;
            background-color: #2196F3;
            position: relative;
            overflow: hidden;
        }
        .good {
            background-color: #4CAF50 !important;
        }
        .warning {
            background-color: #FF9800 !important;
        }
        .critical {
            background-color: #F44336 !important;
        }
        .toggle-button {
            background: #007BFF;
            color: white;
            border: none;
            padding: 10px 15px;
            cursor: pointer;
            border-radius: 4px;
            margin-bottom: 10px;
        }
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <h1>Vibe Check Analysis Report</h1>

    <div class="summary">
        <h2>Project Summary</h2>
        <p><strong>Project Path:</strong> {'metrics': {'complexity': 5}, 'issues': []}</p>
        <p><strong>Analysis Date:</strong> 2025-05-10 19:21:57</p>
        <p><strong>Files Analyzed:</strong> 0</p>
        <p><strong>Directories Analyzed:</strong> 0</p>
    </div>

    <h2>Overall Metrics</h2>

    <h3>Complexity</h3>
    <div class="meter">
        <span style="width: 100.0%" class="good"></span>
    </div>
    <p>Average Complexity: 0.0 (lower is better)</p>

    <h3>Documentation Coverage</h3>
    <div class="meter">
        <span style="width: 0.0%" class="critical"></span>
    </div>
    <p>Documentation Coverage: 0.0%</p>

    <h3>Type Coverage</h3>
    <div class="meter">
        <span style="width: 0.0%" class="critical"></span>
    </div>
    <p>Type Coverage: 0.0%</p>

    <h2>Issue Summary</h2>
    <p><strong>Total Issues:</strong> 0</p>
    <p><strong>Issues by Severity:</strong> {}</p>

    <h2>File Analysis</h2>
    <button class="toggle-button" onclick="toggleFileTable()">Show/Hide File Details</button>
    <div id="fileTable" class="hidden">
        <table>
            <thead>
                <tr>
                    <th>File</th>
                    <th>Lines</th>
                    <th>Quality</th>
                    <th>Complexity</th>
                    <th>Doc Coverage</th>
                    <th>Issues</th>
                </tr>
            </thead>
            <tbody>
                
            </tbody>
        </table>
    </div>

    <script>
function toggleFileTable() {
    var fileTable = document.getElementById('fileTable');
    fileTable.classList.toggle('hidden');
}
    </script>
</body>
</html>

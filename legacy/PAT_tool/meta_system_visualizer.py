"""
Meta-System Separation Visualizer

Generates visualizations for meta-system separation analysis results.
Creates network graphs showing dependencies between meta-systems.
"""

import json
from pathlib import Path
from typing import Any, Dict, List

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from models import ProjectMetrics
    from utils import logger

class MetaSystemVisualizer:
    """Generates visualizations for meta-system separation analysis."""

    def __init__(self, project_name: str, metrics: ProjectMetrics, output_dir: str):
        """Initialize the meta-system visualizer.

        Args:
            project_name: Name of the project
            metrics: ProjectMetrics instance with analysis results
            output_dir: Directory to save visualizations
        """
        self.project_name = project_name
        self.metrics = metrics
        self.output_dir = Path(output_dir)

    def generate(self):
        """Generate visualizations for meta-system separation analysis."""
        # Check if meta-system separation analysis was run
        if "meta_system_separation" not in self.metrics.tool_results:
            logger.warning("No meta-system separation analysis results found. Skipping visualization.")
            return

        # Get analysis results
        results = self.metrics.tool_results["meta_system_separation"]
        violations = results.get("violations", [])
        meta_system_graph = results.get("meta_system_graph", {})

        # Generate network graph visualization
        self._generate_network_graph(meta_system_graph, violations)

        # Generate HTML report
        self._generate_html_report(meta_system_graph, violations)

    def _generate_network_graph(self, meta_system_graph: Dict[str, Any], violations: List[Dict[str, Any]]):
        """Generate a network graph visualization of meta-system dependencies.

        Args:
            meta_system_graph: Graph of meta-system dependencies
            violations: List of meta-system separation violations
        """
        try:
            import matplotlib.pyplot as plt
            import networkx as nx

            # Create graph
            G = nx.DiGraph()

            # Add nodes
            for meta in meta_system_graph.keys():
                G.add_node(meta)

            # Add edges
            for source, data in meta_system_graph.items():
                for target in data["imports"]:
                    G.add_edge(source, target)

            # Set up the plot
            plt.figure(figsize=(10, 8))

            # Define node colors
            node_colors = {
                "PC": "blue",
                "AN": "green",
                "PR": "red",
                "SIO": "purple",
                "UI": "orange",
                "CORE": "gray",
                "OTHER": "lightgray"
            }

            # Get node colors
            colors = [node_colors.get(node, "lightgray") for node in G.nodes()]

            # Draw the graph
            pos = nx.spring_layout(G, seed=42)
            nx.draw_networkx_nodes(G, pos, node_color=colors, node_size=1000, alpha=0.8)
            nx.draw_networkx_labels(G, pos, font_size=12, font_weight="bold")

            # Draw edges with different colors for violations
            edges = G.edges()
            edge_colors = []
            edge_widths = []

            for u, v in edges:
                if any(viol["source_meta"] == u and viol["target_meta"] == v for viol in violations):
                    edge_colors.append("red")
                    edge_widths.append(2.0)
                else:
                    edge_colors.append("black")
                    edge_widths.append(1.0)

            nx.draw_networkx_edges(G, pos, edgelist=edges, width=edge_widths, edge_color=edge_colors,
                                  arrowsize=20, connectionstyle="arc3,rad=0.1")

            # Add a title
            plt.title("Meta-System Dependency Graph", fontsize=16)

            # Add a legend
            legend_elements = []
            import matplotlib.patches as mpatches
            for meta, color in node_colors.items():
                if meta in G.nodes():
                    legend_elements.append(mpatches.Patch(color=color, label=meta))
            plt.legend(handles=legend_elements, loc="upper right")

            # Save the figure
            output_path = self.output_dir / f"{self.project_name}_meta_system_graph.png"
            plt.savefig(output_path, bbox_inches="tight")
            plt.close()

            logger.info(f"Meta-system graph saved to {output_path}")

        except ImportError as e:
            logger.error(f"Error generating meta-system graph: {e}")
            logger.error("Make sure networkx and matplotlib are installed.")

    def _generate_html_report(self, meta_system_graph: Dict[str, Any], violations: List[Dict[str, Any]]):
        """Generate an interactive HTML report for meta-system separation analysis.

        Args:
            meta_system_graph: Graph of meta-system dependencies
            violations: List of meta-system separation violations
        """
        try:
            # Create data for visualization
            nodes = []
            for meta, data in meta_system_graph.items():
                nodes.append({
                    "id": meta,
                    "label": meta,
                    "imports": len(data["imports"]),
                    "imported_by": len(data["imported_by"])
                })

            links = []
            for source, data in meta_system_graph.items():
                for target in data["imports"]:
                    is_violation = any(v["source_meta"] == source and v["target_meta"] == target for v in violations)
                    links.append({
                        "source": source,
                        "target": target,
                        "is_violation": is_violation
                    })

            # Generate violations HTML
            violations_html = self._generate_violations_html(violations)

            # Load template
            template_path = Path(__file__).parent / "templates" / "meta_system_report_template.html"
            if not template_path.exists():
                logger.warning(f"Template file not found at {template_path}. Using default template.")
                template_path = None

            if template_path:
                with open(template_path, "r") as f:
                    template = f.read()
            else:
                # Use a simple default template
                template = """<!DOCTYPE html>
                <html>
                <head>
                    <title>Meta-System Separation Analysis - {project_name}</title>
                </head>
                <body>
                    <h1>Meta-System Separation Analysis - {project_name}</h1>
                    <p>{violation_count} violations found.</p>
                    <div>{violations_html}</div>
                </body>
                </html>
                """

            # Format template
            html_content = template.format(
                project_name=self.project_name,
                violation_count=len(violations),
                violations_html=violations_html,
                nodes_json=json.dumps(nodes),
                links_json=json.dumps(links)
            )

            # Save HTML file
            output_path = self.output_dir / f"{self.project_name}_meta_system_report.html"
            with open(output_path, "w") as f:
                f.write(html_content)

            logger.info(f"Meta-system HTML report saved to {output_path}")

        except Exception as e:
            logger.error(f"Error generating meta-system HTML report: {e}")

    def _generate_violations_html(self, violations: List[Dict[str, Any]]) -> str:
        """Generate HTML for violations list.

        Args:
            violations: List of meta-system separation violations

        Returns:
            HTML string for violations list
        """
        if not violations:
            return "<p>No violations found.</p>"

        html = ""
        for i, violation in enumerate(violations):
            source_meta = violation["source_meta"]
            target_meta = violation["target_meta"]
            source_module = violation["source_module"]
            target_module = violation["target_module"]
            file_path = violation["file_path"]

            html += f"""
            <div class="violation-item violation-{source_meta} violation-{target_meta}">
                <h4>Violation #{i+1}</h4>
                <p>
                    <span class="meta-system {source_meta}">{source_meta}</span> imports from
                    <span class="meta-system {target_meta}">{target_meta}</span>
                </p>
                <p><strong>Source:</strong> {source_module}</p>
                <p><strong>Target:</strong> {target_module}</p>
                <p><strong>File:</strong> {file_path}</p>
            </div>
            """

        return html

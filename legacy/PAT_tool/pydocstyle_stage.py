"""
PydocstyleStage for PAT
======================

Integrates pydocstyle (Python docstring style checker) as a modular pipeline stage. Runs pydocstyle on the codebase,
parses docstring issues, and updates ProjectMetrics/FileMetrics for downstream use.

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)
    - flake8_stage.py, ruff_stage.py (similar linter integration)

External Dependencies:
    - pydocstyle (must be installed in the environment)

"""

import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics

import re

try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class PydocstyleStage(ToolStage):
    """Pipeline stage for running pydocstyle and collecting docstring diagnostics.

    Assigns docstring issues to both project-level and per-file tool_results.
    """
    tool_name = "pydocstyle"

    def _get_tool_path(self):
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "pydocstyle"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_executable} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run pydocstyle as a subprocess on the project root.
        Interruptible via skip_controller.
        Returns:
            Raw output from pydocstyle (str) or error dict
        """
        if skip_controller and skip_controller.should_skip():
            print("[PydocstyleStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}

        options = self.config.get("options", "").split()
        pydocstyle_executable = self._get_tool_path()
        
        cmd = [pydocstyle_executable] + options + [str(self.project_root)]
        
        logger.info(f"[PydocstyleStage] Running command: {' '.join(cmd)}")
        self.progress.start_phase("Pydocstyle", total_steps=1)

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
            if result.returncode not in (0, 1):
                error_msg = result.stderr.strip() or f"Pydocstyle execution failed with code {result.returncode}"
                logger.error(f"[PydocstyleStage] Pydocstyle failed: {error_msg}")
                self.progress.complete_phase("Pydocstyle", status="Error")
                return {"error": f"Pydocstyle execution failed: {error_msg}", "raw_output": result.stdout, "stderr": result.stderr}
            
            self.progress.complete_phase("Pydocstyle", status="Complete!")
            return result.stdout
        except FileNotFoundError:
            error_msg = f"Pydocstyle executable not found at '{pydocstyle_executable}'. Ensure PAT setup completed correctly."
            logger.error(f"[PydocstyleStage] {error_msg}")
            self.progress.complete_phase("Pydocstyle", status="Not Found")
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error running pydocstyle: {e}"
            logger.error(f"[PydocstyleStage] {error_msg}")
            self.progress.complete_phase("Pydocstyle", status="Error")
            return {"error": error_msg}
        finally:
            self.progress.increment()

    def parse_output(self, output: Any) -> None:
        """
        Parse pydocstyle output, update metrics with docstring issues and summary.
        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['pydocstyle'] includes: issues (list), summary (str), raw_output (str)
            - FileMetrics.tool_results['pydocstyle'] (for all files): issues (list), summary (str)
        Args:
            output: Raw output from pydocstyle (str) or error dict
        """
        if isinstance(output, dict) and (output.get("skipped") or output.get("error")):
            self.metrics.tool_results["pydocstyle"] = output
            return

        if not isinstance(output, str):
            logger.warning(f"[PydocstyleStage] parse_output received unexpected data type: {type(output)}")
            self.metrics.tool_results["pydocstyle"] = {"error": "Invalid data received by parse_output"}
            return

        issues = []
        file_issue_map: Dict[str, list] = {}
        # Pydocstyle output format: path/file.py:1 at module level:
        #         D100: Missing docstring in public module
        # Use regex to capture file path reliably, handle potential path variations
        file_line_pattern = re.compile(r"^(.+?):(\d+) .*$")

        current_file = "<unknown>"
        for line in output.splitlines():
            line_strip = line.strip()
            if not line_strip:
                continue
            
            file_match = file_line_pattern.match(line)
            if file_match:
                try:
                    current_file_rel = file_match.group(1).strip()
                    current_file = str((self.project_root / current_file_rel).resolve())
                except Exception as e:
                    logger.warning(f"[PydocstyleStage] Could not resolve path '{file_match.group(1).strip()}' relative to {self.project_root}: {e}")
                    current_file = file_match.group(1).strip()
                issues.append(line_strip)
                file_issue_map.setdefault(current_file, []).append(line_strip)
            elif line_strip.startswith("D" + '\\d{3}'):
                issues.append(line_strip)
                if current_file != "<unknown>":
                    file_issue_map.setdefault(current_file, []).append(line_strip)

        summary = f"{len(issues)} issues"
        self.metrics.tool_results["pydocstyle"] = {
            "issues": issues,
            "summary": summary,
            "raw_output": output,
        }
        
        for file_metrics in self.metrics.files.values():
            file_metrics.tool_results["pydocstyle"] = {"issues": [], "summary": "0 issues"}
            
        for file_path_abs, file_issues in file_issue_map.items():
            found = False
            for key, file_metrics in self.metrics.files.items():
                abs_key = str(Path(self.project_root) / file_metrics.path)
                if file_path_abs == abs_key:
                    file_metrics.tool_results["pydocstyle"] = {
                        "issues": file_issues,
                        "summary": f"{len(file_issues)} issues"
                    }
                    found = True
                    break
            if not found:
                for key, file_metrics in self.metrics.files.items():
                    if file_path_abs == file_metrics.path: 
                        file_metrics.tool_results["pydocstyle"] = {
                            "issues": file_issues,
                            "summary": f"{len(file_issues)} issues"
                        }
                        break

# TODO:
# - Add unit tests for output parsing and per-file assignment
# - Add config options for enabling/disabling, custom pydocstyle config

#

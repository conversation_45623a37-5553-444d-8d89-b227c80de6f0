{"include": ["."], "exclude": ["**/node_modules", "**/__pycache__", "**/.*", "**/*.pyc", "**/*.pyo", "**/*.pyd", "**/.git"], "ignore": ["**/*.ipynb", "**/*.json", "**/*.md", "**/*.txt", "**/*.yaml", "**/*.yml"], "defineConstant": {"DEBUG": true}, "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "reportMissingImports": "warning", "reportMissingTypeStubs": "none", "reportUnknownMemberType": "none", "reportUnknownArgumentType": "none", "reportUnknownVariableType": "none", "reportUnknownLambdaType": "none", "reportInvalidTypeVarUse": "warning", "reportGeneralTypeIssues": "warning", "reportOptionalSubscript": "warning", "reportOptionalMemberAccess": "warning", "reportOptionalCall": "warning", "reportOptionalIterable": "warning", "reportOptionalContextManager": "warning", "reportOptionalOperand": "warning", "reportTypedDictNotRequiredAccess": "warning", "reportPrivateImportUsage": "none", "reportUnboundVariable": "warning", "reportUndefinedVariable": "warning", "reportInvalidStubStatement": "warning", "reportIncompleteStub": "warning", "reportUnsupportedDunderAll": "warning", "reportUnusedClass": "none", "reportUnusedFunction": "none", "reportUnusedImport": "none", "reportUnusedVariable": "none", "pythonVersion": "3.10", "pythonPlatform": "All", "executionEnvironments": [{"root": ".", "extraPaths": ["."]}]}
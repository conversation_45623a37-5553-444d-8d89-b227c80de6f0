"""
Dependency Analyzer Module

Analyzes project dependencies and module relationships, with special handling for
recursive structures and protection against stack overflows.
"""

import ast
import re
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
    from PAT_tool.utils import (SafetySettings, is_excluded_path,
                                load_exclusion_patterns, logger,
                                read_file_content, safe_execution)
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
    from utils import (SafetySettings, is_excluded_path,
                       load_exclusion_patterns, logger, read_file_content,
                       safe_execution)

class DependencyAnalyzer:
    """Analyzes dependencies and relationships between modules in a project.

    Supports exclusion patterns from a .patignore file at the project root (standard .gitignore-like syntax).
    """

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress: Any):
        """Initialize the dependency analyzer.

        Args:
            project_root: Path to the project root
            metrics: ProjectMetrics instance to store results
            progress: ProgressTracker instance for tracking progress
        """
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        self.project_name = project_root.name
        self._ast_recursion_count = 0
        self.excluded_patterns = load_exclusion_patterns(project_root)

    def analyze(self):
        """Analyze project dependencies."""
        # Only process files not excluded by .patignore
        valid_files = {k: v for k, v in self.metrics.files.items()
                       if not is_excluded_path(self.project_root / v.path, self.excluded_patterns, self.project_root)}
        self.progress.start_phase("Analyzing dependencies", len(valid_files))

        for module_name, metrics in valid_files.items():
            try:
                self._analyze_module_dependencies(module_name, metrics)
            except Exception as e:
                logger.error(f"Error analyzing dependencies for {module_name}: {e}")

            self.progress.increment()

        # Analyze circular dependencies after all modules are processed
        self._detect_circular_dependencies()

    @safe_execution
    def _analyze_module_dependencies(self, module_name: str, metrics: FileMetrics):
        """Analyze dependencies for a specific module.

        Args:
            module_name: Name of the module
            metrics: FileMetrics instance for the module
        """
        file_path = self.project_root / metrics.path

        content, _ = read_file_content(file_path)
        if not content:
            return

        # Reset recursion counter for each file
        self._ast_recursion_count = 0

        # Use safe AST parsing with depth limit
        imports = self._extract_imports_safely(content)

        internal_deps = []
        external_deps = []

        for imp in imports:
            if imp.startswith(self.project_name) or (self.project_name + '.') in imp:
                internal_deps.append(imp)
            else:
                external_deps.append(imp)

        metrics.imports = imports
        metrics.internal_deps = internal_deps
        metrics.external_deps = external_deps

        # Add to dependency graph
        self.metrics.dependency_graph.add_node(module_name)
        for dep in internal_deps:
            self.metrics.dependency_graph.add_edge(module_name, dep)

    def _extract_imports_safely(self, content: str) -> List[str]:
        """Extract imports from content with protection against recursion.

        Args:
            content: Python source code

        Returns:
            List of imported module names
        """
        try:
            tree = ast.parse(content)
            return self._extract_imports(tree)
        except RecursionError:
            logger.error("RecursionError during AST parsing. Consider simpler parsing approach.")
            # Fall back to regex-based import extraction
            return self._extract_imports_regex(content)
        except SyntaxError:
            logger.warning("Syntax error in file, unable to parse imports properly")
            return []
        except Exception as e:
            logger.error(f"Error extracting imports: {e}")
            return []

    def _extract_imports(self, tree: ast.AST) -> List[str]:
        """Extract all imports from an AST.

        Args:
            tree: AST tree

        Returns:
            List of imported module names
        """
        imports = []
        try:
            for node in ast.walk(tree):
                # Check recursion depth
                self._ast_recursion_count += 1
                if self._ast_recursion_count > SafetySettings.MAX_AST_RECURSION_DEPTH:
                    logger.warning("AST recursion depth exceeded, falling back to regex approach")
                    return self._extract_imports_regex(ast.unparse(tree))

                if isinstance(node, ast.Import):
                    for name in node.names:
                        imports.append(name.name)
                elif isinstance(node, ast.ImportFrom):
                    module = node.module or ""
                    for name in node.names:
                        if module:
                            imports.append(f"{module}.{name.name}")
                        else:
                            imports.append(name.name)
        except Exception as e:
            logger.error(f"Error walking AST: {e}")
            # Fall back to regex-based extraction
            return self._extract_imports_regex(ast.unparse(tree))

        return imports

    def _extract_imports_regex(self, content: str) -> List[str]:
        """Extract imports using regex as a fallback method.

        Args:
            content: Python source code

        Returns:
            List of imported module names
        """
        imports = []

        # Match "import X" and "import X as Y"
        import_pattern = r'^\s*import\s+([a-zA-Z0-9_.,\s]+)(?:\s+as\s+[a-zA-Z0-9_]+)?'

        # Match "from X import Y" and "from X import Y as Z"
        from_pattern = r'^\s*from\s+([a-zA-Z0-9_.]+)\s+import\s+'

        lines = content.split('\n')
        for line in lines:
            line = line.strip()

            # Skip comments
            if line.startswith('#'):
                continue

            # Process "import X"
            import_match = re.match(import_pattern, line)
            if import_match:
                modules = import_match.group(1).split(',')
                for module in modules:
                    module_name = module.strip()
                    if module_name:
                        imports.append(module_name)

            # Process "from X import Y"
            from_match = re.match(from_pattern, line)
            if from_match:
                module = from_match.group(1)
                # This is a simplified approach - a more robust parser would
                # properly handle multiline imports and other complexities
                imported_part = line[from_match.end():]
                for part in imported_part.split(','):
                    name = part.strip().split(' as ')[0].strip()
                    if name and name != '*':
                        imports.append(f"{module}.{name}")

        return imports

    @safe_execution
    def _detect_circular_dependencies(self):
        """Detect circular dependencies in the project."""
        logger.info("Detecting circular dependencies")

        import networkx as nx

        # Find cycles in the dependency graph
        try:
            cycles = list(nx.simple_cycles(self.metrics.dependency_graph))

            # Update metrics with circular dependencies
            for cycle in cycles:
                if len(cycle) > 1:  # Ignore self-dependencies
                    for module in cycle:
                        if module in self.metrics.files:
                            self.metrics.files[module].circular_deps = cycle
        except Exception as e:
            logger.error(f"Error detecting circular dependencies: {e}")
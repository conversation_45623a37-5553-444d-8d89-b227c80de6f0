"""
MypyStage for PAT
=================

Integrates mypy static type checking as a modular pipeline stage. Runs mypy on the codebase,
parses errors and type coverage, and updates ProjectMetrics/FileMetrics for downstream use.

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)
"""

import json
import os
import platform
import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.tool_stage import ToolStage
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from models import ProjectMetrics
    from tool_stage import ToolStage
    from utils import logger

class MypyStage(ToolStage):
    tool_name = "mypy"

    def _get_tool_path(self):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "mypy"
        # Handle Windows executable name if needed
        if platform.system() == "Windows":
            tool_executable += ".exe"
        return str(pat_venv_bin / tool_executable)

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run mypy as a subprocess on each Python file individually, with robust error handling.
        Interruptible via skip_controller.
        Returns:
            Dict with per-file results and errors.
        """
        # Discover all Python files
        file_list = []
        for root, _, files in os.walk(self.project_root):
            # Add logic to respect .patignore
            ignore_path = Path(root) / ".patignore"
            ignored_files = set()
            if ignore_path.exists():
                with open(ignore_path, 'r') as f:
                    ignored_files = {Path(root) / line.strip() for line in f}

            for fname in files:
                if fname.endswith(".py"):
                    full_path = Path(root) / fname
                    if full_path not in ignored_files:
                        file_list.append(str(full_path))
                    # else:
                    #     logger.debug(f"[MypyStage] Skipping ignored file: {full_path}") # Optional debug

        num_files = len(file_list)
        if num_files == 0:
            logger.warning("[MypyStage] No Python files found for analysis.")
            return {"error": "No Python files found for analysis."}

        self.progress.start_phase("Mypy", total_steps=num_files)
        results = {}
        errors = []
        interrupted = False
        options = self.config.get("options", "").split()
        mypy_executable = self._get_tool_path()

        # Load skip patterns from config
        skip_patterns = self.config.get("skip_patterns", [])
        if isinstance(skip_patterns, str):
            skip_patterns = [skip_patterns]

        for idx, fpath in enumerate(file_list, 1):
            if skip_controller and skip_controller.should_skip():
                print("[MypyStage] Skip requested by user. Exiting phase early.")
                interrupted = True
                break

            # Skip files matching skip patterns
            should_skip = False
            for pattern in skip_patterns:
                if pattern in fpath:
                    logger.info(f"[MypyStage] Skipping file {fpath} due to skip pattern '{pattern}'")
                    should_skip = True
                    break
            if should_skip:
                continue
            # Construct command with full path to executable
            # Add --config-file to use our custom mypy.ini configuration
            mypy_ini_path = Path(__file__).parent / "mypy.ini"
            cmd = [mypy_executable, fpath, "--show-error-codes", "--no-color-output", "--no-error-summary", "--hide-error-context", "--pretty", "--config-file", str(mypy_ini_path)] + options
            try:
                # Log the command being run
                logger.debug(f"[MypyStage] Running command: {' '.join(cmd)}")
                try:
                    result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root, timeout=30)
                    if result.returncode not in (0, 1):  # 0: success, 1: type errors
                        # Log stderr if mypy itself failed (other than type errors)
                        error_msg = result.stderr.strip()
                        logger.error(f"[MypyStage] Mypy failed for {fpath} (return code {result.returncode}): {error_msg}")
                        errors.append({"file": fpath, "error": f"Mypy execution failed: {error_msg}"})
                        continue # Skip parsing stdout if mypy execution failed
                    # Store stdout even if there are type errors (return code 1)
                    results[fpath] = result.stdout
                except subprocess.TimeoutExpired:
                    error_msg = "Mypy execution timed out after 30 seconds"
                    logger.error(f"[MypyStage] {error_msg} for {fpath}")
                    errors.append({"file": fpath, "error": error_msg})
                    continue
            except FileNotFoundError:
                error_msg = f"Mypy executable not found at '{mypy_executable}'. Ensure PAT setup completed correctly."
                logger.error(f"[MypyStage] {error_msg}")
                errors.append({"file": fpath, "error": error_msg})
                # If one file fails due to not finding the executable, likely all will. Stop the phase.
                interrupted = True
                break
            except Exception as e:
                error_msg = f"Unexpected error running mypy for {fpath}: {e}"
                logger.error(f"[MypyStage] {error_msg}")
                errors.append({"file": fpath, "error": error_msg})
            self.progress.increment()

        self.progress.complete_phase("Mypy", status="Complete!" if not interrupted else "Skipped or Failed Early")
        # Print summary
        print(f"\n[MypyStage] {idx if interrupted else num_files} files processed. {len(errors)} files failed. See log for details.")
        return {"results": results, "errors": errors, "skipped": interrupted}

    def parse_output(self, output: Any) -> None:
        """
        Parse mypy output, update metrics with errors and type coverage.
        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['mypy'] includes: errors (list), type_coverage (float or dict), summary (str), raw_output (str)
            - FileMetrics.tool_results['mypy'] (for all files): errors (list), type_coverage (float)
        Args:
            output: Dict with per-file results and errors
        """
        if isinstance(output, dict) and output.get("skipped"):
            self.metrics.tool_results["mypy"] = output
            return
        if not isinstance(output, dict):
            return
        errors = []
        file_error_map: Dict[str, list] = {}
        total_files = 0
        files_with_errors = 0
        for fpath, out in output.get("results", {}).items():
            if not isinstance(out, str):
                continue
            for line in out.splitlines():
                if line.strip() == "":
                    continue
                if ": error:" in line:
                    errors.append(line)
                    file_error_map.setdefault(fpath, []).append(line)
                    files_with_errors += 1
                elif line.endswith(": note:") or line.startswith("Found "):
                    continue
                elif line.endswith(":"):
                    total_files += 1
        # Compute type coverage (if possible)
        type_coverage = 0.0
        if total_files > 0:
            type_coverage = 100.0 * (total_files - files_with_errors) / total_files
        summary = f"{len(errors)} errors, type coverage: {type_coverage:.1f}%"
        self.metrics.tool_results["mypy"] = {
            "errors": errors,
            "type_coverage": type_coverage,
            "summary": summary,
            "raw_output": output,
        }
        for key, file_metrics in self.metrics.files.items():
            abs_key = str(Path(file_metrics.path).resolve())
            file_errors = file_error_map.get(abs_key, [])
            file_metrics.tool_results["mypy"] = {
                "errors": file_errors,
                "type_coverage": 0.0 if file_errors else 100.0
            }

"""
Chunked Prompt Generator Module for PAT
=======================================

Generates LLM-ready prompt chunks from codebase analysis results, optimized for
context window size and overlap. Each chunk includes summaries, docstrings,
key code blocks, and architectural metadata. Outputs Markdown files and a manifest.

This is a backward compatibility module that imports from the modularized
chunked_prompt_generator package. New code should import directly from the package.
"""

# Import from the modularized package
from .chunked_prompt_generator import ChunkedPromptGenerator

# For backward compatibility, re-export ChunkedPromptGenerator
__all__ = ['ChunkedPromptGenerator']

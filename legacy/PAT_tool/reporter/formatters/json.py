"""
JSON formatter for PAT reports.

This module contains the JsonFormatter class for generating reports in JSON format.
"""

import json
from typing import Any, Dict, List, Optional

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics


class JsonFormatter:
    """Formatter for generating reports in JSON format."""
    
    def __init__(self):
        """Initialize the JSON formatter."""
        self.extension = "json"
    
    def format_summary_report(self, metrics: ProjectMetrics) -> Dict[str, Any]:
        """
        Format a summary report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted summary report as a dictionary
        """
        # Calculate metrics
        total_lines = sum(f.lines for f in metrics.files.values() if hasattr(f, 'lines'))
        avg_complexity = self._calculate_avg_complexity(metrics)
        issue_summary = self._generate_issue_summary(metrics)
        problematic_files = self._identify_problematic_files(metrics)
        recommendations = self._generate_recommendations(metrics)
        
        # Build summary report
        summary = {
            "report_type": "summary",
            "project": {
                "name": metrics.project_name,
                "files_analyzed": len(metrics.files),
                "total_lines": total_lines,
                "average_complexity": avg_complexity
            },
            "issue_summary": issue_summary,
            "problematic_files": problematic_files[:5],  # Top 5
            "recommendations": recommendations
        }
        
        return summary
    
    def format_detailed_report(self, metrics: ProjectMetrics) -> Dict[str, Any]:
        """
        Format a detailed report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted detailed report as a dictionary
        """
        # Calculate metrics
        total_lines = sum(f.lines for f in metrics.files.values() if hasattr(f, 'lines'))
        avg_complexity = self._calculate_avg_complexity(metrics)
        quality_metrics = self._generate_quality_metrics(metrics)
        issue_analysis = self._generate_issue_analysis(metrics)
        file_analysis = self._generate_file_analysis(metrics)
        recommendations = self._generate_recommendations(metrics)
        
        # Build detailed report
        detailed = {
            "report_type": "detailed",
            "project": {
                "name": metrics.project_name,
                "files_analyzed": len(metrics.files),
                "total_lines": total_lines,
                "average_complexity": avg_complexity
            },
            "quality_metrics": quality_metrics,
            "issue_analysis": issue_analysis,
            "file_analysis": file_analysis,
            "recommendations": recommendations
        }
        
        return detailed
    
    def format_issue_report(self, metrics: ProjectMetrics) -> Dict[str, Any]:
        """
        Format an issue report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted issue report as a dictionary
        """
        # Calculate metrics
        issue_summary = self._generate_issue_summary(metrics)
        severity_issues = self._generate_severity_issues(metrics)
        type_issues = self._generate_type_issues(metrics)
        file_issues = self._generate_file_issues(metrics)
        
        # Build issue report
        issue_report = {
            "report_type": "issues",
            "issue_summary": issue_summary,
            "issues_by_severity": severity_issues,
            "issues_by_type": type_issues,
            "issues_by_file": file_issues
        }
        
        return issue_report
    
    def format_meta_system_report(self, metrics: ProjectMetrics) -> Dict[str, Any]:
        """
        Format a meta-system report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted meta-system report as a dictionary
        """
        # Build meta-system report
        meta_system_report = {
            "report_type": "meta_system",
            "meta_systems": {}
        }
        
        # Add meta-system overview
        if hasattr(metrics, 'meta_systems') and metrics.meta_systems:
            meta_system_report["meta_systems"] = metrics.meta_systems
        
        # Add violations
        if hasattr(metrics, 'meta_system_violations') and metrics.meta_system_violations:
            meta_system_report["violations"] = metrics.meta_system_violations
        
        # Add recommendations
        meta_system_report["recommendations"] = [
            "Ensure meta-systems are properly separated",
            "Use interfaces for communication between meta-systems",
            "Avoid direct dependencies between meta-systems"
        ]
        
        return meta_system_report
    
    def _calculate_avg_complexity(self, metrics: ProjectMetrics) -> float:
        """Calculate average complexity from project metrics."""
        complexities = [f.complexity for f in metrics.files.values() if hasattr(f, 'complexity')]
        return sum(complexities) / len(complexities) if complexities else 0
    
    def _generate_issue_summary(self, metrics: ProjectMetrics) -> Dict[str, int]:
        """Generate issue summary from project metrics."""
        tool_issues = {}
        
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool not in tool_issues:
                    tool_issues[tool] = 0
                
                if tool == 'mypy' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    tool_issues[tool] += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    tool_issues[tool] += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
        
        return tool_issues
    
    def _identify_problematic_files(self, metrics: ProjectMetrics) -> List[Dict[str, Any]]:
        """Identify problematic files from project metrics."""
        problematic_files = []
        
        for path, file_metric in metrics.files.items():
            issues = 0
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issues += len(results['errors'])
            
            if issues > 0 or getattr(file_metric, 'complexity', 0) > 5:
                problematic_files.append({
                    'path': path,
                    'issues': issues,
                    'complexity': getattr(file_metric, 'complexity', 0),
                    'lines': getattr(file_metric, 'lines', 0)
                })
        
        # Sort by issues (descending)
        problematic_files.sort(key=lambda x: x['issues'], reverse=True)
        
        return problematic_files
    
    def _generate_recommendations(self, metrics: ProjectMetrics) -> List[str]:
        """Generate recommendations from project metrics."""
        recommendations = []
        
        # Count issues by type
        issue_types = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issue_types['type_errors'] = issue_types.get('type_errors', 0) + len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issue_types['lint_issues'] = issue_types.get('lint_issues', 0) + len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issue_types['security_issues'] = issue_types.get('security_issues', 0) + len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issue_types['docstring_issues'] = issue_types.get('docstring_issues', 0) + len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issue_types['pylint_issues'] = issue_types.get('pylint_issues', 0) + len(results['errors'])
        
        # Generate recommendations based on issue types
        if issue_types.get('security_issues', 0) > 0:
            recommendations.append("Address Security Issues: Fix security vulnerabilities identified by Bandit")
        
        if issue_types.get('type_errors', 0) > 0:
            recommendations.append("Fix Type Errors: Resolve type inconsistencies identified by mypy")
        
        if issue_types.get('lint_issues', 0) > 0:
            recommendations.append("Address Lint Issues: Fix code quality issues identified by ruff")
        
        if issue_types.get('docstring_issues', 0) > 0:
            recommendations.append("Improve Documentation: Add or fix docstrings as identified by pydocstyle")
        
        # Add general recommendations
        if not recommendations:
            recommendations.append("Maintain Code Quality: Continue to monitor and maintain code quality")
            recommendations.append("Add Tests: Increase test coverage for critical components")
            recommendations.append("Refactor Complex Code: Simplify complex functions and classes")
        
        return recommendations
    
    def _generate_quality_metrics(self, metrics: ProjectMetrics) -> Dict[str, Any]:
        """Generate quality metrics from project metrics."""
        # Calculate complexity metrics
        complexities = [f.complexity for f in metrics.files.values() if hasattr(f, 'complexity')]
        avg_complexity = sum(complexities) / len(complexities) if complexities else 0
        max_complexity = max(complexities) if complexities else 0
        
        # Calculate size metrics
        lines = [f.lines for f in metrics.files.values() if hasattr(f, 'lines')]
        avg_lines = sum(lines) / len(lines) if lines else 0
        max_lines = max(lines) if lines else 0
        
        # Format quality metrics
        quality_metrics = {
            "complexity": {
                "average": avg_complexity,
                "maximum": max_complexity,
                "high_complexity_files": sum(1 for c in complexities if c > 10)
            },
            "size": {
                "average_lines": avg_lines,
                "maximum_lines": max_lines,
                "large_files": sum(1 for l in lines if l > 500)
            }
        }
        
        return quality_metrics
    
    def _generate_issue_analysis(self, metrics: ProjectMetrics) -> Dict[str, Dict[str, int]]:
        """Generate issue analysis from project metrics."""
        tool_issues = {}
        
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool not in tool_issues:
                    tool_issues[tool] = {}
                
                if tool == 'mypy' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('type', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('code', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('test_id', 'unknown')
                        tool_issues[tool][issue_type] = tool_issues[tool].get(issue_type, 0) + 1
                elif tool == 'pydocstyle' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('code', 'unknown')
                        tool_issues[tool][issue_type] = tool_issues[tool].get(issue_type, 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('symbol', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
        
        return tool_issues
    
    def _generate_file_analysis(self, metrics: ProjectMetrics) -> Dict[str, Dict[str, Any]]:
        """Generate file-by-file analysis from project metrics."""
        file_analysis = {}
        
        for path, file_metric in metrics.files.items():
            file_analysis[path] = {
                "lines": getattr(file_metric, 'lines', 0),
                "complexity": getattr(file_metric, 'complexity', 0),
                "issues": {}
            }
            
            # Add file issues
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results and results['errors']:
                    file_analysis[path]["issues"]["type_errors"] = len(results['errors'])
                elif tool == 'ruff' and 'errors' in results and results['errors']:
                    file_analysis[path]["issues"]["lint_issues"] = len(results['errors'])
                elif tool == 'bandit' and 'issues' in results and results['issues']:
                    file_analysis[path]["issues"]["security_issues"] = len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results and results['issues']:
                    file_analysis[path]["issues"]["docstring_issues"] = len(results['issues'])
                elif tool == 'pylint' and 'errors' in results and results['errors']:
                    file_analysis[path]["issues"]["pylint_issues"] = len(results['errors'])
        
        return file_analysis
    
    def _generate_severity_issues(self, metrics: ProjectMetrics) -> Dict[str, int]:
        """Generate issues by severity from project metrics."""
        severity_counts = {}
        
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    severity_counts['error'] = severity_counts.get('error', 0) + len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        severity = error.get('severity', 'warning')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        severity = issue.get('issue_severity', 'medium')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        severity = error.get('type', 'warning')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        return severity_counts
    
    def _generate_type_issues(self, metrics: ProjectMetrics) -> Dict[str, int]:
        """Generate issues by type from project metrics."""
        type_counts = {}
        
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('type', 'unknown')
                        type_counts[f"mypy:{error_type}"] = type_counts.get(f"mypy:{error_type}", 0) + 1
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('code', 'unknown')
                        type_counts[f"ruff:{error_type}"] = type_counts.get(f"ruff:{error_type}", 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('test_id', 'unknown')
                        type_counts[f"bandit:{issue_type}"] = type_counts.get(f"bandit:{issue_type}", 0) + 1
                elif tool == 'pydocstyle' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('code', 'unknown')
                        type_counts[f"pydocstyle:{issue_type}"] = type_counts.get(f"pydocstyle:{issue_type}", 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('symbol', 'unknown')
                        type_counts[f"pylint:{error_type}"] = type_counts.get(f"pylint:{error_type}", 0) + 1
        
        return type_counts
    
    def _generate_file_issues(self, metrics: ProjectMetrics) -> Dict[str, int]:
        """Generate issues by file from project metrics."""
        file_counts = {}
        
        for path, file_metric in metrics.files.items():
            issues = 0
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issues += len(results['errors'])
            
            if issues > 0:
                file_counts[path] = issues
        
        return file_counts

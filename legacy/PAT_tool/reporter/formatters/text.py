"""
Text formatter for PAT reports.

This module contains the TextFormatter class for generating reports in text format.
"""

from typing import Any, Dict, List, Optional

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics


class TextFormatter:
    """Formatter for generating reports in text (Markdown) format."""
    
    def __init__(self):
        """Initialize the text formatter."""
        self.extension = "md"
    
    def format_summary_report(self, metrics: ProjectMetrics) -> str:
        """
        Format a summary report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted summary report as a string
        """
        summary = "# PAT Analysis Summary Report\n\n"
        
        # Add project overview
        summary += "## Project Overview\n\n"
        summary += f"- **Project**: {metrics.project_name}\n"
        summary += f"- **Files Analyzed**: {len(metrics.files)}\n"
        summary += f"- **Total Lines**: {sum(f.lines for f in metrics.files.values() if hasattr(f, 'lines'))}\n"
        summary += f"- **Average Complexity**: {self._calculate_avg_complexity(metrics):.2f}\n\n"
        
        # Add issue summary
        summary += "## Issue Summary\n\n"
        issue_summary = self._generate_issue_summary(metrics)
        summary += issue_summary + "\n\n"
        
        # Add top problematic files
        summary += "## Top Problematic Files\n\n"
        problematic_files = self._identify_problematic_files(metrics)
        for i, file in enumerate(problematic_files[:5]):  # Show top 5
            summary += f"{i+1}. **{file['path']}**\n"
            summary += f"   - Issues: {file.get('issues', 0)}\n"
            summary += f"   - Complexity: {file.get('complexity', 0)}\n"
            summary += f"   - Lines: {file.get('lines', 0)}\n\n"
        
        # Add recommendations
        summary += "## Recommendations\n\n"
        recommendations = self._generate_recommendations(metrics)
        summary += recommendations
        
        return summary
    
    def format_detailed_report(self, metrics: ProjectMetrics) -> str:
        """
        Format a detailed report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted detailed report as a string
        """
        report = "# PAT Detailed Analysis Report\n\n"
        
        # Add table of contents
        report += "## Table of Contents\n\n"
        report += "1. [Project Overview](#project-overview)\n"
        report += "2. [Code Quality Metrics](#code-quality-metrics)\n"
        report += "3. [Issue Analysis](#issue-analysis)\n"
        report += "4. [File-by-File Analysis](#file-by-file-analysis)\n"
        report += "5. [Recommendations](#recommendations)\n\n"
        
        # Add project overview
        report += "## Project Overview\n\n"
        report += f"- **Project**: {metrics.project_name}\n"
        report += f"- **Files Analyzed**: {len(metrics.files)}\n"
        report += f"- **Total Lines**: {sum(f.lines for f in metrics.files.values() if hasattr(f, 'lines'))}\n"
        report += f"- **Average Complexity**: {self._calculate_avg_complexity(metrics):.2f}\n\n"
        
        # Add code quality metrics
        report += "## Code Quality Metrics\n\n"
        quality_metrics = self._generate_quality_metrics(metrics)
        report += quality_metrics + "\n\n"
        
        # Add issue analysis
        report += "## Issue Analysis\n\n"
        issue_analysis = self._generate_issue_analysis(metrics)
        report += issue_analysis + "\n\n"
        
        # Add file-by-file analysis
        report += "## File-by-File Analysis\n\n"
        file_analysis = self._generate_file_analysis(metrics)
        report += file_analysis + "\n\n"
        
        # Add recommendations
        report += "## Recommendations\n\n"
        recommendations = self._generate_recommendations(metrics)
        report += recommendations
        
        return report
    
    def format_issue_report(self, metrics: ProjectMetrics) -> str:
        """
        Format an issue report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted issue report as a string
        """
        report = "# PAT Issue Report\n\n"
        
        # Add issue summary
        report += "## Issue Summary\n\n"
        issue_summary = self._generate_issue_summary(metrics)
        report += issue_summary + "\n\n"
        
        # Add issues by severity
        report += "## Issues by Severity\n\n"
        severity_issues = self._generate_severity_issues(metrics)
        report += severity_issues + "\n\n"
        
        # Add issues by type
        report += "## Issues by Type\n\n"
        type_issues = self._generate_type_issues(metrics)
        report += type_issues + "\n\n"
        
        # Add issues by file
        report += "## Issues by File\n\n"
        file_issues = self._generate_file_issues(metrics)
        report += file_issues
        
        return report
    
    def format_meta_system_report(self, metrics: ProjectMetrics) -> str:
        """
        Format a meta-system report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted meta-system report as a string
        """
        report = "# PAT Meta-System Separation Report\n\n"
        
        # Add meta-system overview
        report += "## Meta-System Overview\n\n"
        if hasattr(metrics, 'meta_systems') and metrics.meta_systems:
            report += f"- **Meta-Systems Identified**: {len(metrics.meta_systems)}\n\n"
            
            for name, meta_system in metrics.meta_systems.items():
                report += f"### {name}\n\n"
                report += f"- **Files**: {len(meta_system.get('files', []))}\n"
                report += f"- **Dependencies**: {len(meta_system.get('dependencies', []))}\n\n"
        else:
            report += "No meta-systems identified in the project.\n\n"
        
        # Add violations
        report += "## Meta-System Violations\n\n"
        if hasattr(metrics, 'meta_system_violations') and metrics.meta_system_violations:
            for violation in metrics.meta_system_violations:
                report += f"- **{violation['type']}**: {violation['description']}\n"
                report += f"  - Source: {violation['source']}\n"
                report += f"  - Target: {violation['target']}\n\n"
        else:
            report += "No meta-system violations identified.\n\n"
        
        # Add recommendations
        report += "## Recommendations\n\n"
        report += "1. Ensure meta-systems are properly separated\n"
        report += "2. Use interfaces for communication between meta-systems\n"
        report += "3. Avoid direct dependencies between meta-systems\n"
        
        return report
    
    def _calculate_avg_complexity(self, metrics: ProjectMetrics) -> float:
        """Calculate average complexity from project metrics."""
        complexities = [f.complexity for f in metrics.files.values() if hasattr(f, 'complexity')]
        return sum(complexities) / len(complexities) if complexities else 0
    
    def _generate_issue_summary(self, metrics: ProjectMetrics) -> str:
        """Generate issue summary from project metrics."""
        summary = ""
        
        # Count issues by tool
        tool_issues = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool not in tool_issues:
                    tool_issues[tool] = 0
                
                if tool == 'mypy' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    tool_issues[tool] += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    tool_issues[tool] += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
        
        # Format issue summary
        for tool, count in tool_issues.items():
            summary += f"- **{tool.capitalize()}**: {count} issues\n"
        
        return summary
    
    def _identify_problematic_files(self, metrics: ProjectMetrics) -> List[Dict[str, Any]]:
        """Identify problematic files from project metrics."""
        problematic_files = []
        
        for path, file_metric in metrics.files.items():
            issues = 0
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issues += len(results['errors'])
            
            if issues > 0 or getattr(file_metric, 'complexity', 0) > 5:
                problematic_files.append({
                    'path': path,
                    'issues': issues,
                    'complexity': getattr(file_metric, 'complexity', 0),
                    'lines': getattr(file_metric, 'lines', 0)
                })
        
        # Sort by issues (descending)
        problematic_files.sort(key=lambda x: x['issues'], reverse=True)
        
        return problematic_files
    
    def _generate_recommendations(self, metrics: ProjectMetrics) -> str:
        """Generate recommendations from project metrics."""
        recommendations = ""
        
        # Count issues by type
        issue_types = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issue_types['type_errors'] = issue_types.get('type_errors', 0) + len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issue_types['lint_issues'] = issue_types.get('lint_issues', 0) + len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issue_types['security_issues'] = issue_types.get('security_issues', 0) + len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issue_types['docstring_issues'] = issue_types.get('docstring_issues', 0) + len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issue_types['pylint_issues'] = issue_types.get('pylint_issues', 0) + len(results['errors'])
        
        # Generate recommendations based on issue types
        if issue_types.get('security_issues', 0) > 0:
            recommendations += "1. **Address Security Issues**: Fix security vulnerabilities identified by Bandit\n"
        
        if issue_types.get('type_errors', 0) > 0:
            recommendations += "2. **Fix Type Errors**: Resolve type inconsistencies identified by mypy\n"
        
        if issue_types.get('lint_issues', 0) > 0:
            recommendations += "3. **Address Lint Issues**: Fix code quality issues identified by ruff\n"
        
        if issue_types.get('docstring_issues', 0) > 0:
            recommendations += "4. **Improve Documentation**: Add or fix docstrings as identified by pydocstyle\n"
        
        # Add general recommendations
        if not recommendations:
            recommendations += "1. **Maintain Code Quality**: Continue to monitor and maintain code quality\n"
            recommendations += "2. **Add Tests**: Increase test coverage for critical components\n"
            recommendations += "3. **Refactor Complex Code**: Simplify complex functions and classes\n"
        
        return recommendations
    
    def _generate_quality_metrics(self, metrics: ProjectMetrics) -> str:
        """Generate quality metrics from project metrics."""
        quality_metrics = ""
        
        # Calculate complexity metrics
        complexities = [f.complexity for f in metrics.files.values() if hasattr(f, 'complexity')]
        avg_complexity = sum(complexities) / len(complexities) if complexities else 0
        max_complexity = max(complexities) if complexities else 0
        
        # Calculate size metrics
        lines = [f.lines for f in metrics.files.values() if hasattr(f, 'lines')]
        avg_lines = sum(lines) / len(lines) if lines else 0
        max_lines = max(lines) if lines else 0
        
        # Format quality metrics
        quality_metrics += "### Complexity Metrics\n\n"
        quality_metrics += f"- **Average Complexity**: {avg_complexity:.2f}\n"
        quality_metrics += f"- **Maximum Complexity**: {max_complexity}\n"
        quality_metrics += f"- **Files with High Complexity**: {sum(1 for c in complexities if c > 10)}\n\n"
        
        quality_metrics += "### Size Metrics\n\n"
        quality_metrics += f"- **Average Lines**: {avg_lines:.2f}\n"
        quality_metrics += f"- **Maximum Lines**: {max_lines}\n"
        quality_metrics += f"- **Files with High Line Count**: {sum(1 for l in lines if l > 500)}\n\n"
        
        return quality_metrics
    
    def _generate_issue_analysis(self, metrics: ProjectMetrics) -> str:
        """Generate issue analysis from project metrics."""
        issue_analysis = ""
        
        # Count issues by tool and type
        tool_issues = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool not in tool_issues:
                    tool_issues[tool] = {}
                
                if tool == 'mypy' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('type', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('code', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('test_id', 'unknown')
                        tool_issues[tool][issue_type] = tool_issues[tool].get(issue_type, 0) + 1
                elif tool == 'pydocstyle' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('code', 'unknown')
                        tool_issues[tool][issue_type] = tool_issues[tool].get(issue_type, 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('symbol', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
        
        # Format issue analysis
        for tool, issues in tool_issues.items():
            issue_analysis += f"### {tool.capitalize()} Issues\n\n"
            
            # Sort issues by count (descending)
            sorted_issues = sorted(issues.items(), key=lambda x: x[1], reverse=True)
            
            for issue_type, count in sorted_issues[:10]:  # Show top 10
                issue_analysis += f"- **{issue_type}**: {count}\n"
            
            if len(sorted_issues) > 10:
                issue_analysis += f"- ... and {len(sorted_issues) - 10} more\n"
            
            issue_analysis += "\n"
        
        return issue_analysis
    
    def _generate_file_analysis(self, metrics: ProjectMetrics) -> str:
        """Generate file-by-file analysis from project metrics."""
        file_analysis = ""
        
        # Sort files by path
        sorted_files = sorted(metrics.files.items(), key=lambda x: x[0])
        
        for path, file_metric in sorted_files:
            file_analysis += f"### {path}\n\n"
            
            # Add file metrics
            file_analysis += f"- **Lines**: {getattr(file_metric, 'lines', 'Unknown')}\n"
            file_analysis += f"- **Complexity**: {getattr(file_metric, 'complexity', 'Unknown')}\n"
            
            # Add file issues
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results and results['errors']:
                    file_analysis += f"- **Type Errors**: {len(results['errors'])}\n"
                elif tool == 'ruff' and 'errors' in results and results['errors']:
                    file_analysis += f"- **Lint Issues**: {len(results['errors'])}\n"
                elif tool == 'bandit' and 'issues' in results and results['issues']:
                    file_analysis += f"- **Security Issues**: {len(results['issues'])}\n"
                elif tool == 'pydocstyle' and 'issues' in results and results['issues']:
                    file_analysis += f"- **Docstring Issues**: {len(results['issues'])}\n"
                elif tool == 'pylint' and 'errors' in results and results['errors']:
                    file_analysis += f"- **Pylint Issues**: {len(results['errors'])}\n"
            
            file_analysis += "\n"
        
        return file_analysis
    
    def _generate_severity_issues(self, metrics: ProjectMetrics) -> str:
        """Generate issues by severity from project metrics."""
        severity_issues = ""
        
        # Count issues by severity
        severity_counts = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    severity_counts['error'] = severity_counts.get('error', 0) + len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        severity = error.get('severity', 'warning')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        severity = issue.get('issue_severity', 'medium')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        severity = error.get('type', 'warning')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        # Format severity issues
        for severity, count in sorted(severity_counts.items(), key=lambda x: x[1], reverse=True):
            severity_issues += f"- **{severity.capitalize()}**: {count}\n"
        
        return severity_issues
    
    def _generate_type_issues(self, metrics: ProjectMetrics) -> str:
        """Generate issues by type from project metrics."""
        type_issues = ""
        
        # Count issues by type
        type_counts = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('type', 'unknown')
                        type_counts[f"mypy:{error_type}"] = type_counts.get(f"mypy:{error_type}", 0) + 1
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('code', 'unknown')
                        type_counts[f"ruff:{error_type}"] = type_counts.get(f"ruff:{error_type}", 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('test_id', 'unknown')
                        type_counts[f"bandit:{issue_type}"] = type_counts.get(f"bandit:{issue_type}", 0) + 1
                elif tool == 'pydocstyle' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('code', 'unknown')
                        type_counts[f"pydocstyle:{issue_type}"] = type_counts.get(f"pydocstyle:{issue_type}", 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('symbol', 'unknown')
                        type_counts[f"pylint:{error_type}"] = type_counts.get(f"pylint:{error_type}", 0) + 1
        
        # Format type issues
        for issue_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True)[:20]:  # Show top 20
            type_issues += f"- **{issue_type}**: {count}\n"
        
        if len(type_counts) > 20:
            type_issues += f"- ... and {len(type_counts) - 20} more\n"
        
        return type_issues
    
    def _generate_file_issues(self, metrics: ProjectMetrics) -> str:
        """Generate issues by file from project metrics."""
        file_issues = ""
        
        # Count issues by file
        file_counts = {}
        for path, file_metric in metrics.files.items():
            issues = 0
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issues += len(results['errors'])
            
            if issues > 0:
                file_counts[path] = issues
        
        # Format file issues
        for path, count in sorted(file_counts.items(), key=lambda x: x[1], reverse=True)[:20]:  # Show top 20
            file_issues += f"- **{path}**: {count}\n"
        
        if len(file_counts) > 20:
            file_issues += f"- ... and {len(file_counts) - 20} more\n"
        
        return file_issues

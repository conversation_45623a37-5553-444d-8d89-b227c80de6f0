"""
Base reporter module for PAT.

This module contains the main Pat<PERSON><PERSON>orter class that orchestrates
the report generation process.
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.pipeline import AnalysisStage
    from PAT_tool.utils import ensure_dir, logger
    from .formatters import <PERSON><PERSON><PERSON>atter, HtmlFormatter, JsonFormatter
except ImportError:
    # When running as a script
    from models import ProjectMetrics
    from pipeline import AnalysisStage
    from utils import ensure_dir, logger
    from formatters import TextFormatter, HtmlFormatter, JsonFormatter


class PatReporter:
    """
    Pipeline stage that generates comprehensive reports from PAT analysis results.
    Supports multiple output formats including text, HTML, and JSON.
    """
    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress: Any,
                 output_dir: Optional[str] = None, formats: Optional[List[str]] = None):
        """
        Args:
            project_root: Path to the project root directory.
            metrics: Shared ProjectMetrics object for storing results.
            progress: progress tracker instance for reporting progress.
            output_dir: Directory to write reports (default: PAT_output/reports)
            formats: List of output formats to generate (default: ["text", "html", "json"])
        """
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        self.output_dir = output_dir or str(project_root / "PAT_output" / "reports")
        self.formats = formats or ["text", "html", "json"]
        ensure_dir(self.output_dir)
        
        # Initialize formatters
        self.formatters = {
            "text": TextFormatter(),
            "html": HtmlFormatter(),
            "json": JsonFormatter()
        }

    def run(self, *args, **kwargs) -> None:
        """Generate reports from analyzed codebase.

        Args:
            *args: Ignored. Present for pipeline compatibility.
            **kwargs: Ignored. Present for pipeline compatibility.
        """
        # Ensure output directory exists with proper permissions
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            # Test write permissions by creating a test file
            test_file = os.path.join(self.output_dir, "test_write_permission.txt")
            with open(test_file, "w") as f:
                f.write("Test write permission")
            os.remove(test_file)  # Clean up test file
            logger.info(f"Output directory {self.output_dir} exists and is writable")
        except Exception as e:
            logger.error(f"Error with output directory {self.output_dir}: {e}")
            # Try to create an alternative output directory
            alt_output_dir = os.path.join(os.path.dirname(self.output_dir), "reports_alt")
            try:
                os.makedirs(alt_output_dir, exist_ok=True)
                self.output_dir = alt_output_dir
                logger.info(f"Using alternative output directory: {self.output_dir}")
            except Exception as e2:
                logger.error(f"Error creating alternative output directory: {e2}")
                # Continue anyway, but log the error
        
        # Generate reports in each format
        for fmt in self.formats:
            if fmt not in self.formatters:
                logger.warning(f"Unsupported format: {fmt}")
                continue
                
            try:
                formatter = self.formatters[fmt]
                
                # Generate summary report
                summary_report = formatter.format_summary_report(self.metrics)
                self._write_report(summary_report, f"summary.{formatter.extension}", fmt)
                
                # Generate detailed report
                detailed_report = formatter.format_detailed_report(self.metrics)
                self._write_report(detailed_report, f"detailed.{formatter.extension}", fmt)
                
                # Generate issue report
                issue_report = formatter.format_issue_report(self.metrics)
                self._write_report(issue_report, f"issues.{formatter.extension}", fmt)
                
                # Generate meta-system report if applicable
                if hasattr(self.metrics, 'meta_systems') and self.metrics.meta_systems:
                    meta_system_report = formatter.format_meta_system_report(self.metrics)
                    self._write_report(meta_system_report, f"meta_systems.{formatter.extension}", fmt)
                
                logger.info(f"Generated {fmt} reports in {self.output_dir}")
            except Exception as e:
                logger.error(f"Error generating {fmt} reports: {e}")
        
        logger.info(f"Report generation complete. Reports available in {self.output_dir}")

    def _write_report(self, content: Union[str, Dict[str, Any]], filename: str, format_type: str) -> None:
        """Write report content to a file.

        Args:
            content: Report content (string for text/html, dict for JSON)
            filename: Output filename
            format_type: Format type (text, html, json)
        """
        output_path = os.path.join(self.output_dir, filename)
        try:
            if format_type == "json":
                import json
                with open(output_path, "w", encoding="utf-8") as f:
                    json.dump(content, f, indent=2)
            else:
                with open(output_path, "w", encoding="utf-8") as f:
                    f.write(content)
        except Exception as e:
            logger.error(f"Error writing report to {output_path}: {e}")

# Simplified PAT Tool Configuration
# This configuration disables problematic components and focuses on essential analysis

# Progress bar configuration
progress_bar:
  type: simple  # Use simple progress bar to avoid rich.live issues
  simple_tracker_details: true  # Show details in simple tracker

# Pre-analysis configuration
pre_analysis:
  enabled: true  # Enable pre-analysis to check for syntax errors
  auto_patignore: true  # Automatically create .patignore file for files with syntax errors

# Dependency audit configuration
dependency_audit:
  enabled: false  # Disable dependency audit to avoid issues

# Profiling configuration
profiling:
  timing: true  # Enable timing to see how long each phase takes
  cprofile: false  # Disable cProfile to avoid overhead

# Tool configurations
tools:
  # Disable problematic tools
  effect_overlay:
    enabled: false  # Disable effect overlay to avoid plotly dependency issues
  pycontract:
    enabled: false  # Disable pycontract to avoid missing executable issues
  tla:
    enabled: false  # Disable TLA+ to avoid missing spec file issues
  graph_overlay:
    enabled: false  # Disable graph overlay to avoid pyvis dependency issues
  tda_overlay:
    enabled: false  # Disable TDA overlay to avoid dependency issues
  hypothesis:
    enabled: false  # Disable hypothesis to avoid dependency issues
  
  # Enable essential tools
  protocol_extraction:
    enabled: false  # Disable protocol extraction to simplify analysis
  
  # Type checking tools
  mypy:
    enabled: false  # Disable mypy to avoid issues
  pyright:
    enabled: true   # Enable pyright for type checking
    options: "--level warning"  # Use warning level instead of error
    skip_on_error: true
    skip_phase: false
    # Patterns to skip files that are known to cause issues with pyright
    skip_patterns: [
      "__pycache__",
      ".git",
      "venv",
      "node_modules",
      "test_",
      "_test"
    ]
  
  # Code quality tools
  ruff:
    enabled: true  # Enable ruff for linting
    options: "--select=E,F,W --ignore=E501,E722,F401,F841"  # Ignore common issues
    skip_on_error: true
    skip_phase: false
  
  bandit:
    enabled: false  # Disable bandit to simplify analysis
  
  coverage:
    enabled: false  # Disable coverage to simplify analysis
  
  flake8:
    enabled: false  # Disable flake8 to simplify analysis
  
  pylint:
    enabled: false  # Disable pylint to simplify analysis
  
  black:
    enabled: false  # Disable black to simplify analysis
  
  isort:
    enabled: false  # Disable isort to simplify analysis
  
  pydocstyle:
    enabled: false  # Disable pydocstyle to simplify analysis
  
  dependency:
    enabled: true  # Enable dependency analysis
    options: ""
    skip_on_error: true
    skip_phase: false
  
  call_graph:
    enabled: false  # Disable call graph to simplify analysis
  
  pyre:
    enabled: false  # Disable pyre to simplify analysis

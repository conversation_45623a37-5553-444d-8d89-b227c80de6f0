"""
Flake8Stage for PAT
==================

Integrates flake8 (Python linter/style checker) as a modular pipeline stage. Runs flake8 on the codebase,
parses lint errors/warnings, and updates ProjectMetrics/FileMetrics for downstream use.

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)
    - ruff_stage.py (similar linter integration)

External Dependencies:
    - flake8 (must be installed in the environment)

"""

import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics

import re

try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class Flake8Stage(ToolStage):
    """Pipeline stage for running flake8 and collecting lint/style diagnostics.

    Assigns errors to both project-level and per-file tool_results.
    """
    tool_name = "flake8"

    def _get_tool_path(self):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "flake8"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_executable} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run flake8 as a subprocess on each Python file individually, with robust error handling.
        Interruptible via skip_controller.
        Returns:
            Dict with per-file results and errors.
        """
        # Discover all Python files
        file_list = []
        for root, _, files in os.walk(self.project_root):
            # Respect .patignore
            ignore_path = Path(root) / ".patignore"
            ignored_files = set()
            if ignore_path.exists():
                with open(ignore_path, 'r') as f:
                    ignored_files = {Path(root) / line.strip() for line in f}

            for fname in files:
                if fname.endswith(".py"):
                    full_path = Path(root) / fname
                    if full_path not in ignored_files:
                        file_list.append(str(full_path))

        num_files = len(file_list)
        if num_files == 0:
            logger.warning("[Flake8Stage] No Python files found for analysis.")
            return {"error": "No Python files found for analysis."}
        self.progress.start_phase("Flake8", total_steps=num_files)
        results = {}
        errors = []
        interrupted = False
        options = self.config.get("options", "").split()
        flake8_executable = self._get_tool_path()

        for idx, fpath in enumerate(file_list, 1):
            if skip_controller and skip_controller.should_skip():
                print("[Flake8Stage] Skip requested by user. Exiting phase early.")
                interrupted = True
                break
            # Construct command with full path to executable
            cmd = [flake8_executable, fpath] + options
            try:
                # logger.debug(f"[Flake8Stage] Running command: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
                if result.returncode not in (0, 1):  # 0: success, 1: lint errors
                    error_msg = result.stderr.strip() or f"Flake8 execution failed with code {result.returncode}"
                    logger.error(f"[Flake8Stage] Flake8 failed for {fpath}: {error_msg}")
                    errors.append({"file": fpath, "error": f"Flake8 execution failed: {error_msg}"})
                    continue
                # Store stdout even if lint errors exist (return code 1)
                results[fpath] = result.stdout
            except FileNotFoundError:
                error_msg = f"Flake8 executable not found at '{flake8_executable}'. Ensure PAT setup completed correctly."
                logger.error(f"[Flake8Stage] {error_msg}")
                errors.append({"file": fpath, "error": error_msg})
                interrupted = True
                break # Assume all will fail
            except Exception as e:
                error_msg = f"Unexpected error running flake8 for {fpath}: {e}"
                logger.error(f"[Flake8Stage] {error_msg}")
                errors.append({"file": fpath, "error": error_msg})

            self.progress.increment()

        self.progress.complete_phase("Flake8", status="Complete!" if not interrupted else "Skipped or Failed Early")
        print(f"\n[Flake8Stage] {idx if interrupted else num_files} files processed. {len(errors)} files failed. See log for details.")
        return {"results": results, "errors": errors, "skipped": interrupted}

    def parse_output(self, output: Any) -> None:
        """
        Parse flake8 output, update metrics with linting errors and summary.
        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['flake8'] includes: errors (list), summary (str), raw_output (str)
            - FileMetrics.tool_results['flake8'] (for all files): errors (list), summary (str)
        Args:
            output: Dict with per-file results and errors
        """
        if isinstance(output, dict) and output.get("skipped"):
            self.metrics.tool_results["flake8"] = output
            return
        if not isinstance(output, dict):
            return
        errors = []
        file_error_map: Dict[str, list] = {}
        flake8_pattern = re.compile(r"^(.*?):(\d+):(\d+): (\w+) (.*)$")
        for fpath, out in output.get("results", {}).items():
            if not isinstance(out, str):
                continue
            for line in out.splitlines():
                if line.strip() == "":
                    continue
                if ":" in line:
                    errors.append(line)
                    match = flake8_pattern.match(line)
                    if match:
                        file_path = fpath
                        file_error_map.setdefault(file_path, []).append(line)
        summary = f"{len(errors)} errors"
        self.metrics.tool_results["flake8"] = {
            "errors": errors,
            "summary": summary,
            "raw_output": output,
        }
        for key, file_metrics in self.metrics.files.items():
            abs_key = str(Path(file_metrics.path).resolve())
            file_errors = file_error_map.get(abs_key, [])
            file_metrics.tool_results["flake8"] = {
                "errors": file_errors,
                "summary": f"{len(file_errors)} errors"
            }

# TODO:
# - Add unit tests for output parsing and per-file assignment
# - Add config options for enabling/disabling, custom flake8 config

# TODO:
# - Add per-file error assignment if needed (update FileMetrics)
# - Add unit tests for output parsing
# - Add config options for enabling/disabling, custom flake8 config 
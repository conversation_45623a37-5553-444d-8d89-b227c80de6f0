#!/usr/bin/env python3
"""
Setup script for the Project Analysis Tool

This script sets up a virtual environment with all necessary dependencies
for the Project Analysis Tool.
"""

import argparse
import os
import platform
import subprocess
import sys
from pathlib import Path

# Define required packages
REQUIRED_PACKAGES = [
    "networkx",
    "matplotlib",
    "radon",
    "astroid",
    "pylint",
    "pydeps",
    "graphviz"
]

def parse_args():
    """Parse command line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Setup script for the Project Analysis Tool"
    )
    parser.add_argument(
        "--venv-dir",
        default="scripts/project_analyse_tool/venv",
        help="Path to create virtual environment (default: scripts/project_analyse_tool/venv)"
    )
    parser.add_argument(
        "--no-graphviz",
        action="store_true",
        help="Skip installing graphviz (use if you don't need dependency graph visualization)"
    )
    
    return parser.parse_args()

def run_command(cmd, cwd=None):
    """Run a shell command and capture output.
    
    Args:
        cmd: Command to run as a list of strings
        cwd: Working directory
        
    Returns:
        Command output
        
    Raises:
        SystemExit on command failure
    """
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {' '.join(cmd)}")
        print(f"Error output:\n{e.stderr}")
        sys.exit(1)

def create_virtualenv(venv_dir):
    """Create a virtual environment for the analysis tool.
    
    Args:
        venv_dir: Path to create virtual environment
        
    Returns:
        Path to the created virtual environment
    """
    venv_path = Path(venv_dir)
    
    if venv_path.exists():
        print(f"Virtual environment already exists at {venv_path}")
        overwrite = input("Overwrite? (y/n): ").lower()
        if overwrite != 'y':
            print("Using existing virtual environment")
            return venv_path
        
        # Remove existing venv
        if platform.system() == "Windows":
            run_command(["rmdir", "/s", "/q", str(venv_path)])
        else:
            run_command(["rm", "-rf", str(venv_path)])
    
    # Create parent directory if needed
    venv_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Create virtual environment
    print(f"Creating virtual environment at {venv_path}...")
    run_command([sys.executable, "-m", "venv", str(venv_path)])
    
    return venv_path

def install_packages(venv_path, skip_graphviz=False):
    """Install required packages in the virtual environment.
    
    Args:
        venv_path: Path to virtual environment
        skip_graphviz: Whether to skip installing graphviz
    """
    # Determine pip path
    if platform.system() == "Windows":
        pip_path = venv_path / "Scripts" / "pip"
    else:
        pip_path = venv_path / "bin" / "pip"
    
    # Upgrade pip
    print("Upgrading pip...")
    run_command([str(pip_path), "install", "--upgrade", "pip"])
    
    # Install packages
    packages = REQUIRED_PACKAGES.copy()
    if skip_graphviz:
        packages.remove("graphviz")
    
    print(f"Installing required packages: {', '.join(packages)}")
    run_command([str(pip_path), "install"] + packages)

def create_output_directory():
    """Create output directory for analysis results."""
    output_dir = Path("scripts/project_analyse_tool/output")
    output_dir.mkdir(parents=True, exist_ok=True)
    print(f"Created output directory at {output_dir}")

def main():
    """Main entry point for the setup script."""
    args = parse_args()
    
    # Create virtual environment
    venv_path = create_virtualenv(args.venv_dir)
    
    # Install required packages
    install_packages(venv_path, args.no_graphviz)
    
    # Create output directory
    create_output_directory()
    
    # Show activation instructions
    if platform.system() == "Windows":
        activate_cmd = f"{venv_path}\\Scripts\\activate"
    else:
        activate_cmd = f"source {venv_path}/bin/activate"
    
    print("\nSetup complete!")
    print("=" * 80)
    print(f"To activate the virtual environment, run:\n\n{activate_cmd}\n")
    print("Then run the analysis tool with:")
    print(f"python -m scripts.project_analyse_tool.main <project_path>")
    print("=" * 80)

if __name__ == "__main__":
    main() 
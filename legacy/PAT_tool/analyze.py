#!/usr/bin/env python3
"""
Project Analysis Tool - Wrapper Script

This is a simple wrapper script for running the Project Analysis Tool.
It ensures that the virtual environment is activated before running the analysis.

Usage:
    python scripts/project_analyse_tool/analyze.py <project_root>
"""

import os
import platform
import subprocess
import sys
from pathlib import Path


def find_venv():
    """Find the virtual environment directory.
    
    Returns:
        Path to virtual environment or None if not found
    """
    # Check default location
    default_venv = Path("scripts/project_analyse_tool/venv")
    if default_venv.exists():
        return default_venv
    
    # Check alternative location
    alt_venv = Path("analysis_venv")
    if alt_venv.exists():
        return alt_venv
    
    return None

def is_venv_active():
    """Check if a virtual environment is already active.
    
    Returns:
        True if a virtual environment is active, False otherwise
    """
    return sys.prefix != sys.base_prefix

def run_in_venv(venv_path, args):
    """Run the analysis tool in the virtual environment.
    
    Args:
        venv_path: Path to virtual environment
        args: Command line arguments
        
    Returns:
        Exit code from the command
    """
    # Determine python path in the venv
    if platform.system() == "Windows":
        python_path = venv_path / "Scripts" / "python"
    else:
        python_path = venv_path / "bin" / "python"
    
    # Build the command
    cmd = [str(python_path), "-m", "scripts.project_analyse_tool.main"] + args
    
    # Run the command
    print(f"Running: {' '.join(cmd)}")
    try:
        return subprocess.call(cmd)
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user.")
        return 1

def main():
    """Main entry point for the wrapper script."""
    # Get command line arguments
    args = sys.argv[1:]
    
    if not args or args[0] in ["-h", "--help"]:
        print(__doc__)
        return 0
    
    # Check if running in a virtual environment
    if is_venv_active():
        # Already in a venv, just run the tool
        from scripts.project_analyse_tool.main import main as run_analysis
        return run_analysis()
    
    # Find virtual environment
    venv_path = find_venv()
    if not venv_path:
        print("Error: Virtual environment not found!")
        print("Please run 'python scripts/project_analyse_tool/setup.py' first.")
        return 1
    
    # Run the tool in the virtual environment
    return run_in_venv(venv_path, args)

if __name__ == "__main__":
    sys.exit(main()) 
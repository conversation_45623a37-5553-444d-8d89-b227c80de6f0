"""
DependencyStage for PAT
======================

Integrates pipdeptree (dependency analysis) and safety (vulnerability scanning) as a modular pipeline stage. Runs both tools on the environment,
parses the dependency tree and security issues, and updates ProjectMetrics for downstream use.

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)

External Dependencies:
    - pipdeptree (must be installed in the environment)
    - safety (must be installed in the environment)

"""

import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import json
import os
import platform


class DependencyStage(ToolStage):
    """Pipeline stage for running pipdeptree and safety to collect dependency and security diagnostics.

    Stores results in ProjectMetrics.tool_results['dependency'].
    """
    tool_name = "dependency"

    def _get_tool_path(self, tool_name: str):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = tool_name
        if platform.system() == "Windows":
            # pipdeptree might be a script, safety might be exe?
            if tool_name == "safety":
                tool_executable += ".exe"
            # No extension usually needed for pipdeptree script wrapper
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_executable} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run pipdeptree and safety as subprocesses.
        Interruptible via skip_controller.
        Returns:
            Dict with 'dependency_tree' and 'security_issues' (both str or error messages)
        """
        results = {
            "dependency_tree": "",
            "security_issues": "",
            "errors": []
        }
        interrupted = False
        
        # Check for skip request before starting subprocess
        if skip_controller and skip_controller.should_skip():
            print("[DependencyStage] Skip requested by user. Skipping phase.")
            results["skipped"] = True
            results["summary"] = "Phase skipped by user before execution."
            return results
            
        # --- Run pipdeptree --- 
        pipdeptree_executable = self._get_tool_path("pipdeptree")
        pipdeptree_cmd = [pipdeptree_executable, "--json"]
        logger.info(f"[DependencyStage] Running command: {' '.join(pipdeptree_cmd)}")
        self.progress.start_phase("Dependency Tree (pipdeptree)", total_steps=1)
        try:
            pipdeptree_result = subprocess.run(pipdeptree_cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
            if pipdeptree_result.returncode != 0:
                error_msg = pipdeptree_result.stderr.strip() or f"pipdeptree failed with code {pipdeptree_result.returncode}"
                logger.error(f"[DependencyStage] {error_msg}")
                results["errors"].append(f"pipdeptree: {error_msg}")
                results["dependency_tree"] = f"[pipdeptree] Failed: {error_msg}" # Store error message
            else:
                results["dependency_tree"] = pipdeptree_result.stdout
            self.progress.complete_phase("Dependency Tree (pipdeptree)", status="Complete!")
        except FileNotFoundError:
            error_msg = f"pipdeptree executable not found at '{pipdeptree_executable}'. Ensure PAT setup completed correctly."
            logger.error(f"[DependencyStage] {error_msg}")
            results["errors"].append(error_msg)
            results["dependency_tree"] = f"[pipdeptree] {error_msg}" # Store error message
            self.progress.complete_phase("Dependency Tree (pipdeptree)", status="Not Found")
            interrupted = True # If one tool is missing, maybe stop?
        except Exception as e:
            error_msg = f"[pipdeptree] Failed to run: {e}"
            logger.error(f"[DependencyStage] {error_msg}")
            results["errors"].append(error_msg)
            results["dependency_tree"] = error_msg
            self.progress.complete_phase("Dependency Tree (pipdeptree)", status="Error")
        finally:
            self.progress.increment() # Increment once for this sub-step

        # Check for skip request between tools
        if skip_controller and skip_controller.should_skip():
            print("[DependencyStage] Skip requested by user before safety check.")
            results["skipped"] = True
            results["summary"] = "Phase skipped by user before safety check."
            return results
        
        if interrupted:
            logger.warning("[DependencyStage] Skipping safety check due to previous error (pipdeptree not found).")
            results["security_issues"] = "[safety] Skipped due to previous error."
            return results

        # --- Run safety --- 
        safety_executable = self._get_tool_path("safety")
        # Safety needs requirements file(s) or uses the current environment.
        # Assuming it checks the environment PAT is installed in.
        safety_cmd = [safety_executable, "check", "--json"] 
        logger.info(f"[DependencyStage] Running command: {' '.join(safety_cmd)}")
        self.progress.start_phase("Vulnerability Scan (safety)", total_steps=1)
        try:
            # Safety check can take time, run in project root context if needed
            safety_result = subprocess.run(safety_cmd, capture_output=True, text=True, check=False, cwd=self.project_root) 
            # Safety exits 0 if no vulns, non-zero if vulns found or error
            if safety_result.returncode != 0:
                # Check stderr first for actual execution errors
                if safety_result.stderr:
                    error_msg = safety_result.stderr.strip()
                    logger.error(f"[DependencyStage] Safety check failed: {error_msg}")
                    results["errors"].append(f"safety: {error_msg}")
                    results["security_issues"] = f"[safety] Failed: {error_msg}" # Store error
                else:
                    # Assume non-zero means vulnerabilities found, stdout has JSON
                    logger.warning(f"[DependencyStage] Safety check found vulnerabilities (exit code {safety_result.returncode}).")
                results["security_issues"] = safety_result.stdout
            else:
                # Exit code 0 means no vulnerabilities found
                results["security_issues"] = safety_result.stdout # Store empty JSON array or similar
            self.progress.complete_phase("Vulnerability Scan (safety)", status="Complete!")
        except FileNotFoundError:
            error_msg = f"Safety executable not found at '{safety_executable}'. Ensure PAT setup completed correctly."
            logger.error(f"[DependencyStage] {error_msg}")
            results["errors"].append(error_msg)
            results["security_issues"] = f"[safety] {error_msg}" # Store error message
            self.progress.complete_phase("Vulnerability Scan (safety)", status="Not Found")
        except Exception as e:
            error_msg = f"[safety] Failed to run: {e}"
            logger.error(f"[DependencyStage] {error_msg}")
            results["errors"].append(error_msg)
            results["security_issues"] = error_msg
            self.progress.complete_phase("Vulnerability Scan (safety)", status="Error")
        finally:
            self.progress.increment() # Increment once for this sub-step
             
        return results

    def parse_output(self, output: Any) -> None:
        """
        Parse pipdeptree and safety outputs, update metrics with dependency and security info.
        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['dependency'] includes: dependency_tree (str), security_issues (str), summary (str)
            - FileMetrics.tool_results['dependency']: (optional, not implemented by default)
        Args:
            output: Dict with 'dependency_tree' and 'security_issues' (both str)
        """
        # Handle skip or error cases passed from run_tool
        if isinstance(output, dict) and output.get("skipped"):
            self.metrics.tool_results["dependency"] = output
            return
        if not isinstance(output, dict):
            logger.warning(f"[DependencyStage] parse_output received unexpected data type: {type(output)}")
            self.metrics.tool_results["dependency"] = {"error": "Invalid data received by parse_output"}
            return

        # Parse dependency count and vulnerability count for summary
        dep_count = 0
        vuln_count = 0
        dep_tree_str = output.get("dependency_tree", "")
        sec_issues_str = output.get("security_issues", "")
        errors = output.get("errors", []) # Get errors collected during run_tool

        # Check if the output strings indicate failure before attempting JSON parse
        dep_failed = dep_tree_str.startswith("[pipdeptree] Failed")
        sec_failed = sec_issues_str.startswith("[safety] Failed") or sec_issues_str.startswith("[safety] Skipped")

        if not dep_failed:
            try:
                if dep_tree_str:
                    dep_json = json.loads(dep_tree_str)
                dep_count = len(dep_json)
            except json.JSONDecodeError:
                logger.error("[DependencyStage] Failed to parse pipdeptree JSON output.")
                errors.append("Failed to parse pipdeptree JSON")
                dep_failed = True # Mark as failed if parse fails
            except Exception as e:
                logger.error(f"[DependencyStage] Error processing pipdeptree output: {e}")
                errors.append(f"Error processing pipdeptree: {e}")
                dep_failed = True
        else:
            dep_count = 0 # Set count to 0 if failed

        if not sec_failed:
            try:
                if sec_issues_str:
                    sec_json = json.loads(sec_issues_str)
                    # Safety JSON output is a list of vulnerabilities
                vuln_count = len(sec_json)
            except json.JSONDecodeError:
                # Check if it was empty JSON [] first
                if sec_issues_str.strip() != '[]':
                    logger.error("[DependencyStage] Failed to parse safety JSON output.")
                    errors.append("Failed to parse safety JSON")
                    sec_failed = True # Mark as failed if parse fails
            except Exception as e:
                logger.error(f"[DependencyStage] Error processing safety output: {e}")
                errors.append(f"Error processing safety: {e}")
                sec_failed = True
        else:
            vuln_count = 0 # Set count to 0 if failed or skipped

        summary = f"{dep_count if not dep_failed else 'Error'} dependencies, {vuln_count if not sec_failed else 'Error/Skipped'} vulnerabilities"
        
        # Store in ProjectMetrics tool_results, including errors
        self.metrics.tool_results["dependency"] = {
            "dependency_tree": dep_tree_str,
            "security_issues": sec_issues_str,
            "summary": summary,
            "errors": errors # Include errors encountered during run/parse
        }
        # (Optional) Per-file/per-package assignment could be added here if needed

# TODO:
# - Add per-package assignment if needed (update FileMetrics)
# - Add unit tests for output parsing
# - Add config options for enabling/disabling, custom pipdeptree/safety config

#

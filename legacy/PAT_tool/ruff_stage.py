"""
RuffStage for PAT
=================

Integrates ruff (Python linter) as a modular pipeline stage. Runs ruff on the codebase,
parses lint errors/warnings, and updates ProjectMetrics/FileMetrics for downstream use.

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)
"""

import os
import platform
import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.tool_stage import ToolStage
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from models import ProjectMetrics
    from tool_stage import ToolStage
    from utils import logger

class RuffStage(ToolStage):
    tool_name = "ruff"

    def _get_tool_path(self):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "ruff"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        return str(pat_venv_bin / tool_executable)

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run ruff as a subprocess on each Python file individually, with robust error handling.
        Interruptible via skip_controller.
        Returns:
            Dict with per-file results and errors.
        """
        # Discover all Python files
        file_list = []
        for root, _, files in os.walk(self.project_root):
            # Respect .patignore
            ignore_path = Path(root) / ".patignore"
            ignored_files = set()
            if ignore_path.exists():
                with open(ignore_path, 'r') as f:
                    ignored_files = {Path(root) / line.strip() for line in f}

            for fname in files:
                if fname.endswith(".py"):
                    full_path = Path(root) / fname
                    if full_path not in ignored_files:
                        file_list.append(str(full_path))

        num_files = len(file_list)
        if num_files == 0:
            logger.warning("[RuffStage] No Python files found for analysis.")
            return {"error": "No Python files found for analysis."}
        self.progress.start_phase("Ruff", total_steps=num_files)
        results = {}
        errors = []
        interrupted = False
        options = self.config.get("options", "").split()
        ruff_executable = self._get_tool_path()

        for idx, fpath in enumerate(file_list, 1):
            if skip_controller and skip_controller.should_skip():
                print("[RuffStage] Skip requested by user. Exiting phase early.")
                interrupted = True
                break
            # Construct command with full path to executable
            # Add 'check' subcommand for newer versions of Ruff
            cmd = [ruff_executable, "check", fpath] + options
            try:
                # logger.debug(f"[RuffStage] Running command: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
                if result.returncode not in (0, 1):  # 0: success, 1: lint errors
                    error_msg = result.stderr.strip()
                    logger.error(f"[RuffStage] Ruff failed for {fpath} (return code {result.returncode}): {error_msg}")
                    errors.append({"file": fpath, "error": f"Ruff execution failed: {error_msg}"})
                    continue
                # Store stdout even if lint errors exist (return code 1)
                results[fpath] = result.stdout
            except FileNotFoundError:
                error_msg = f"Ruff executable not found at '{ruff_executable}'. Ensure PAT setup completed correctly."
                logger.error(f"[RuffStage] {error_msg}")
                errors.append({"file": fpath, "error": error_msg})
                interrupted = True
                break # Assume all will fail if executable not found
            except Exception as e:
                error_msg = f"Unexpected error running ruff for {fpath}: {e}"
                logger.error(f"[RuffStage] {error_msg}")
                errors.append({"file": fpath, "error": error_msg})

            self.progress.increment()

        self.progress.complete_phase("Ruff", status="Complete!" if not interrupted else "Skipped or Failed Early")
        print(f"\n[RuffStage] {idx if interrupted else num_files} files processed. {len(errors)} files failed. See log for details.")
        return {"results": results, "errors": errors, "skipped": interrupted}

    def parse_output(self, output: Any) -> None:
        """
        Parse ruff output, update metrics with linting errors and summary.
        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['ruff'] includes: errors (list), raw_output (str)
            - FileMetrics.tool_results['ruff'] (for all files): errors (list)
        Args:
            output: Dict with per-file results and errors
        """
        if isinstance(output, dict) and output.get("skipped"):
            self.metrics.tool_results["ruff"] = output
            return
        if not isinstance(output, dict):
            return
        errors = []
        file_error_map: Dict[str, list] = {}
        for fpath, out in output.get("results", {}).items():
            if not isinstance(out, str):
                continue
            for line in out.splitlines():
                if line.strip() == "":
                    continue
                if ":" in line:
                    errors.append(line)
                    file_error_map.setdefault(fpath, []).append(line)
        self.metrics.tool_results["ruff"] = {
            "errors": errors,
            "raw_output": output,
        }
        for key, file_metrics in self.metrics.files.items():
            abs_key = str(Path(file_metrics.path).resolve())
            file_errors = file_error_map.get(abs_key, [])
            file_metrics.tool_results["ruff"] = {
                "errors": file_errors
            }
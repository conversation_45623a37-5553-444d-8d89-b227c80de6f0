"""
Reporter Module for PAT
======================

Generates comprehensive reports from PAT analysis results in various formats.
Includes summary, detailed, and issue reports with metrics, diagnostics, and recommendations.

This is a backward compatibility module that imports from the modularized
reporter package. New code should import directly from the package.
"""

# Import from the modularized package
from .reporter import PatReporter, TextFormatter, HtmlFormatter, JsonFormatter

# For backward compatibility, re-export PatReporter and formatters
__all__ = ['PatReporter', 'TextFormatter', 'HtmlFormatter', 'JsonFormatter']

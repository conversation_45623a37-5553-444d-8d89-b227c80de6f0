"""
pyright_stage.py

Pipeline stage for integrating Pyright static type checker into the PAT analysis pipeline.

- Purpose: Run Pyright on the codebase, parse its output, and populate diagnostics in the metrics model.
- Related: mypy_stage.py, tool_stage.py, models.py
- Dependencies: pyright (npm package), Python 3.8+, subprocess
"""

import json
import os
import platform
import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
    from PAT_tool.tool_stage import ToolStage
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
    from tool_stage import ToolStage
    from utils import logger

class PyrightStage(ToolStage):
    """Pipeline stage for running Pyright static analysis and collecting diagnostics.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for Pyright.
    """
    tool_name: str = "pyright"

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress, config: Optional[Dict[str, Any]] = None):
        super().__init__(project_root, metrics, progress, config=config)
        self.extra_args = self.config.get("extra_args", [])

    def _get_tool_path(self):
        # Construct path relative to this script's location
        # Note: Pyright is typically installed via npm, so we need to check the system PATH first,
        # or potentially a configured location. For simplicity, assume it's accessible via PATH for now,
        # or installed in a specific known location if PAT setup manages it.
        # Let's check PAT_venv/bin first, then fallback to PATH.
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "pyright"
        # Windows might use pyright.cmd or similar - handle later if needed
        # if platform.system() == "Windows":
        #     tool_executable += ".cmd" # or .exe?

        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            # Fallback to assuming it's in the system PATH
            # This might fail if the main venv doesn't have it and PAT_venv isn't setup correctly
            logger.warning(f"[PyrightStage] Pyright not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run(self, project_root: Path, metrics: ProjectMetrics, progress, **kwargs) -> None:
        """Run Pyright on the project and update metrics with diagnostics."""
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        self.run_tool()

    def run_tool(self, skip_controller=None) -> Any:
        """Run Pyright via subprocess and return parsed JSON output or raw text on failure."""
        if skip_controller and skip_controller.should_skip():
            print("[PyrightStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}

        # Load skip patterns from config
        skip_patterns = self.config.get("skip_patterns", [])
        if isinstance(skip_patterns, str):
            skip_patterns = [skip_patterns]

        # Create a .pyrightignore file to skip files matching the patterns
        pyright_ignore_path = Path(self.project_root) / ".pyrightignore"
        with open(pyright_ignore_path, "w") as f:
            for pattern in skip_patterns:
                f.write(f"**/*{pattern}*\n")
            # Add files with syntax errors from .patignore if it exists
            patignore_path = Path(self.project_root) / ".patignore"
            if patignore_path.exists():
                with open(patignore_path, "r") as patignore:
                    for line in patignore:
                        f.write(f"{line.strip()}\n")
        logger.info(f"[PyrightStage] Created .pyrightignore file with {len(skip_patterns)} patterns")
        pyright_executable = self._get_tool_path()
        # Add path to pyrightconfig.json
        pyright_config_path = Path(__file__).parent / "pyrightconfig.json"
        cmd = [pyright_executable, str(self.project_root), "--outputjson", "--project", str(pyright_config_path)] + self.extra_args
        logger.info(f"[PyrightStage] Running: {' '.join(cmd)}")
        self.progress.start_phase("Pyright", total_steps=1) # Pyright runs on whole project
        try:
            logger.debug(f"[PyrightStage] Running command: {' '.join(cmd)}")
            try:
                result = subprocess.run(
                    cmd,
                    capture_output=True, # Use capture_output instead of stdout/stderr directly
                    text=True,
                    check=False,
                    cwd=self.project_root, # Run from project root
                    timeout=120 # 2 minute timeout
                )
            except subprocess.TimeoutExpired:
                error_msg = "Pyright execution timed out after 120 seconds"
                logger.error(f"[PyrightStage] {error_msg}")
                self.progress.complete_phase("Pyright", status="Timeout")
                return {"error": error_msg}
            if result.returncode not in (0, 1):  # 0: success, 1: type errors found
                error_msg = result.stderr.strip() or f"Pyright execution failed with code {result.returncode}"
                logger.error(f"[PyrightStage] Pyright failed: {error_msg}")
                self.progress.complete_phase("Pyright", status="Failed")
                return {"error": f"Pyright execution failed: {error_msg}", "raw_output": result.stdout}
            try:
                output = json.loads(result.stdout)
                self.parse_output(output)
                self.progress.complete_phase("Pyright", status="Complete!")
                return output
            except json.JSONDecodeError:
                error_msg = "Failed to parse Pyright JSON output"
                logger.error(f"[PyrightStage] {error_msg}. stdout:\n{result.stdout}")
                self.progress.complete_phase("Pyright", status="Parse Error")
                return {"error": error_msg, "raw_output": result.stdout}
        except FileNotFoundError:
            error_msg = f"Pyright executable not found (checked '{pyright_executable}' and PATH). Ensure Node.js and Pyright are installed and accessible."
            logger.error(f"[PyrightStage] {error_msg}")
            self.progress.complete_phase("Pyright", status="Not Found")
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error running pyright: {e}"
            logger.error(f"[PyrightStage] {error_msg}")
            self.progress.complete_phase("Pyright", status="Error")
            return {"error": error_msg}
        finally:
            # Ensure progress is updated even if parsing fails or early exit
            self.progress.increment() # Increment once for the single project-wide run

    def parse_output(self, output: Any) -> None:
        """Parse Pyright output and update metrics for each file and the project."""
        if not output or (isinstance(output, dict) and output.get("error")):
            # Store error at project level
            self.metrics.tool_results["pyright"] = output
            return
        # Pyright JSON output structure: { "generalDiagnostics": [...], "summary": {...}, "version": ... }
        general_diagnostics = output.get("generalDiagnostics", [])
        summary = output.get("summary", {})
        # Map diagnostics to files
        file_diagnostics: Dict[str, Dict[str, Any]] = {}
        for diag in general_diagnostics:
            file_path = diag.get("file", "<unknown>")
            if file_path not in file_diagnostics:
                file_diagnostics[file_path] = {"errors": []}
            file_diagnostics[file_path]["errors"].append(diag)
        # Update FileMetrics for each file
        for file_path, diag in file_diagnostics.items():
            # Normalize path to match FileMetrics keys
            rel_path = str(Path(file_path).relative_to(self.project_root)) if file_path != "<unknown>" else file_path
            file_metrics = self.metrics.files.get(rel_path)
            if not file_metrics:
                # Create a new FileMetrics if not present
                file_metrics = FileMetrics(path=rel_path)
                self.metrics.files[rel_path] = file_metrics
            file_metrics.tool_results["pyright"] = diag
        # Store project-level summary
        self.metrics.tool_results["pyright"] = {
            "summary": summary,
            "errors": general_diagnostics,
            "raw_output": output
        }

# TODO: Add unit tests for output parsing and error handling in tests/ or PAT_tool/tests/

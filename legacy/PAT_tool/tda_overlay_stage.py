"""
tda_overlay_stage.py

Pipeline stage for Topological Data Analysis (TDA) overlays using GUDHI.

- Purpose: Analyze project graphs (call graphs, effect graphs, etc.) for topological features using persistent homology. Generate persistence diagrams/barcodes and integrate results into project metrics and visualization index.
- Related: graph_overlay_stage.py, effect_overlay_stage.py, models.py, visualization.py
- Dependencies: GUDHI (pip install gudhi), matplotlib (for diagrams), Python 3.8+
"""

from pathlib import Path
from typing import Any, Dict, List, Optional

import networkx as nx
import numpy as np

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

try:
    import gudhi as gd
except ImportError:
    gd = None

try:
    import matplotlib.pyplot as plt
except ImportError:
    plt = None

class TDAOverlayStage(ToolStage):
    """Pipeline stage for Topological Data Analysis (TDA) overlays using GUDHI.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for TDA overlays.
    """
    tool_name = "tda_overlay"

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress, config: Optional[Dict[str, Any]] = None):
        """Initialize the TDA overlay stage."""
        super().__init__(project_root, metrics, progress, config)
        self.output_dir = Path(self.config.get("output_dir", "analysis"))
        self.output_dir.mkdir(parents=True, exist_ok=True)
        # TODO: Parse config for TDA options (e.g., which graphs to analyze, diagram output format)

    def run_tool(self) -> Dict[str, Any]:
        """Run TDA analysis on project graphs and generate persistence diagrams.

        Returns:
            Dict with TDA overlay results (summaries, diagram paths, topological features).
        """
        if gd is None or plt is None:
            logger.warning("GUDHI or matplotlib not installed; skipping TDA overlay.")
            return {}
        results = {"summaries": [], "diagrams": [], "features": []}
        graphs = self._find_graphs()
        for name, graph in graphs.items():
            try:
                persistence_data = self._compute_persistence(graph)
                diagram_path = str(self.output_dir / f"tda_{name}_diagram.png")
                self._export_persistence_diagram(persistence_data, diagram_path, title=f"TDA Persistence Diagram: {name}")
                summary = f"Graph '{name}': Betti numbers: {persistence_data.get('betti', {})}, Num points: {len(persistence_data.get('diagram', []))}"
                results["summaries"].append(summary)
                results["diagrams"].append({"name": name, "path": diagram_path})
                results["features"].append({"name": name, "betti": persistence_data.get("betti", {})})
            except Exception as e:
                logger.error(f"[TDAOverlayStage] Failed for graph '{name}': {e}")
        return results

    def parse_output(self, output: Any) -> None:
        """Parse TDA overlay output and update metrics with TDA overlays and summaries.

        Args:
            output: Dict with TDA overlay results
        """
        if not isinstance(output, dict):
            return
        self.metrics.tool_results["tda_overlay"] = output

    def _find_graphs(self) -> Dict[str, nx.Graph]:
        """Find graphs in project metrics to analyze with TDA.

        Returns:
            Dict of graph name to NetworkX graph.
        """
        graphs = {}
        # Call graph
        call_graph_data = self.metrics.tool_results.get("call_graph", {})
        if "edges" in call_graph_data:
            G = nx.DiGraph()
            for edge in call_graph_data["edges"]:
                G.add_edge(edge[0], edge[1])
            graphs["call_graph"] = G
        # Effect graph (if present)
        effect_graph_data = self.metrics.tool_results.get("effect_overlay", {})
        if "effects_detailed" in effect_graph_data:
            G = nx.DiGraph()
            for eff in effect_graph_data["effects_detailed"]:
                src = eff.get("function")
                tgt = eff.get("effect_types", [])
                for t in tgt:
                    G.add_edge(src, t)
            graphs["effect_graph"] = G
        return graphs

    def _compute_persistence(self, graph: nx.Graph) -> Dict[str, Any]:
        """Compute persistent homology for a given graph using GUDHI.

        Args:
            graph: A NetworkX graph.
        Returns:
            Dict with persistence diagram/barcode data and topological features.
        """
        # Use shortest path distances as metric
        if not isinstance(graph, nx.Graph):
            raise ValueError("Input must be a NetworkX graph.")
        nodes = list(graph.nodes)
        if not nodes:
            return {"diagram": [], "betti": {}}
        sp_length = dict(nx.all_pairs_shortest_path_length(graph.to_undirected()))
        n = len(nodes)
        dist_matrix = np.zeros((n, n))
        for i, u in enumerate(nodes):
            for j, v in enumerate(nodes):
                if u == v:
                    dist_matrix[i, j] = 0.0
                else:
                    dist_matrix[i, j] = sp_length.get(u, {}).get(v, np.inf)
        # Build Rips complex
        rc = gd.RipsComplex(distance_matrix=dist_matrix, max_edge_length=np.max(dist_matrix[np.isfinite(dist_matrix)]))
        st = rc.create_simplex_tree(max_dimension=2)
        diag = st.persistence()
        betti = {dim: st.betti_numbers()[dim] for dim in range(len(st.betti_numbers()))}
        diagram = st.persistence_intervals_in_dimension(1)
        return {"diagram": diagram, "betti": betti, "raw": diag}

    def _export_persistence_diagram(self, diagram_data: Dict[str, Any], output_path: str, title: str = "TDA Persistence Diagram") -> str:
        """Export persistence diagram/barcode as PNG using matplotlib.

        Args:
            diagram_data: Dict with persistence points/barcode.
            output_path: Path to save the diagram.
            title: Title for the visualization.
        Returns:
            Path to the generated file.
        """
        if plt is None:
            logger.warning("matplotlib not installed; cannot export persistence diagram.")
            return ""
        diagram = diagram_data.get("diagram", [])
        plt.figure(figsize=(6, 4))
        if len(diagram) > 0:
            for birth, death in diagram:
                plt.plot([birth, death], [birth, death], "ro-")
            plt.title(title)
            plt.xlabel("Birth")
            plt.ylabel("Death")
        else:
            plt.title(f"{title} (No features)")
        plt.tight_layout()
        plt.savefig(output_path)
        plt.close()
        return output_path 
"""
Analysis Pipeline Module
=======================

Defines the modular, pluggable analysis pipeline for PAT. This enables
ordered, configurable execution of analysis stages (structure, dependency,
complexity, etc.) and provides a foundation for future extensibility.

Related Files:
    - main.py (entry point)
    - structure_analyzer.py, dependency_analyzer.py, complexity_analyzer.py (stages)
    - models.py (shared state)

External Dependencies:
    - Python 3.8+
    - typing (Protocol, runtime_checkable)

"""

from pathlib import Path
from typing import Any, Dict, List, Optional, Protocol, runtime_checkable

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from models import ProjectMetrics
    from utils import logger

@runtime_checkable
class AnalysisStage(Protocol):
    """Protocol for an analysis stage in the pipeline."""
    def run(self, project_root: Path, metrics: ProjectMetrics, progress, **kwargs) -> None:
        """
        Execute the analysis stage.

        Args:
            project_root: Path to the project root directory.
            metrics: Shared ProjectMetrics object for storing results.
            progress: progress tracker instance for reporting progress.
            **kwargs: Additional stage-specific arguments.
        """
        ...

class AnalysisPipeline:
    """
    Manages and executes a sequence of analysis stages.

    Allows for pluggable, ordered analysis stages. Each stage receives the
    shared project state and may update it in place.
    """
    def __init__(self, stages: Optional[List[AnalysisStage]] = None):
        """
        Initialize the analysis pipeline.

        Args:
            stages: Optional list of AnalysisStage instances (ordered).
        """
        self.stages: List[AnalysisStage] = stages or []

    def add_stage(self, stage: AnalysisStage) -> None:
        """
        Add an analysis stage to the pipeline.

        Args:
            stage: An instance implementing the AnalysisStage protocol.
        """
        self.stages.append(stage)

    def run(self, project_root: Path, metrics: ProjectMetrics, progress, **kwargs) -> None:
        """
        Execute all analysis stages in order.

        Args:
            project_root: Path to the project root directory.
            metrics: Shared ProjectMetrics object for storing results.
            progress: progress tracker instance for reporting progress.
            **kwargs: Additional arguments passed to each stage.
        """
        for stage in self.stages:
            stage_name = stage.__class__.__name__
            progress.start_phase(f"Running {stage_name}")
            try:
                stage.run(project_root, metrics, progress, **kwargs)
            except Exception as e:
                logger.error(f"[Pipeline] Stage {stage_name} failed: {e}")
            progress.complete_phase(f"{stage_name}")
            progress.increment_global()
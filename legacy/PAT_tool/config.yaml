# PAT Tool Configuration File
#
# This file controls which external analysis tools are enabled in the pipeline
# and allows you to pass tool-specific options (as CLI flags or arguments).
#
# - 'enabled': Set to true to include the tool in the analysis pipeline, false to skip it.
# - 'options': (Optional) Additional command-line options to pass to the tool.
#
# ---
#
# MYPY: Common and Useful Options for PAT
#
# --ignore-missing-imports   # Suppress errors for missing type hints/stubs in imports (treats as Any)
# --strict                   # Enable all optional error checking flags (max safety, more noise)
# --disallow-untyped-defs    # Error for any function without type annotations
# --check-untyped-defs       # Type check bodies of all functions, even if untyped
# --warn-unused-ignores      # Warn if a # type: ignore is not needed
# --exclude PATTERN          # Exclude files/dirs matching regex from type checking
# --install-types            # Auto-install missing type stubs for third-party libraries
# --python-version X.Y       # Type check as if running under Python version X.Y
# --show-error-codes         # Show error codes in output (for silencing specific errors)
# --pretty                   # Nicer formatting for error messages
# --html-report DIR          # Generate an HTML type coverage report (for PAT visualization)
#
# Example:
#   mypy:
#     enabled: true
#     options: "--ignore-missing-imports --strict --warn-unused-ignores"
#
# For more, see: https://mypy.readthedocs.io/en/stable/command_line.html
#
# RUFF: Common and Useful Options for PAT
#
# --select <codes>      # Only enable specific lint rules (e.g., "E,F,W").
# --ignore <codes>      # Ignore specific rules (e.g., "E501" for line length).
# --fix                 # Automatically fix issues where possible.
# --format <format>     # Output format: text, json, github, etc.
# --output-file <file>  # Write output to a file (PAT captures stdout by default).
# --show-source         # Show source code for each issue.
# --statistics          # Show summary statistics.
#
# Example:
#   ruff:
#     enabled: true
#     options: "--select E,F,W --ignore E501 --statistics"
#
# For more, see: https://docs.astral.sh/ruff/
#
# BANDIT: Common and Useful Options for PAT
#
# -r <dir>              # Recursively scan a directory (default in PAT integration)
# -f <format>           # Output format: txt, json, csv, yaml, html, etc.
# -ll                   # Only show issues with high severity/likelihood
# --skip B101,B102      # Skip specific tests (comma-separated list)
# --exclude <dirs>      # Exclude directories from analysis
# --ini <file>          # Use a specific bandit config file
#
# Example:
#   bandit:
#     enabled: true
#     options: "-r --format json --skip B101,B102"
#
# For more, see: https://bandit.readthedocs.io/en/latest/config.html
#
# COVERAGE: Common and Useful Options for PAT
#
# --source <pkg>         # Measure coverage only for the given package/module
# --omit <pattern>       # Omit files/directories from coverage measurement
# --include <pattern>    # Include only files matching pattern
# --fail-under <N>       # Fail if total coverage is under N percent
# --branch               # Measure branch coverage in addition to statement coverage
# --json                 # Output coverage report in JSON (used by PAT)
#
# Example:
#   coverage:
#     enabled: true
#     options: "--source mypackage --branch --fail-under 80"
#
# For more, see: https://coverage.readthedocs.io/en/latest/cmd.html
#
# ---

pre_analysis:
  enabled: true         # Run pre-analysis to check for syntax errors before main analysis
  auto_patignore: true # Automatically add files with syntax errors to .patignore
  incremental: false   # Analyze all files, even if they haven't changed since last successful run

exclude_patterns:
  - "venv/**"           # Exclude all virtual environment directories
  - "**/venv/**"        # Exclude nested virtual environments
  - "PAT_project_analysis/**"  # Exclude the PAT tool itself
  - "**/__pycache__/**"  # Exclude Python cache directories
  - "**/.git/**"         # Exclude git directories
  - "**/.pytest_cache/**" # Exclude pytest cache
  - "**/site-packages/**" # Exclude site-packages
  - "**/dist-packages/**" # Exclude dist-packages

include_patterns:
  - "Person_Suit/**"    # Only include files in the Person_Suit directory

progress_bar:
  always: false           # Always show progress bars, even if subprocesses are verbose
  enabled: false           # Set to false to disable all progress bars (rich and simple) entirely
  simple_tracker_details: true # Show count/percent/spinner for SimpleProgressTracker

parallel_processing:
  enabled: true           # Enable parallel processing globally
  max_workers: 8          # Default number of parallel processes
  incremental: false      # Analyze all files, even if they haven't changed since last successful run

dependency_audit:
  enabled: true         # Enable dependency audit phase (import vs installed)
  log_file: "dependency_audit.log"  # Log file for audit results

tools:
  mypy:
    enabled: false        # Disable mypy static type checking in favor of pyright
    # Options for mypy type checking
    options: "--ignore-missing-imports --strict --namespace-packages"
    skip_on_error: true   # Skip to next phase if mypy fails
    skip_phase: true     # Manually skip this phase
    # Patterns to skip files that are known to cause issues with mypy
    skip_patterns: [
      "__pycache__",
      ".git",
      "venv",
      "node_modules",
      "test_",
      "_test"
    ]
  ruff:
    enabled: true        # Ruff is now the primary linter for performance and coverage
    # Select error, warning, and style rules
    options: "--select E,F,W"  # Removed --jobs parameter as it's not supported
    skip_on_error: true
    skip_phase: false
    parallel: true       # Enable parallel processing
    max_workers: 8       # Number of parallel processes
    incremental: false   # Analyze all files, even if they haven't changed
  bandit:
    enabled: false        # Disable bandit security analysis as it takes too long
    # Use JSON format for better parsing
    options: "-r --format json -n 8"  # Use 8 parallel processes
    skip_on_error: true
    skip_phase: true
    force_full_scan: false  # Set to true to force a full scan (ignore cache)
    parallel: true         # Enable parallel processing
    max_workers: 8         # Number of parallel processes (null = auto-detect)
  coverage:
    enabled: true        # Enable coverage.py test coverage analysis
    options: ""          # Extra coverage CLI options
    skip_on_error: true
    skip_phase: false
  hypothesis:
    enabled: true        # Enable Hypothesis property-based testing (set to true to enable)
    options: ""          # Extra pytest/Hypothesis CLI options
    skip_on_error: true
    skip_phase: false
  flake8:
    enabled: false       # Flake8 is disabled; use Ruff as the primary linter
    options: ""
    skip_on_error: true
    skip_phase: false
  pylint:
    enabled: true        # Enable pylint advanced static analysis (set to true to enable)
    # Output JSON, disable docstring warnings, skip score/reports
    # Ignore .json and other non-Python files (if any are picked up)
    options: "--output-format=json --disable=C0114,C0115,C0116 --score=n --reports=n --ignore-patterns=.*\\.json$ -j 8"
    skip_on_error: true   # Skip to next phase if pylint fails
    skip_phase: false     # Manually skip this phase
    parallel: true       # Enable parallel processing
    max_workers: 8       # Number of parallel processes
    incremental: false   # Analyze all files, even if they haven't changed
  black:
    enabled: true        # Enable black code formatter (check mode)
    # Use fast mode (parallelizes internally)
    options: "--check --fast"
    skip_on_error: true
    skip_phase: false
  isort:
    enabled: true        # Enable isort import sorting checker (check mode)
    options: ""          # Extra isort CLI options
    skip_on_error: true
    skip_phase: false
  pydocstyle:
    enabled: true        # Enable pydocstyle docstring quality checker
    options: ""          # Extra pydocstyle CLI options
    skip_on_error: true
    skip_phase: false
  dependency:
    enabled: true        # Enable pipdeptree/safety dependency and security analysis
    options: ""          # Extra pipdeptree/safety CLI options
    skip_on_error: true
    skip_phase: false
  call_graph:
    enabled: true        # Enable pyan3 call graph extraction
    options: ""          # Extra pyan3 CLI options
    skip_on_error: true
    skip_phase: false
  pyright:
    enabled: true        # Enable pyright static type checking
    options: "--level warning"  # Use warning level instead of error to be more forgiving
    skip_on_error: true
    skip_phase: false
    parallel: true       # Enable parallel processing
    max_workers: 8       # Number of parallel processes
    incremental: false   # Analyze all files, even if they haven't changed
    # Patterns to skip files that are known to cause issues with pyright
    skip_patterns: [
      "__pycache__",
      ".git",
      "venv",
      "node_modules",
      "test_",
      "_test"
    ]
  protocol_extraction:
    enabled: true        # Enable protocol/choreography extraction (set to true to enable)
    options: ""          # Extra protocol extraction options (future use)
    skip_on_error: true
    skip_phase: false
  effect_overlay:
    enabled: true        # Enable effect system/context overlay analysis (set to true to enable)
    options: ""          # Extra effect overlay options (future use)
    skip_on_error: true
    skip_phase: false
  pycontract:
    enabled: true        # Enable PyContract runtime contract verification (set to true to enable)
    options: ""          # Extra PyContract options (future use)
    skip_on_error: true
    skip_phase: false
  tla:
    enabled: true        # Enable TLA+ model checking (set to true to enable)
    options: ""          # Extra TLA+ CLI options (future use)
    spec: "spec.tla"     # Path to TLA+ spec file (user-provided or auto-generated)
    skip_on_error: true
    skip_phase: false
  graph_overlay:
    enabled: true        # Enable graph-based/topological overlays (set to true to enable)
    options: ""          # Extra graph overlay options (future use)
    skip_on_error: true
    skip_phase: false
  meta_system_separation:
    enabled: true        # Enable meta-system separation analysis (set to false to disable)
    options: ""          # Extra meta-system separation options (future use)
    skip_on_error: true
    skip_phase: false

profiling:
  timing: true         # Enable timing of each major pipeline stage (time.perf_counter)
  cprofile: false      # Enable cProfile profiling of the entire PAT run
  cprofile_output: "pat_profile.prof"  # Output file for cProfile stats

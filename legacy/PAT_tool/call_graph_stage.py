"""
CallGraphStage for PAT
=====================

Integrates pyan3 (primary) for call graph extraction as a modular pipeline stage. Runs pyan3 on the codebase,
parses DOT and JSON outputs, and updates ProjectMetrics for downstream use and interactive visualization.

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)

External Dependencies:
    - pyan3 (must be installed in the environment)
    - (Optional) code2flow for secondary/quick overviews

"""

import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class CallGraphStage(ToolStage):
    """Pipeline stage for running pyan3 to collect call graph data (DOT and JSON).

    Stores results in ProjectMetrics.tool_results['call_graph'].
    """
    tool_name = "call_graph"

    def _get_tool_path(self):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        # pyan3 might install as 'pyan' or 'pyan3'?
        tool_executable_pyan3 = "pyan3"
        tool_executable_pyan = "pyan"
        if platform.system() == "Windows":
             # Check if scripts end with .exe on Windows
             tool_executable_pyan3 += ".exe"
             tool_executable_pyan += ".exe"
        
        venv_path_pyan3 = pat_venv_bin / tool_executable_pyan3
        venv_path_pyan = pat_venv_bin / tool_executable_pyan

        if venv_path_pyan3.is_file():
            return str(venv_path_pyan3)
        elif venv_path_pyan.is_file():
             logger.warning(f"[{self.__class__.__name__}] Found 'pyan' instead of 'pyan3' in {pat_venv_bin}.")
             return str(venv_path_pyan)
        else:
            # Fallback to PATH check
            logger.warning(f"[{self.__class__.__name__}] Neither 'pyan3' nor 'pyan' found in {pat_venv_bin}. Assuming 'pyan3' is in PATH.")
            return "pyan3"

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run pyan3 as a subprocess on the project root to generate DOT and JSON call graphs.
        Interruptible via skip_controller.
        Returns:
            Dict with 'pyan3_dot' and 'pyan3_json' (both str or error messages)
        """
        results = {
             "pyan3_dot": "",
             "pyan3_json": "",
             "errors": []
        }
        interrupted = False
        pyan_executable = self._get_tool_path()

        # Check for skip request before starting subprocess
        if skip_controller and skip_controller.should_skip():
            print("[CallGraphStage] Skip requested by user. Skipping phase.")
            results["skipped"] = True
            results["summary"] = "Phase skipped by user before execution."
            return results

        # --- Run pyan3 for DOT output --- 
        options = self.config.get("options", "").split()
        pyan3_dot_cmd = [
            pyan_executable, str(self.project_root),
            "--dot", "--no-defines", "--no-imports", "--grouped"
        ] + options
        logger.info(f"[CallGraphStage] Running command: {' '.join(pyan3_dot_cmd)}")
        self.progress.start_phase("Call Graph (DOT)", total_steps=1)
        try:
            dot_result = subprocess.run(pyan3_dot_cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
            if dot_result.returncode != 0:
                 error_msg = dot_result.stderr.strip() or f"Pyan3 (DOT) failed with code {dot_result.returncode}"
                 logger.error(f"[CallGraphStage] {error_msg}")
                 results["errors"].append(f"pyan3-dot: {error_msg}")
                 results["pyan3_dot"] = f"[pyan3] Failed (DOT): {error_msg}"
            else:
                results["pyan3_dot"] = dot_result.stdout
            self.progress.complete_phase("Call Graph (DOT)", status="Complete!")
        except FileNotFoundError:
            error_msg = f"Pyan3 executable not found at '{pyan_executable}' or PATH. Ensure pyan3 is installed."
            logger.error(f"[CallGraphStage] {error_msg}")
            results["errors"].append(error_msg)
            results["pyan3_dot"] = f"[pyan3] {error_msg}"
            self.progress.complete_phase("Call Graph (DOT)", status="Not Found")
            interrupted = True # If pyan3 is missing, json call will also fail
        except Exception as e:
            error_msg = f"[pyan3] Failed to run (DOT): {e}"
            logger.error(f"[CallGraphStage] {error_msg}")
            results["errors"].append(error_msg)
            results["pyan3_dot"] = error_msg
            self.progress.complete_phase("Call Graph (DOT)", status="Error")
            interrupted = True # Error likely affects JSON run too
        finally:
             self.progress.increment()
        
        # Check for skip request or interruption between runs
        if interrupted:
             results["pyan3_json"] = "[pyan3] Skipped (JSON) due to previous error."
             return results
        if skip_controller and skip_controller.should_skip():
            print("[CallGraphStage] Skip requested by user before JSON generation.")
            results["skipped"] = True
            results["summary"] = "Phase skipped by user before JSON generation."
            results["pyan3_json"] = "[pyan3] Skipped (JSON) by user."
            return results

        # --- Run pyan3 for JSON output --- 
        pyan3_json_cmd = [
            pyan_executable, str(self.project_root),
            "--json", "--no-defines", "--no-imports", "--grouped"
        ] + options
        logger.info(f"[CallGraphStage] Running command: {' '.join(pyan3_json_cmd)}")
        self.progress.start_phase("Call Graph (JSON)", total_steps=1)
        try:
            json_result = subprocess.run(pyan3_json_cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
            if json_result.returncode != 0:
                 error_msg = json_result.stderr.strip() or f"Pyan3 (JSON) failed with code {json_result.returncode}"
                 logger.error(f"[CallGraphStage] {error_msg}")
                 results["errors"].append(f"pyan3-json: {error_msg}")
                 results["pyan3_json"] = f"[pyan3] Failed (JSON): {error_msg}"
            else:
                results["pyan3_json"] = json_result.stdout
            self.progress.complete_phase("Call Graph (JSON)", status="Complete!")
        except FileNotFoundError: # Should have been caught above, but handle defensively
            error_msg = f"Pyan3 executable not found at '{pyan_executable}' or PATH. Ensure pyan3 is installed."
            logger.error(f"[CallGraphStage] {error_msg}")
            results["errors"].append(error_msg)
            results["pyan3_json"] = f"[pyan3] {error_msg}"
            self.progress.complete_phase("Call Graph (JSON)", status="Not Found")
        except Exception as e:
            error_msg = f"[pyan3] Failed to run (JSON): {e}"
            logger.error(f"[CallGraphStage] {error_msg}")
            results["errors"].append(error_msg)
            results["pyan3_json"] = error_msg
            self.progress.complete_phase("Call Graph (JSON)", status="Error")
        finally:
             self.progress.increment()

        return results

    def parse_output(self, output: Any) -> None:
        """
        Parse pyan3 outputs, update metrics with call graph data.

        Args:
            output: Dict with 'pyan3_dot' and 'pyan3_json' (both str or error messages)
        """
        # Handle skip or error cases passed from run_tool
        if isinstance(output, dict) and output.get("skipped"):
            self.metrics.tool_results["call_graph"] = output
            return
        if not isinstance(output, dict):
             logger.warning(f"[CallGraphStage] parse_output received unexpected data type: {type(output)}")
             self.metrics.tool_results["call_graph"] = {"error": "Invalid data received by parse_output"}
             return

        # Store in ProjectMetrics tool_results, including errors
        self.metrics.tool_results["call_graph"] = {
            "pyan3_dot": output.get("pyan3_dot", ""),
            "pyan3_json": output.get("pyan3_json", ""),
            "errors": output.get("errors", [])
        }

# TODO:
# - Add code2flow support for comparison (optional)
# - Add post-processing for interactive visualization
# - Add unit tests for output parsing
# - Add config options for enabling/disabling, custom pyan3 options

# TODO:
# - Add code2flow support for comparison (optional)
# - Add post-processing for interactive visualization
# - Add unit tests for output parsing
# - Add config options for enabling/disabling, custom pyan3 options

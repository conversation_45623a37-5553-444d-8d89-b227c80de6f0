"""
Data models for project analysis.

This module contains the data classes that represent the metrics and results
of various project analysis operations.
"""

import sys
import threading
import time
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

import networkx as nx

# Try to import rich for modern progress bars
try:
    from rich.console import Console
    from rich.progress import (BarColumn, Progress, SpinnerColumn, TaskID,
                               TextColumn, TimeElapsedColumn,
                               TimeRemainingColumn)
    from rich.status import Status
    RICH_AVAILABLE = True
except ImportError:
    # Define dummy classes for type checking when rich is not available
    class Progress:
        pass
    class Console:
        pass
    class Status:
        pass
    class TaskID:
        pass
    RICH_AVAILABLE = False


@dataclass
class FileMetrics:
    """Detailed metrics for a single file.

    Attributes:
        tool_results: Structured outputs from analysis tools (mypy, ruff, bandit, coverage, etc.)
            for this file. Keyed by tool name. Each value is a dict with tool-specific fields.

            Example structure:
                {
                  "mypy": {"errors": [...], "type_coverage": float},
                  "ruff": {"errors": [...], "summary": str},
                  "flake8": {"errors": [...]},
                  "bandit": {"issues": [...], "summary": str},
                  "coverage": {"coverage": float, "missing": [...]},
                  "black": {"reformatted": bool},
                  "pydocstyle": {"issues": [...]}
                }

            Required fields per tool:
                - "errors" or "issues": list of strings or dicts (diagnostics)
                - "summary": (optional) string summary
                - Tool-specific fields (e.g., "type_coverage", "coverage", etc.)
    """
    path: str
    name: str = ""
    size: int = 0
    lines: int = 0
    imports: List[str] = field(default_factory=list)
    imported_by: List[str] = field(default_factory=list)
    functions: List[str] = field(default_factory=list)
    classes: List[str] = field(default_factory=list)
    complexity: int = 0
    docstring_coverage: float = 0.0
    type_coverage: float = 0.0
    maintainability_index: float = 0.0
    circular_deps: List[str] = field(default_factory=list)
    internal_deps: List[str] = field(default_factory=list)
    external_deps: List[str] = field(default_factory=list)
    coupled_modules: List[str] = field(default_factory=list)
    description: str = ""
    # Documentation-specific metrics
    is_documentation: bool = False
    is_package: bool = False
    is_test: bool = False
    documentation_quality: float = 0.0
    sections: int = 0
    code_examples: int = 0
    # Content extraction fields
    docstrings: Dict[str, Any] = field(default_factory=dict)  # module/class/function docstrings
    roles: str = ""  # Architectural role (PC, AN, PR, SIO, utility, test, etc.)
    tool_results: Dict[str, Any] = field(default_factory=dict)  # Tool outputs for this file


@dataclass
class DirectoryMetrics:
    """Metrics for a directory."""
    path: str
    files: List[str] = field(default_factory=list)
    total_lines: int = 0
    avg_lines: float = 0.0
    max_file_lines: int = 0
    max_file: str = ""
    description: str = "No description available"
    # Documentation-specific metrics
    doc_files_count: int = 0
    total_doc_size: int = 0
    avg_doc_quality: float = 0.0


@dataclass
class ProjectMetrics:
    """Complete project analysis results.

    Attributes:
        tool_results: Structured outputs from all analysis tools (mypy, ruff, bandit, coverage, etc.)
            for the entire project. Keyed by tool name. Each value is a dict with tool-specific fields.

            Example structure:
                {
                  "mypy": {"errors": [...], "type_coverage": float, "raw_output": str},
                  "ruff": {"errors": [...], "summary": str, "raw_output": str},
                  "flake8": {"errors": [...], "raw_output": str},
                  "bandit": {"issues": [...], "summary": str, "raw_output": str},
                  "coverage": {"coverage": float, "missing": [...], "raw_output": str},
                  "black": {"reformatted": [...], "raw_output": str},
                  "pydocstyle": {"issues": [...], "raw_output": str},
                  "dependency": {"dependency_tree": str, "security_issues": str},
                  "call_graph": {"dot": str, "json": dict, "summary": str}
                }

            Required fields per tool:
                - "errors" or "issues": list of strings or dicts (diagnostics)
                - "summary": (optional) string summary
                - "raw_output": (optional) string, raw tool output
                - Tool-specific fields (e.g., "type_coverage", "coverage", etc.)
    """
    files: Dict[str, FileMetrics] = field(default_factory=dict)
    directories: Dict[str, DirectoryMetrics] = field(default_factory=dict)
    dependency_graph: nx.DiGraph = field(default_factory=nx.DiGraph)
    complexity_scores: Dict[str, int] = field(default_factory=dict)
    quality_metrics: Dict[str, float] = field(default_factory=dict)
    type_coverage: Dict[str, float] = field(default_factory=dict)
    doc_coverage: Dict[str, float] = field(default_factory=dict)
    # Documentation-specific metrics
    documentation_files: Dict[str, FileMetrics] = field(default_factory=dict)
    documentation_quality_scores: Dict[str, float] = field(default_factory=dict)
    documentation_size_distribution: Dict[str, int] = field(default_factory=dict)
    tool_results: Dict[str, Any] = field(default_factory=dict)  # Tool outputs for the project


class RichProgressTracker:
    """Modern, colorful progress tracker using rich.progress.Progress and live status.
    Only one instance should be active per process to avoid live display conflicts.
    """
    _live_display_active = False  # Class-level flag to prevent multiple live displays

    def __init__(self):
        if RichProgressTracker._live_display_active:
            # Instead of raising, fallback to SimpleProgressTracker
            raise RuntimeError("RichProgressTracker: Only one live display may be active at once. Fallback to SimpleProgressTracker.")
        RichProgressTracker._live_display_active = True
        self.console = Console()
        self.progress = Progress(
            SpinnerColumn(),
            TextColumn("[bold blue]{task.description}"),
            BarColumn(),
            "[progress.percentage]{task.percentage:>3.0f}%",
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            console=self.console,
            transient=True,
        )
        self.status = None
        self.global_task: Optional[TaskID] = None
        self.phase_task: Optional[TaskID] = None
        self.current_phase = None
        self._progress_thread = None
        self._stop_event = threading.Event()

    def __del__(self):
        RichProgressTracker._live_display_active = False

    @classmethod
    def force_reset(cls):
        cls._live_display_active = False

    def start_global(self, phase_name: str, total_steps: int):
        self.progress.start()
        self.global_task = self.progress.add_task(f"{phase_name}", total=total_steps)
        self.console.print(f"[bold green]Starting: {phase_name}")

    def increment_global(self):
        if self.global_task is not None:
            self.progress.update(self.global_task, advance=1)

    def complete_global(self, status: str = "Complete!"):
        if self.global_task is not None:
            self.progress.update(self.global_task, completed=self.progress.tasks[self.global_task].total)
            self.console.print(f"[bold green]{status}")
        self.progress.stop()
        self.global_task = None

    def start_phase(self, phase_name: str, total_steps: int = 0):
        self.current_phase = phase_name
        if self.phase_task is not None:
            self.progress.remove_task(self.phase_task)
        self.phase_task = self.progress.add_task(f"{phase_name}", total=total_steps)
        self.status = self.console.status(f"[cyan]Working on: {phase_name}")
        self.status.start()

    def increment(self):
        if self.phase_task is not None:
            self.progress.update(self.phase_task, advance=1)

    def complete_phase(self, phase_name: str, status: str = "Complete!"):
        if self.phase_task is not None:
            self.progress.update(self.phase_task, completed=self.progress.tasks[self.phase_task].total)
            self.console.print(f"[bold yellow]{phase_name}: {status}")
            self.progress.remove_task(self.phase_task)
            self.phase_task = None
        if self.status is not None:
            self.status.stop()
            self.status = None
        self.current_phase = None

    def update_phase(self, phase_name: str, total_steps: int):
        self.start_phase(phase_name, total_steps)

    def set_status(self, message: str):
        if self.status is not None:
            self.status.update(message)


class SimpleProgressTracker:
    """Simple console-based progress tracker as a fallback when rich is not available.
    Always shows detailed progress with counts, percentage, and a spinner.
    """

    _spinner_chars = ['-', '\\', '|', '/']

    def __init__(self, show_details: bool = True):
        self.current_phase = None
        self.global_phase = None
        self.total_global_steps = 0
        self.current_global_step = 0
        self.total_phase_steps = 0
        self.current_phase_step = 0
        self._spinner_index = 0
        self.show_details = True  # Always show detailed output
        self.start_time = time.time()
        self.phase_start_time = time.time()

    def _get_spinner(self) -> str:
        char = self._spinner_chars[self._spinner_index % len(self._spinner_chars)]
        self._spinner_index += 1
        return char

    def start_global(self, phase_name: str, total_steps: int):
        self.global_phase = phase_name
        self.total_global_steps = total_steps
        self.current_global_step = 0
        print(f"Starting: {phase_name} (0/{total_steps})")

    def increment_global(self):
        self.current_global_step += 1
        percent = (self.current_global_step / self.total_global_steps * 100) if self.total_global_steps > 0 else 0
        # Overwrite previous line
        print(f"\r{self.global_phase}: Progress {self.current_global_step}/{self.total_global_steps} ({percent:.0f}%)", end='')

    def complete_global(self, status: str = "Complete!"):
        # Ensure the final status is on a new line
        print(f"\r{self.global_phase}: {status}            ") # Clear rest of line
        self.global_phase = None

    def start_phase(self, phase_name: str, total_steps: int = 0):
        self.current_phase = phase_name
        self.total_phase_steps = total_steps
        self.current_phase_step = 0
        self.phase_start_time = time.time()
        # Print initial phase message only if not showing details, otherwise increment will handle it
        if not self.show_details or total_steps <= 0:
            print(f"Working on: {phase_name} {'with steps: ' + str(total_steps) if total_steps > 0 else ''}")

    def _format_time(self, seconds):
        """Format time in a consistent way across all displays.

        Args:
            seconds: Time in seconds

        Returns:
            Formatted time string (HH:MM:SS for times >= 1 hour, MM:SS otherwise)
        """
        hours, remainder = divmod(int(seconds), 3600)
        minutes, seconds = divmod(remainder, 60)

        if hours > 0:
            return f"{hours:02d}:{minutes:02d}:{seconds:02d}"
        else:
            return f"{minutes:02d}:{seconds:02d}"

    def increment(self):
        if not self.current_phase:
             print("  Step completed (no active phase)") # Should not happen
             return

        self.current_phase_step += 1
        current_time = time.time()
        elapsed = current_time - self.phase_start_time
        total_elapsed = current_time - self.start_time

        # Always show detailed progress information
        percent = (self.current_phase_step / self.total_phase_steps * 100) if self.total_phase_steps > 0 else 0
        spinner = self._get_spinner()

        # Format times consistently
        elapsed_str = self._format_time(elapsed)
        total_str = self._format_time(total_elapsed)

        # Calculate ETA
        if self.current_phase_step > 0 and self.total_phase_steps > 0:
            eta = (elapsed / self.current_phase_step) * (self.total_phase_steps - self.current_phase_step)
            eta_str = self._format_time(eta)

            # Overwrite previous line with updated progress including timing information
            print(f"\r {spinner} {self.current_phase}: {self.current_phase_step}/{self.total_phase_steps} ({percent:.0f}%) - Elapsed: {elapsed_str} - ETA: {eta_str} - Total: {total_str}            ", end='')
        elif self.total_phase_steps > 0:
            # No ETA for first step
            print(f"\r {spinner} {self.current_phase}: {self.current_phase_step}/{self.total_phase_steps} ({percent:.0f}%) - Elapsed: {elapsed_str} - Total: {total_str}            ", end='')
        else:
            # Show spinner and phase name even if total_steps is unknown
            print(f"\r {spinner} {self.current_phase}: Step {self.current_phase_step} - Elapsed: {elapsed_str} - Total: {total_str}            ", end='')

    def complete_phase(self, phase_name: str, status: str = "Complete!"):
        # Always show detailed completion status
        if self.total_phase_steps > 0:
            # Clear the progress line and print final status
            elapsed = time.time() - self.phase_start_time
            elapsed_str = self._format_time(elapsed)
            print(f"\r {phase_name}: {status} ({self.current_phase_step}/{self.total_phase_steps}) - Time: {elapsed_str}            ")
        else:
            # Simple message for phases without steps
            elapsed = time.time() - self.phase_start_time
            elapsed_str = self._format_time(elapsed)
            print(f"{phase_name}: {status} - Time: {elapsed_str}")
        self.current_phase = None
        self.total_phase_steps = 0
        self.current_phase_step = 0

    def update_phase(self, phase_name: str, total_steps: int):
        # Reset spinner index for new phase display
        self._spinner_index = 0
        self.start_phase(phase_name, total_steps)

    def set_status(self, message: str):
        # Can optionally overwrite the last progress line if detailed view is on
        if self.show_details and self.current_phase:
             print(f"\r Status: {message}                                ", end='')
        else:
            print(f"Status: {message}")


class NoOpProgressTracker:
    """A progress tracker that does nothing (for disabling all progress output)."""
    def __init__(self, *args, **kwargs):
        pass
    def start_global(self, phase_name: str, total_steps: int):
        pass
    def increment_global(self):
        pass
    def complete_global(self, status: str = "Complete!"):
        pass
    def start_phase(self, phase_name: str, total_steps: int = 0):
        pass
    def increment(self):
        pass
    def complete_phase(self, phase_name: str, status: str = "Complete!"):
        pass
    def update_phase(self, phase_name: str, total_steps: int):
        pass
    def set_status(self, message: str):
        pass


def get_progress_tracker(config: Optional[dict] = None):
    # If config disables progress bar, return NoOpProgressTracker
    if config is not None:
        pb_cfg = config.get('progress_bar', {})
        if pb_cfg.get('enabled', True) is False:
            return NoOpProgressTracker()

    if RICH_AVAILABLE:
        try:
            return RichProgressTracker()
        except RuntimeError as e:
            # Fallback to simple tracker if live display is already active
            print("[PAT] Progress bar fallback: ", e)
            # Always show details in the SimpleProgressTracker
            return SimpleProgressTracker()
    else:
        # Always show details in the SimpleProgressTracker
        return SimpleProgressTracker()


# ProgressTracker = get_progress_tracker

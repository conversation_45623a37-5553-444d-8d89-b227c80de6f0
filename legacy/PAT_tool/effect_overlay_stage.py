"""
effect_overlay_stage.py

Pipeline stage for analyzing and visualizing effectful/contextual operations in code (effect system overlay).

- Purpose: Analyze Python source code to detect effectful functions (state changes, I/O, context propagation, explicit effect annotations), and generate effect overlays and context propagation maps.
- Related: protocol_extraction_stage.py, tool_stage.py, models.py
- Dependencies: ast (stdlib), optionally Effect library, Python 3.8+
"""

import ast
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger
try:
    # When running as a package
    from PAT_tool.visualization import export_effects_to_sankey_html
except ImportError:
    # When running as a script
    from visualization import export_effects_to_sankey_html

class EffectOverlayStage(ToolStage):
    """Pipeline stage for analyzing effectful/contextual operations using AST (effect system overlay).

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for effect overlay.
        output_dir (Optional[Path]): Directory to write all overlay outputs (Sankey diagrams, etc).
    """
    tool_name = "effect_overlay"

    def __init__(self, project_root, metrics, progress, config=None, output_dir=None):
        super().__init__(project_root, metrics, progress, config)
        self.output_dir = Path(output_dir) if output_dir else Path(project_root) / "PAT_output"
        self.per_file_sankey_dir = self.output_dir / "per_file_effect_sankey"
        self.per_file_sankey_dir.mkdir(parents=True, exist_ok=True)

    def run_tool(self) -> Any:
        """Analyze Python files to detect effectful/contextual operations and generate overlays."""
        effect_info = {
            "effectful_functions": [],
            "context_propagation": [],
            "effect_summaries": [],
            "sankey_html": None,
            "effects_detailed": [],  # New: list of dicts with metadata
            "per_file_sankey": {},   # New: per-file sankey data
        }
        for file_path in self._find_python_files(self.project_root):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    source = f.read()
                tree = ast.parse(source, filename=str(file_path))
                for node in ast.walk(tree):
                    if isinstance(node, ast.FunctionDef):
                        has_effect = False
                        effect_types = set()
                        effect_counts = {}
                        summary = f"Function {node.name} in {file_path.name}:"
                        # Assignments (state change)
                        assign_count = sum(1 for n in ast.walk(node) if isinstance(n, ast.Assign))
                        if assign_count:
                            has_effect = True
                            effect_types.add("state_change")
                            effect_counts["state_change"] = assign_count
                            summary += f" state change({assign_count});"
                        # File I/O
                        fileio_calls = {"open", "read", "write", "close"}
                        fileio_count = sum(1 for n in ast.walk(node) if isinstance(n, ast.Call) and hasattr(n.func, "id") and n.func.id in fileio_calls)
                        if fileio_count:
                            has_effect = True
                            effect_types.add("file_io")
                            effect_counts["file_io"] = fileio_count
                            summary += f" file I/O({fileio_count});"
                        # Network I/O (simple heuristic: 'requests', 'socket', 'http')
                        networkio_count = sum(1 for n in ast.walk(node) if isinstance(n, ast.Call) and hasattr(n.func, "attr") and n.func.attr in {"get", "post", "send", "recv"})
                        if networkio_count:
                            has_effect = True
                            effect_types.add("network_io")
                            effect_counts["network_io"] = networkio_count
                            summary += f" network I/O({networkio_count});"
                        # Logging
                        logging_count = sum(1 for n in ast.walk(node) if isinstance(n, ast.Call) and hasattr(n.func, "attr") and n.func.attr in {"info", "debug", "warning", "error", "critical"})
                        if logging_count:
                            has_effect = True
                            effect_types.add("logging")
                            effect_counts["logging"] = logging_count
                            summary += f" logging({logging_count});"
                        # DB (simple: 'execute', 'commit')
                        db_count = sum(1 for n in ast.walk(node) if isinstance(n, ast.Call) and hasattr(n.func, "attr") and n.func.attr in {"execute", "commit"})
                        if db_count:
                            has_effect = True
                            effect_types.add("db")
                            effect_counts["db"] = db_count
                            summary += f" db({db_count});"
                        # Context/contextual arguments
                        if any(arg.arg in {"context", "ctx"} for arg in node.args.args):
                            has_effect = True
                            effect_types.add("context_arg")
                            effect_counts["context_arg"] = 1
                            summary += " context arg;"
                        # Explicit effect annotation
                        if any(isinstance(n, ast.Expr) and isinstance(n.value, ast.Str) and "effect" in n.value.s.lower() for n in ast.walk(node)):
                            has_effect = True
                            effect_types.add("explicit_effect")
                            effect_counts["explicit_effect"] = 1
                            summary += " explicit effect annotation;"
                        if has_effect:
                            effect_info["effectful_functions"].append(node.name)
                            effect_info["effect_summaries"].append(summary)
                            # Store detailed effect metadata
                            effect_info["effects_detailed"].append({
                                "function": node.name,
                                "file": str(file_path),
                                "lineno": getattr(node, "lineno", None),
                                "effect_types": list(effect_types),
                                "effect_counts": effect_counts,
                                "summary": summary,
                            })
                        # Track context propagation (calls with context/ctx)
                        for call in [n for n in ast.walk(node) if isinstance(n, ast.Call)]:
                            for arg in getattr(call, "args", []):
                                if isinstance(arg, ast.Name) and arg.id in {"context", "ctx"}:
                                    effect_info["context_propagation"].append(f"{node.name} -> {ast.unparse(call)}")
            except Exception as e:
                logger.error(f"[EffectOverlayStage] Failed to analyze {file_path}: {e}")
        # Build Sankey data (project-wide and per-file)
        sankey_data = []
        per_file_sankey = {}
        for eff in effect_info["effects_detailed"]:
            func = eff["function"]
            file = eff["file"]
            for etype in eff["effect_types"]:
                value = eff["effect_counts"].get(etype, 1)
                sankey_data.append({
                    "source": func,
                    "target": etype,
                    "type": etype,
                    "value": value,
                    "file": file,
                    "lineno": eff["lineno"],
                    "summary": eff["summary"],
                })
                # Per-file
                if file not in per_file_sankey:
                    per_file_sankey[file] = []
                per_file_sankey[file].append({
                    "source": func,
                    "target": etype,
                    "type": etype,
                    "value": value,
                    "file": file,
                    "lineno": eff["lineno"],
                    "summary": eff["summary"],
                })
        # Add context propagation as links
        for prop in effect_info["context_propagation"]:
            if "->" in prop:
                src, tgt = prop.split("->", 1)
                sankey_data.append({
                    "source": src.strip(),
                    "target": tgt.strip(),
                    "type": "context_propagation",
                    "value": 1,
                    "file": None,
                    "lineno": None,
                    "summary": prop,
                })
        effect_info["per_file_sankey"] = per_file_sankey
        # Generate Sankey diagram if possible
        output_path = str(self.output_dir / "effect_overlay_sankey.html")
        sankey_html = export_effects_to_sankey_html(sankey_data, output_path, title="Effect Overlay Sankey Diagram")
        if sankey_html:
            effect_info["sankey_html"] = sankey_html
            effect_info["visualization"] = sankey_html
        # Generate per-file Sankey diagrams
        for file, file_sankey in per_file_sankey.items():
            rel_path = Path(file).relative_to(self.project_root)
            file_html = str(self.per_file_sankey_dir / rel_path.with_suffix(".effect_sankey.html"))
            (self.per_file_sankey_dir / rel_path.parent).mkdir(parents=True, exist_ok=True)
            export_effects_to_sankey_html(file_sankey, file_html, title=f"Effect Overlay: {Path(file).name}")
        return effect_info

    def parse_output(self, output: Any) -> None:
        """Parse effect overlay output and update metrics with effect/context overlays and summaries.

        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['effect_overlay'] includes: effectful_functions (list), context_propagation (list), effect_summaries (list), sankey_html (str)
        Args:
            output: Dict with effect overlay results
        """
        if not isinstance(output, dict):
            return
        self.metrics.tool_results["effect_overlay"] = {
            "effectful_functions": output.get("effectful_functions", []),
            "context_propagation": output.get("context_propagation", []),
            "effect_summaries": output.get("effect_summaries", []),
            "sankey_html": output.get("sankey_html"),
        }

    def _find_python_files(self, root: Path):
        """Yield all Python files under the given root directory."""
        for path in root.rglob("*.py"):
            yield path 
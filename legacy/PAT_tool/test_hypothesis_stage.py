import os
import sys
import tempfile
from pathlib import Path

import pytest

try:
    # When running as a package
    from PAT_tool.hypothesis_stage import HypothesisStage
except ImportError:
    # When running as a script
    from hypothesis_stage import HypothesisStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics

pytestmark = pytest.mark.skipif(
    not (hasattr(sys.modules.get('pytest'), '__version__') and hasattr(sys.modules.get('hypothesis'), '__version__')),
    reason="pytest or hypothesis not installed"
)

def test_hypothesis_stage_basic(tmp_path):
    """Test HypothesisStage on a synthetic test file with a simple property-based test."""
    # Create a temporary test file with a simple Hypothesis test
    test_code = '''
import pytest
from hypothesis import given, strategies as st

@given(x=st.integers())
def test_identity(x):
    assert x == x
'''
    test_file = tmp_path / "test_hypothesis_identity.py"
    test_file.write_text(test_code)
    # Mock ProjectMetrics
    metrics = ProjectMetrics()
    # Run HypothesisStage
    stage = HypothesisStage(project_root=tmp_path, metrics=metrics, progress=None, config={})
    results = stage.run_tool()
    # Check that results are present and summary is as expected
    assert "summary" in results
    assert "All Hypothesis tests passed" in results["summary"] or "0 property-based test failures" in results["summary"]
    # Check that metrics are updated via parse_output
    stage.parse_output(results)
    assert "hypothesis" in metrics.tool_results 
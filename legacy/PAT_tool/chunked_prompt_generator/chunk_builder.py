"""
Chunk building functions for the chunked prompt generator.

This module contains functions to format and build chunks from content blocks.
"""

from typing import Any, Dict, List


def format_block(block: Dict[str, Any]) -> str:
    """
    Format a content block into a markdown chunk.
    
    Args:
        block: Dictionary containing file metadata and content
        
    Returns:
        Formatted markdown string
    """
    path = block.get('path', 'Unknown')
    role = block.get('role', [])
    summary = block.get('summary', '')
    classes = block.get('classes', [])
    functions = block.get('functions', [])
    class_docstrings = block.get('class_docstrings', {})
    function_docstrings = block.get('function_docstrings', {})
    complexity = block.get('complexity', 0)
    imports = block.get('imports', [])
    issues = block.get('issues', {})
    
    # Start with file header and summary
    text = f"# File: {path}\n\n"
    
    if role:
        text += f"**Role**: {', '.join(role)}\n\n"
    
    if summary:
        text += f"**Summary**: {summary}\n\n"
    
    # Add file metrics
    text += "## File Metrics\n\n"
    text += f"- **Complexity**: {complexity}\n"
    text += f"- **Lines**: {block.get('lines', 'Unknown')}\n"
    text += f"- **Imports**: {len(imports)}\n\n"
    
    # Add diagnostics section if there are issues
    if issues:
        text += "## Diagnostics\n\n"
        
        # Add issue summary
        for issue_type, value in issues.items():
            if issue_type == "high_complexity":
                text += f"- **High Complexity**: {value}\n"
            elif issue_type == "large_file":
                text += f"- **Large File**: {value} lines\n"
            elif issue_type == "type_errors":
                text += f"- **Type Errors**: {value}\n"
            elif issue_type == "lint_issues":
                text += f"- **Lint Issues**: {value}\n"
            elif issue_type == "security_issues":
                text += f"- **Security Issues**: {value}\n"
            elif issue_type == "low_coverage":
                text += f"- **Low Test Coverage**: {value:.1f}%\n"
            elif issue_type == "formatting_issues":
                text += f"- **Formatting Issues**: Yes\n"
            elif issue_type == "docstring_issues":
                text += f"- **Docstring Issues**: {value}\n"
            elif issue_type == "dependency_issues":
                text += f"- **Dependency Issues**: Yes\n"
            elif issue_type == "many_imports":
                text += f"- **Many Imports**: {value}\n"
            elif issue_type == "circular_imports":
                text += f"- **Circular Import**: Yes\n"
            elif issue_type == "pylint_issues":
                text += f"- **Pylint Issues**: {value}\n"
        
        text += "\n"
        
        # Add CAW-specific guidance based on issues
        text += "**CAW Reflection:**\n"
        if "type_errors" in issues:
            text += "- Type errors may disrupt wave-particle information flow. Refactor to ensure type safety propagates contextually.\n"
        if "lint_issues" in issues:
            text += "- Lint issues may indicate rigid code. Adapt style for flexible, context-driven computation as per CAW.\n"
        if "security_issues" in issues:
            text += "- Address security issues by propagating capability tokens and contextual access control, in line with CAW's capability-based security.\n"
        if "low_coverage" in issues:
            text += "- Low test coverage reduces adaptive computational fidelity. Add tests to ensure robust context propagation.\n"
        if "circular_imports" in issues:
            text += "- Circular imports create feedback loops that can destabilize the wave function. Refactor to ensure clean, directed information flow.\n"
        
        text += "\n**CAW-Aligned Next Steps:**\n"
        if "high_complexity" in issues:
            text += "- Reduce complexity by decomposing into smaller, context-aware components that can be composed through wave interference patterns.\n"
        if "large_file" in issues:
            text += "- Split large files into smaller, focused modules that can be dynamically composed based on context.\n"
        if "docstring_issues" in issues:
            text += "- Improve docstrings to clearly document context requirements and wave transformation properties.\n"
        
        text += "\n"
    else:
        text += "## Diagnostics\n\n"
        text += "No critical issues detected in this file.\n\n"
    
    # Add classes section
    if classes:
        text += "## Classes\n\n"
        for cls in classes:
            text += f"- **{cls}**\n"
            if cls in class_docstrings:
                text += f"  {class_docstrings[cls]}\n"
        text += "\n"
    
    # Add functions section
    if functions:
        text += "## Functions\n\n"
        for func in functions:
            text += f"- **{func}**\n"
            if func in function_docstrings:
                text += f"  {function_docstrings[func]}\n"
        text += "\n"
    
    # Add imports section
    if imports:
        text += "## Imports\n\n"
        for imp in imports[:10]:  # Limit to 10 imports to save space
            text += f"- {imp}\n"
        if len(imports) > 10:
            text += f"- ... and {len(imports) - 10} more\n"
        text += "\n"
    
    return text


def has_meaningful_content(chunk_text: str) -> bool:
    """
    Determine if a chunk has meaningful content beyond basic file information.

    This version has lowered thresholds to generate more prompts.

    Args:
        chunk_text: The text content of the chunk

    Returns:
        bool: True if the chunk has meaningful content, False otherwise
    """
    # Skip empty chunks or chunks with no title
    if not chunk_text or not chunk_text.strip():
        return False

    # Skip chunks that only contain "No critical issues detected"
    # Relaxed condition to generate more prompts
    if "No critical issues detected" in chunk_text and len(chunk_text) < 150:
        return False

    # Accept chunks with any meaningful content
    # Relaxed condition to generate more prompts

    # Check for meaningful content like classes, functions, or diagnostics
    meaningful_markers = [
        "## Classes",
        "## Functions",
        "**Diagnostics",
        "**CAW Reflection:**",
        "**CAW-Aligned Next Steps:**",
        "### Type Errors",
        "### Lint Issues",
        "### Security Issues",
        "High Complexity",
        "Large File",
        "Type Errors",
        "Security Issues",
        "Circular Import",
        "Lint Issues",
        "Docstring Issues",
        "Many Imports",
        "Formatting Issues",
        "Low Test Coverage",
        "Dependency Issues",
        "Pylint Issues"
    ]

    for marker in meaningful_markers:
        if marker in chunk_text:
            return True

    # Ensure the chunk has enough content to be meaningful
    # Lowered from 5 to 3 lines
    if chunk_text.count('\n') < 3:  # Very few lines
        return False

    # If the file has any content beyond basic info, consider it meaningful
    if len(chunk_text) > 150:
        return True

    return True

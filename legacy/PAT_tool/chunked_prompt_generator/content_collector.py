"""
Content collection functions for the chunked prompt generator.

This module contains functions to collect and filter content from FileMetrics objects.
"""

from typing import Any, Dict, List

try:
    # When running as a package
    from PAT_tool.models import FileMetrics
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from models import FileMetrics
    from utils import logger


def collect_content(files: List[FileMetrics]) -> List[Dict[str, Any]]:
    """
    Collects and formats content from FileMetrics for chunking.
    Returns a list of dicts with file metadata and content, including tool diagnostics.
    Only includes files that have issues or high complexity.
    
    Args:
        files: List of FileMetrics objects
        
    Returns:
        List of dictionaries with file metadata and content
    """
    content_blocks = []
    for f in files:
        # Skip files that don't have issues or high complexity
        if not file_needs_attention(f):
            continue

        block = {
            "path": f.path,
            "role": f.roles,
            "summary": f.docstrings.get("module", ""),
            "classes": f.classes,
            "functions": f.functions,
            "class_docstrings": f.docstrings.get("classes", {}),
            "function_docstrings": f.docstrings.get("functions", {}),
            "lines": f.lines,
            "complexity": f.complexity,
            "imports": f.imports,
            "tool_results": getattr(f, "tool_results", {}),
            "issues": identify_file_issues(f),
        }
        content_blocks.append(block)
    return content_blocks


def file_needs_attention(file_metric: FileMetrics) -> bool:
    """
    Determine if a file needs attention based on various criteria.

    This version has lowered thresholds to generate more prompts.

    Args:
        file_metric: The file metrics to check

    Returns:
        bool: True if the file needs attention, False otherwise
    """
    # Check for complexity (lowered threshold from 10 to 5)
    if file_metric.complexity > 5:
        return True

    # Check for large file size (threshold set to 600 lines)
    if file_metric.lines > 600:
        return True

    # Check for tool issues
    tool_results = getattr(file_metric, "tool_results", {})

    # Check for mypy errors
    if "mypy" in tool_results and tool_results["mypy"].get("errors"):
        return True

    # Check for ruff errors
    if "ruff" in tool_results and tool_results["ruff"].get("errors"):
        return True

    # Check for bandit issues
    if "bandit" in tool_results and tool_results["bandit"].get("issues"):
        return True

    # Check for low test coverage (lowered threshold from 70.0 to 60.0)
    if "coverage" in tool_results and tool_results["coverage"].get("coverage", 100.0) < 60.0:
        return True

    # Check for formatting issues
    if "black" in tool_results and tool_results["black"].get("reformatted"):
        return True

    # Check for docstring issues
    if "pydocstyle" in tool_results and tool_results["pydocstyle"].get("issues"):
        return True

    # Check for dependency issues
    if "dependency" in tool_results and tool_results["dependency"].get("summary") and "vulnerabilities" in tool_results["dependency"]["summary"]:
        return True

    # Check for many imports (lowered threshold from 15 to 10)
    if len(file_metric.imports) > 10:
        return True

    # Check for circular imports
    if any("circular" in str(err).lower() for err in tool_results.get("mypy", {}).get("errors", [])):
        return True

    # Check for pylint issues (added)
    if "pylint" in tool_results and tool_results["pylint"].get("errors"):
        return True

    return False


def identify_file_issues(file_metric: FileMetrics) -> Dict[str, Any]:
    """
    Identify specific issues in a file that need attention.

    Args:
        file_metric: The file metrics to check

    Returns:
        Dict[str, Any]: Dictionary of issues identified
    """
    issues = {}

    # Check for high complexity (lowered threshold from 10 to 5)
    if file_metric.complexity > 5:
        issues["high_complexity"] = file_metric.complexity

    # Check for large file size (threshold set to 600 lines)
    if file_metric.lines > 600:
        issues["large_file"] = file_metric.lines

    # Check for tool issues
    tool_results = getattr(file_metric, "tool_results", {})

    # Check for mypy errors
    if "mypy" in tool_results and tool_results["mypy"].get("errors"):
        issues["type_errors"] = len(tool_results["mypy"].get("errors", []))

    # Check for ruff errors
    if "ruff" in tool_results and tool_results["ruff"].get("errors"):
        issues["lint_issues"] = len(tool_results["ruff"].get("errors", []))

    # Check for bandit issues
    if "bandit" in tool_results and tool_results["bandit"].get("issues"):
        issues["security_issues"] = len(tool_results["bandit"].get("issues", []))

    # Check for low test coverage
    if "coverage" in tool_results and tool_results["coverage"].get("coverage", 100.0) < 70.0:
        issues["low_coverage"] = tool_results["coverage"].get("coverage", 0.0)

    # Check for formatting issues
    if "black" in tool_results and tool_results["black"].get("reformatted"):
        issues["formatting_issues"] = True

    # Check for docstring issues
    if "pydocstyle" in tool_results and tool_results["pydocstyle"].get("issues"):
        issues["docstring_issues"] = len(tool_results["pydocstyle"].get("issues", []))

    # Check for dependency issues
    if "dependency" in tool_results and tool_results["dependency"].get("summary") and "vulnerabilities" in tool_results["dependency"]["summary"]:
        issues["dependency_issues"] = True

    # Check for many imports (potential dependency issues)
    if len(file_metric.imports) > 15:
        issues["many_imports"] = len(file_metric.imports)

    # Check for circular imports
    if any("circular" in str(err).lower() for err in tool_results.get("mypy", {}).get("errors", [])):
        issues["circular_imports"] = True

    # Check for pylint issues
    if "pylint" in tool_results and tool_results["pylint"].get("errors"):
        issues["pylint_issues"] = len(tool_results["pylint"].get("errors", []))

    return issues

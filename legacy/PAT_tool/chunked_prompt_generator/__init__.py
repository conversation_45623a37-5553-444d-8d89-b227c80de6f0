"""
Chunked Prompt Generator Module for PAT
=======================================

Generates LLM-ready prompt chunks from codebase analysis results, optimized for
context window size and overlap. Each chunk includes summaries, docstrings,
key code blocks, and architectural metadata. Outputs Markdown files and a manifest.

This module has been modularized for better maintainability and extensibility.
"""

# Import the main class for backward compatibility
from .generator import ChunkedPromptGenerator

# Import other modules for easier access
from . import content_collector
from . import chunk_builder
from . import manifest
from . import verification

__all__ = ['ChunkedPromptGenerator', 'content_collector', 'chunk_builder', 'manifest', 'verification']

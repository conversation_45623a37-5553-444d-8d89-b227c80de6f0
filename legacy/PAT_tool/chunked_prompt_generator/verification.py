"""
Verification prompt generation functions for the chunked prompt generator.

This module contains functions to generate verification prompts and project diagnostics.
"""

from collections import Counter, defaultdict
from typing import Any, Dict, List, <PERSON><PERSON>


def insert_verification_prompts(chunks: List[str], manifest: List[Dict[str, Any]], 
                               interval: int = 3) -> Tuple[List[str], List[Dict[str, Any]]]:
    """
    Insert verification prompts every N chunks.
    
    Args:
        chunks: List of chunk strings
        manifest: List of chunk manifest dictionaries
        interval: Interval between verification prompts (default: 3)
        
    Returns:
        Tuple of (list of chunks with verification prompts, updated manifest)
    """
    if not chunks:
        return [], []
    
    new_chunks = []
    new_manifest = []
    
    for i, (chunk, meta) in enumerate(zip(chunks, manifest)):
        # Add the original chunk
        new_chunks.append(chunk)
        new_manifest.append(meta)
        
        # Add a verification prompt after every N chunks
        if (i + 1) % interval == 0 and i < len(chunks) - 1:
            # Get the files from the last N chunks
            recent_files = []
            for j in range(max(0, i - interval + 1), i + 1):
                recent_files.extend(manifest[j].get("files", []))
            
            # Generate a verification prompt
            verification_prompt = generate_verification_prompt(recent_files, i // interval)
            verification_meta = {
                "files": recent_files,
                "issue_areas": ["Verification"],
                "issue_counts": {},
                "caw_guidance": {},
                "caw_priority": 0,
                "review_required": False,
                "chunk_index": len(new_chunks),
                "chunk_file": f"chunk_{len(new_chunks)+1:03d}.md",
                "entities": [],
                "diagnostics": [],
                "top_issues": {},
                "is_verification": True
            }
            
            new_chunks.append(verification_prompt)
            new_manifest.append(verification_meta)
    
    return new_chunks, new_manifest


def generate_verification_prompt(recent_files: List[str], verification_idx: int) -> str:
    """
    Generate a verification prompt for the given files.
    
    Args:
        recent_files: List of file paths to verify
        verification_idx: Index of the verification prompt
        
    Returns:
        Verification prompt string
    """
    prompt = f"# Verification Checkpoint {verification_idx + 1}\n\n"
    prompt += "## Verification Questions\n\n"
    
    prompt += "Please answer the following questions to verify your understanding of the codebase so far:\n\n"
    
    prompt += "1. What are the main architectural patterns you've observed in the codebase?\n"
    prompt += "2. What are the most critical issues you've identified so far?\n"
    prompt += "3. How would you prioritize addressing these issues?\n"
    prompt += "4. What CAW principles are most relevant to the issues you've seen?\n"
    prompt += "5. What refactoring strategies would you recommend?\n\n"
    
    prompt += "## Recent Files\n\n"
    for file in recent_files:
        prompt += f"- {file}\n"
    
    prompt += "\n## Reflection\n\n"
    prompt += "Take a moment to reflect on the codebase structure and quality. "
    prompt += "Are there any patterns or anti-patterns emerging? "
    prompt += "How well does the code align with CAW principles? "
    prompt += "What architectural improvements would you suggest?\n\n"
    
    return prompt


def generate_project_diagnostics_prompt(files: List[Any]) -> Tuple[str, Dict[str, Any]]:
    """
    Generate a project-level diagnostics prompt.
    
    Args:
        files: List of FileMetrics objects
        
    Returns:
        Tuple of (project diagnostics prompt, manifest metadata)
    """
    # Collect project-level metrics
    total_files = len(files)
    total_lines = sum(f.lines for f in files if hasattr(f, 'lines'))
    avg_complexity = sum(f.complexity for f in files if hasattr(f, 'complexity')) / total_files if total_files > 0 else 0
    
    # Collect issues by type
    issue_counts = defaultdict(int)
    for f in files:
        tool_results = getattr(f, "tool_results", {})
        
        # Count mypy errors
        if "mypy" in tool_results and tool_results["mypy"].get("errors"):
            issue_counts["type_errors"] += len(tool_results["mypy"].get("errors", []))
        
        # Count ruff errors
        if "ruff" in tool_results and tool_results["ruff"].get("errors"):
            issue_counts["lint_issues"] += len(tool_results["ruff"].get("errors", []))
        
        # Count bandit issues
        if "bandit" in tool_results and tool_results["bandit"].get("issues"):
            issue_counts["security_issues"] += len(tool_results["bandit"].get("issues", []))
        
        # Count docstring issues
        if "pydocstyle" in tool_results and tool_results["pydocstyle"].get("issues"):
            issue_counts["docstring_issues"] += len(tool_results["pydocstyle"].get("issues", []))
        
        # Count pylint issues
        if "pylint" in tool_results and tool_results["pylint"].get("errors"):
            issue_counts["pylint_issues"] += len(tool_results["pylint"].get("errors", []))
    
    # Generate the prompt
    prompt = "# Project-Level Diagnostics\n\n"
    
    prompt += "## Project Overview\n\n"
    prompt += f"- **Total Files**: {total_files}\n"
    prompt += f"- **Total Lines**: {total_lines}\n"
    prompt += f"- **Average Complexity**: {avg_complexity:.2f}\n\n"
    
    prompt += "## Issue Summary\n\n"
    for issue_type, count in issue_counts.items():
        if issue_type == "type_errors":
            prompt += f"- **Type Errors**: {count}\n"
        elif issue_type == "lint_issues":
            prompt += f"- **Lint Issues**: {count}\n"
        elif issue_type == "security_issues":
            prompt += f"- **Security Issues**: {count}\n"
        elif issue_type == "docstring_issues":
            prompt += f"- **Docstring Issues**: {count}\n"
        elif issue_type == "pylint_issues":
            prompt += f"- **Pylint Issues**: {count}\n"
    
    prompt += "\n## CAW Alignment Assessment\n\n"
    prompt += "Based on the project diagnostics, assess how well the codebase aligns with CAW principles:\n\n"
    
    prompt += "1. **Context Sensitivity**: How well does the code handle different contexts?\n"
    prompt += "2. **Wave-Particle Duality**: Does the code represent information as both discrete entities and continuous waves?\n"
    prompt += "3. **Choreographic Coordination**: How well are distributed interactions coordinated?\n"
    prompt += "4. **Capability-Based Security**: Is security implemented using unforgeable capability tokens?\n"
    prompt += "5. **Differentiable Programming**: Are components designed to be differentiable?\n\n"
    
    prompt += "## Recommended Approach\n\n"
    prompt += "Based on the diagnostics, what approach would you recommend for improving the codebase?\n"
    prompt += "Consider the following aspects:\n\n"
    
    prompt += "1. **Prioritization**: Which issues should be addressed first?\n"
    prompt += "2. **Refactoring Strategy**: What refactoring strategies would be most effective?\n"
    prompt += "3. **CAW Integration**: How can CAW principles be better integrated?\n"
    prompt += "4. **Testing Strategy**: How can testing be improved?\n"
    prompt += "5. **Documentation**: What documentation improvements are needed?\n\n"
    
    # Create manifest metadata
    manifest_meta = {
        "files": [],
        "issue_areas": list(issue_counts.keys()),
        "issue_counts": dict(issue_counts),
        "caw_guidance": {},
        "caw_priority": 0,
        "review_required": False,
        "chunk_index": 0,
        "chunk_file": "chunk_001.md",
        "entities": [],
        "diagnostics": [],
        "top_issues": dict(Counter(issue_counts).most_common(3)),
        "is_project_diagnostics": True
    }
    
    return prompt, manifest_meta

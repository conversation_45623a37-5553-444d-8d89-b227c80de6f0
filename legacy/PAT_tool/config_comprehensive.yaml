# Full PAT Tool Configuration
# This configuration enables all possible tools with fixes for known issues

# Progress bar configuration
progress_bar:
  type: simple  # Use simple progress bar to avoid rich.live issues
  simple_tracker_details: true  # Show details in simple tracker
  enabled: true  # Enable progress bars

# Pre-analysis configuration
pre_analysis:
  enabled: true  # Enable pre-analysis to check for syntax errors
  auto_patignore: true  # Automatically create .patignore file for files with syntax errors

# Dependency audit configuration
dependency_audit:
  enabled: true  # Enable dependency audit
  log_file: "dependency_audit.log"  # Log file for audit results

# Profiling configuration
profiling:
  timing: true  # Enable timing to see how long each phase takes
  cprofile: false  # Disable cProfile to avoid overhead

# Tool configurations
tools:
  # Type checking tools
  mypy:
    enabled: true  # Disable mypy to avoid issues
  pyright:
    enabled: true   # Enable pyright for type checking
    options: "--level warning"  # Use warning level instead of error
    skip_on_error: true
    skip_phase: false
    # Patterns to skip files that are known to cause issues with pyright
    skip_patterns: [
      "__pycache__",
      ".git",
      "venv",
      "node_modules",
      "test_",
      "_test"
    ]

  # Code quality tools
  ruff:
    enabled: true  # Enable ruff for linting
    options: "--select=E,F,W --ignore=E501,E722,F401,F841"  # Ignore common issues
    skip_on_error: true
    skip_phase: false

  bandit:
    enabled: true  # Enable bandit for security analysis
    options: "-r --format json"
    skip_on_error: true
    skip_phase: false

  coverage:
    enabled: true  # Enable coverage analysis
    options: ""
    skip_on_error: true
    skip_phase: false

  flake8:
    enabled: true  # Disable flake8 as ruff is the primary linter

  pylint:
    enabled: true  # Enable pylint for advanced static analysis
    options: "--output-format=json --disable=C0114,C0115,C0116 --score=n --reports=n --ignore-patterns=.*\\.json$"
    skip_on_error: true
    skip_phase: false

  black:
    enabled: true  # Enable black code formatter (check mode)
    options: "--check --fast"
    skip_on_error: true
    skip_phase: false

  isort:
    enabled: true  # Enable isort import sorting checker
    options: "--check-only"
    skip_on_error: true
    skip_phase: false

  pydocstyle:
    enabled: true  # Enable pydocstyle docstring quality checker
    options: ""
    skip_on_error: true
    skip_phase: false

  dependency:
    enabled: true  # Enable dependency analysis
    options: ""
    skip_on_error: true
    skip_phase: false

  call_graph:
    enabled: true  # Enable call graph extraction
    options: ""
    skip_on_error: true
    skip_phase: false

  # Advanced analysis tools
  protocol_extraction:
    enabled: true  # Enable protocol extraction
    options: ""
    skip_on_error: true
    skip_phase: false

  effect_overlay:
    enabled: true  # Enable effect overlay
    options: ""
    skip_on_error: true
    skip_phase: false

  # Visualization tools - enable if graphviz and scipy are installed
  graph_overlay:
    enabled: true  # Enable graph overlay
    options: ""
    skip_on_error: true
    skip_phase: false

  tda_overlay:
    enabled: true  # Enable TDA overlay
    options: ""
    skip_on_error: true
    skip_phase: false

  # Specialized tools - disable if not installed
  pycontract:
    enabled: true  # Disable pycontract as it's not installed

  tla:
    enabled: true  # Disable TLA+ as it's not installed

  hypothesis:
    enabled: true  # Enable hypothesis testing
    options: ""
    skip_on_error: true
    skip_phase: false

  pyre:
    enabled: true  # Disable pyre as it's not installed

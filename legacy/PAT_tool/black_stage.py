"""
BlackStage for PAT
=================

Integrates black (Python code formatter) as a modular pipeline stage. Runs black in check mode on the codebase,
parses formatting issues, and updates ProjectMetrics/FileMetrics for downstream use.

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)
    - flake8_stage.py, ruff_stage.py (similar linter integration)

External Dependencies:
    - black (must be installed in the environment)

"""

import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics

import re

try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class BlackStage(ToolStage):
    """Pipeline stage for running black in check mode and collecting formatting diagnostics.

    Assigns formatting issues to both project-level and per-file tool_results.
    """
    tool_name = "black"

    def _get_tool_path(self):
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "black"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_executable} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run black in check mode as a subprocess on the project root.
        Interruptible via skip_controller.
        Returns:
            Raw output from black (str) or error dict
        """
        if skip_controller and skip_controller.should_skip():
            print("[BlackStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}
            
        options = self.config.get("options", "").split()
        black_executable = self._get_tool_path()
        
        base_cmd = [black_executable, "--check", "--diff"]
        base_cmd.extend(options)
        base_cmd.append(str(self.project_root))
        
        cmd = base_cmd
        
        logger.info(f"[BlackStage] Running command: {' '.join(cmd)}")
        self.progress.start_phase("Black (check)", total_steps=1)
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
            if result.returncode == 123:
                error_msg = result.stderr.strip() or "Black internal error"
                logger.error(f"[BlackStage] Black failed with internal error: {error_msg}")
                self.progress.complete_phase("Black (check)", status="Error")
                return {"error": f"Black execution failed: {error_msg}", "raw_output": result.stdout, "stderr": result.stderr}
            self.progress.complete_phase("Black (check)", status="Complete!")
            return result.stdout
        except FileNotFoundError:
            error_msg = f"Black executable not found at '{black_executable}'. Ensure PAT setup completed correctly."
            logger.error(f"[BlackStage] {error_msg}")
            self.progress.complete_phase("Black (check)", status="Not Found")
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error running black: {e}"
            logger.error(f"[BlackStage] {error_msg}")
            self.progress.complete_phase("Black (check)", status="Error")
            return {"error": error_msg}
        finally:
            self.progress.increment()

    def parse_output(self, output: Any) -> None:
        """
        Parse black output, update metrics with formatting issues and summary.
        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['black'] includes: reformatted (list), summary (str), raw_output (str)
            - FileMetrics.tool_results['black']: reformatted (bool), summary (str)
        Args:
            output: Raw output from black (str) or error dict
        """
        if isinstance(output, dict) and (output.get("skipped") or output.get("error")):
            self.metrics.tool_results["black"] = output
            return

        if not isinstance(output, str):
            logger.warning(f"[BlackStage] parse_output received unexpected data type: {type(output)}")
            self.metrics.tool_results["black"] = {"error": "Invalid data received by parse_output"}
            return

        reformatted_files = []
        file_reformatted_map: Dict[str, bool] = {}
        reformatted_pattern = re.compile(r"^reformatted (.+)$")
        would_reformat_pattern = re.compile(r"^would reformat (.+)$")

        for line in output.splitlines():
            if line.strip() == "":
                continue
            match = reformatted_pattern.match(line.strip())
            if not match:
                match = would_reformat_pattern.match(line.strip())
            
            if match:
                try:
                    file_path_rel = match.group(1)
                    file_path_abs = str((self.project_root / file_path_rel).resolve())
                    reformatted_files.append(file_path_rel)
                    file_reformatted_map[file_path_abs] = True
                except Exception as e:
                    logger.warning(f"[BlackStage] Could not resolve path '{match.group(1)}' relative to {self.project_root}: {e}")
                    reformatted_files.append(match.group(1))
                    file_reformatted_map[match.group(1)] = True 

        summary = f"{len(reformatted_files)} files would be reformatted"
        self.metrics.tool_results["black"] = {
            "reformatted": reformatted_files,
            "summary": summary,
            "raw_output": output,
        }
        
        for file_metrics in self.metrics.files.values():
            file_metrics.tool_results["black"] = {"reformatted": False, "summary": "clean"}

        for file_path_abs, was_reformatted in file_reformatted_map.items():
            found = False
            for key, file_metrics in self.metrics.files.items():
                abs_key = str(Path(self.project_root) / file_metrics.path) 
                if file_path_abs == abs_key:
                    file_metrics.tool_results["black"] = {
                        "reformatted": was_reformatted,
                        "summary": "would be reformatted" if was_reformatted else "clean"
                    }
                    found = True
                    break
            if not found:
                for key, file_metrics in self.metrics.files.items():
                    if file_path_abs == file_metrics.path:
                        file_metrics.tool_results["black"] = {
                            "reformatted": was_reformatted,
                            "summary": "would be reformatted" if was_reformatted else "clean"
                        }
                    break

# TODO:
# - Add unit tests for output parsing and per-file assignment
# - Add config options for enabling/disabling, custom black config

#

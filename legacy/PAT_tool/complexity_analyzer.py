"""
Complexity Analyzer Module

Analyzes the complexity of Python code, calculating metrics such as cyclomatic
complexity, maintainability index, and other quality indicators.
"""

import ast
import sys
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from radon.raw import analyze
from radon.visitors import ComplexityVisitor

try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
    from PAT_tool.utils import logger, read_file_content, safe_execution
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
    from utils import logger, read_file_content, safe_execution

class ComplexityAnalyzer:
    """Analyzes the complexity of Python code."""

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress: Any):
        """Initialize the complexity analyzer.

        Args:
            project_root: Path to the project root
            metrics: ProjectMetrics instance to store results
            progress: progress tracker instance for tracking progress
        """
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress

    def analyze(self):
        """Analyze code complexity for all files."""
        self.progress.start_phase("Calculating complexity metrics", len(self.metrics.files))

        for module_name, metrics in self.metrics.files.items():
            self._analyze_file_complexity(module_name, metrics)
            self.progress.increment()

        # Sort complexity scores for reporting
        self.metrics.complexity_scores = dict(sorted(
            self.metrics.complexity_scores.items(),
            key=lambda x: x[1],
            reverse=True
        ))

    @safe_execution
    def _analyze_file_complexity(self, module_name: str, metrics: FileMetrics):
        """Analyze complexity for a single file.

        Args:
            module_name: Name of the module
            metrics: FileMetrics instance for the module
        """
        file_path = self.project_root / metrics.path

        content, _ = read_file_content(file_path)
        if not content:
            return

        # Make sure content doesn't have syntax errors
        try:
            ast.parse(content)
        except SyntaxError:
            logger.warning(f"Syntax error in {file_path}, skipping complexity analysis")
            return
        except Exception as e:
            logger.warning(f"Error parsing {file_path}: {e}")
            return

        try:
            # Calculate cyclomatic complexity
            complexity = ComplexityVisitor.from_code(content)
            total_cc = sum(obj.complexity for obj in complexity.functions)

            # Calculate maintainability index
            raw_metrics = analyze(content)

            metrics.complexity = total_cc
            self.metrics.complexity_scores[module_name] = total_cc

            # Extract functions and classes
            self._extract_definitions(content, metrics)

        except RecursionError:
            logger.error(f"RecursionError analyzing complexity in {file_path}")
            # Set default values to avoid null references
            metrics.complexity = 0
            self.metrics.complexity_scores[module_name] = 0

        except Exception as e:
            logger.error(f"Error analyzing complexity in {file_path}: {e}")
            # Set default values
            metrics.complexity = 0
            self.metrics.complexity_scores[module_name] = 0

    @safe_execution
    def _extract_definitions(self, content: str, metrics: FileMetrics):
        """Extract function and class definitions from code.

        Args:
            content: Python source code
            metrics: FileMetrics instance to update
        """
        try:
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    metrics.functions.append(node.name)
                elif isinstance(node, ast.ClassDef):
                    metrics.classes.append(node.name)

        except RecursionError:
            logger.error("RecursionError extracting definitions, using simplified approach")
            # Simplified function/class extraction using regex
            self._extract_definitions_regex(content, metrics)

        except Exception as e:
            logger.error(f"Error extracting definitions: {e}")

    def _extract_definitions_regex(self, content: str, metrics: FileMetrics):
        """Extract function and class definitions using regex as fallback.

        Args:
            content: Python source code
            metrics: FileMetrics instance to update
        """
        import re

        # Simple regex patterns - not perfect but works as fallback
        function_pattern = r'^\s*def\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\('
        class_pattern = r'^\s*class\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*[:\(]'

        for line in content.split('\n'):
            # Extract function names
            func_match = re.match(function_pattern, line)
            if func_match:
                metrics.functions.append(func_match.group(1))

            # Extract class names
            class_match = re.match(class_pattern, line)
            if class_match:
                metrics.classes.append(class_match.group(1))
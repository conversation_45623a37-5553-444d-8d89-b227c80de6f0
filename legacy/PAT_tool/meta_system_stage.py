"""
Meta-System Separation Analysis Stage

Pipeline stage for analyzing meta-system separation in the person_suit project.
Identifies violations where one meta-system imports from another.
"""

from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.meta_system_analyzer import MetaSystemAnalyzer
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from meta_system_analyzer import MetaSystemAnalyzer
    from models import ProjectMetrics
    from utils import logger

class MetaSystemStage:
    """Pipeline stage for analyzing meta-system separation in the person_suit project."""
    
    tool_name = "meta_system_separation"
    
    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress: Any, config: Optional[Dict[str, Any]] = None):
        """Initialize the meta-system separation stage.
        
        Args:
            project_root: Path to the project root
            metrics: ProjectMetrics instance to store results
            progress: ProgressTracker instance for tracking progress
            config: Optional configuration for the stage
        """
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        self.config = config or {}
        self.analyzer = MetaSystemAnalyzer(project_root, metrics, progress)
        
    def run(self, project_root: Path, metrics: ProjectMetrics, progress: Any, config: Optional[Dict[str, Any]] = None, skip_controller=None) -> Dict[str, Any]:
        """Run the meta-system separation analysis.
        
        Args:
            project_root: Path to the project root
            metrics: ProjectMetrics instance to store results
            progress: ProgressTracker instance for tracking progress
            config: Optional configuration for the stage
            skip_controller: Optional controller for skipping the stage
            
        Returns:
            Dict with analysis results
        """
        # Check for skip request
        if skip_controller and skip_controller.should_skip():
            logger.info("Skipping meta-system separation analysis")
            return {"skipped": True, "summary": "Meta-system separation analysis skipped"}
            
        logger.info("Starting meta-system separation analysis")
        
        try:
            # Run the analyzer
            self.analyzer.analyze()
            
            # Get results
            results = metrics.tool_results.get("meta_system_separation", {})
            violations = results.get("violations", [])
            
            logger.info(f"Meta-system separation analysis complete. Found {len(violations)} violations.")
            
            return {
                "violations": violations,
                "meta_system_graph": results.get("meta_system_graph", {}),
                "summary": f"Found {len(violations)} meta-system separation violations"
            }
        except Exception as e:
            logger.error(f"Error in meta-system separation analysis: {e}")
            return {
                "error": str(e),
                "summary": "Meta-system separation analysis failed"
            }

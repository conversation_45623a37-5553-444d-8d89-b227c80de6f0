"""
Utility functions for project analysis.

This module contains utility functions that support the project analysis
process, including file reading and handling, error management, and other
common operations.

Note: Logging is configured centrally in main.py. This module only defines a logger instance.
"""

import fnmatch
import logging
import os
import sys
import traceback
from pathlib import Path
from typing import List, Optional, Tuple

# Do NOT configure logging here; it is handled by main.py
logger = logging.getLogger('project_analyze')

# Fallback logger configuration if main.py doesn't configure it
if not logger.handlers:
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# Set recursion limit to avoid stack overflow
import sys

sys.setrecursionlimit(3000)  # Default is typically 1000

class SafetySettings:
    """Configuration class for safety and error handling settings."""

    # Maximum file size to analyze in bytes (default: 5MB)
    MAX_FILE_SIZE = 5 * 1024 * 1024

    # Maximum number of files to analyze (set a reasonable limit to prevent overload)
    MAX_FILES = 10000

    # Maximum recursion depth for AST parsing
    MAX_AST_RECURSION_DEPTH = 500

    # Timeout for long-running operations (in seconds)
    OPERATION_TIMEOUT = 60


def read_file_content(file_path: Path) -> Tuple[str, int]:
    """Read file content with proper encoding handling and safety checks.

    Args:
        file_path: Path to the file

    Returns:
        Tuple of (content, line_count)

    Raises:
        Various exceptions related to file reading, which should be caught by callers
    """
    if not file_path.exists():
        logger.warning(f"File does not exist: {file_path}")
        return "", 0

    if file_path.stat().st_size > SafetySettings.MAX_FILE_SIZE:
        logger.warning(f"File too large (>{SafetySettings.MAX_FILE_SIZE/1024/1024:.1f}MB): {file_path}")
        return "", 0

    # Try multiple encodings
    encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
    content = ""
    line_count = 0

    for encoding in encodings:
        try:
            with open(file_path, 'r', encoding=encoding) as file:
                content = file.read()
                line_count = content.count('\n') + 1
                return content, line_count
        except UnicodeDecodeError:
            continue
        except Exception as e:
            logger.warning(f"Error reading {file_path} with {encoding} encoding: {e}")
            continue

    # If all encodings fail
    logger.warning(f"Could not read {file_path} with any encoding")
    return "", 0


def safe_execution(func):
    """Decorator for safe execution of functions with proper error handling.

    This decorator catches exceptions, logs them, and allows the program to continue
    running even if a function fails.

    Args:
        func: The function to wrap

    Returns:
        Wrapped function with error handling
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except RecursionError:
            logger.error(f"RecursionError in {func.__name__}: {traceback.format_exc()}")
            return None
        except Exception as e:
            logger.error(f"Error in {func.__name__}: {e}\n{traceback.format_exc()}")
            return None
    return wrapper


def load_exclusion_patterns(project_root: Path) -> List[str]:
    """
    Load exclusion patterns from a .patignore file at the project root, if present.
    Falls back to default patterns if not found.

    Args:
        project_root: Path to the project root directory
    Returns:
        List of exclusion patterns (strings)
    """
    patignore_path = project_root / '.patignore'
    patterns = []
    if patignore_path.exists():
        with open(patignore_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                patterns.append(line)
    # Always include standard exclusions
    patterns += [
        '__pycache__', '.git', '.pytest_cache', '.mypy_cache',
        '.venv', 'venv', 'PAT_venv', 'build', 'dist', 'site-packages',
        'lib/python', 'analysis_venv', 'node_modules', 'packages', 'vendor', 'libs', 'dependencies',
        'docs_backup_', 'backup_', '.bak', '.backup',
        'nltk_data', 'PAT_output', 'PAT_tool/__pycache__',
    ]
    return patterns


def is_excluded_path(path: Path, excluded_patterns: List[str], project_root: Path) -> bool:
    """
    Check if a path matches any of the excluded patterns (supports .gitignore-like syntax).

    Args:
        path: Path to check
        excluded_patterns: List of patterns to exclude
        project_root: Path to the project root directory
    Returns:
        True if the path should be excluded, False otherwise
    """
    rel_path = str(path.relative_to(project_root)) if project_root in path.parents or path == project_root else str(path)
    for pattern in excluded_patterns:
        # Simple substring match
        if pattern in rel_path or pattern in path.name:
            return True
        # Support for directory patterns (e.g., 'venv/')
        if pattern.endswith('/') and rel_path.startswith(pattern.rstrip('/')):
            return True
        # Support for wildcard patterns (e.g., '*.pyc')
        if '*' in pattern:
            if fnmatch.fnmatch(rel_path, pattern) or fnmatch.fnmatch(path.name, pattern):
                return True
    # Special cases for common patterns
    if any(backup_pattern in rel_path.lower() for backup_pattern in ['backup', '.bak', 'old_', '_old', 'archive', '.tmp', '.temp']):
        return True
    return False


def get_file_status(lines: int) -> str:
    """Get a status indicator based on file size.

    Args:
        lines: Number of lines in the file

    Returns:
        Status indicator (emoji and text)
    """
    if lines < 50:
        return "🟢 Very Small"
    elif lines < 300:
        return "🟢 Small"
    elif lines < 600:
        return "🟢 Optimal"
    elif lines < 1000:
        return "🟡 Medium"
    else:
        return "🔴 Large"


def ensure_dir(path: str) -> None:
    """Ensure a directory exists, creating it if necessary.

    Args:
        path: Directory path to ensure
    """
    Path(path).mkdir(exist_ok=True, parents=True)


def load_patignore_patterns(project_root: Path) -> List[str]:
    """Load exclusion patterns from a .patignore file in the project root.

    Args:
        project_root: Path to the project root directory.
    Returns:
        List of glob/wildcard patterns to exclude.
    """
    patignore_path = project_root / ".patignore"
    if not patignore_path.exists():
        return []
    patterns = []
    with open(patignore_path, "r", encoding="utf-8") as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith("#"):
                continue
            patterns.append(line)
    return patterns


def is_excluded_by_patignore(path: Path, patterns: List[str]) -> bool:
    """Check if a path matches any exclusion pattern from .patignore.

    Args:
        path: Path to check (absolute or relative to project root).
        patterns: List of glob/wildcard patterns.
    Returns:
        True if the path matches any exclusion pattern, False otherwise.
    """
    rel_path = str(path)
    for pat in patterns:
        if fnmatch.fnmatch(rel_path, pat) or fnmatch.fnmatch(path.name, pat):
            return True
    return False
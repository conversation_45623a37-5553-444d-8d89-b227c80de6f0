#!/usr/bin/env python3
"""
Project Analysis Tool (PAT) - Main Entry Point

A comprehensive project analysis tool that provides insights into code structure,
dependencies, complexity, and quality metrics.

Usage:
    python PAT_analyze.py <project_root>

Example:
    python PAT_analyze.py .
"""

import os
import platform
import subprocess
import sys
from pathlib import Path

# Adjust Python path to include PAT_tool directory
current_dir = Path(__file__).parent.absolute()
sys.path.insert(0, str(current_dir))

# Add PAT_tool directory to Python path
pat_tool_dir = current_dir / "PAT_tool"
sys.path.insert(0, str(pat_tool_dir))

# Import main module
try:
    from PAT_tool import main
except ImportError:
    import main

def find_venv():
    """Find the virtual environment directory.

    Returns:
        Path to virtual environment or None if not found
    """
    # Check default location
    default_venv = Path(__file__).parent / "PAT_venv"
    if default_venv.exists():
        return default_venv

    return None

def is_venv_active():
    """Check if a virtual environment is already active.

    Returns:
        True if a virtual environment is active, False otherwise
    """
    return sys.prefix != sys.base_prefix

def run_in_venv(venv_path, args):
    """Run the analysis tool in the virtual environment.

    Args:
        venv_path: Path to virtual environment
        args: Command line arguments

    Returns:
        Exit code from the command
    """
    # Determine python path in the venv
    if platform.system() == "Windows":
        python_path = venv_path / "Scripts" / "python"
    else:
        python_path = venv_path / "bin" / "python"

    # Create a small wrapper script to set up the Python path correctly
    wrapper_script = Path(__file__).parent / "_temp_pat_wrapper.py"
    pat_tool_dir = Path(__file__).parent / "PAT_tool"
    main_script = pat_tool_dir / "main.py"

    with open(wrapper_script, "w") as f:
        f.write(f'''
#!/usr/bin/env python3
import sys
import os

# Add PAT_tool directory to Python path
sys.path.insert(0, "{str(Path(__file__).parent)}")
sys.path.insert(0, "{str(pat_tool_dir)}")

# Run the main script
with open("{str(main_script)}") as script_file:
    script_content = script_file.read()
    code = compile(script_content, "{str(main_script)}", 'exec')
    exec(code, {{'__file__': "{str(main_script)}"}}, {{}})
''')

    # Check if output directory is already specified in args
    output_dir_specified = False
    for i, arg in enumerate(args):
        if arg == "--output-dir" and i + 1 < len(args):
            output_dir_specified = True
            break

    # Build the command, only adding output directory if not already specified
    cmd = [
        str(python_path),
        str(wrapper_script),
        *args
    ]

    # Add default output directory only if not already specified
    if not output_dir_specified:
        cmd.extend(["--output-dir", str(Path(__file__).parent / "PAT_output")])

    # Run the command
    print(f"Running: {' '.join(cmd)}")
    try:
        result = subprocess.call(cmd)
        # Clean up the temporary wrapper script
        if wrapper_script.exists():
            os.unlink(wrapper_script)
        return result
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user.")
        # Clean up the temporary wrapper script
        if wrapper_script.exists():
            os.unlink(wrapper_script)
        return 1
    except Exception as e:
        print(f"\nError running analysis tool: {e}")
        # Clean up the temporary wrapper script
        if wrapper_script.exists():
            os.unlink(wrapper_script)
        return 1

def setup_venv():
    """Create virtual environment and install dependencies.

    Returns:
        Path to the virtual environment if successful, None otherwise
    """
    venv_path = Path(__file__).parent / "PAT_venv"
    requirements_path = Path(__file__).parent / "PAT_requirements.txt"

    try:
        # Create virtual environment
        print(f"Creating virtual environment at {venv_path}...")
        subprocess.check_call([sys.executable, "-m", "venv", str(venv_path)])

        # Determine pip path
        if platform.system() == "Windows":
            pip_path = venv_path / "Scripts" / "pip"
        else:
            pip_path = venv_path / "bin" / "pip"

        # Upgrade pip
        print("Upgrading pip...")
        subprocess.check_call([str(pip_path), "install", "--upgrade", "pip"])

        # Install requirements
        print("Installing dependencies...")
        subprocess.check_call([str(pip_path), "install", "-r", str(requirements_path)])

        return venv_path
    except subprocess.CalledProcessError as e:
        print(f"Error setting up virtual environment: {e}")
        return None

def main_cli():
    """Main entry point for the command-line interface."""
    # Get command line arguments
    args = sys.argv[1:]

    if not args or args[0] in ["-h", "--help"]:
        print(__doc__)
        return 0

    # Check if running in a virtual environment
    if is_venv_active():
        # Check if output directory is already specified in args
        output_dir_specified = False
        for i, arg in enumerate(sys.argv):
            if arg == "--output-dir" and i + 1 < len(sys.argv):
                output_dir_specified = True
                break

        # Add default output directory only if not already specified
        if not output_dir_specified:
            sys.argv.extend(["--output-dir", str(Path(__file__).parent / "PAT_output")])

        from PAT_tool.main import main as run_analysis
        return run_analysis()

    # Find virtual environment
    venv_path = find_venv()
    if not venv_path:
        print("Virtual environment not found. Creating one now...")
        venv_path = setup_venv()
        if not venv_path:
            print("Failed to set up virtual environment. Please run setup manually.")
            return 1

    # Run the tool in the virtual environment
    return run_in_venv(venv_path, args)

if __name__ == "__main__":
    sys.exit(main_cli())
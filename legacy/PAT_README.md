# Project Analysis Tool (PAT)

A comprehensive Python project analysis tool that provides insights into code structure, dependencies, complexity, and quality metrics. This tool has been designed to be modular, maintainable, and resilient against errors that might occur when analyzing large or complex codebases.

## Features

- **Structure Analysis**: Analyze directory organization, file size distribution, and module descriptions
- **Dependency Analysis**: Discover module dependencies and detect circular references
- **Complexity Analysis**: Calculate cyclomatic complexity and maintainability metrics
- **Visualization**: Generate dependency graphs and complexity heatmaps
- **Reporting**: Create detailed reports in JSON and Markdown formats

## Directory Structure

- **PAT_project_analysis/**: Root directory for the tool
  - **PAT_tool/**: Python modules for the analysis tool
  - **PAT_output/**: Generated analysis reports and visualizations
  - **PAT_venv/**: Virtual environment for tool dependencies
  - **PAT_analyze.py**: Main analysis script
  - **PAT_setup.py**: Environment setup script
  - **PAT_install.py**: Installation script
  - **PAT_run.py**: Convenience runner script
  - **PAT_requirements.txt**: Required Python packages
  - **PAT_README.md**: This documentation file
  - **CHANGES.md**: Documentation of recent changes and improvements

## Key Improvements

This version of the analysis tool offers several improvements:

- **Error Resilience**: Gracefully handles recursion errors, syntax errors, and other exceptions
- **Modular Architecture**: Each analysis phase is implemented in a separate module for better maintainability
- **Fallback Mechanisms**: Provides alternative approaches when primary analysis methods fail
- **Incremental Analysis**: Processes files and directories incrementally to prevent memory issues
- **Improved Reporting**: Generates more comprehensive and useful reports
- **Self-contained Environment**: Includes virtual environment setup for dependencies
- **Clear Separation**: Completely separated from the main project with PAT_ prefix naming convention for all files

## Setup

### Option 1: Automatic Setup (Recommended)

Run the installation script to set up everything automatically:

```bash
python PAT_project_analysis/PAT_install.py
```

This will:
1. Make all scripts executable
2. Create the output directory for analysis results
3. Set up a virtual environment in `PAT_project_analysis/PAT_venv/`
4. Install all required dependencies

### Option 2: Manual Setup

1. Create a virtual environment:
   ```bash
   python -m venv PAT_project_analysis/PAT_venv
   ```

2. Activate the virtual environment:
   - Windows: `PAT_project_analysis\PAT_venv\Scripts\activate`
   - Unix/Linux: `source PAT_project_analysis/PAT_venv/bin/activate`

3. Install dependencies:
   ```bash
   pip install -r PAT_project_analysis/PAT_requirements.txt
   ```

4. Create the output directory:
   ```bash
   mkdir -p PAT_project_analysis/PAT_output
   ```

## Usage

### Simple Usage

Run the analysis using one of these methods:

```bash
# Option 1: Use the convenience runner
python PAT_project_analysis/PAT_run.py <project_path>

# Option 2: Use the main analysis script directly
python PAT_project_analysis/PAT_analyze.py <project_path>
```

Example:
```bash
python PAT_project_analysis/PAT_run.py .
```

### Advanced Usage

For more control over the analysis process, use the main module directly:

```bash
python -m PAT_project_analysis.PAT_tool.main <project_path> [options]
```

Options:
- `--output-dir <dir>`: Directory to save analysis results (default: PAT_project_analysis/PAT_output)
- `--project-name <name>`: Project name (default: derived from project root)
- `--skip-visualizations`: Skip generating visualizations (useful if graphviz is not available)
- `--log-level {DEBUG,INFO,WARNING,ERROR,CRITICAL}`: Set logging level (default: INFO)

## Output

The tool generates the following reports in the output directory:

- `PAT_<project>_analysis.json`: Complete analysis data in JSON format
- `PAT_<project>_report.md`: Comprehensive project analysis report
- `PAT_<project>_structure.md`: Detailed project structure analysis
- `PAT_<project>_analysis_summary.md`: Key findings and recommendations
- `PAT_<project>_dependency_graph.png`: Visual representation of module dependencies
- `PAT_<project>_complexity_heatmap.png`: Visual representation of module complexity

## Architecture

The tool is designed with a modular architecture within the PAT_tool directory:

- `models.py`: Data classes for project metrics
- `utils.py`: Utility functions and error handling
- `structure_analyzer.py`: Analyzes project structure
- `dependency_analyzer.py`: Analyzes module dependencies
- `complexity_analyzer.py`: Analyzes code complexity
- `visualization.py`: Generates visual representations
- `reporter.py`: Generates reports in various formats
- `main.py`: Main entry point for the tool

## Troubleshooting

### Enhanced Error Handling

The tool now includes comprehensive error handling and reporting:

- **Detailed Error Summaries**: Critical errors are summarized at the end of the analysis
- **Error Classification**: Errors are categorized as critical errors, warnings, or tool-specific errors
- **JSON Error Report**: A detailed error report is saved in JSON format for further analysis
- **Helpful Hints**: Common errors include hints for resolution

### Common Issues

- **RecursionError**: The tool implements fallback mechanisms to handle recursion errors that might occur when parsing complex files.
- **Dependency graph generation fails**: Ensure that graphviz is installed, or use the `--skip-visualizations` option.
- **Missing dependencies**: Run the PAT_install.py script to ensure all dependencies are installed.
- **Import errors**: The tool now handles import issues more gracefully with improved Python path management.

### Logging

The tool generates several log files:

- `PAT_output/latest/PAT_analysis.log`: Main log file with detailed information
- `PAT_output/latest/error_summary.json`: Structured error report in JSON format
- `PAT_output/latest/dependency_audit.log`: Log of dependency-related issues
- `PAT_output/latest/syntax_errors.log`: Log of syntax errors found in the codebase

Check these files for detailed error information if something goes wrong.

## License

This tool is part of the project and follows the same licensing terms.
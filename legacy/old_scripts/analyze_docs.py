#!/usr/bin/env python3
"""
Documentation File Size Analyzer

A simple script to analyze the size distribution of Markdown files in the documentation directory.
This helps identify large files that might need splitting or small files that might need consolidation.

Usage:
    python analyze_docs.py <docs_directory>

Example:
    python analyze_docs.py ./docs/New
"""

import math
import os
import statistics
import sys
from collections import defaultdict
from pathlib import Path


def human_readable_size(size_bytes):
    """Convert a size in bytes to a human-readable format."""
    if size_bytes == 0:
        return "0B"
    size_names = ("B", "KB", "MB", "GB")
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def count_lines(file_path):
    """Count the number of lines in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f)
    except Exception as e:
        print(f"Error counting lines in {file_path}: {e}")
        return 0

def analyze_directory(directory_path, extensions=None):
    """
    Analyze file sizes in a directory recursively.
    
    Args:
        directory_path: Path to the directory
        extensions: List of file extensions to include (e.g., ['.md'])
    
    Returns:
        Dictionary with analysis results
    """
    if extensions is None:
        extensions = ['.md']
    
    directory_path = Path(directory_path)
    results = {
        'total_files': 0,
        'total_size': 0,
        'files_by_extension': defaultdict(int),
        'size_by_extension': defaultdict(int),
        'file_details': [],
        'directory_stats': defaultdict(lambda: {'count': 0, 'size': 0}),
        'file_sizes': [],
        'line_counts': [],
    }
    
    # Walk through the directory
    for root, _, files in os.walk(directory_path):
        rel_path = Path(root).relative_to(directory_path)
        dir_path = str(rel_path) if str(rel_path) != '.' else '(root)'
        
        for file in files:
            file_path = Path(root) / file
            ext = file_path.suffix.lower()
            
            # Skip files with extensions not in the list
            if extensions and ext not in extensions:
                continue
            
            # Get file stats
            size = file_path.stat().st_size
            line_count = count_lines(file_path)
            
            # Update counters
            results['total_files'] += 1
            results['total_size'] += size
            results['files_by_extension'][ext] += 1
            results['size_by_extension'][ext] += size
            results['file_sizes'].append(size)
            results['line_counts'].append(line_count)
            
            # Update directory stats
            results['directory_stats'][dir_path]['count'] += 1
            results['directory_stats'][dir_path]['size'] += size
            
            # Add file details
            rel_file_path = file_path.relative_to(directory_path)
            results['file_details'].append({
                'path': str(rel_file_path),
                'size': size,
                'size_human': human_readable_size(size),
                'lines': line_count,
                'directory': dir_path,
            })
    
    # Sort file details by size (largest first)
    results['file_details'] = sorted(
        results['file_details'], 
        key=lambda x: x['size'], 
        reverse=True
    )
    
    return results

def print_results(results, top_n=10):
    """Print analysis results."""
    print("\n" + "=" * 80)
    print("DOCUMENTATION FILE ANALYSIS SUMMARY")
    print("=" * 80)
    
    print(f"\nTotal files analyzed: {results['total_files']}")
    print(f"Total size: {human_readable_size(results['total_size'])}")
    
    # Print statistics
    if results['file_sizes']:
        print("\nFile Size Statistics:")
        print(f"  Average size: {human_readable_size(statistics.mean(results['file_sizes']))}")
        print(f"  Median size: {human_readable_size(statistics.median(results['file_sizes']))}")
        try:
            print(f"  Standard deviation: {human_readable_size(statistics.stdev(results['file_sizes']))}")
        except statistics.StatisticsError:
            print("  Standard deviation: N/A (not enough data)")
        print(f"  Smallest file: {human_readable_size(min(results['file_sizes']))}")
        print(f"  Largest file: {human_readable_size(max(results['file_sizes']))}")
    
    if results['line_counts']:
        print("\nLine Count Statistics:")
        print(f"  Average lines: {int(statistics.mean(results['line_counts']))}")
        print(f"  Median lines: {int(statistics.median(results['line_counts']))}")
        try:
            print(f"  Standard deviation: {int(statistics.stdev(results['line_counts']))}")
        except statistics.StatisticsError:
            print("  Standard deviation: N/A (not enough data)")
        print(f"  Fewest lines: {min(results['line_counts'])}")
        print(f"  Most lines: {max(results['line_counts'])}")
    
    # Print extensions information
    print("\nFiles by Extension:")
    for ext, count in sorted(results['files_by_extension'].items(), key=lambda x: x[1], reverse=True):
        size = results['size_by_extension'][ext]
        print(f"  {ext}: {count} files, {human_readable_size(size)} total")
    
    # Print directory information
    print("\nFiles by Directory:")
    for dir_path, stats in sorted(results['directory_stats'].items(), key=lambda x: x[1]['size'], reverse=True):
        print(f"  {dir_path}: {stats['count']} files, {human_readable_size(stats['size'])}")
    
    # Print largest files
    print(f"\nTop {top_n} Largest Files:")
    for i, file_info in enumerate(results['file_details'][:top_n], 1):
        print(f"  {i}. {file_info['path']} - {file_info['size_human']} ({file_info['lines']} lines)")
    
    # Print smallest files
    print(f"\nTop {top_n} Smallest Files:")
    for i, file_info in enumerate(sorted(results['file_details'], key=lambda x: x['size'])[:top_n], 1):
        print(f"  {i}. {file_info['path']} - {file_info['size_human']} ({file_info['lines']} lines)")
    
    # File size distribution
    size_ranges = [
        (0, 1024, "< 1 KB"),
        (1024, 5*1024, "1-5 KB"),
        (5*1024, 10*1024, "5-10 KB"),
        (10*1024, 20*1024, "10-20 KB"),
        (20*1024, 50*1024, "20-50 KB"),
        (50*1024, 100*1024, "50-100 KB"),
        (100*1024, float('inf'), "> 100 KB")
    ]
    
    print("\nFile Size Distribution:")
    for min_size, max_size, label in size_ranges:
        count = sum(1 for size in results['file_sizes'] if min_size <= size < max_size)
        percentage = (count / results['total_files'] * 100) if results['total_files'] > 0 else 0
        print(f"  {label}: {count} files ({percentage:.1f}%)")
    
    print("\n" + "=" * 80)
    print("END OF ANALYSIS")
    print("=" * 80)

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <docs_directory>")
        sys.exit(1)
    
    docs_dir = sys.argv[1]
    if not os.path.isdir(docs_dir):
        print(f"Error: '{docs_dir}' is not a valid directory")
        sys.exit(1)
    
    print(f"Analyzing documentation files in: {docs_dir}")
    results = analyze_directory(docs_dir, extensions=['.md'])
    print_results(results)

if __name__ == "__main__":
    main() 
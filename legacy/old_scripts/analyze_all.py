#!/usr/bin/env python3
"""
Comprehensive Project Analysis Script

This script analyzes the entire CONVERSATIONAL directory structure,
including all document types, code files, and other relevant content.
It excludes backups, virtual environments, and imported libraries.

Usage:
    python analyze_all.py

Output will be saved to PAT_project_analysis/PAT_output/
"""

import os
import subprocess
import sys
from pathlib import Path

# Common file extensions to analyze
CODE_EXTENSIONS = [
    'py', 'js', 'ts', 'html', 'css', 'sh', 'bash', 'json', 'yml', 'yaml', 
    'xml', 'c', 'cpp', 'h', 'hpp', 'java', 'go', 'rs', 'rb'
]

DOCUMENT_EXTENSIONS = [
    'md', 'txt', 'rst', 'tex', 'pdf', 'docx', 'csv', 'org'
]

CONFIG_EXTENSIONS = [
    'ini', 'cfg', 'conf', 'config', 'env', 'properties'
]

def main():
    """Main function to run the comprehensive analysis."""
    # Get the project root directory
    project_root = Path(os.path.abspath(__file__)).parent
    
    # Path to the PAT tool
    pat_dir = project_root / "PAT_project_analysis"
    
    # All extensions to analyze
    all_extensions = CODE_EXTENSIONS + DOCUMENT_EXTENSIONS + CONFIG_EXTENSIONS
    
    # First use analyze_docs.py to get a quick overview of documentation
    print("\n=== Quick Documentation Analysis ===\n")
    try:
        subprocess.run(["python", "analyze_docs.py", str(project_root)], check=True)
    except subprocess.CalledProcessError:
        print("Warning: Quick documentation analysis failed, continuing with full analysis.")
    
    print("\n=== Comprehensive Project Analysis ===\n")
    
    # Ensure PAT environment is set up
    setup_cmd = [
        "python", 
        str(pat_dir / "PAT_setup.py")
    ]
    
    print("Setting up PAT environment...")
    try:
        subprocess.run(setup_cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Warning: PAT setup failed: {e}")
        print("Continuing with analysis anyway...")
    
    # Run PAT with all file extensions
    # Build the command with all file extensions
    cmd = [
        "python", 
        str(pat_dir / "PAT_run.py"),
        str(project_root)
    ]
    
    # Add all file extensions
    cmd.extend(["--file-extensions"])
    cmd.extend(all_extensions)
    
    print(f"Analyzing all files in: {project_root}")
    print("Including file types:", ", ".join(all_extensions))
    print("Running command:", " ".join(cmd))
    
    try:
        # Run PAT with all file extensions
        result = subprocess.run(cmd, check=True)
        
        # If PAT ran successfully, display location of reports
        if result.returncode == 0:
            output_dir = pat_dir / "PAT_output"
            print(f"\nComprehensive analysis completed successfully!")
            print(f"Reports are available in: {output_dir}")
            print(f"Key reports:")
            print(f"  - PAT_*_report.md (Overall project report)")
            print(f"  - PAT_*_structure.md (Directory and file structure)")
            print(f"  - PAT_*_documentation_report.md (Documentation-specific analysis)")
            print(f"  - PAT_*_analysis_summary.md (Key findings and recommendations)")
    except subprocess.CalledProcessError as e:
        print(f"Error running PAT tool: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 
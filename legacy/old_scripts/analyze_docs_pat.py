#!/usr/bin/env python3
"""
Documentation Analysis Script using PAT

This script runs the extended Project Analysis Tool (PAT) to analyze documentation files.
It specifically focuses on analyzing Markdown (.md) files and generating
a detailed report on documentation quality, size distribution, and structure.

Usage:
    python analyze_docs_pat.py <docs_directory>

Example:
    python analyze_docs_pat.py ./docs/New
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <docs_directory>")
        sys.exit(1)
    
    docs_dir = sys.argv[1]
    if not os.path.isdir(docs_dir):
        print(f"Error: '{docs_dir}' is not a valid directory")
        sys.exit(1)
    
    # Run the simpler analyze_docs.py script first to get a quick overview
    print("\n=== Quick Analysis with analyze_docs.py ===\n")
    try:
        subprocess.run(["python", "analyze_docs.py", docs_dir], check=True)
    except subprocess.CalledProcessError:
        print("Warning: Quick analysis failed, continuing with PAT analysis.")
    
    print("\n=== Detailed Analysis with PAT ===\n")
    
    # Path to the PAT tool
    pat_dir = Path(os.path.abspath(__file__)).parent / "PAT_project_analysis"
    
    # First use the PAT_setup.py to ensure all dependencies are installed
    setup_cmd = [
        "python", 
        str(pat_dir / "PAT_setup.py")
    ]
    
    print("Setting up PAT environment...")
    try:
        subprocess.run(setup_cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Warning: PAT setup failed: {e}")
    
    # Command to run PAT with documentation analysis enabled
    # Note: We're using .md without the dot to match PAT's expectation
    cmd = [
        "python", 
        str(pat_dir / "PAT_run.py"),
        docs_dir,
        "--file-extensions", "md"
    ]
    
    print(f"Analyzing documentation files in: {docs_dir}")
    print("Running command:", " ".join(cmd))
    
    try:
        # Run PAT with documentation analysis
        result = subprocess.run(cmd, check=True)
        
        # If PAT ran successfully, display location of reports
        if result.returncode == 0:
            output_dir = pat_dir / "PAT_output"
            print(f"\nDocumentation analysis completed successfully!")
            print(f"Reports are available in: {output_dir}")
            print(f"Key documentation reports:")
            print(f"  - PAT_*_documentation_report.md (Documentation-specific analysis)")
            print(f"  - PAT_*_structure.md (Directory and file structure)")
            print(f"  - PAT_*_analysis_summary.md (Key findings and recommendations)")
    except subprocess.CalledProcessError as e:
        print(f"Error running PAT tool: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main() 
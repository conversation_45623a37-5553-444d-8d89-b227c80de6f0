#!/usr/bin/env python3
"""
PAT Setup Script

Sets up the Project Analysis Tool (PAT) environment with all necessary dependencies.

Usage:
    python PAT_setup.py
"""

import argparse
import os
import platform
import subprocess
import sys
from pathlib import Path


def parse_args():
    """Parse command line arguments.
    
    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Setup script for the Project Analysis Tool (PAT)"
    )
    parser.add_argument(
        "--no-graphviz",
        action="store_true",
        help="Skip installing graphviz (use if you don't need dependency graph visualization)"
    )
    
    return parser.parse_args()

def run_command(cmd, cwd=None):
    """Run a shell command and capture output.
    
    Args:
        cmd: Command to run as a list of strings
        cwd: Working directory
        
    Returns:
        Command output
        
    Raises:
        SystemExit on command failure
    """
    try:
        result = subprocess.run(
            cmd,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {' '.join(cmd)}")
        print(f"Error output:\n{e.stderr}")
        sys.exit(1)

def create_virtualenv():
    """Create the PAT virtual environment.
    
    Returns:
        Path to the created virtual environment
    """
    venv_path = Path(__file__).parent / "PAT_venv"
    
    if venv_path.exists():
        print(f"Virtual environment already exists at {venv_path}")
        overwrite = input("Overwrite? (y/n): ").lower()
        if overwrite != 'y':
            print("Using existing virtual environment")
            return venv_path
        
        # Remove existing venv
        if platform.system() == "Windows":
            run_command(["rmdir", "/s", "/q", str(venv_path)])
        else:
            run_command(["rm", "-rf", str(venv_path)])
    
    # Create virtual environment
    print(f"Creating virtual environment at {venv_path}...")
    run_command([sys.executable, "-m", "venv", str(venv_path)])
    
    return venv_path

def install_packages(venv_path, skip_graphviz=False):
    """Install required packages in the virtual environment.
    
    Args:
        venv_path: Path to virtual environment
        skip_graphviz: Whether to skip installing graphviz
    """
    # Determine pip path
    if platform.system() == "Windows":
        pip_path = venv_path / "Scripts" / "pip"
    else:
        pip_path = venv_path / "bin" / "pip"
    
    # Upgrade pip
    print("Upgrading pip...")
    run_command([str(pip_path), "install", "--upgrade", "pip"])
    
    # Install packages from requirements file
    requirements_path = Path(__file__).parent / "PAT_requirements.txt"
    print(f"Installing requirements from {requirements_path}...")
    run_command([str(pip_path), "install", "-r", str(requirements_path)])
    
    # Install graphviz separately if not skipped
    if not skip_graphviz:
        try:
            print("Installing graphviz Python package...")
            run_command([str(pip_path), "install", "graphviz"])
        except:
            print("Warning: Could not install graphviz package. Dependency visualization may not work.")

def create_output_directory():
    """Create output directory for analysis results."""
    output_dir = Path(__file__).parent / "PAT_output"
    output_dir.mkdir(parents=True, exist_ok=True)
    print(f"Created output directory at {output_dir}")

def make_scripts_executable():
    """Make the main scripts executable."""
    analyze_script = Path(__file__).parent / "PAT_analyze.py"
    setup_script = Path(__file__).parent / "PAT_setup.py"
    
    if platform.system() != "Windows":
        print("Making scripts executable...")
        try:
            run_command(["chmod", "+x", str(analyze_script), str(setup_script)])
        except:
            print("Warning: Could not make scripts executable. You may need to run them with 'python PAT_analyze.py' explicitly.")

def main():
    """Main entry point for the setup script."""
    args = parse_args()
    
    # Create virtual environment
    venv_path = create_virtualenv()
    
    # Install required packages
    install_packages(venv_path, args.no_graphviz)
    
    # Create output directory
    create_output_directory()
    
    # Make scripts executable
    make_scripts_executable()
    
    # Show activation instructions
    if platform.system() == "Windows":
        activate_cmd = f"{venv_path}\\Scripts\\activate"
        run_cmd = "python PAT_analyze.py"
    else:
        activate_cmd = f"source {venv_path}/bin/activate"
        run_cmd = "./PAT_analyze.py"
    
    print("\nSetup complete!")
    print("=" * 80)
    print(f"To activate the virtual environment, run:\n\n{activate_cmd}\n")
    print("Then run the analysis tool with:")
    print(f"{run_cmd} <project_path>")
    print("\nOr run directly without activating:")
    print(f"python PAT_analyze.py <project_path>")
    print("=" * 80)

if __name__ == "__main__":
    main() 
#!/usr/bin/env python3
"""
Comprehensive Project Analysis Tool (PAT)

A consolidated tool designed specifically for analyzing Conversational and Person_Suit projects.
This tool analyzes the entire project directory structure, including all document types, 
code files, and other relevant content while excluding backups, virtual environments, 
and imported libraries.

Usage:
    python PAT_analyze_all.py [path] [--docs-only] [--code-only] [--all]

Options:
    path        Path to analyze (default: current directory)
    --docs-only Analyze only documentation files
    --code-only Analyze only code files
    --all       Analyze all files (default)

Examples:
    python PAT_analyze_all.py                  # Analyze current directory (all files)
    python PAT_analyze_all.py --docs-only      # Analyze only documentation files
    python PAT_analyze_all.py ../some_dir      # Analyze a specific directory
"""

import argparse
import math
import os
import re
import statistics
import subprocess
import sys
import time
import traceback
from collections import defaultdict
from pathlib import Path

# Common file extensions to analyze
CODE_EXTENSIONS = [
    'py', 'js', 'ts', 'html', 'css', 'sh', 'bash', 'json', 'yml', 'yaml', 
    'xml', 'c', 'cpp', 'h', 'hpp', 'java', 'go', 'rs', 'rb'
]

DOCUMENT_EXTENSIONS = [
    'md', 'txt', 'rst', 'tex', 'pdf', 'docx', 'csv', 'org'
]

CONFIG_EXTENSIONS = [
    'ini', 'cfg', 'conf', 'config', 'env', 'properties'
]

def human_readable_size(size_bytes):
    """Convert a size in bytes to a human-readable format."""
    if size_bytes == 0:
        return "0B"
    size_names = ("B", "KB", "MB", "GB")
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    return f"{s} {size_names[i]}"

def count_lines(file_path):
    """Count the number of lines in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return sum(1 for _ in f)
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='latin-1') as f:
                return sum(1 for _ in f)
        except Exception as e:
            print(f"Error counting lines in {file_path}: {e}")
            return 0
    except Exception as e:
        print(f"Error counting lines in {file_path}: {e}")
        return 0

def is_excluded(path):
    """Check if a path should be excluded from analysis."""
    # Patterns to exclude
    exclude_patterns = [
        # Standard Python exclusions
        '__pycache__', '.git', '.pytest_cache', '.mypy_cache',
        '.venv', 'venv', 'venv311', 'build', 'dist', 'site-packages',
        'lib/python', 'analysis_venv', 'PAT_venv',
        # Backup exclusions
        'docs_backup_', 'backup_', '.bak', '.backup',
        # Library exclusions
        'node_modules', 'packages', 'vendor', 'libs', 'dependencies'
    ]
    
    str_path = str(path)
    
    # Check each excluded pattern
    for pattern in exclude_patterns:
        if pattern in str_path:
            return True
            
    # Special cases for common patterns
    if any(backup_pattern in str_path.lower() for backup_pattern in 
           ['backup', '.bak', 'old_', '_old', 'archive', '.tmp', '.temp']):
        return True
    
    return False

def analyze_docs(directory_path, output_file=None):
    """
    Analyze documentation files in a directory recursively.
    
    Args:
        directory_path: Path to the directory
        output_file: Path to output file (if None, print to console)
    """
    print(f"Analyzing documentation files in: {directory_path}")
    
    directory_path = Path(directory_path)
    results = {
        'total_files': 0,
        'total_size': 0,
        'files_by_extension': defaultdict(int),
        'size_by_extension': defaultdict(int),
        'file_details': [],
        'directory_stats': defaultdict(lambda: {'count': 0, 'size': 0}),
        'file_sizes': [],
        'line_counts': [],
    }
    
    # Process all markdown files
    for ext in DOCUMENT_EXTENSIONS:
        for file_path in directory_path.glob(f"**/*.{ext}"):
            if is_excluded(file_path):
                continue
                
            # Get file stats
            try:
                size = file_path.stat().st_size
                line_count = count_lines(file_path)
                
                # Update counters
                results['total_files'] += 1
                results['total_size'] += size
                results['files_by_extension'][file_path.suffix.lower()] += 1
                results['size_by_extension'][file_path.suffix.lower()] += size
                results['file_sizes'].append(size)
                results['line_counts'].append(line_count)
                
                # Update directory stats
                rel_path = file_path.relative_to(directory_path)
                dir_path = str(rel_path.parent) if str(rel_path.parent) != '.' else '(root)'
                results['directory_stats'][dir_path]['count'] += 1
                results['directory_stats'][dir_path]['size'] += size
                
                # Add file details
                results['file_details'].append({
                    'path': str(rel_path),
                    'size': size,
                    'size_human': human_readable_size(size),
                    'lines': line_count,
                    'directory': dir_path,
                })
            except Exception as e:
                print(f"Error processing {file_path}: {e}")
    
    # Sort file details by size (largest first)
    results['file_details'] = sorted(
        results['file_details'], 
        key=lambda x: x['size'], 
        reverse=True
    )
    
    # Output results
    if output_file:
        with open(output_file, 'w', encoding='utf-8') as f:
            print_results(results, f)
        print(f"Documentation analysis saved to {output_file}")
    else:
        print_results(results)
    
    return results

def print_results(results, file=sys.stdout, top_n=20):
    """Print analysis results.
    
    Args:
        results: Analysis results dictionary
        file: File-like object to write to (default: sys.stdout)
        top_n: Number of top files to show
    """
    print("\n" + "=" * 80, file=file)
    print("DOCUMENTATION FILE ANALYSIS SUMMARY", file=file)
    print("=" * 80, file=file)
    
    print(f"\nTotal files analyzed: {results['total_files']}", file=file)
    print(f"Total size: {human_readable_size(results['total_size'])}", file=file)
    
    # Print statistics
    if results['file_sizes']:
        print("\nFile Size Statistics:", file=file)
        print(f"  Average size: {human_readable_size(statistics.mean(results['file_sizes']))}", file=file)
        print(f"  Median size: {human_readable_size(statistics.median(results['file_sizes']))}", file=file)
        try:
            print(f"  Standard deviation: {human_readable_size(statistics.stdev(results['file_sizes']))}", file=file)
        except statistics.StatisticsError:
            print("  Standard deviation: N/A (not enough data)", file=file)
        print(f"  Smallest file: {human_readable_size(min(results['file_sizes']))}", file=file)
        print(f"  Largest file: {human_readable_size(max(results['file_sizes']))}", file=file)
    
    if results['line_counts']:
        print("\nLine Count Statistics:", file=file)
        print(f"  Average lines: {int(statistics.mean(results['line_counts']))}", file=file)
        print(f"  Median lines: {int(statistics.median(results['line_counts']))}", file=file)
        try:
            print(f"  Standard deviation: {int(statistics.stdev(results['line_counts']))}", file=file)
        except statistics.StatisticsError:
            print("  Standard deviation: N/A (not enough data)", file=file)
        print(f"  Fewest lines: {min(results['line_counts'])}", file=file)
        print(f"  Most lines: {max(results['line_counts'])}", file=file)
    
    # Print extensions information
    print("\nFiles by Extension:", file=file)
    for ext, count in sorted(results['files_by_extension'].items(), key=lambda x: x[1], reverse=True):
        size = results['size_by_extension'][ext]
        print(f"  {ext}: {count} files, {human_readable_size(size)} total", file=file)
    
    # Print directory information
    print("\nFiles by Directory:", file=file)
    for dir_path, stats in sorted(results['directory_stats'].items(), key=lambda x: x[1]['size'], reverse=True):
        print(f"  {dir_path}: {stats['count']} files, {human_readable_size(stats['size'])}", file=file)
    
    # Print largest files
    if results['file_details']:
        print(f"\nTop {min(top_n, len(results['file_details']))} Largest Files:", file=file)
        for i, file_info in enumerate(results['file_details'][:top_n], 1):
            print(f"  {i}. {file_info['path']} - {file_info['size_human']} ({file_info['lines']} lines)", file=file)
        
        # Print smallest files
        print(f"\nTop {min(top_n, len(results['file_details']))} Smallest Files:", file=file)
        for i, file_info in enumerate(sorted(results['file_details'], key=lambda x: x['size'])[:top_n], 1):
            print(f"  {i}. {file_info['path']} - {file_info['size_human']} ({file_info['lines']} lines)", file=file)
    else:
        print("\nNo files found to analyze.", file=file)
    
    # File size distribution
    if results['file_sizes']:
        size_ranges = [
            (0, 1024, "< 1 KB"),
            (1024, 5*1024, "1-5 KB"),
            (5*1024, 10*1024, "5-10 KB"),
            (10*1024, 20*1024, "10-20 KB"),
            (20*1024, 50*1024, "20-50 KB"),
            (50*1024, 100*1024, "50-100 KB"),
            (100*1024, float('inf'), "> 100 KB")
        ]
        
        print("\nFile Size Distribution:", file=file)
        for min_size, max_size, label in size_ranges:
            count = sum(1 for size in results['file_sizes'] if min_size <= size < max_size)
            percentage = (count / results['total_files'] * 100) if results['total_files'] > 0 else 0
            print(f"  {label}: {count} files ({percentage:.1f}%)", file=file)
        
        # Quality assessment (simplified)
        print("\nDocumentation Quality Assessment:", file=file)
        
        # Identify large files that might need splitting
        large_files = [file for file in results['file_details'] if file['size'] > 50 * 1024]
        if large_files:
            print("\nLarge Files to Consider Splitting:", file=file)
            for file in large_files[:10]:  # Show at most 10
                print(f"  - {file['path']} ({file['size_human']}, {file['lines']} lines)", file=file)
        
        # Identify very small files that might need consolidation
        tiny_files = [file for file in results['file_details'] if file['size'] < 1024 and file['lines'] < 30]
        if tiny_files:
            print("\nVery Small Files to Consider Consolidating:", file=file)
            for file in tiny_files[:10]:  # Show at most 10
                print(f"  - {file['path']} ({file['size_human']}, {file['lines']} lines)", file=file)
    
    print("\n" + "=" * 80, file=file)
    print("END OF ANALYSIS", file=file)
    print("=" * 80, file=file)

def run_pat_analysis(target_dir, file_types=None, output_dir=None):
    """
    Run the PAT analysis tool on the target directory.
    
    Args:
        target_dir: Directory to analyze
        file_types: List of file extensions to analyze (without dot)
        output_dir: Directory to save results (default: PAT_output)
    """
    # Current directory (where this script is)
    current_dir = Path(__file__).parent.absolute()
    
    # Default to analyzing all file types if none specified
    if file_types is None:
        file_types = CODE_EXTENSIONS + DOCUMENT_EXTENSIONS + CONFIG_EXTENSIONS
    
    # Make sure output directory exists
    if output_dir is None:
        output_dir = current_dir / "PAT_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # Set up the PAT environment
    setup_cmd = [
        sys.executable,  # Use the current Python interpreter 
        str(current_dir / "PAT_setup.py")
    ]
    
    print("Setting up PAT environment...")
    try:
        subprocess.run(setup_cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Warning: PAT setup failed: {e}")
        print("Continuing with analysis anyway...")
    
    # Run the PAT analysis
    cmd = [
        sys.executable,
        str(current_dir / "PAT_run.py"),
        str(target_dir)
    ]
    
    # Add file extensions (make sure they start with dots)
    cmd.extend(["--file-extensions"])
    cmd.extend([ext if ext.startswith('.') else '.' + ext for ext in file_types])
    
    # Set output directory
    cmd.extend(["--output-dir", str(output_dir)])
    
    print(f"Analyzing files in: {target_dir}")
    print("Including file types:", ", ".join(file_types))
    print("Running command:", " ".join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True)
        
        if result.returncode == 0:
            print(f"\nAnalysis completed successfully!")
            print(f"Reports are available in: {output_dir}")
            print(f"Key reports:")
            print(f"  - PAT_*_report.md (Overall project report)")
            print(f"  - PAT_*_structure.md (Directory and file structure)")
            print(f"  - PAT_*_documentation_report.md (Documentation-specific analysis)")
            print(f"  - PAT_*_analysis_summary.md (Key findings and recommendations)")
            return True
    except subprocess.CalledProcessError as e:
        print(f"Error running PAT tool: {e}")
        return False
    
    return False

def main():
    """Main function."""
    parser = argparse.ArgumentParser(
        description="Project Analysis Tool (PAT) - Comprehensive analysis of Conversational/Person_Suit projects",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__.split("\n\n")[1]  # Use the second paragraph of docstring as epilog
    )
    parser.add_argument(
        "path", 
        nargs="?", 
        default=".", 
        help="Path to analyze (default: current directory)"
    )
    parser.add_argument(
        "--docs-only", 
        action="store_true", 
        help="Analyze only documentation files"
    )
    parser.add_argument(
        "--code-only", 
        action="store_true", 
        help="Analyze only code files"
    )
    parser.add_argument(
        "--quick-docs", 
        action="store_true", 
        help="Run quick documentation analysis only"
    )
    parser.add_argument(
        "--output-dir",
        help="Directory to save results (default: PAT_output)"
    )
    
    args = parser.parse_args()
    
    # Convert to absolute path
    target_dir = Path(args.path).absolute()
    
    # Validate target directory
    if not target_dir.is_dir():
        print(f"Error: '{target_dir}' is not a valid directory")
        sys.exit(1)
    
    # Determine output directory
    output_dir = None
    if args.output_dir:
        output_dir = Path(args.output_dir).absolute()
    
    # Quick documentation analysis
    if args.quick_docs:
        print("\n=== Quick Documentation Analysis ===\n")
        output_dir = Path(__file__).parent / "PAT_output"
        os.makedirs(output_dir, exist_ok=True)
        doc_output = output_dir / "documentation_analysis.txt"
        analyze_docs(target_dir, str(doc_output))
        print(f"\nQuick documentation analysis saved to {doc_output}")
        return
    
    # Determine which file types to analyze
    file_types = []
    if args.docs_only:
        file_types = DOCUMENT_EXTENSIONS
        print("\n=== Documentation Files Analysis ===\n")
    elif args.code_only:
        file_types = CODE_EXTENSIONS
        print("\n=== Code Files Analysis ===\n")
    else:
        file_types = CODE_EXTENSIONS + DOCUMENT_EXTENSIONS + CONFIG_EXTENSIONS
        print("\n=== Comprehensive Project Analysis ===\n")
    
    # Run the analysis
    run_pat_analysis(target_dir, file_types, output_dir)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\nError during analysis: {e}")
        print(traceback.format_exc())
        sys.exit(1) 
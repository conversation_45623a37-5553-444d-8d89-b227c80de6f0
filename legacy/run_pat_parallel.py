#!/usr/bin/env python3
"""
Parallel PAT Runner

This script runs the PAT tool with parallel processing for improved performance.
It divides the project into chunks and analyzes them in parallel.

Usage:
    python run_pat_parallel.py <project_path> [--workers N]

Arguments:
    project_path: Path to the project to analyze
    --workers: Number of worker processes (default: CPU count)
"""

import argparse
import json
import multiprocessing
import os
import shutil
import subprocess
import sys
import tempfile
import time
from pathlib import Path
from typing import Any, Dict, List, Tuple


# ANSI color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run PAT with parallel processing")
    parser.add_argument("project_path", help="Path to the project to analyze")
    parser.add_argument("--workers", type=int, default=multiprocessing.cpu_count(),
                        help=f"Number of worker processes (default: {multiprocessing.cpu_count()})")
    return parser.parse_args()

def find_python_files(directory: Path) -> List[Path]:
    """Find all Python files in the given directory."""
    return list(directory.glob("**/*.py"))

def chunk_files(files: List[Path], num_chunks: int) -> List[List[Path]]:
    """Divide files into chunks for parallel processing."""
    # Sort files by size to balance the workload
    files_with_size = [(f, f.stat().st_size) for f in files]
    files_with_size.sort(key=lambda x: x[1], reverse=True)
    
    # Initialize chunks
    chunks = [[] for _ in range(num_chunks)]
    chunk_sizes = [0] * num_chunks
    
    # Distribute files using a greedy algorithm
    for file_path, size in files_with_size:
        # Find the chunk with the smallest total size
        min_idx = chunk_sizes.index(min(chunk_sizes))
        chunks[min_idx].append(file_path)
        chunk_sizes[min_idx] += size
    
    return chunks

def analyze_chunk(chunk_id: int, files: List[Path], project_path: Path, config_path: Path) -> Dict[str, Any]:
    """Analyze a chunk of files using PAT."""
    # Create a temporary directory for this chunk
    with tempfile.TemporaryDirectory(prefix=f"pat_chunk_{chunk_id}_") as temp_dir:
        temp_dir_path = Path(temp_dir)
        
        # Create a file list for this chunk
        file_list_path = temp_dir_path / "file_list.txt"
        with open(file_list_path, 'w') as f:
            for file_path in files:
                f.write(f"{file_path.relative_to(project_path)}\n")
        
        # Create a temporary output directory
        output_dir = temp_dir_path / "output"
        output_dir.mkdir()
        
        # Get the PAT tool directory
        pat_dir = Path(__file__).parent.absolute() / "PAT_tool"
        
        # Run PAT on this chunk
        cmd = [
            sys.executable,
            str(pat_dir / "main.py"),
            str(project_path),
            "--file-list", str(file_list_path),
            "--output-dir", str(output_dir)
        ]
        
        # Set environment variable for config
        env = os.environ.copy()
        env["PAT_CONFIG_PATH"] = str(config_path)
        
        # Run the command
        start_time = time.time()
        print(f"Chunk {chunk_id}: Starting analysis of {len(files)} files...")
        
        try:
            result = subprocess.run(cmd, env=env, capture_output=True, text=True)
            
            elapsed = time.time() - start_time
            minutes, seconds = divmod(int(elapsed), 60)
            
            if result.returncode == 0:
                print(f"Chunk {chunk_id}: Completed in {minutes:02d}:{seconds:02d} ({len(files)} files)")
                
                # Load the analysis results
                json_files = list(output_dir.glob("PAT_*_analysis.json"))
                if json_files:
                    with open(json_files[0], 'r') as f:
                        analysis_data = json.load(f)
                    return {
                        "chunk_id": chunk_id,
                        "success": True,
                        "files": analysis_data.get("files", []),
                        "metrics": analysis_data.get("metrics", {}),
                        "elapsed": elapsed
                    }
                else:
                    print(f"Chunk {chunk_id}: No analysis data found")
                    return {
                        "chunk_id": chunk_id,
                        "success": False,
                        "error": "No analysis data found",
                        "elapsed": elapsed
                    }
            else:
                print(f"Chunk {chunk_id}: Failed with return code {result.returncode}")
                return {
                    "chunk_id": chunk_id,
                    "success": False,
                    "error": result.stderr,
                    "elapsed": elapsed
                }
        except Exception as e:
            elapsed = time.time() - start_time
            print(f"Chunk {chunk_id}: Error - {str(e)}")
            return {
                "chunk_id": chunk_id,
                "success": False,
                "error": str(e),
                "elapsed": elapsed
            }

def merge_results(results: List[Dict[str, Any]], output_dir: Path) -> Dict[str, Any]:
    """Merge results from multiple chunks."""
    # Initialize merged data
    merged_data = {
        "files": [],
        "metrics": {},
        "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
        "success_count": 0,
        "error_count": 0,
        "total_elapsed": 0
    }
    
    # Merge data from each chunk
    for result in results:
        if result.get("success", False):
            merged_data["files"].extend(result.get("files", []))
            
            # Merge metrics
            for key, value in result.get("metrics", {}).items():
                if key in merged_data["metrics"]:
                    # If the value is a number, sum it
                    if isinstance(value, (int, float)) and isinstance(merged_data["metrics"][key], (int, float)):
                        merged_data["metrics"][key] += value
                    # If the value is a list, extend it
                    elif isinstance(value, list) and isinstance(merged_data["metrics"][key], list):
                        merged_data["metrics"][key].extend(value)
                    # If the value is a dict, update it
                    elif isinstance(value, dict) and isinstance(merged_data["metrics"][key], dict):
                        merged_data["metrics"][key].update(value)
                    # Otherwise, keep the existing value
                else:
                    merged_data["metrics"][key] = value
            
            merged_data["success_count"] += 1
        else:
            merged_data["error_count"] += 1
        
        merged_data["total_elapsed"] += result.get("elapsed", 0)
    
    # Save merged data
    output_dir.mkdir(parents=True, exist_ok=True)
    with open(output_dir / "PAT_merged_analysis.json", 'w') as f:
        json.dump(merged_data, f, indent=2)
    
    return merged_data

def run_parallel_analysis(project_path: Path, num_workers: int) -> Path:
    """Run PAT analysis in parallel."""
    # Get the PAT tool directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Create a config file for parallel processing
    config_path = pat_dir / "PAT_tool" / "config_parallel.yaml"
    shutil.copy(pat_dir / "PAT_tool" / "config_all_tools.yaml", config_path)
    
    # Find all Python files
    print(f"Finding Python files in {project_path}...")
    python_files = find_python_files(project_path)
    print(f"Found {len(python_files)} Python files")
    
    # Divide files into chunks
    num_chunks = min(num_workers, len(python_files))
    if num_chunks == 0:
        print(f"No Python files found in {project_path}")
        return None
    
    chunks = chunk_files(python_files, num_chunks)
    print(f"Divided files into {num_chunks} chunks")
    
    # Create output directory
    timestamp = time.strftime("%Y-%m-%d_%H-%M-%S")
    output_dir = pat_dir / "PAT_output" / timestamp
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Create a symlink to the latest output
    latest_link = pat_dir / "PAT_output" / "latest"
    if latest_link.exists():
        latest_link.unlink()
    latest_link.symlink_to(output_dir.relative_to(pat_dir / "PAT_output"))
    
    # Run analysis in parallel
    print(f"Starting parallel analysis with {num_chunks} workers...")
    start_time = time.time()
    
    # Create a pool of workers
    with multiprocessing.Pool(processes=num_chunks) as pool:
        # Submit tasks
        results = []
        for i, chunk in enumerate(chunks):
            result = pool.apply_async(analyze_chunk, (i, chunk, project_path, config_path))
            results.append(result)
        
        # Wait for all tasks to complete
        pool.close()
        
        # Show progress
        completed = 0
        while completed < len(results):
            completed = sum(1 for r in results if r.ready())
            elapsed = time.time() - start_time
            minutes, seconds = divmod(int(elapsed), 60)
            
            # Calculate ETA
            if completed > 0:
                eta = (elapsed / completed) * (len(results) - completed)
                eta_minutes, eta_seconds = divmod(int(eta), 60)
                eta_str = f"{eta_minutes:02d}:{eta_seconds:02d}"
            else:
                eta_str = "??:??"
            
            print(f"\rProgress: {completed}/{len(results)} chunks completed ({completed/len(results)*100:.1f}%) - Elapsed: {minutes:02d}:{seconds:02d} - ETA: {eta_str}", end="")
            
            if completed < len(results):
                time.sleep(1)
        
        print()  # Add a newline after progress
        
        # Get results
        chunk_results = [r.get() for r in results]
    
    # Merge results
    print("Merging results...")
    merged_data = merge_results(chunk_results, output_dir)
    
    # Print summary
    elapsed = time.time() - start_time
    minutes, seconds = divmod(int(elapsed), 60)
    
    print(f"\nAnalysis completed in {minutes:02d}:{seconds:02d}")
    print(f"Processed {len(python_files)} files in {num_chunks} chunks")
    print(f"Success: {merged_data['success_count']}/{num_chunks} chunks")
    print(f"Output directory: {output_dir}")
    
    return output_dir

def main():
    """Main function."""
    args = parse_args()
    
    project_path = Path(args.project_path)
    if not project_path.exists():
        print(f"Error: Project path {project_path} does not exist")
        return 1
    
    print(f"{Colors.BOLD}{Colors.HEADER}Parallel PAT Analysis{Colors.ENDC}")
    print(f"{Colors.CYAN}Project:{Colors.ENDC} {project_path}")
    print(f"{Colors.CYAN}Workers:{Colors.ENDC} {args.workers}")
    print()
    
    # Run parallel analysis
    output_dir = run_parallel_analysis(project_path, args.workers)
    if not output_dir:
        print(f"{Colors.RED}Error: Analysis failed{Colors.ENDC}")
        return 1
    
    # Generate issue-specific prompts
    print(f"\n{Colors.BOLD}{Colors.HEADER}Generating Issue-Specific Prompts{Colors.ENDC}")
    pat_dir = Path(__file__).parent.absolute()
    cmd = [
        sys.executable,
        str(pat_dir / "generate_issue_prompts.py"),
        str(output_dir),
        "--output-dir", str(output_dir / "issue_prompts")
    ]
    subprocess.run(cmd)
    
    # Extract high-priority files
    print(f"\n{Colors.BOLD}{Colors.HEADER}Extracting High-Priority Files{Colors.ENDC}")
    cmd = [
        sys.executable,
        str(pat_dir / "extract_high_priority.py"),
        str(output_dir),
        "--output-dir", str(output_dir / "high_priority")
    ]
    subprocess.run(cmd)
    
    print(f"\n{Colors.BOLD}{Colors.HEADER}Analysis Complete{Colors.ENDC}")
    print(f"{Colors.CYAN}Output directory:{Colors.ENDC} {output_dir}")
    print(f"{Colors.CYAN}Issue prompts:{Colors.ENDC} {output_dir / 'issue_prompts'}")
    print(f"{Colors.CYAN}High-priority files:{Colors.ENDC} {output_dir / 'high_priority'}")
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}Analysis interrupted by user.{Colors.ENDC}")
        sys.exit(1)

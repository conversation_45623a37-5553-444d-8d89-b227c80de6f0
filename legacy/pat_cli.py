"""
PAT CLI Orchestration Script (Minimal Example)

This script demonstrates how to run PAT analyzer plugins in sequence with a
shared analysis context, enabling cross-plugin checks and data sharing.

Usage:
    python pat_cli.py <project_path>

"""
import sys
from pathlib import Path

from pat_plugins.analyzers.codebase_graph_analyzer import \
    CodebaseGraphAnalyzerPlugin
from pat_plugins.analyzers.effect_system_analyzer import \
    EffectSystemAnalyzerPlugin
from pat_plugins.core import SharedAnalysisContext

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python pat_cli.py <project_path>")
        sys.exit(1)
    project_path = Path(sys.argv[1])
    context = SharedAnalysisContext()

    # Run the codebase graph analyzer first
    graph_analyzer = CodebaseGraphAnalyzerPlugin()
    graph_analyzer.initialize({})
    graph_result = graph_analyzer.analyze_project(project_path, context)
    context.set_plugin_result("codebase_graph", graph_result)
    print("[Graph Analyzer] Nodes:", graph_result["nodes"], "Edges:", graph_result["edges"])
    if graph_result["errors"]:
        print("[Graph Analyzer] Errors:", graph_result["errors"])

    # Run the effect system analyzer, with access to the graph
    effect_analyzer = EffectSystemAnalyzerPlugin()
    effect_analyzer.initialize({})
    effect_result = effect_analyzer.analyze_project(project_path, context)
    context.set_plugin_result("effect_system", effect_result)
    print("[Effect System Analyzer] Files analyzed:", effect_result["files_analyzed"])
    print("[Effect System Analyzer] Top files:", effect_result["top_files"])
    if effect_result["needs_improvement"]:
        print("[Effect System Analyzer] Needs improvement:")
        for item in effect_result["needs_improvement"]:
            print("  ", item)

    # TODO: Add more analyzers and reporting as needed 
# Legacy Directory

This directory contains legacy, experimental, and deprecated code from the Vibe Check project. Files here are preserved for reference but are not part of the main codebase.

## Directory Structure

- `standalone_scripts/` - Standalone analysis scripts and experimental implementations
- `debug_artifacts/` - Debug outputs, logs, and diagnostic files  
- `analysis_outputs/` - Historical analysis results and reports
- `experimental/` - Experimental features and prototypes
- `deprecated/` - Deprecated implementations and old code

## Purpose

These files have been moved here during the project cleanup to:
1. Maintain a clean root directory structure
2. Preserve historical implementations for reference
3. Keep experimental code separate from production code
4. Follow Python packaging best practices

## Usage

Files in this directory should not be imported or used in the main application. They are kept for:
- Historical reference
- Understanding evolution of the codebase
- Potential future inspiration
- Debugging legacy issues

## Maintenance

Files in this directory are not actively maintained and may not work with current dependencies or Python versions.

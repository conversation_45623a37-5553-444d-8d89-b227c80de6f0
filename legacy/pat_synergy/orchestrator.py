"""
PAT Synergy Orchestrator

This module provides the orchestration framework for combining results from
multiple analysis tools to create synergistic insights that would not be
possible from any single tool.
"""

import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Type, Union

import yaml


class AnalysisTool:
    """Base class for analysis tools that can be orchestrated."""
    
    def __init__(self, name: str, config: Optional[Dict[str, Any]] = None):
        """
        Initialize an analysis tool.
        
        Args:
            name: Name of the tool
            config: Tool-specific configuration
        """
        self.name = name
        self.config = config or {}
        self.logger = logging.getLogger(f"pat.synergy.tools.{name}")
    
    def analyze(self, project_path: Union[str, Path], 
               focus_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """
        Run the analysis tool on a project.
        
        Args:
            project_path: Path to the project to analyze
            focus_path: Optional path to focus analysis on
            
        Returns:
            Analysis results
        """
        raise NotImplementedError("Subclasses must implement analyze()")
    
    def get_capabilities(self) -> List[str]:
        """
        Get the capabilities of this tool.
        
        Returns:
            List of capability strings
        """
        return []


class SynergyRule:
    """
    Defines a rule for combining results from multiple tools.
    
    Synergy rules take results from multiple tools and produce new insights
    that would not be possible from any single tool.
    """
    
    def __init__(self, name: str, description: str, 
                required_tools: List[str], required_capabilities: List[str]):
        """
        Initialize a synergy rule.
        
        Args:
            name: Name of the rule
            description: Description of the rule
            required_tools: Names of tools required for this rule
            required_capabilities: Capabilities required for this rule
        """
        self.name = name
        self.description = description
        self.required_tools = required_tools
        self.required_capabilities = required_capabilities
    
    def apply(self, tool_results: Dict[str, Dict[str, Any]]) -> Dict[str, Any]:
        """
        Apply the synergy rule to combine results from multiple tools.
        
        Args:
            tool_results: Dictionary mapping tool names to their results
            
        Returns:
            Combined results with new insights
        """
        raise NotImplementedError("Subclasses must implement apply()")


class AnalysisOrchestrator:
    """
    Orchestrates multiple analysis tools to create synergistic insights.
    
    The orchestrator coordinates the execution of multiple analysis tools,
    manages dependencies between them, and applies synergy rules to combine
    their results into deeper insights.
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the orchestrator.
        
        Args:
            config_path: Path to the configuration file (optional)
        """
        self.logger = logging.getLogger("pat.synergy.orchestrator")
        self.tools: Dict[str, AnalysisTool] = {}
        self.tool_results: Dict[str, Dict[str, Any]] = {}
        self.synergy_rules: Dict[str, SynergyRule] = {}
        
        # Load configuration if provided
        self.config = self._load_config(config_path) if config_path else {}
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """
        Load configuration from a YAML file.
        
        Args:
            config_path: Path to the configuration file
            
        Returns:
            Configuration dictionary
        """
        try:
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            self.logger.info(f"Loaded configuration from {config_path}")
            return config
        except Exception as e:
            self.logger.error(f"Failed to load configuration from {config_path}: {e}")
            return {}
    
    def register_tool(self, tool: AnalysisTool) -> None:
        """
        Register an analysis tool.
        
        Args:
            tool: The analysis tool to register
        """
        self.tools[tool.name] = tool
        self.logger.info(f"Registered tool: {tool.name}")
    
    def register_tools(self, tools: List[AnalysisTool]) -> None:
        """
        Register multiple analysis tools.
        
        Args:
            tools: List of analysis tools to register
        """
        for tool in tools:
            self.register_tool(tool)
    
    def register_synergy_rule(self, rule: SynergyRule) -> None:
        """
        Register a synergy rule.
        
        Args:
            rule: The synergy rule to register
        """
        self.synergy_rules[rule.name] = rule
        self.logger.info(f"Registered synergy rule: {rule.name}")
    
    def register_synergy_rules(self, rules: List[SynergyRule]) -> None:
        """
        Register multiple synergy rules.
        
        Args:
            rules: List of synergy rules to register
        """
        for rule in rules:
            self.register_synergy_rule(rule)
    
    def execute_tool(self, tool_name: str, project_path: Union[str, Path],
                    focus_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """
        Execute a single analysis tool.
        
        Args:
            tool_name: Name of the tool to execute
            project_path: Path to the project to analyze
            focus_path: Optional path to focus analysis on
            
        Returns:
            Analysis results from the tool
        """
        if tool_name not in self.tools:
            self.logger.error(f"Tool not found: {tool_name}")
            return {"error": f"Tool not found: {tool_name}"}
        
        tool = self.tools[tool_name]
        
        try:
            self.logger.info(f"Executing tool: {tool_name}")
            result = tool.analyze(project_path, focus_path)
            self.tool_results[tool_name] = result
            return result
        except Exception as e:
            self.logger.error(f"Error executing tool {tool_name}: {e}")
            return {"error": str(e)}
    
    def get_applicable_rules(self) -> List[SynergyRule]:
        """
        Get the synergy rules that can be applied with the current tools and results.
        
        Returns:
            List of applicable synergy rules
        """
        applicable_rules = []
        
        # Get all available tool names and capabilities
        available_tools = set(self.tools.keys())
        available_tools_with_results = set(self.tool_results.keys())
        
        available_capabilities = set()
        for tool in self.tools.values():
            available_capabilities.update(tool.get_capabilities())
        
        for rule in self.synergy_rules.values():
            # Check if all required tools are available and have results
            required_tools = set(rule.required_tools)
            if required_tools.issubset(available_tools) and required_tools.issubset(available_tools_with_results):
                # Check if all required capabilities are available
                required_capabilities = set(rule.required_capabilities)
                if required_capabilities.issubset(available_capabilities):
                    applicable_rules.append(rule)
        
        return applicable_rules
    
    def apply_synergy_rule(self, rule_name: str) -> Dict[str, Any]:
        """
        Apply a specific synergy rule.
        
        Args:
            rule_name: Name of the rule to apply
            
        Returns:
            Combined results with new insights
        """
        if rule_name not in self.synergy_rules:
            self.logger.error(f"Synergy rule not found: {rule_name}")
            return {"error": f"Synergy rule not found: {rule_name}"}
        
        rule = self.synergy_rules[rule_name]
        
        # Check if all required tools have results
        for tool_name in rule.required_tools:
            if tool_name not in self.tool_results:
                self.logger.error(f"Missing results for tool {tool_name} required by rule {rule_name}")
                return {"error": f"Missing results for tool {tool_name}"}
        
        try:
            self.logger.info(f"Applying synergy rule: {rule_name}")
            return rule.apply(self.tool_results)
        except Exception as e:
            self.logger.error(f"Error applying synergy rule {rule_name}: {e}")
            return {"error": str(e)}
    
    def apply_all_applicable_rules(self) -> Dict[str, Dict[str, Any]]:
        """
        Apply all applicable synergy rules.
        
        Returns:
            Dictionary mapping rule names to their results
        """
        results = {}
        applicable_rules = self.get_applicable_rules()
        
        for rule in applicable_rules:
            results[rule.name] = self.apply_synergy_rule(rule.name)
        
        return results
    
    def execute_synergy_pipeline(self, project_path: Union[str, Path],
                                focus_path: Optional[Union[str, Path]] = None) -> Dict[str, Any]:
        """
        Execute the full synergy pipeline on a project.
        
        Args:
            project_path: Path to the project to analyze
            focus_path: Optional path to focus analysis on
            
        Returns:
            Combined results from all tools and synergy rules
        """
        project_path = Path(project_path)
        if focus_path:
            focus_path = Path(focus_path)
        
        if not project_path.exists():
            self.logger.error(f"Project path does not exist: {project_path}")
            return {"error": f"Project path does not exist: {project_path}"}
        
        # Clear previous results
        self.tool_results.clear()
        
        # Execute all tools
        for tool_name, tool in self.tools.items():
            self.logger.info(f"Executing tool: {tool_name}")
            try:
                result = tool.analyze(project_path, focus_path)
                self.tool_results[tool_name] = result
            except Exception as e:
                self.logger.error(f"Error executing tool {tool_name}: {e}")
                self.tool_results[tool_name] = {"error": str(e)}
        
        # Apply all applicable synergy rules
        synergy_results = self.apply_all_applicable_rules()
        
        # Combine all results
        return {
            "project_path": str(project_path),
            "focus_path": str(focus_path) if focus_path else None,
            "tool_results": self.tool_results,
            "synergy_results": synergy_results
        } 
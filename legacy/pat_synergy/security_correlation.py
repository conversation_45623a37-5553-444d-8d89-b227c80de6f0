"""
File: security_correlation.py
Purpose: Implements a synergy analyzer for correlating security findings.

This module provides the SecurityCorrelationAnalyzer class, which correlates
security findings from multiple plugins to identify high-risk issues.
"""

from collections import defaultdict
from typing import Any, Dict, List

from pat_core.plugin import PluginResult


class SecurityCorrelationAnalyzer:
    """
    Synergy analyzer for correlating security findings from multiple plugins.
    
    This analyzer identifies files with security issues reported by multiple
    tools, which may indicate higher-risk vulnerabilities.
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
    
    @property
    def name(self) -> str:
        return "security_correlation"
    
    @property
    def description(self) -> str:
        return "Correlates security findings from multiple plugins to identify high-risk issues"
    
    def analyze(self, results: Dict[str, PluginResult]) -> Dict[str, Any]:
        """
        Correlate security findings from multiple plugins.
        
        Args:
            results: Dictionary mapping plugin names to PluginResult objects
            
        Returns:
            Dictionary containing correlation analysis results
        """
        # Get security plugins from configuration
        security_plugins = self.config.get("plugins", ["bandit", "semgrep", "safety"])
        
        # Filter results to include only security plugins
        security_results = {k: v for k, v in results.items() if k in security_plugins}
        
        # Find patterns across tools
        correlated_findings = []
        
        # Example: If the same file has security issues from multiple tools
        file_issues = defaultdict(list)
        for tool, result in security_results.items():
            for issue in result.issues:
                file_issues[issue.get("file", "")].append({
                    "tool": tool,
                    "issue": issue
                })
        
        # Files with issues from multiple tools are higher risk
        threshold = self.config.get("threshold", 2)
        for file, issues in file_issues.items():
            tools = set(i["tool"] for i in issues)
            if len(tools) >= threshold:
                correlated_findings.append({
                    "file": file,
                    "risk_level": "high",
                    "tools": list(tools),
                    "issue_count": len(issues),
                    "correlated_issues": issues,
                    "recommendation": self._generate_recommendation(issues)
                })
        
        return {
            "name": self.name,
            "description": self.description,
            "correlated_findings": correlated_findings,
            "summary": f"Found {len(correlated_findings)} high-risk files with security issues from multiple tools"
        }
    
    def _generate_recommendation(self, issues: List[Dict[str, Any]]) -> str:
        """
        Generate a recommendation based on correlated issues.
        
        Args:
            issues: List of correlated issues
            
        Returns:
            Recommendation string
        """
        # Count issue types
        issue_types = defaultdict(int)
        for issue in issues:
            issue_type = issue["issue"].get("test_id", "unknown")
            issue_types[issue_type] += 1
        
        # Generate recommendation based on most common issue types
        common_issues = sorted(issue_types.items(), key=lambda x: x[1], reverse=True)[:3]
        
        if common_issues:
            recommendation = "Prioritize fixing the following security issues:\n"
            for issue_type, count in common_issues:
                recommendation += f"- {issue_type} ({count} occurrences)\n"
            return recommendation
        
        return "Review all security issues in this file as it was flagged by multiple security tools."

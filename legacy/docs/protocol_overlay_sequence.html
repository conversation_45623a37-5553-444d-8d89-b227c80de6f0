
    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <title>Protocol Overlay Sequence Diagram</title>
        <script type="module">
          import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
          mermaid.initialize({ startOnLoad: true });
        </script>
    </head>
    <body>
        <h2>Protocol Overlay Sequence Diagram</h2>
        <pre class="mermaid">
sequenceDiagram
    ComparativeAnalyzer->>SkipController: extract_file_scores
    ComparativeAnalyzer->>SkipController: build_directory_tree
    ComparativeAnalyzer->>SkipController: format_directory_tree
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: find_max_scores
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: load_pat_json
    ComparativeAnalyzer->>SkipController: calculate_priority_score
    ComparativeAnalyzer->>SkipController: rank_files
    ComparativeAnalyzer->>SkipController: generate_prompt
    ComparativeAnalyzer->>SkipController: extract_high_priority_files
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: load_pat_json
    ComparativeAnalyzer->>SkipController: extract_code_entities
    ComparativeAnalyzer->>SkipController: get_entity_complexity
    ComparativeAnalyzer->>SkipController: extract_function_content
    ComparativeAnalyzer->>SkipController: extract_class_content
    ComparativeAnalyzer->>SkipController: extract_methods_from_class
    ComparativeAnalyzer->>SkipController: calculate_similarity_matrix
    ComparativeAnalyzer->>SkipController: group_similar_entities
    ComparativeAnalyzer->>SkipController: generate_similarity_report
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: __post_init__
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: _get_config_path
    ComparativeAnalyzer->>SkipController: _print_banner
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: _fix_syntax_errors
    ComparativeAnalyzer->>SkipController: _run_standard_analysis
    ComparativeAnalyzer->>SkipController: _run_parallel_analysis
    ComparativeAnalyzer->>SkipController: _chunk_files
    ComparativeAnalyzer->>SkipController: _analyze_chunk
    ComparativeAnalyzer->>SkipController: _merge_results
    ComparativeAnalyzer->>SkipController: _generate_enhanced_structure
    ComparativeAnalyzer->>SkipController: _generate_issue_prompts
    ComparativeAnalyzer->>SkipController: _generate_similarity_report
    ComparativeAnalyzer->>SkipController: _run_command
    ComparativeAnalyzer->>SkipController: _print_summary
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: run_pat
    ComparativeAnalyzer->>SkipController: extract_high_priority_files
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: check_graphviz_installed
    ComparativeAnalyzer->>SkipController: install_graphviz_package
    ComparativeAnalyzer->>SkipController: provide_installation_instructions
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: find_python_files
    ComparativeAnalyzer->>SkipController: fix_escape_sequences
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: load_pat_json
    ComparativeAnalyzer->>SkipController: extract_issues
    ComparativeAnalyzer->>SkipController: generate_issue_prompt
    ComparativeAnalyzer->>SkipController: generate_issue_prompts
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: find_python_files
    ComparativeAnalyzer->>SkipController: check_syntax
    ComparativeAnalyzer->>SkipController: fix_invalid_escape_sequences
    ComparativeAnalyzer->>SkipController: fix_line_continuation
    ComparativeAnalyzer->>SkipController: fix_indentation
    ComparativeAnalyzer->>SkipController: fix_missing_parentheses
    ComparativeAnalyzer->>SkipController: fix_unclosed_strings
    ComparativeAnalyzer->>SkipController: fix_syntax_errors
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: fix_escapes
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: find_venv
    ComparativeAnalyzer->>SkipController: is_venv_active
    ComparativeAnalyzer->>SkipController: run_in_venv
    ComparativeAnalyzer->>SkipController: setup_venv
    ComparativeAnalyzer->>SkipController: main_cli
    ComparativeAnalyzer->>SkipController: create_comprehensive_config
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: run_pat
    ComparativeAnalyzer->>SkipController: extract_high_priority_files
    ComparativeAnalyzer->>SkipController: print_summary
    ComparativeAnalyzer->>SkipController: explain_parameters
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: spin
    ComparativeAnalyzer->>SkipController: start
    ComparativeAnalyzer->>SkipController: stop
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: fix_imports_in_file
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: fix_file
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: find_python_files
    ComparativeAnalyzer->>SkipController: chunk_files
    ComparativeAnalyzer->>SkipController: analyze_chunk
    ComparativeAnalyzer->>SkipController: merge_results
    ComparativeAnalyzer->>SkipController: run_parallel_analysis
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: run_command
    ComparativeAnalyzer->>SkipController: create_virtualenv
    ComparativeAnalyzer->>SkipController: install_packages
    ComparativeAnalyzer->>SkipController: create_output_directory
    ComparativeAnalyzer->>SkipController: make_scripts_executable
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: human_readable_size
    ComparativeAnalyzer->>SkipController: count_lines
    ComparativeAnalyzer->>SkipController: is_excluded
    ComparativeAnalyzer->>SkipController: analyze_docs
    ComparativeAnalyzer->>SkipController: print_results
    ComparativeAnalyzer->>SkipController: run_pat_analysis
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: fix_file
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: extract_pyright_issues
    ComparativeAnalyzer->>SkipController: extract_ruff_issues
    ComparativeAnalyzer->>SkipController: format_pyright_issues
    ComparativeAnalyzer->>SkipController: format_ruff_issues
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: run_command
    ComparativeAnalyzer->>SkipController: fix_syntax_errors
    ComparativeAnalyzer->>SkipController: run_pat_with_all_tools
    ComparativeAnalyzer->>SkipController: generate_issue_prompts
    ComparativeAnalyzer->>SkipController: extract_high_priority_files
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: run_comprehensive_analysis
    ComparativeAnalyzer->>SkipController: extract_high_priority_files
    ComparativeAnalyzer->>SkipController: generate_issue_prompts
    ComparativeAnalyzer->>SkipController: print_summary
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: load_pat_json
    ComparativeAnalyzer->>SkipController: extract_code_entities
    ComparativeAnalyzer->>SkipController: get_entity_complexity
    ComparativeAnalyzer->>SkipController: extract_function_content
    ComparativeAnalyzer->>SkipController: extract_class_content
    ComparativeAnalyzer->>SkipController: extract_methods_from_class
    ComparativeAnalyzer->>SkipController: calculate_similarity_matrix
    ComparativeAnalyzer->>SkipController: group_similar_entities
    ComparativeAnalyzer->>SkipController: generate_similarity_report
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: __post_init__
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: _extract_content
    ComparativeAnalyzer->>SkipController: _tag_roles
    ComparativeAnalyzer->>SkipController: generate
    ComparativeAnalyzer->>SkipController: is_excluded
    ComparativeAnalyzer->>SkipController: find_python_files
    ComparativeAnalyzer->>SkipController: check_syntax
    ComparativeAnalyzer->>SkipController: update_patignore
    ComparativeAnalyzer->>SkipController: run_pre_analysis
    ComparativeAnalyzer->>SkipController: test_hypothesis_stage_basic
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: analyze
    ComparativeAnalyzer->>SkipController: _analyze_file_complexity
    ComparativeAnalyzer->>SkipController: _extract_definitions
    ComparativeAnalyzer->>SkipController: _extract_definitions_regex
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: get_progress_tracker
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: __del__
    ComparativeAnalyzer->>SkipController: force_reset
    ComparativeAnalyzer->>SkipController: start_global
    ComparativeAnalyzer->>SkipController: increment_global
    ComparativeAnalyzer->>SkipController: complete_global
    ComparativeAnalyzer->>SkipController: start_phase
    ComparativeAnalyzer->>SkipController: increment
    ComparativeAnalyzer->>SkipController: complete_phase
    ComparativeAnalyzer->>SkipController: update_phase
    ComparativeAnalyzer->>SkipController: set_status
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: _get_spinner
    ComparativeAnalyzer->>SkipController: start_global
    ComparativeAnalyzer->>SkipController: increment_global
    ComparativeAnalyzer->>SkipController: complete_global
    ComparativeAnalyzer->>SkipController: start_phase
    ComparativeAnalyzer->>SkipController: _format_time
    ComparativeAnalyzer->>SkipController: increment
    ComparativeAnalyzer->>SkipController: complete_phase
    ComparativeAnalyzer->>SkipController: update_phase
    ComparativeAnalyzer->>SkipController: set_status
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: start_global
    ComparativeAnalyzer->>SkipController: increment_global
    ComparativeAnalyzer->>SkipController: complete_global
    ComparativeAnalyzer->>SkipController: start_phase
    ComparativeAnalyzer->>SkipController: increment
    ComparativeAnalyzer->>SkipController: complete_phase
    ComparativeAnalyzer->>SkipController: update_phase
    ComparativeAnalyzer->>SkipController: set_status
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: find_imports_in_file
    ComparativeAnalyzer->>SkipController: find_all_imports
    ComparativeAnalyzer->>SkipController: get_installed_packages
    ComparativeAnalyzer->>SkipController: audit_missing_dependencies
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: visit_FunctionDef
    ComparativeAnalyzer->>SkipController: visit_AsyncFunctionDef
    ComparativeAnalyzer->>SkipController: visit_ClassDef
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: analyze
    ComparativeAnalyzer->>SkipController: _is_project_file
    ComparativeAnalyzer->>SkipController: _is_project_dir
    ComparativeAnalyzer->>SkipController: _analyze_file
    ComparativeAnalyzer->>SkipController: _analyze_directory
    ComparativeAnalyzer->>SkipController: _get_file_description
    ComparativeAnalyzer->>SkipController: _get_dir_description
    ComparativeAnalyzer->>SkipController: _analyze_python_file
    ComparativeAnalyzer->>SkipController: _analyze_markdown_file
    ComparativeAnalyzer->>SkipController: _get_size_category
    ComparativeAnalyzer->>SkipController: _analyze_config_file
    ComparativeAnalyzer->>SkipController: _analyze_web_file
    ComparativeAnalyzer->>SkipController: _analyze_shell_script
    ComparativeAnalyzer->>SkipController: _analyze_source_file
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: generate_reports
    ComparativeAnalyzer->>SkipController: _save_json_report
    ComparativeAnalyzer->>SkipController: _save_markdown_report
    ComparativeAnalyzer->>SkipController: _save_structure_report
    ComparativeAnalyzer->>SkipController: _write_tree_structure
    ComparativeAnalyzer->>SkipController: _save_summary_report
    ComparativeAnalyzer->>SkipController: _generate_documentation_report
    ComparativeAnalyzer->>SkipController: _format_bytes
    ComparativeAnalyzer->>SkipController: _map_to_caw_system
    ComparativeAnalyzer->>SkipController: _aggregate_diagnostics
    ComparativeAnalyzer->>SkipController: _generate_caw_recommendations
    ComparativeAnalyzer->>SkipController: load_manifest
    ComparativeAnalyzer->>SkipController: build_file_to_chunk_map
    ComparativeAnalyzer->>SkipController: _find_prompt_link
    ComparativeAnalyzer->>SkipController: _get_chunk_metadata_for_file
    ComparativeAnalyzer->>SkipController: _get_top_issues_for_file
    ComparativeAnalyzer->>SkipController: _get_diagnostics_for_file
    ComparativeAnalyzer->>SkipController: _get_actionable_recommendations_for_file
    ComparativeAnalyzer->>SkipController: save_areas_of_concern_report
    ComparativeAnalyzer->>SkipController: get_file_status
    ComparativeAnalyzer->>SkipController: write_tree
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: analyze
    ComparativeAnalyzer->>SkipController: _analyze_module_dependencies
    ComparativeAnalyzer->>SkipController: _extract_imports_safely
    ComparativeAnalyzer->>SkipController: _extract_imports
    ComparativeAnalyzer->>SkipController: _extract_imports_regex
    ComparativeAnalyzer->>SkipController: _detect_circular_dependencies
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: _find_spec_file
    ComparativeAnalyzer->>SkipController: _run_tla_plus
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: _find_graphs
    ComparativeAnalyzer->>SkipController: _compute_persistence
    ComparativeAnalyzer->>SkipController: _export_persistence_diagram
    ComparativeAnalyzer->>SkipController: export_networkx_to_pyvis_html
    ComparativeAnalyzer->>SkipController: export_effects_to_sankey_html
    ComparativeAnalyzer->>SkipController: generate_visualization_index
    ComparativeAnalyzer->>SkipController: export_protocol_to_mermaid_html
    ComparativeAnalyzer->>SkipController: export_tda_persistence_diagram
    ComparativeAnalyzer->>SkipController: update_visualization_dashboard
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: generate
    ComparativeAnalyzer->>SkipController: _generate_dependency_graph
    ComparativeAnalyzer->>SkipController: _generate_networkx_dependency_graph
    ComparativeAnalyzer->>SkipController: _generate_complexity_heatmap
    ComparativeAnalyzer->>SkipController: _check_graphviz_available
    ComparativeAnalyzer->>SkipController: _shorten_module_name
    ComparativeAnalyzer->>SkipController: export_html_dependency_graph
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: _collect_content
    ComparativeAnalyzer->>SkipController: _file_needs_attention
    ComparativeAnalyzer->>SkipController: _identify_file_issues
    ComparativeAnalyzer->>SkipController: _chunk_content
    ComparativeAnalyzer->>SkipController: _has_meaningful_content
    ComparativeAnalyzer->>SkipController: _build_chunk_manifest
    ComparativeAnalyzer->>SkipController: _safe_str
    ComparativeAnalyzer->>SkipController: _format_block
    ComparativeAnalyzer->>SkipController: _generate_issue_summary
    ComparativeAnalyzer->>SkipController: _generate_refactoring_instructions
    ComparativeAnalyzer->>SkipController: _summarize_diagnostics
    ComparativeAnalyzer->>SkipController: _caw_reflection
    ComparativeAnalyzer->>SkipController: _caw_next_steps
    ComparativeAnalyzer->>SkipController: _insert_verification_prompts
    ComparativeAnalyzer->>SkipController: _make_verification_prompt
    ComparativeAnalyzer->>SkipController: _summarize_overall_diagnostics
    ComparativeAnalyzer->>SkipController: _sanitize_content
    ComparativeAnalyzer->>SkipController: _write_chunks
    ComparativeAnalyzer->>SkipController: _write_manifest
    ComparativeAnalyzer->>SkipController: generate
    ComparativeAnalyzer->>SkipController: _find_non_critical_issues
    ComparativeAnalyzer->>SkipController: _generate_project_diagnostics_prompt
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: run_command
    ComparativeAnalyzer->>SkipController: create_virtualenv
    ComparativeAnalyzer->>SkipController: install_packages
    ComparativeAnalyzer->>SkipController: create_output_directory
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: read_file_content
    ComparativeAnalyzer->>SkipController: safe_execution
    ComparativeAnalyzer->>SkipController: load_exclusion_patterns
    ComparativeAnalyzer->>SkipController: is_excluded_path
    ComparativeAnalyzer->>SkipController: get_file_status
    ComparativeAnalyzer->>SkipController: ensure_dir
    ComparativeAnalyzer->>SkipController: load_patignore_patterns
    ComparativeAnalyzer->>SkipController: is_excluded_by_patignore
    ComparativeAnalyzer->>SkipController: wrapper
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: add_stage
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: _find_python_files
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: load_project_data
    ComparativeAnalyzer->>SkipController: compare
    ComparativeAnalyzer->>SkipController: _compare_structure
    ComparativeAnalyzer->>SkipController: _compare_complexity
    ComparativeAnalyzer->>SkipController: _compare_dependencies
    ComparativeAnalyzer->>SkipController: _generate_comparative_report
    ComparativeAnalyzer->>SkipController: _write_structure_comparison
    ComparativeAnalyzer->>SkipController: _write_complexity_comparison
    ComparativeAnalyzer->>SkipController: _write_dependency_comparison
    ComparativeAnalyzer->>SkipController: _write_migration_analysis
    ComparativeAnalyzer->>SkipController: _generate_comparative_visualizations
    ComparativeAnalyzer->>SkipController: _generate_complexity_chart
    ComparativeAnalyzer->>SkipController: _generate_dependency_chart
    ComparativeAnalyzer->>SkipController: parse_args
    ComparativeAnalyzer->>SkipController: make_stage_from_analyzer
    ComparativeAnalyzer->>SkipController: load_config
    ComparativeAnalyzer->>SkipController: check_graphviz_availability
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: start
    ComparativeAnalyzer->>SkipController: stop
    ComparativeAnalyzer->>SkipController: _listen
    ComparativeAnalyzer->>SkipController: should_skip
    ComparativeAnalyzer->>SkipController: clear
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: run_pipeline
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: run
    ComparativeAnalyzer->>SkipController: __init__
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: _discover_hypothesis_tests
    ComparativeAnalyzer->>SkipController: _run_hypothesis_tests
    ComparativeAnalyzer->>SkipController: _generate_property_tests
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: test_tda_overlay_stage_basic
    ComparativeAnalyzer->>SkipController: test_tda_overlay_stage_empty_graph
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: _get_file_hash
    ComparativeAnalyzer->>SkipController: _load_cache
    ComparativeAnalyzer->>SkipController: _save_cache
    ComparativeAnalyzer->>SkipController: _process_file_static
    ComparativeAnalyzer->>SkipController: _process_file
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: find_venv
    ComparativeAnalyzer->>SkipController: is_venv_active
    ComparativeAnalyzer->>SkipController: run_in_venv
    ComparativeAnalyzer->>SkipController: main
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: _get_tool_path
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: run_tool
    ComparativeAnalyzer->>SkipController: parse_output
    ComparativeAnalyzer->>SkipController: _find_python_files
        </pre>
    </body>
    </html>
    
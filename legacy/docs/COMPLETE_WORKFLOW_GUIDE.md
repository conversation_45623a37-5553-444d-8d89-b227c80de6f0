# Complete PAT Workflow Guide

## Overview

The Complete PAT Workflow is a comprehensive analysis pipeline that:

1. Runs the PAT tool with all available tools enabled
2. Extracts high-priority files based on a priority score
3. Generates issue-specific prompts for individual issues

This guide explains how to use the complete workflow to get a comprehensive analysis of your codebase and actionable recommendations for improvement.

## Prerequisites

- Python 3.6 or higher
- PAT tool installed and configured
- Project to analyze

## Usage

```bash
python PAT_project_analysis/run_pat_complete_workflow.py <project_path> [--top N] [--min-score SCORE]
```

### Arguments

- `<project_path>`: Path to the project to analyze
- `--top N`: Number of top files to extract (default: 10)
- `--min-score SCORE`: Minimum priority score to include a file (default: 5)

## Workflow Steps

### Step 1: Comprehensive PAT Analysis

The first step runs the PAT tool with all available tools enabled:

- **Complexity Analysis**: Measures cyclomatic complexity of functions and methods
- **Type Checking**: Identifies type errors using pyright
- **Linting**: Checks for code style and quality issues using ruff
- **Security Analysis**: Identifies security vulnerabilities using bandit
- **Test Coverage**: Measures test coverage using coverage
- **Dependency Analysis**: Analyzes project dependencies
- **Visualization**: Generates visualizations of the codebase structure

This step provides a comprehensive analysis of the codebase and generates detailed reports.

### Step 2: High-Priority File Extraction

The second step extracts high-priority files based on a priority score:

- **Priority Score Calculation**: Files are ranked based on a priority score calculated from various metrics
- **File Selection**: The top N files with a score above the minimum threshold are selected
- **Prompt Generation**: Focused prompts are generated for each high-priority file

The priority score is calculated based on these metrics:

- **Complexity**: Files with high cyclomatic complexity (>10)
- **File Size**: Large files (>600 lines)
- **Security Issues**: Files with security vulnerabilities detected by bandit
- **Type Errors**: Files with type errors detected by pyright
- **Lint Issues**: Files with lint issues detected by ruff
- **Circular Imports**: Files with circular import issues
- **Many Imports**: Files with excessive imports (>15)
- **Low Test Coverage**: Files with low test coverage (<70%)

### Step 3: Issue-Specific Prompt Generation

The third step generates issue-specific prompts for individual issues:

- **Issue Extraction**: Individual issues are extracted from the PAT output
- **Prompt Generation**: A focused prompt is generated for each issue
- **Issue Categorization**: Issues are categorized by type and severity

Each prompt focuses on a single issue and provides:

- **Issue Description**: A detailed description of the issue
- **Impact Analysis**: An explanation of the impact of the issue
- **Refactoring Recommendations**: Specific recommendations for addressing the issue

## Output Files

The complete workflow generates the following output files:

### PAT Analysis Reports

- **PAT_report.md**: Detailed metrics and findings from the PAT analysis
- **PAT_analysis_summary.md**: High-level overview of the analysis
- **PAT_structure.md**: Directory structure insights

### High-Priority Prompts

- **high_priority_summary.md**: Summary of high-priority files
- **priority_XX_filename.md**: Focused prompts for high-priority files

### Issue-Specific Prompts

- **issue_prompts_summary.md**: Summary of issue-specific prompts
- **severity_issue-type_XXX_filename.md**: Focused prompts for individual issues

## Using the Prompts with LLMs

The generated prompts are designed to be fed to an LLM (like Claude or GPT-4) for further analysis and refactoring recommendations:

### High-Priority Prompts

High-priority prompts focus on files that require immediate attention due to multiple issues. Use these prompts when you want to:

- Get a comprehensive refactoring plan for a file
- Address multiple issues in a single file
- Understand the overall quality of a file

### Issue-Specific Prompts

Issue-specific prompts focus on individual issues. Use these prompts when you want to:

- Address a specific issue type across multiple files
- Get detailed recommendations for a particular issue
- Understand the impact of a specific issue type

## Best Practices

1. **Start with high-priority files**: Address files with the highest priority scores first
2. **Focus on high-severity issues**: Address high-severity issues before medium and low-severity issues
3. **Implement recommendations incrementally**: Make small, focused changes based on the recommendations
4. **Verify changes**: Test your changes to ensure they don't break functionality
5. **Run the analysis regularly**: Use the complete workflow regularly to track progress

## Conclusion

The Complete PAT Workflow provides a comprehensive analysis of your codebase and actionable recommendations for improvement. By addressing the high-priority files and individual issues identified by the workflow, you can significantly improve the quality, maintainability, and security of your codebase.

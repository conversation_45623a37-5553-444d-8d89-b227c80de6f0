# Adaptive Execution Mode API Documentation

## Overview

The Adaptive Execution Mode component provides a sophisticated execution mode management system for the Vibe Check actor system. It enables the actor system to dynamically adjust its execution behavior based on workload, resource availability, and custom policies.

This document provides detailed API documentation for the Adaptive Execution Mode component, including all public methods, usage examples, and information about the interaction between the ExecutionManager and the actor system.

## Component Structure

The Adaptive Execution Mode component consists of four main subcomponents:

1. **ExecutionManager**: Core component that integrates the other subcomponents and provides a unified interface for the actor system.
2. **ExecutionModeManager**: Manages execution modes and transitions between them.
3. **WorkloadMonitor**: Monitors system workload and triggers mode switches based on workload changes.
4. **ResourceMonitor**: Monitors system resources and triggers mode switches based on resource constraints.
5. **PolicyManager**: Applies custom policies for mode switching based on various conditions.

## Enumerations

### ExecutionMode

```python
class ExecutionMode(Enum):
    """
    Execution modes for the actor system.

    These modes determine how actors process messages and interact with each other.
    """
    # Fully parallel execution with maximum concurrency
    PARALLEL = "parallel"

    # Sequential execution with minimal concurrency
    SEQUENTIAL = "sequential"

    # Hybrid mode with controlled concurrency
    HYBRID = "hybrid"

    # Adaptive mode that automatically adjusts concurrency based on workload
    ADAPTIVE = "adaptive"

    # Emergency mode for critical situations
    EMERGENCY = "emergency"
```

### ModeTransitionReason

```python
class ModeTransitionReason(Enum):
    """
    Reasons for execution mode transitions.

    These reasons provide context for why the execution mode was changed.
    """
    # Manual transition requested by user or code
    MANUAL = "manual"

    # Transition due to system stability issues
    STABILITY = "stability"

    # Transition due to workload changes
    WORKLOAD = "workload"

    # Transition due to resource constraints
    RESOURCE = "resource"

    # Transition due to policy rules
    POLICY = "policy"

    # Transition due to system initialization
    INITIALIZATION = "initialization"

    # Transition due to system shutdown
    SHUTDOWN = "shutdown"

    # Transition due to error recovery
    ERROR_RECOVERY = "error_recovery"

    # Transition due to scheduled change
    SCHEDULED = "scheduled"

    # Transition due to external event
    EXTERNAL_EVENT = "external_event"
```

## ExecutionManager API

The `ExecutionManager` is the main entry point for the Adaptive Execution Mode component. It integrates the other subcomponents and provides a unified interface for the actor system.

### Initialization

```python
def __init__(self, config: Dict[str, Any]):
    """
    Initialize the execution manager.

    Args:
        config: Configuration dictionary
    """
```

### Starting and Stopping

```python
async def start(self) -> None:
    """
    Start the execution manager and its subcomponents.
    """

async def stop(self) -> None:
    """
    Stop the execution manager and its subcomponents.
    """
```

### Execution Mode Management

```python
def get_execution_mode(self) -> ExecutionMode:
    """
    Get the current execution mode.

    Returns:
        Current execution mode
    """

async def set_execution_mode(self, mode: Union[str, ExecutionMode],
                           reason: Union[str, ModeTransitionReason] = ModeTransitionReason.MANUAL,
                           context: Optional[Dict[str, Any]] = None) -> bool:
    """
    Set the execution mode.

    Args:
        mode: Execution mode to set
        reason: Reason for the mode change
        context: Optional context for the mode change

    Returns:
        True if the mode was changed, False otherwise
    """
```

### Mode Properties

```python
def get_mode_property(self, property_name: str) -> Any:
    """
    Get a property of the current execution mode.

    Args:
        property_name: Name of the property to get

    Returns:
        Property value, or None if not found
    """
```

### Message Processing

```python
def should_process_message(self, actor_id: str, priority: int = 0) -> bool:
    """
    Determine if a message should be processed based on the current execution mode.

    Args:
        actor_id: ID of the actor
        priority: Priority of the message

    Returns:
        True if the message should be processed, False otherwise
    """

def get_batch_size(self, actor_id: Optional[str] = None) -> int:
    """
    Get the batch size for an actor based on the current execution mode.

    Args:
        actor_id: ID of the actor, or None for default batch size

    Returns:
        Batch size for the actor
    """
```

### Resource Management

```python
def get_resource_allocation(self, actor_id: Optional[str] = None, resource_type: str = "cpu") -> float:
    """
    Get the resource allocation for an actor based on the current execution mode.

    Args:
        actor_id: ID of the actor, or None for default allocation
        resource_type: Type of resource (default: "cpu")

    Returns:
        Resource allocation for the actor (0.0 to 1.0)
    """
```

### Transition Handlers

```python
def register_transition_handler(self, source_mode: Optional[ExecutionMode],
                              target_mode: ExecutionMode,
                              handler: Callable[[Any], None]) -> None:
    """
    Register a handler for mode transitions.

    Args:
        source_mode: Source execution mode (None for any source mode)
        target_mode: Target execution mode
        handler: Handler function to call during the transition
    """
```

### Status Information

```python
def get_status(self) -> Dict[str, Any]:
    """
    Get the status of the execution manager.

    Returns:
        Dictionary with status information
    """
```

## Global Access

The `ExecutionManager` is typically accessed through the global instance provided by the `get_execution_manager()` function:

```python
def get_execution_manager(config: Optional[Dict[str, Any]] = None) -> ExecutionManager:
    """
    Get the global execution manager instance.

    Args:
        config: Optional configuration dictionary

    Returns:
        Global execution manager instance
    """
```

## Usage Examples

### Basic Usage

```python
from vibe_check.core.actor_system.execution.integration import get_execution_manager
from vibe_check.core.actor_system.execution.execution_mode_manager import ExecutionMode, ModeTransitionReason

# Get the execution manager
manager = get_execution_manager()

# Get the current execution mode
current_mode = manager.get_execution_mode()
print(f"Current mode: {current_mode}")

# Set the execution mode
await manager.set_execution_mode(ExecutionMode.PARALLEL, ModeTransitionReason.MANUAL)

# Get a mode property
batch_size = manager.get_mode_property("message_batch_size")
print(f"Batch size: {batch_size}")
```

### Registering Transition Handlers

```python
def handle_transition(transition):
    print(f"Transitioning from {transition.source_mode} to {transition.target_mode}")
    print(f"Reason: {transition.reason}")
    print(f"Context: {transition.context}")

# Register a handler for transitions from PARALLEL to SEQUENTIAL
manager.register_transition_handler(
    ExecutionMode.PARALLEL,
    ExecutionMode.SEQUENTIAL,
    handle_transition
)

# Register a handler for all transitions to EMERGENCY mode
manager.register_transition_handler(
    None,  # Any source mode
    ExecutionMode.EMERGENCY,
    handle_transition
)
```

### Using the Execution Manager in an Actor

```python
class MyActor(Actor):
    async def _process_message(self, message: Message) -> None:
        # Get the execution manager
        manager = get_execution_manager()
        
        # Determine if we should process the message
        if not manager.should_process_message(self.actor_id, message.priority):
            # Queue the message for later processing
            await self.queue_message(message)
            return
            
        # Get the batch size for processing
        batch_size = manager.get_batch_size(self.actor_id)
        
        # Process the message
        # ...
```

## Interaction with Actor System

The Adaptive Execution Mode component integrates with the actor system through the `ExecutionManager`. The actor system uses the `ExecutionManager` to:

1. **Determine Message Processing**: The actor system calls `should_process_message()` to determine if a message should be processed based on the current execution mode.

2. **Get Batch Size**: The actor system calls `get_batch_size()` to determine how many messages to process in a batch.

3. **Get Resource Allocation**: The actor system calls `get_resource_allocation()` to determine how much of a resource an actor should use.

4. **Register Transition Handlers**: The actor system can register handlers for mode transitions to be notified when the execution mode changes.

5. **Set Execution Mode**: The actor system can set the execution mode based on various conditions, such as system load or error conditions.

## Configuration

The `ExecutionManager` and its subcomponents can be configured through a configuration dictionary:

```python
config = {
    "execution_mode": "adaptive",  # Initial execution mode
    "mode_properties": {
        "parallel": {
            "concurrency_limit": None,  # No limit
            "message_batch_size": 100,
            "priority_boost": {},
            "resource_allocation": {"cpu": 1.0, "memory": 1.0}
        },
        "sequential": {
            "concurrency_limit": 1,  # One at a time
            "message_batch_size": 1,
            "priority_boost": {"system": 2.0},
            "resource_allocation": {"cpu": 0.5, "memory": 0.8}
        },
        # ... other modes ...
    },
    "mode_switch_cooldown": 60.0,  # Cooldown period in seconds
    "instability_threshold": 10,  # Threshold for instability detection
    "stability_threshold": 3,  # Threshold for stability detection
    # ... other configuration options ...
}
```

## Best Practices

1. **Use Appropriate Modes**: Choose the right execution mode for your workload. Use `PARALLEL` for high throughput, `SEQUENTIAL` for stability, and `ADAPTIVE` for automatic adjustment.

2. **Configure Mode Properties**: Customize mode properties for your specific use case. Adjust batch sizes, concurrency limits, and resource allocations based on your workload.

3. **Register Transition Handlers**: Monitor mode transitions to understand system behavior. Register handlers for important transitions to be notified when the execution mode changes.

4. **Use Critical Reasons**: Use critical reasons like `ERROR_RECOVERY` and `SHUTDOWN` for important mode transitions that should bypass the cooldown period.

5. **Monitor Performance**: Track the impact of mode changes on system performance. Use the `get_status()` method to get information about the current state of the execution manager.

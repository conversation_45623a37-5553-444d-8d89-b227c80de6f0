"""
Analysis Cache Manager Plugin

This plugin provides caching functionality for analysis results,
improving performance by avoiding redundant analysis of unchanged files.
"""

import hashlib
import json
import logging
import os
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from pat_plugins.core import PluginInterface


class AnalysisCacheManager(PluginInterface):
    """
    Manages caching of analysis results to improve performance.
    
    This plugin maintains a cache of analysis results indexed by file path and content hash.
    When a file is analyzed, it checks if a valid cache entry exists and returns it instead
    of performing a full analysis if the file hasn't changed.
    """
    
    @property
    def name(self) -> str:
        return "cache_manager"
    
    @property
    def version(self) -> str:
        return "0.1.0"
    
    @property
    def description(self) -> str:
        return "Improves performance by caching analysis results"
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the cache manager with configuration.
        
        Args:
            config: Plugin configuration
        """
        self.logger = logging.getLogger("pat.plugins.cache_manager")
        
        # Cache configuration
        self.cache_dir = Path(config.get("cache_dir", os.path.expanduser("~/.pat/cache")))
        self.cache_ttl = config.get("cache_ttl", 86400)  # Default: 1 day in seconds
        self.max_cache_size = config.get("max_cache_size", 1024 * 1024 * 100)  # Default: 100 MB
        self.enabled = config.get("enabled", True)
        
        # Create cache directory if it doesn't exist
        if self.enabled and not self.cache_dir.exists():
            try:
                self.cache_dir.mkdir(parents=True, exist_ok=True)
                self.logger.info(f"Created cache directory: {self.cache_dir}")
            except Exception as e:
                self.logger.error(f"Failed to create cache directory: {e}")
                self.enabled = False
        
        # Cache metadata
        self.cache_index: Dict[str, Dict[str, Any]] = {}
        self.cache_size = 0
        
        if self.enabled:
            self._load_cache_index()
            self._clean_expired_entries()
    
    def shutdown(self) -> None:
        """Perform cleanup when shutting down."""
        if self.enabled:
            self._save_cache_index()
    
    def _load_cache_index(self) -> None:
        """Load the cache index from disk."""
        index_path = self.cache_dir / "index.json"
        if index_path.exists():
            try:
                with open(index_path, 'r') as f:
                    self.cache_index = json.load(f)
                self.logger.info(f"Loaded cache index with {len(self.cache_index)} entries")
                
                # Calculate current cache size
                self.cache_size = sum(
                    entry.get("size", 0) 
                    for entry in self.cache_index.values()
                )
                self.logger.info(f"Current cache size: {self.cache_size / 1024 / 1024:.2f} MB")
                
            except Exception as e:
                self.logger.error(f"Failed to load cache index: {e}")
                self.cache_index = {}
                self.cache_size = 0
    
    def _save_cache_index(self) -> None:
        """Save the cache index to disk."""
        index_path = self.cache_dir / "index.json"
        try:
            with open(index_path, 'w') as f:
                json.dump(self.cache_index, f)
            self.logger.info(f"Saved cache index with {len(self.cache_index)} entries")
        except Exception as e:
            self.logger.error(f"Failed to save cache index: {e}")
    
    def _clean_expired_entries(self) -> None:
        """Clean expired cache entries."""
        now = time.time()
        expired_entries = []
        
        for cache_key, entry in self.cache_index.items():
            if now - entry.get("timestamp", 0) > self.cache_ttl:
                expired_entries.append(cache_key)
        
        if expired_entries:
            self.logger.info(f"Cleaning {len(expired_entries)} expired cache entries")
            
            for cache_key in expired_entries:
                self._remove_cache_entry(cache_key)
    
    def _ensure_cache_size(self, required_size: int) -> bool:
        """
        Ensure there's enough space in the cache for a new entry.
        
        Args:
            required_size: Size of the new entry in bytes
            
        Returns:
            True if there's enough space, False otherwise
        """
        if self.cache_size + required_size <= self.max_cache_size:
            return True
        
        # Need to free up some space
        self.logger.info(f"Cache size limit exceeded, cleaning old entries")
        
        # Sort entries by access time (oldest first)
        entries = [(k, v) for k, v in self.cache_index.items()]
        entries.sort(key=lambda x: x[1].get("last_access", 0))
        
        # Remove oldest entries until we have enough space
        freed_space = 0
        for cache_key, entry in entries:
            if self.cache_size - entry.get("size", 0) + required_size <= self.max_cache_size:
                break
                
            self._remove_cache_entry(cache_key)
            freed_space += entry.get("size", 0)
            
            if self.cache_size + required_size <= self.max_cache_size:
                break
        
        self.logger.info(f"Freed {freed_space / 1024 / 1024:.2f} MB from cache")
        return self.cache_size + required_size <= self.max_cache_size
    
    def _remove_cache_entry(self, cache_key: str) -> None:
        """
        Remove a cache entry.
        
        Args:
            cache_key: The cache key to remove
        """
        if cache_key in self.cache_index:
            entry = self.cache_index[cache_key]
            cache_path = self.cache_dir / entry.get("filename", "")
            
            # Delete the cache file
            if cache_path.exists():
                try:
                    cache_path.unlink()
                except Exception as e:
                    self.logger.error(f"Failed to delete cache file {cache_path}: {e}")
            
            # Update cache size
            self.cache_size -= entry.get("size", 0)
            
            # Remove from index
            del self.cache_index[cache_key]
    
    def compute_file_hash(self, file_path: Union[str, Path]) -> str:
        """
        Compute a hash of the file contents.
        
        Args:
            file_path: Path to the file
            
        Returns:
            SHA-256 hash of the file contents
        """
        if not self.enabled:
            return ""
            
        try:
            with open(file_path, 'rb') as f:
                file_hash = hashlib.sha256(f.read()).hexdigest()
            return file_hash
        except Exception as e:
            self.logger.error(f"Failed to compute hash for {file_path}: {e}")
            return ""
    
    def get_cache_key(self, file_path: Union[str, Path], analyzer_name: str, 
                     version: str, config_hash: str = "") -> str:
        """
        Generate a cache key for a file and analyzer.
        
        Args:
            file_path: Path to the file
            analyzer_name: Name of the analyzer
            version: Version of the analyzer
            config_hash: Hash of analyzer configuration (optional)
            
        Returns:
            Cache key string
        """
        file_path_str = str(file_path)
        file_hash = self.compute_file_hash(file_path)
        
        if not file_hash:
            return ""
            
        components = [file_path_str, file_hash, analyzer_name, version]
        
        if config_hash:
            components.append(config_hash)
            
        return hashlib.md5("|".join(components).encode()).hexdigest()
    
    def get_cached_result(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """
        Get a cached analysis result.
        
        Args:
            cache_key: The cache key to look up
            
        Returns:
            The cached result, or None if not found or expired
        """
        if not self.enabled or not cache_key:
            return None
            
        if cache_key not in self.cache_index:
            return None
            
        entry = self.cache_index[cache_key]
        
        # Check if entry is expired
        if time.time() - entry.get("timestamp", 0) > self.cache_ttl:
            self._remove_cache_entry(cache_key)
            return None
        
        # Get the cache file
        cache_path = self.cache_dir / entry.get("filename", "")
        
        if not cache_path.exists():
            # Cache file is missing, remove from index
            self._remove_cache_entry(cache_key)
            return None
            
        try:
            with open(cache_path, 'r') as f:
                result = json.load(f)
                
            # Update last access time
            entry["last_access"] = time.time()
            
            return result
        except Exception as e:
            self.logger.error(f"Failed to read cache file {cache_path}: {e}")
            self._remove_cache_entry(cache_key)
            return None
    
    def store_result(self, cache_key: str, result: Dict[str, Any], 
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Store an analysis result in the cache.
        
        Args:
            cache_key: The cache key to store under
            result: The analysis result to store
            metadata: Additional metadata to store (optional)
            
        Returns:
            True if stored successfully, False otherwise
        """
        if not self.enabled or not cache_key:
            return False
            
        try:
            # Serialize result to JSON
            result_json = json.dumps(result)
            result_size = len(result_json)
            
            # Make sure we have enough space
            if not self._ensure_cache_size(result_size):
                self.logger.warning(f"Not enough space in cache for {cache_key}")
                return False
                
            # Generate a filename for the cache entry
            timestamp = int(time.time())
            filename = f"{cache_key}_{timestamp}.json"
            cache_path = self.cache_dir / filename
            
            # Write the result to the cache file
            with open(cache_path, 'w') as f:
                f.write(result_json)
                
            # Update the cache index
            self.cache_index[cache_key] = {
                "filename": filename,
                "timestamp": timestamp,
                "last_access": timestamp,
                "size": result_size,
                "metadata": metadata or {}
            }
            
            # Update cache size
            self.cache_size += result_size
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to store result in cache: {e}")
            return False 
"""
Memory System Analyzer Plugin

This plugin analyzes code for implementation of the Person Suit memory system,
including layered memory structure, hybrid persistence, and proper interaction
with the Memory Orchestration Service.
"""

import ast
import logging
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from pat_plugins.core import PluginInterface


class MemoryPattern:
    """Represents a pattern in the memory system architecture."""
    
    def __init__(self, name: str, description: str, keywords: List[str]):
        """
        Initialize a memory pattern.
        
        Args:
            name: Name of the pattern
            description: Description of the pattern
            keywords: Keywords to look for in code
        """
        self.name = name
        self.description = description
        self.keywords = keywords


class MemorySystemAnalyzerPlugin(PluginInterface):
    """
    Analyzes Python code for memory system patterns.
    
    This plugin scans Python files for evidence of the layered memory system 
    implementation, including sensory, working, and long-term memory layers,
    as well as Neo4j, ArangoDB, and PostgreSQL integration.
    """
    
    @property
    def name(self) -> str:
        return "memory_analyzer"
    
    @property
    def version(self) -> str:
        return "0.1.0"
    
    @property
    def description(self) -> str:
        return "Analyzes code for memory system patterns and database integration issues"
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the plugin with configuration.
        
        Args:
            config: Plugin configuration
        """
        self.logger = logging.getLogger("pat.plugins.memory_analyzer")
        self.patterns = self._create_patterns()
        self.config = config
        self.min_confidence = config.get("min_confidence", 0.5)
        self.ignored_dirs = config.get("ignored_dirs", ["venv", "__pycache__", ".git"])
        
        # Database adapter patterns
        self.database_patterns = {
            "neo4j": re.compile(r'neo4j|graph_db|graphdb', re.IGNORECASE),
            "arango": re.compile(r'arango|arangodb|document_db', re.IGNORECASE),
            "postgres": re.compile(r'postgres|postgresql|relational_db', re.IGNORECASE),
        }
        
        # Memory layer patterns
        self.memory_layer_patterns = {
            "sensory": re.compile(r'sensory|short_term|stm', re.IGNORECASE),
            "working": re.compile(r'working|wm|active', re.IGNORECASE),
            "long_term": re.compile(r'long_term|ltm|persistent', re.IGNORECASE),
            "orchestration": re.compile(r'orchestrat|memory_service|mos', re.IGNORECASE),
        }
        
        # Track memory structure for the project
        self.memory_structure = {
            "databases": {
                "neo4j": {"files": set(), "imports": set()},
                "arango": {"files": set(), "imports": set()},
                "postgres": {"files": set(), "imports": set()},
            },
            "layers": {
                "sensory": {"files": set()},
                "working": {"files": set()},
                "long_term": {"files": set()},
                "orchestration": {"files": set()},
            }
        }
    
    def _create_patterns(self) -> List[MemoryPattern]:
        """
        Create the list of memory patterns to detect.
        
        Returns:
            List of memory patterns
        """
        patterns = [
            MemoryPattern(
                name="sensory_memory",
                description="Sensory memory implementation",
                keywords=["sensory memory", "sensory buffer", "sensory input", "short_term_memory"]
            ),
            MemoryPattern(
                name="working_memory",
                description="Working memory implementation",
                keywords=["working memory", "active memory", "short-term store", "working_memory"]
            ),
            MemoryPattern(
                name="long_term_memory",
                description="Long-term memory implementation",
                keywords=["long term memory", "persistent memory", "long_term_memory", "ltm"]
            ),
            MemoryPattern(
                name="memory_orchestration",
                description="Memory orchestration service",
                keywords=["memory orchestration", "memory service", "orchestrator", "memory_orchestrator"]
            ),
            MemoryPattern(
                name="neo4j_integration",
                description="Neo4j graph database integration",
                keywords=["neo4j", "graph database", "graph_db", "neo4j_client"]
            ),
            MemoryPattern(
                name="arango_integration",
                description="ArangoDB integration",
                keywords=["arango", "arangodb", "document database", "arango_client"]
            ),
            MemoryPattern(
                name="postgres_integration",
                description="PostgreSQL integration",
                keywords=["postgres", "postgresql", "relational database", "postgres_client"]
            ),
            MemoryPattern(
                name="memory_encoding",
                description="Memory encoding process",
                keywords=["memory encoding", "encode memory", "encoding process", "memory_encoder"]
            ),
            MemoryPattern(
                name="memory_consolidation",
                description="Memory consolidation process",
                keywords=["memory consolidation", "consolidate memory", "consolidation process", "memory_consolidator"]
            ),
            MemoryPattern(
                name="memory_retrieval",
                description="Memory retrieval process",
                keywords=["memory retrieval", "retrieve memory", "retrieval process", "memory_retriever"]
            )
        ]
        
        return patterns
    
    def analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Analyze a single file for memory system patterns.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        if not file_path.exists() or not file_path.is_file():
            self.logger.warning(f"File not found or not a file: {file_path}")
            return {"error": "File not found or not a file"}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Create base result structure
            result = {
                "file_path": str(file_path),
                "patterns_found": [],
                "overall_score": 0.0,
                "pattern_details": {},
                "memory_layer": self._classify_memory_layer(file_path, content),
                "database_integrations": self._identify_database_integrations(content),
                "suggests_improvements": False,
                "improvement_suggestions": []
            }
            
            # Track file in memory structure
            layer = result["memory_layer"]
            if layer != "unknown" and layer in self.memory_structure["layers"]:
                self.memory_structure["layers"][layer]["files"].add(str(file_path))
            
            for db in result["database_integrations"]:
                if db in self.memory_structure["databases"]:
                    self.memory_structure["databases"][db]["files"].add(str(file_path))
            
            # Identify patterns via keyword analysis
            keyword_results = self._analyze_keywords(content)
            
            # Perform AST analysis if possible
            ast_results = {}
            try:
                tree = ast.parse(content)
                ast_results = self._analyze_ast(tree, file_path)
            except SyntaxError:
                self.logger.warning(f"Syntax error in file: {file_path}")
                result["syntax_error"] = True
            
            # Combine results from different analysis methods
            for pattern in self.patterns:
                confidence = keyword_results.get(pattern.name, 0.0)
                ast_confidence = ast_results.get(pattern.name, 0.0)
                
                combined_confidence = (confidence + ast_confidence) / 2
                
                if combined_confidence > self.min_confidence:
                    result["patterns_found"].append(pattern.name)
                    result["pattern_details"][pattern.name] = {
                        "confidence": combined_confidence,
                        "description": pattern.description,
                        "matches": keyword_results.get(f"{pattern.name}_matches", [])
                    }
            
            # Calculate overall score
            if result["patterns_found"]:
                result["overall_score"] = sum(
                    result["pattern_details"][p]["confidence"] for p in result["patterns_found"]
                ) / len(self.patterns)
                
            # Analyze architecture issues
            result["architecture_issues"] = self._check_architecture_issues(result, content)
                
            # Generate improvement suggestions
            result.update(self._generate_suggestions(result))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing file {file_path}: {str(e)}")
            return {"error": str(e)}
    
    def _classify_memory_layer(self, file_path: Path, content: str) -> str:
        """
        Classify a file as part of a memory layer.
        
        Args:
            file_path: Path to the file
            content: Content of the file
            
        Returns:
            Classification as "sensory", "working", "long_term", "orchestration", or "unknown"
        """
        path_str = str(file_path)
        
        # First, check the path for layer indicators
        for layer, pattern in self.memory_layer_patterns.items():
            if pattern.search(path_str):
                return layer
        
        # Second, check content for strong indicators
        layer_indicators = {
            "sensory": ["sensory memory", "sensory buffer", "sensory processing"],
            "working": ["working memory", "active memory", "working buffer"],
            "long_term": ["long term memory", "persistent memory", "ltm"],
            "orchestration": ["memory orchestration", "memory service", "orchestrator"]
        }
        
        # Count matches for each layer
        layer_counts = {}
        for layer, indicators in layer_indicators.items():
            layer_counts[layer] = sum(content.count(indicator) for indicator in indicators)
        
        # Find the layer with the most indicators
        max_count = max(layer_counts.values())
        if max_count > 0:
            for layer, count in layer_counts.items():
                if count == max_count:
                    return layer
        
        # Default to unknown if no strong indicators
        return "unknown"
    
    def _identify_database_integrations(self, content: str) -> List[str]:
        """
        Identify database integrations in the file.
        
        Args:
            content: Content of the file
            
        Returns:
            List of database types found ("neo4j", "arango", "postgres")
        """
        integrations = []
        
        for db, pattern in self.database_patterns.items():
            if pattern.search(content):
                integrations.append(db)
        
        return integrations
    
    def _analyze_keywords(self, content: str) -> Dict[str, Any]:
        """
        Analyze file content for memory system keywords.
        
        Args:
            content: The file content to analyze
            
        Returns:
            Dictionary of pattern names and confidence scores
        """
        results = {}
        content_lower = content.lower()
        
        for pattern in self.patterns:
            matches = []
            match_count = 0
            
            for keyword in pattern.keywords:
                keyword_lower = keyword.lower()
                count = content_lower.count(keyword_lower)
                
                if count > 0:
                    matches.append((keyword, count))
                    match_count += count
            
            # Calculate confidence based on matches
            if matches:
                # More keywords matched = higher confidence
                unique_keyword_ratio = len(matches) / len(pattern.keywords)
                
                # More matches overall = higher confidence, but with diminishing returns
                import math
                match_score = min(1.0, math.log(match_count + 1) / 4)
                
                confidence = (unique_keyword_ratio * 0.7) + (match_score * 0.3)
                
                results[pattern.name] = confidence
                results[f"{pattern.name}_matches"] = matches
            else:
                results[pattern.name] = 0.0
                results[f"{pattern.name}_matches"] = []
        
        return results
    
    def _analyze_ast(self, tree: ast.AST, file_path: Path) -> Dict[str, float]:
        """
        Analyze AST for memory system patterns.
        
        Args:
            tree: AST of the file
            file_path: Path to the file
            
        Returns:
            Dictionary of pattern names and confidence scores
        """
        results = {}
        
        # Track imported modules for later analysis
        imported_modules = set()
        
        class MemoryVisitor(ast.NodeVisitor):
            def __init__(self):
                self.sensory_indicators = 0
                self.working_indicators = 0
                self.long_term_indicators = 0
                self.orchestration_indicators = 0
                self.neo4j_indicators = 0
                self.arango_indicators = 0
                self.postgres_indicators = 0
                self.encoding_indicators = 0
                self.consolidation_indicators = 0
                self.retrieval_indicators = 0
                
                # Track classes and methods
                self.classes = []
                self.methods = []
                self.imports = []
                self.db_clients = []
                
            def visit_Import(self, node):
                for name in node.names:
                    self.imports.append(name.name)
                    imported_modules.add(name.name)
                    
                    # Check database imports
                    if "neo4j" in name.name.lower():
                        self.neo4j_indicators += 1
                    elif "arango" in name.name.lower():
                        self.arango_indicators += 1
                    elif "postgres" in name.name.lower() or "psycopg" in name.name.lower():
                        self.postgres_indicators += 1
                        
                self.generic_visit(node)
                
            def visit_ImportFrom(self, node):
                if node.module:
                    module_path = node.module
                    for name in node.names:
                        import_name = f"{module_path}.{name.name}"
                        self.imports.append(import_name)
                        imported_modules.add(import_name)
                        
                        # Check database imports in module path
                        if "neo4j" in module_path.lower():
                            self.neo4j_indicators += 1
                        elif "arango" in module_path.lower():
                            self.arango_indicators += 1
                        elif "postgres" in module_path.lower() or "psycopg" in module_path.lower():
                            self.postgres_indicators += 1
                            
                self.generic_visit(node)
            
            def visit_ClassDef(self, node):
                self.classes.append(node)
                
                # Check class name for indicators
                class_name = node.name.lower()
                
                # Memory layer indicators
                if "sensory" in class_name or "stm" in class_name:
                    self.sensory_indicators += 1
                elif "working" in class_name or "wm" in class_name:
                    self.working_indicators += 1
                elif "longterm" in class_name or "long_term" in class_name or "ltm" in class_name:
                    self.long_term_indicators += 1
                elif "orchestration" in class_name or "orchestrator" in class_name or "memoryservice" in class_name:
                    self.orchestration_indicators += 1
                
                # Database indicators
                if "neo4j" in class_name or "graphdb" in class_name:
                    self.neo4j_indicators += 1
                    self.db_clients.append(node.name)
                elif "arango" in class_name or "documentdb" in class_name:
                    self.arango_indicators += 1
                    self.db_clients.append(node.name)
                elif "postgres" in class_name or "relationaldb" in class_name or "sql" in class_name:
                    self.postgres_indicators += 1
                    self.db_clients.append(node.name)
                
                # Memory process indicators
                if "encoder" in class_name or "encoding" in class_name:
                    self.encoding_indicators += 1
                elif "consolidation" in class_name or "consolidator" in class_name:
                    self.consolidation_indicators += 1
                elif "retriever" in class_name or "retrieval" in class_name:
                    self.retrieval_indicators += 1
                
                self.generic_visit(node)
            
            def visit_FunctionDef(self, node):
                self.methods.append(node)
                
                # Check method name for indicators
                method_name = node.name.lower()
                
                # Memory layer indicators
                if "sensory" in method_name or "process_input" in method_name:
                    self.sensory_indicators += 1
                elif "working" in method_name or "active" in method_name:
                    self.working_indicators += 1
                elif "long_term" in method_name or "persistent" in method_name:
                    self.long_term_indicators += 1
                elif "orchestrate" in method_name or "coordinate" in method_name:
                    self.orchestration_indicators += 1
                
                # Memory process indicators
                if "encode" in method_name or "encoding" in method_name:
                    self.encoding_indicators += 1
                elif "consolidate" in method_name or "consolidation" in method_name:
                    self.consolidation_indicators += 1
                elif "retrieve" in method_name or "retrieval" in method_name:
                    self.retrieval_indicators += 1
                
                self.generic_visit(node)
        
        visitor = MemoryVisitor()
        visitor.visit(tree)
        
        # Calculate confidence scores based on visitor findings
        
        # Memory layer detection
        if visitor.sensory_indicators > 0:
            results["sensory_memory"] = min(1.0, visitor.sensory_indicators / 3)
        
        if visitor.working_indicators > 0:
            results["working_memory"] = min(1.0, visitor.working_indicators / 3)
        
        if visitor.long_term_indicators > 0:
            results["long_term_memory"] = min(1.0, visitor.long_term_indicators / 3)
        
        if visitor.orchestration_indicators > 0:
            results["memory_orchestration"] = min(1.0, visitor.orchestration_indicators / 3)
        
        # Database integration detection
        if visitor.neo4j_indicators > 0:
            results["neo4j_integration"] = min(1.0, visitor.neo4j_indicators / 2)
        
        if visitor.arango_indicators > 0:
            results["arango_integration"] = min(1.0, visitor.arango_indicators / 2)
        
        if visitor.postgres_indicators > 0:
            results["postgres_integration"] = min(1.0, visitor.postgres_indicators / 2)
        
        # Memory process detection
        if visitor.encoding_indicators > 0:
            results["memory_encoding"] = min(1.0, visitor.encoding_indicators / 2)
        
        if visitor.consolidation_indicators > 0:
            results["memory_consolidation"] = min(1.0, visitor.consolidation_indicators / 2)
        
        if visitor.retrieval_indicators > 0:
            results["memory_retrieval"] = min(1.0, visitor.retrieval_indicators / 2)
        
        return results
    
    def _check_architecture_issues(self, result: Dict[str, Any], content: str) -> List[Dict[str, str]]:
        """
        Check for architectural issues in the memory system implementation.
        
        Args:
            result: Analysis result so far
            content: File content
            
        Returns:
            List of architecture issues found
        """
        issues = []
        
        # Check for direct database access bypassing Memory Orchestration Service
        memory_layer = result["memory_layer"]
        db_integrations = result["database_integrations"]
        
        # Layers that should not directly access databases
        if memory_layer in ["sensory", "working"] and db_integrations:
            # Check if orchestration is also imported
            if not "memory_orchestration" in result["patterns_found"]:
                issues.append({
                    "issue_type": "direct_db_access",
                    "description": f"{memory_layer.capitalize()} memory layer directly accessing {', '.join(db_integrations)} without going through Memory Orchestration Service."
                })
        
        # Check for mixing of database responsibilities
        neo4j_usage = "neo4j_integration" in result["patterns_found"]
        arango_usage = "arango_integration" in result["patterns_found"]
        postgres_usage = "postgres_integration" in result["patterns_found"]
        
        db_count = sum([neo4j_usage, arango_usage, postgres_usage])
        
        if db_count > 1:
            used_dbs = []
            if neo4j_usage: used_dbs.append("Neo4j")
            if arango_usage: used_dbs.append("ArangoDB")
            if postgres_usage: used_dbs.append("PostgreSQL")
            
            issues.append({
                "issue_type": "mixed_db_responsibilities",
                "description": f"File mixes responsibilities for multiple databases: {', '.join(used_dbs)}. Consider splitting responsibilities."
            })
        
        # Check for lack of error handling for database operations
        if db_integrations:
            # Simple check for try-except blocks when doing database operations
            if not re.search(r'try\s*:', content) or not re.search(r'except\s+[A-Za-z_][A-Za-z0-9_]*(?:\.[A-Za-z_][A-Za-z0-9_]*)*\s*:', content):
                issues.append({
                    "issue_type": "missing_db_error_handling",
                    "description": f"Database operations without proper error handling detected. Add try-except blocks."
                })
                
        # Check for connection pooling or resource management
        if db_integrations and not re.search(r'with\s+[A-Za-z_][A-Za-z0-9_]*(?:\.[A-Za-z_][A-Za-z0-9_]*)*\s*(?:\(|:)', content):
            issues.append({
                "issue_type": "missing_connection_management",
                "description": f"Database operations without proper connection management detected. Use context managers for database connections."
            })
        
        return issues
    
    def _generate_suggestions(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate improvement suggestions based on analysis.
        
        Args:
            result: Analysis result
            
        Returns:
            Dictionary with improvement suggestions
        """
        suggestions = []
        
        # Add suggestions based on architecture issues
        for issue in result.get("architecture_issues", []):
            suggestions.append({
                "issue": issue["issue_type"],
                "suggestion": issue["description"]
            })
        
        # Check memory layer implementation
        memory_layer = result["memory_layer"]
        
        # Check for appropriate patterns based on memory layer
        if memory_layer == "sensory" and not "sensory_memory" in result["patterns_found"]:
            suggestions.append({
                "issue": "missing_sensory_implementation",
                "suggestion": "This file is in the sensory memory area but lacks sensory memory pattern implementation."
            })
        elif memory_layer == "working" and not "working_memory" in result["patterns_found"]:
            suggestions.append({
                "issue": "missing_working_implementation",
                "suggestion": "This file is in the working memory area but lacks working memory pattern implementation."
            })
        elif memory_layer == "long_term" and not "long_term_memory" in result["patterns_found"]:
            suggestions.append({
                "issue": "missing_longterm_implementation",
                "suggestion": "This file is in the long-term memory area but lacks long-term memory pattern implementation."
            })
        elif memory_layer == "orchestration" and not "memory_orchestration" in result["patterns_found"]:
            suggestions.append({
                "issue": "missing_orchestration_implementation",
                "suggestion": "This file is in the memory orchestration area but lacks orchestration pattern implementation."
            })
        
        # Database usage suggestions
        db_integrations = result["database_integrations"]
        
        # Neo4j should be used for graph relationships
        if "neo4j_integration" in result["patterns_found"] and not re.search(r'relationship|graph|connect|node', result["file_path"], re.IGNORECASE):
            suggestions.append({
                "issue": "neo4j_usage",
                "suggestion": "Neo4j should primarily be used for graph relationships and connections. Verify this file uses Neo4j appropriately."
            })
        
        # ArangoDB should be used for flexible document storage
        if "arango_integration" in result["patterns_found"] and not re.search(r'document|flexible|schema|collection', result["file_path"], re.IGNORECASE):
            suggestions.append({
                "issue": "arango_usage",
                "suggestion": "ArangoDB should primarily be used for flexible document storage. Verify this file uses ArangoDB appropriately."
            })
        
        # PostgreSQL should be used for structured relational data
        if "postgres_integration" in result["patterns_found"] and not re.search(r'relational|structured|table|sql', result["file_path"], re.IGNORECASE):
            suggestions.append({
                "issue": "postgres_usage",
                "suggestion": "PostgreSQL should primarily be used for structured relational data. Verify this file uses PostgreSQL appropriately."
            })
        
        # Check for memory processes
        process_patterns = ["memory_encoding", "memory_consolidation", "memory_retrieval"]
        missing_processes = [p for p in process_patterns if p not in result["patterns_found"]]
        
        if memory_layer != "unknown" and missing_processes:
            process_names = [p.replace("memory_", "").capitalize() for p in missing_processes]
            if memory_layer == "orchestration" and len(missing_processes) == len(process_patterns):
                suggestions.append({
                    "issue": "missing_memory_processes",
                    "suggestion": f"Memory orchestration should coordinate all memory processes: {', '.join(process_names)}."
                })
        
        return {
            "suggests_improvements": len(suggestions) > 0,
            "improvement_suggestions": suggestions
        }
    
    def analyze_project(self, project_path: Path) -> Dict[str, Any]:
        """
        Analyze an entire project for memory system patterns.
        
        Args:
            project_path: Path to the project to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        if not project_path.exists() or not project_path.is_dir():
            self.logger.error(f"Project directory not found: {project_path}")
            return {"error": "Project directory not found"}
        
        results = {
            "project_path": str(project_path),
            "files_analyzed": 0,
            "memory_layer_summary": {
                "sensory": 0,
                "working": 0,
                "long_term": 0,
                "orchestration": 0,
                "unknown": 0
            },
            "database_summary": {
                "neo4j": 0,
                "arango": 0,
                "postgres": 0
            },
            "pattern_summary": {p.name: 0 for p in self.patterns},
            "architecture_issues_summary": {
                "direct_db_access": 0,
                "mixed_db_responsibilities": 0,
                "missing_db_error_handling": 0,
                "missing_connection_management": 0
            },
            "top_memory_files": [],
            "needs_improvement": [],
            "file_results": {}
        }
        
        # Find all Python files in the project
        python_files = []
        for root, dirs, files in os.walk(project_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if d not in self.ignored_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(os.path.join(root, file)))
        
        # Analyze each file
        for file_path in python_files:
            file_result = self.analyze_file(file_path)
            
            if "error" not in file_result:
                results["files_analyzed"] += 1
                
                # Update memory layer summary
                layer = file_result["memory_layer"]
                results["memory_layer_summary"][layer] += 1
                
                # Update database summary
                for db in file_result["database_integrations"]:
                    results["database_summary"][db] += 1
                
                # Update pattern summary
                for pattern in file_result["patterns_found"]:
                    results["pattern_summary"][pattern] += 1
                
                # Update architecture issues summary
                for issue in file_result.get("architecture_issues", []):
                    issue_type = issue["issue_type"]
                    if issue_type in results["architecture_issues_summary"]:
                        results["architecture_issues_summary"][issue_type] += 1
                
                # Track top memory files
                if file_result["patterns_found"]:
                    memory_score = file_result["overall_score"]
                    if any(p.startswith("memory_") for p in file_result["patterns_found"]):
                        memory_score += 0.1  # Boost score for memory process patterns
                    
                    results["top_memory_files"].append({
                        "file_path": file_result["file_path"],
                        "score": memory_score,
                        "layer": layer,
                        "databases": file_result["database_integrations"],
                        "patterns": file_result["patterns_found"]
                    })
                
                # Track files needing improvement
                if file_result["suggests_improvements"]:
                    results["needs_improvement"].append({
                        "file_path": file_result["file_path"],
                        "layer": layer,
                        "suggestions": file_result["improvement_suggestions"]
                    })
                
                # Store individual file results
                rel_path = os.path.relpath(file_result["file_path"], str(project_path))
                results["file_results"][rel_path] = file_result
        
        # Sort top memory files by score
        results["top_memory_files"] = sorted(
            results["top_memory_files"], 
            key=lambda x: x["score"], 
            reverse=True
        )[:10]  # Top 10
        
        # Add memory structure to results
        for db_name, db_info in self.memory_structure["databases"].items():
            results[f"{db_name}_integration"] = {
                "files": len(db_info["files"]),
                "imports": len(db_info["imports"])
            }
        
        for layer_name, layer_info in self.memory_structure["layers"].items():
            results[f"{layer_name}_layer"] = {
                "files": len(layer_info["files"])
            }
        
        # Memory layer coverage analysis
        memory_layers = ["sensory", "working", "long_term", "orchestration"]
        missing_layers = [layer for layer in memory_layers if results["memory_layer_summary"][layer] == 0]
        
        if missing_layers:
            results["missing_memory_layers"] = missing_layers
        
        # Database coverage analysis
        database_types = ["neo4j", "arango", "postgres"]
        missing_databases = [db for db in database_types if results["database_summary"][db] == 0]
        
        if missing_databases:
            results["missing_databases"] = missing_databases
        
        return results 
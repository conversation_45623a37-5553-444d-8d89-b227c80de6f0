"""
Folded Mind Architecture Analyzer Plugin

This plugin analyzes code for the Folded Mind architecture patterns,
including CAM/SEM separation, parallel pathway implementation, and
integration points.
"""

import ast
import logging
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from pat_plugins.core import PluginInterface


class FoldedMindPattern:
    """Represents a pattern in the Folded Mind architecture."""
    
    def __init__(self, name: str, description: str, keywords: List[str]):
        """
        Initialize a Folded Mind pattern.
        
        Args:
            name: Name of the pattern
            description: Description of the pattern
            keywords: Keywords to look for in code
        """
        self.name = name
        self.description = description
        self.keywords = keywords


class FoldedMindAnalyzerPlugin(PluginInterface):
    """
    Analyzes Python code for Folded Mind architecture patterns.
    
    This plugin scans Python files for evidence of Folded Mind implementation,
    including CAM/SEM pathway separation, integration points, and proper
    architecture boundaries.
    """
    
    @property
    def name(self) -> str:
        return "folded_mind_analyzer"
    
    @property
    def version(self) -> str:
        return "0.1.0"
    
    @property
    def description(self) -> str:
        return "Analyzes code for Folded Mind architecture patterns and issues"
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the plugin with configuration.
        
        Args:
            config: Plugin configuration
        """
        self.logger = logging.getLogger("pat.plugins.folded_mind_analyzer")
        self.patterns = self._create_patterns()
        self.config = config
        self.min_confidence = config.get("min_confidence", 0.5)
        self.ignored_dirs = config.get("ignored_dirs", ["venv", "__pycache__", ".git"])
        
        # Folder patterns
        self.cam_folder_pattern = re.compile(r'CAM|cam|computational', re.IGNORECASE)
        self.sem_folder_pattern = re.compile(r'SEM|sem|subjective', re.IGNORECASE)
        
        # Import problem patterns
        self.cross_import_pattern = re.compile(r'import.*\.(?:CAM|SEM)\.', re.IGNORECASE)
        
        # Pathway separation
        self.folded_mind_structure = {
            "CAM": {"folders": set(), "files": set(), "imports": set()},
            "SEM": {"folders": set(), "files": set(), "imports": set()},
            "integration": {"files": set(), "imports": set()}
        }
    
    def _create_patterns(self) -> List[FoldedMindPattern]:
        """
        Create the list of Folded Mind patterns to detect.
        
        Returns:
            List of Folded Mind patterns
        """
        patterns = [
            FoldedMindPattern(
                name="cam_pathway",
                description="Computational-Analytical Pathway implementation",
                keywords=["computational", "analytical", "logical", "CAM pathway", "cam_pathway"]
            ),
            FoldedMindPattern(
                name="sem_pathway",
                description="Subjective-Experiential Pathway implementation",
                keywords=["subjective", "experiential", "emotional", "SEM pathway", "sem_pathway"]
            ),
            FoldedMindPattern(
                name="pathway_integration",
                description="Integration between CAM and SEM pathways",
                keywords=["pathway integration", "cam_sem_integration", "dual pathway", "integrate pathways"]
            ),
            FoldedMindPattern(
                name="imagination_module",
                description="Implementation of the imagination module",
                keywords=["imagination", "imaginative", "scenario", "counterfactual"]
            ),
            FoldedMindPattern(
                name="perspective_taking",
                description="Implementation of perspective-taking capabilities",
                keywords=["perspective", "viewpoint", "theory of mind", "perspective_taker"]
            )
        ]
        
        return patterns
    
    def analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Analyze a single file for Folded Mind architecture patterns.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        if not file_path.exists() or not file_path.is_file():
            self.logger.warning(f"File not found or not a file: {file_path}")
            return {"error": "File not found or not a file"}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Create base result structure
            result = {
                "file_path": str(file_path),
                "patterns_found": [],
                "overall_score": 0.0,
                "pattern_details": {},
                "pathway_classification": self._classify_pathway(file_path, content),
                "circular_imports": [],
                "cross_pathway_imports": [],
                "suggests_improvements": False,
                "improvement_suggestions": []
            }
            
            # Identify patterns via keyword analysis
            keyword_results = self._analyze_keywords(content)
            
            # Perform AST analysis if possible
            ast_results = {}
            try:
                tree = ast.parse(content)
                ast_results = self._analyze_ast(tree, file_path)
            except SyntaxError:
                self.logger.warning(f"Syntax error in file: {file_path}")
                result["syntax_error"] = True
            
            # Check for import problems
            import_problems = self._find_import_problems(content, file_path)
            result["circular_imports"] = import_problems.get("circular_imports", [])
            result["cross_pathway_imports"] = import_problems.get("cross_pathway_imports", [])
            
            # Combine results from different analysis methods
            for pattern in self.patterns:
                confidence = keyword_results.get(pattern.name, 0.0)
                ast_confidence = ast_results.get(pattern.name, 0.0)
                
                combined_confidence = (confidence + ast_confidence) / 2
                
                if combined_confidence > self.min_confidence:
                    result["patterns_found"].append(pattern.name)
                    result["pattern_details"][pattern.name] = {
                        "confidence": combined_confidence,
                        "description": pattern.description,
                        "matches": keyword_results.get(f"{pattern.name}_matches", [])
                    }
            
            # Calculate overall score
            if result["patterns_found"]:
                result["overall_score"] = sum(
                    result["pattern_details"][p]["confidence"] for p in result["patterns_found"]
                ) / len(self.patterns)
                
            # Generate improvement suggestions
            result.update(self._generate_suggestions(result))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing file {file_path}: {str(e)}")
            return {"error": str(e)}
    
    def _classify_pathway(self, file_path: Path, content: str) -> str:
        """
        Classify a file as part of CAM, SEM, or integration.
        
        Args:
            file_path: Path to the file
            content: Content of the file
            
        Returns:
            Classification as "CAM", "SEM", or "integration"
        """
        path_str = str(file_path)
        
        # First, check the path for CAM/SEM indicators
        if "CAM" in path_str or "/cam/" in path_str.lower():
            return "CAM"
        elif "SEM" in path_str or "/sem/" in path_str.lower():
            return "SEM"
        
        # Second, check content for strong indicators
        cam_indicators = ["CAM pathway", "computational pathway", "analytical pathway"]
        sem_indicators = ["SEM pathway", "subjective pathway", "experiential pathway"]
        
        # Simple counting approach
        cam_count = sum(content.count(indicator) for indicator in cam_indicators)
        sem_count = sum(content.count(indicator) for indicator in sem_indicators)
        
        if cam_count > 0 and sem_count == 0:
            return "CAM"
        elif sem_count > 0 and cam_count == 0:
            return "SEM"
        elif cam_count > 0 and sem_count > 0:
            return "integration"
            
        # If path doesn't clearly indicate, look at imports
        cam_imports = re.findall(r'from.*\.CAM import|import.*\.CAM\.', content, re.IGNORECASE)
        sem_imports = re.findall(r'from.*\.SEM import|import.*\.SEM\.', content, re.IGNORECASE)
        
        if cam_imports and not sem_imports:
            return "CAM"
        elif sem_imports and not cam_imports:
            return "SEM"
        elif cam_imports and sem_imports:
            return "integration"
        
        # Default to "unknown" if no strong indicators
        return "unknown"
    
    def _analyze_keywords(self, content: str) -> Dict[str, Any]:
        """
        Analyze file content for Folded Mind architecture keywords.
        
        Args:
            content: The file content to analyze
            
        Returns:
            Dictionary of pattern names and confidence scores
        """
        results = {}
        content_lower = content.lower()
        
        for pattern in self.patterns:
            matches = []
            match_count = 0
            
            for keyword in pattern.keywords:
                keyword_lower = keyword.lower()
                count = content_lower.count(keyword_lower)
                
                if count > 0:
                    matches.append((keyword, count))
                    match_count += count
            
            # Calculate confidence based on matches
            if matches:
                # More keywords matched = higher confidence
                unique_keyword_ratio = len(matches) / len(pattern.keywords)
                
                # More matches overall = higher confidence, but with diminishing returns
                import math
                match_score = min(1.0, math.log(match_count + 1) / 4)
                
                confidence = (unique_keyword_ratio * 0.7) + (match_score * 0.3)
                
                results[pattern.name] = confidence
                results[f"{pattern.name}_matches"] = matches
            else:
                results[pattern.name] = 0.0
                results[f"{pattern.name}_matches"] = []
        
        return results
    
    def _analyze_ast(self, tree: ast.AST, file_path: Path) -> Dict[str, float]:
        """
        Analyze AST for Folded Mind architecture patterns.
        
        Args:
            tree: AST of the file
            file_path: Path to the file
            
        Returns:
            Dictionary of pattern names and confidence scores
        """
        results = {}
        
        # Track imported modules for later analysis
        imported_modules = set()
        
        class FoldedMindVisitor(ast.NodeVisitor):
            def __init__(self):
                self.cam_indicators = 0
                self.sem_indicators = 0
                self.integration_indicators = 0
                self.imagination_indicators = 0
                self.perspective_indicators = 0
                
                # Track classes and methods for structure analysis
                self.classes = []
                self.methods = []
                self.imports = []
                
            def visit_Import(self, node):
                for name in node.names:
                    self.imports.append(name.name)
                    imported_modules.add(name.name)
                self.generic_visit(node)
                
            def visit_ImportFrom(self, node):
                if node.module:
                    module_path = node.module
                    for name in node.names:
                        self.imports.append(f"{module_path}.{name.name}")
                        imported_modules.add(f"{module_path}.{name.name}")
                self.generic_visit(node)
            
            def visit_ClassDef(self, node):
                self.classes.append(node)
                
                # Check class name for indicators
                class_name = node.name.lower()
                
                if "cam" in class_name or "computational" in class_name or "analytical" in class_name:
                    self.cam_indicators += 1
                elif "sem" in class_name or "subjective" in class_name or "experiential" in class_name:
                    self.sem_indicators += 1
                
                if "integration" in class_name or "integrator" in class_name:
                    self.integration_indicators += 1
                
                if "imagination" in class_name or "imagine" in class_name:
                    self.imagination_indicators += 1
                
                if "perspective" in class_name or "viewpoint" in class_name:
                    self.perspective_indicators += 1
                
                self.generic_visit(node)
            
            def visit_FunctionDef(self, node):
                self.methods.append(node)
                
                # Check method name for indicators
                method_name = node.name.lower()
                
                if "cam" in method_name or "computational" in method_name:
                    self.cam_indicators += 1
                elif "sem" in method_name or "subjective" in method_name:
                    self.sem_indicators += 1
                
                if "integrate" in method_name or "combine_pathways" in method_name:
                    self.integration_indicators += 1
                
                if "imagine" in method_name or "simulation" in method_name:
                    self.imagination_indicators += 1
                
                if "perspective" in method_name or "viewpoint" in method_name:
                    self.perspective_indicators += 1
                
                self.generic_visit(node)
        
        visitor = FoldedMindVisitor()
        visitor.visit(tree)
        
        # Calculate confidence scores based on visitor findings
        
        # CAM pathway detection
        if visitor.cam_indicators > 0:
            results["cam_pathway"] = min(1.0, visitor.cam_indicators / 3)
        
        # SEM pathway detection
        if visitor.sem_indicators > 0:
            results["sem_pathway"] = min(1.0, visitor.sem_indicators / 3)
        
        # Integration detection - stronger if both CAM and SEM indicators are present
        if visitor.integration_indicators > 0 or (visitor.cam_indicators > 0 and visitor.sem_indicators > 0):
            integration_score = visitor.integration_indicators
            if visitor.cam_indicators > 0 and visitor.sem_indicators > 0:
                integration_score += 1
            results["pathway_integration"] = min(1.0, integration_score / 3)
        
        # Imagination module detection
        if visitor.imagination_indicators > 0:
            results["imagination_module"] = min(1.0, visitor.imagination_indicators / 2)
        
        # Perspective taking detection
        if visitor.perspective_indicators > 0:
            results["perspective_taking"] = min(1.0, visitor.perspective_indicators / 2)
        
        return results
    
    def _find_import_problems(self, content: str, file_path: Path) -> Dict[str, List[str]]:
        """
        Find import problems like circular imports and cross-pathway imports.
        
        Args:
            content: File content
            file_path: Path to the file
            
        Returns:
            Dictionary with circular_imports and cross_pathway_imports lists
        """
        problems = {
            "circular_imports": [],
            "cross_pathway_imports": []
        }
        
        # Identify the file's pathway
        file_pathway = self._classify_pathway(file_path, content)
        if file_pathway == "unknown" or file_pathway == "integration":
            return problems  # Skip analysis for integration files or unclassified files
        
        # Extract imports
        import_lines = []
        for line in content.split('\n'):
            if line.strip().startswith(('import ', 'from ')):
                import_lines.append(line.strip())
        
        # Check for cross-pathway imports
        for import_line in import_lines:
            opposite_pathway = "SEM" if file_pathway == "CAM" else "CAM"
            if f".{opposite_pathway}." in import_line or f"from {opposite_pathway}" in import_line:
                problems["cross_pathway_imports"].append(import_line)
        
        # TODO: Circular import detection would require deeper project-wide analysis
        
        return problems
    
    def _generate_suggestions(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate improvement suggestions based on analysis.
        
        Args:
            result: Analysis result
            
        Returns:
            Dictionary with improvement suggestions
        """
        suggestions = []
        
        # Add suggestions for missing patterns
        missing_patterns = [p.name for p in self.patterns if p.name not in result["patterns_found"]]
        
        pathway = result["pathway_classification"]
        
        # Check for cross-pathway imports
        if result["cross_pathway_imports"]:
            suggestions.append({
                "issue": "cross_pathway_imports",
                "suggestion": "Avoid direct imports between CAM and SEM pathways. Use integration modules instead."
            })
        
        # Check for circular imports
        if result["circular_imports"]:
            suggestions.append({
                "issue": "circular_imports",
                "suggestion": "Fix circular imports by restructuring module dependencies."
            })
        
        # Pathway-specific suggestions
        if pathway == "CAM" and "sem_pathway" in result["patterns_found"]:
            suggestions.append({
                "issue": "pathway_blending",
                "suggestion": "This file is classified as CAM but contains SEM patterns. Consider separating concerns."
            })
        elif pathway == "SEM" and "cam_pathway" in result["patterns_found"]:
            suggestions.append({
                "issue": "pathway_blending",
                "suggestion": "This file is classified as SEM but contains CAM patterns. Consider separating concerns."
            })
        
        # Integration suggestion
        if pathway in ["CAM", "SEM"] and "pathway_integration" in result["patterns_found"]:
            suggestions.append({
                "issue": "misplaced_integration",
                "suggestion": "Integration logic should be in dedicated integration modules, not in pathway-specific code."
            })
        elif pathway == "unknown" and any(p in result["patterns_found"] for p in ["cam_pathway", "sem_pathway"]):
            suggestions.append({
                "issue": "unclear_pathway",
                "suggestion": "This file contains pathway-specific code but is not clearly organized in the correct folder structure."
            })
        
        return {
            "suggests_improvements": len(suggestions) > 0,
            "improvement_suggestions": suggestions
        }
    
    def analyze_project(self, project_path: Path) -> Dict[str, Any]:
        """
        Analyze an entire project for Folded Mind architecture patterns.
        
        Args:
            project_path: Path to the project to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        if not project_path.exists() or not project_path.is_dir():
            self.logger.error(f"Project directory not found: {project_path}")
            return {"error": "Project directory not found"}
        
        results = {
            "project_path": str(project_path),
            "files_analyzed": 0,
            "pathway_summary": {
                "CAM": 0,
                "SEM": 0,
                "integration": 0,
                "unknown": 0
            },
            "pattern_summary": {p.name: 0 for p in self.patterns},
            "problem_summary": {
                "cross_pathway_imports": 0,
                "circular_imports": 0
            },
            "top_files": [],
            "needs_improvement": [],
            "file_results": {}
        }
        
        # Find all Python files in the project
        python_files = []
        for root, dirs, files in os.walk(project_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if d not in self.ignored_dirs]
            
            # Categorize folders
            path = Path(root)
            rel_path = path.relative_to(project_path)
            folder_path_str = str(rel_path)
            
            if "SEM" in folder_path_str or self.sem_folder_pattern.search(folder_path_str):
                self.folded_mind_structure["SEM"]["folders"].add(str(rel_path))
            elif "CAM" in folder_path_str or self.cam_folder_pattern.search(folder_path_str):
                self.folded_mind_structure["CAM"]["folders"].add(str(rel_path))
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(os.path.join(root, file)))
        
        # Analyze each file
        for file_path in python_files:
            file_result = self.analyze_file(file_path)
            
            if "error" not in file_result:
                results["files_analyzed"] += 1
                
                # Update pathway summary
                pathway = file_result["pathway_classification"]
                results["pathway_summary"][pathway] += 1
                
                # Update pattern summary
                for pattern in file_result["patterns_found"]:
                    results["pattern_summary"][pattern] += 1
                
                # Update problem summary
                results["problem_summary"]["cross_pathway_imports"] += len(file_result["cross_pathway_imports"])
                results["problem_summary"]["circular_imports"] += len(file_result["circular_imports"])
                
                # Track top files
                if file_result["patterns_found"]:
                    results["top_files"].append({
                        "file_path": file_result["file_path"],
                        "score": file_result["overall_score"],
                        "pathway": pathway,
                        "patterns": file_result["patterns_found"]
                    })
                
                # Track files needing improvement
                if file_result["suggests_improvements"]:
                    results["needs_improvement"].append({
                        "file_path": file_result["file_path"],
                        "pathway": pathway,
                        "suggestions": file_result["improvement_suggestions"]
                    })
                
                # Store individual file results
                rel_path = os.path.relpath(file_result["file_path"], str(project_path))
                results["file_results"][rel_path] = file_result
        
        # Sort top files by score
        results["top_files"] = sorted(
            results["top_files"], 
            key=lambda x: x["score"], 
            reverse=True
        )[:10]  # Top 10
        
        # Add folded_mind_structure to results
        results["folded_mind_structure"] = {
            "CAM": {
                "folders": list(self.folded_mind_structure["CAM"]["folders"]),
                "files": len([f for f, r in results["file_results"].items() if r["pathway_classification"] == "CAM"])
            },
            "SEM": {
                "folders": list(self.folded_mind_structure["SEM"]["folders"]),
                "files": len([f for f, r in results["file_results"].items() if r["pathway_classification"] == "SEM"])
            },
            "integration": {
                "files": len([f for f, r in results["file_results"].items() if r["pathway_classification"] == "integration"])
            },
            "unknown": {
                "files": len([f for f, r in results["file_results"].items() if r["pathway_classification"] == "unknown"])
            }
        }
        
        return results 
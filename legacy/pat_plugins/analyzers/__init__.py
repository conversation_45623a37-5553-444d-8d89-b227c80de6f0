"""
PAT Analyzer Plugins Package

This package contains analyzer plugins for the Project Analysis Tool (PAT).
Analyzer plugins scan codebases to identify patterns, issues, and improvement opportunities.

# Note: SharedAnalysisContext is used internally by plugins and orchestration, not exported here by default.
"""

from pat_plugins.analyzers.caw_analyzer import CAWAnalyzerPlugin
from pat_plugins.analyzers.effect_system_analyzer import \
    EffectSystemAnalyzerPlugin
from pat_plugins.analyzers.folded_mind_analyzer import FoldedMindAnalyzerPlugin
from pat_plugins.analyzers.memory_analyzer import MemorySystemAnalyzerPlugin

__all__ = ["CAWAnalyzerPlugin", "FoldedMindAnalyzerPlugin", "MemorySystemAnalyzerPlugin", "EffectSystemAnalyzerPlugin"] 

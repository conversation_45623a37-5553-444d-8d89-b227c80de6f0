"""
Shared Analysis Context for PAT Plugins

This module defines a context object for sharing data (e.g., dependency graph,
plugin results) between PAT analyzer plugins during a project analysis run.

Related Files:
    - All analyzer plugins (for integration)
    - codebase_graph_analyzer.py (for graph sharing)

"""

from typing import Any, Dict, Optional


class SharedAnalysisContext:
    """
    Context object for sharing data between PAT analyzer plugins.

    Stores the codebase dependency graph, plugin results, and arbitrary key-value
    data for cross-plugin analysis and coordination.
    """
    def __init__(self) -> None:
        """Initialize an empty shared context."""
        self._graph = None
        self._plugin_results: Dict[str, Any] = {}
        self._data: Dict[str, Any] = {}

    def set_graph(self, graph: Any) -> None:
        """
        Store the codebase dependency graph.

        Args:
            graph: The dependency graph object (e.g., networkx graph or dict)
        """
        self._graph = graph

    def get_graph(self) -> Any:
        """
        Retrieve the codebase dependency graph.

        Returns:
            The dependency graph object, or None if not set
        """
        return self._graph

    def set_plugin_result(self, plugin_name: str, result: Any) -> None:
        """
        Store the result of a plugin's analysis.

        Args:
            plugin_name: Name of the plugin
            result: Analysis result to store
        """
        self._plugin_results[plugin_name] = result

    def get_plugin_result(self, plugin_name: str) -> Optional[Any]:
        """
        Retrieve the result of a plugin's analysis.

        Args:
            plugin_name: Name of the plugin

        Returns:
            The analysis result, or None if not found
        """
        return self._plugin_results.get(plugin_name)

    def set(self, key: str, value: Any) -> None:
        """
        Store arbitrary key-value data in the context.

        Args:
            key: The key under which to store the value
            value: The value to store
        """
        self._data[key] = value

    def get(self, key: str) -> Optional[Any]:
        """
        Retrieve arbitrary data from the context by key.

        Args:
            key: The key to retrieve

        Returns:
            The value, or None if not found
        """
        return self._data.get(key) 
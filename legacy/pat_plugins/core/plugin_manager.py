"""
Plugin Manager for PAT
=====================

This module provides the core plugin management system that handles discovery,
loading, and lifecycle management of PAT plugins.
"""

import importlib
import inspect
import logging
import os
import sys
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Type


class PluginInterface:
    """Base interface that all plugins must implement."""
    
    @property
    def name(self) -> str:
        """Return the name of the plugin."""
        raise NotImplementedError("Plugin must implement name property")
    
    @property
    def version(self) -> str:
        """Return the version of the plugin."""
        raise NotImplementedError("Plugin must implement version property")
    
    @property
    def description(self) -> str:
        """Return the description of the plugin."""
        raise NotImplementedError("Plugin must implement description property")
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the plugin with the given configuration."""
        raise NotImplementedError("Plugin must implement initialize method")
    
    def shutdown(self) -> None:
        """Perform cleanup when the plugin is being shut down."""
        pass


class PluginManager:
    """
    Manages the discovery, loading, and lifecycle of PAT plugins.
    
    The PluginManager scans for plugins in the specified directories,
    loads them, and provides access to them for the main application.
    """
    
    def __init__(self, plugin_dirs: Optional[List[str]] = None):
        """
        Initialize the plugin manager.
        
        Args:
            plugin_dirs: List of directories to scan for plugins. If None,
                         defaults to ['pat_plugins'] in the current directory.
        """
        self.logger = logging.getLogger("pat.plugin_manager")
        
        if plugin_dirs is None:
            # Default to the standard plugin directories
            self.plugin_dirs = [
                os.path.join(os.path.dirname(__file__), '..', '..', 'pat_plugins'),
                os.path.expanduser('~/.pat/plugins')
            ]
        else:
            self.plugin_dirs = plugin_dirs
            
        # Store loaded plugins by name
        self.plugins: Dict[str, PluginInterface] = {}
        
        # Store plugin module paths to avoid duplication
        self._loaded_modules: Set[str] = set()
        
        # Configuration for plugins
        self.plugin_config: Dict[str, Dict[str, Any]] = {}
        
        # Hook registry for inter-plugin communication
        self._hooks: Dict[str, List[Tuple[str, Callable]]] = {}
    
    def discover_plugins(self) -> List[Tuple[str, Type[PluginInterface]]]:
        """
        Discover available plugins in the plugin directories.
        
        Returns:
            A list of tuples containing plugin name and plugin class.
        """
        discovered_plugins = []
        
        for plugin_dir in self.plugin_dirs:
            if not os.path.exists(plugin_dir):
                self.logger.warning(f"Plugin directory does not exist: {plugin_dir}")
                continue
                
            self.logger.info(f"Scanning for plugins in: {plugin_dir}")
            
            # Add to Python path temporarily to import modules
            if plugin_dir not in sys.path:
                sys.path.insert(0, plugin_dir)
            
            # Walk through the directory structure
            for root, dirs, files in os.walk(plugin_dir):
                # Skip __pycache__ and similar directories
                dirs[:] = [d for d in dirs if not d.startswith('__') and not d.startswith('.')]
                
                for file in files:
                    # Look for Python modules
                    if file.endswith('.py') and not file.startswith('__'):
                        module_path = os.path.join(root, file)
                        rel_path = os.path.relpath(module_path, plugin_dir)
                        
                        # Convert path to module name (e.g., path/to/module.py -> path.to.module)
                        module_name = os.path.splitext(rel_path.replace(os.sep, '.'))[0]
                        
                        try:
                            # Try to import the module
                            if module_name in self._loaded_modules:
                                continue
                                
                            module = importlib.import_module(module_name)
                            self._loaded_modules.add(module_name)
                            
                            # Look for plugin classes in the module
                            for name, obj in inspect.getmembers(module):
                                if (inspect.isclass(obj) and 
                                    issubclass(obj, PluginInterface) and 
                                    obj is not PluginInterface):
                                    
                                    # Found a plugin class
                                    discovered_plugins.append((module_name, obj))
                                    self.logger.debug(f"Discovered plugin: {name} in {module_name}")
                        
                        except Exception as e:
                            self.logger.error(f"Error importing module {module_name}: {str(e)}")
        
        return discovered_plugins
    
    def load_plugins(self, config: Optional[Dict[str, Dict[str, Any]]] = None) -> None:
        """
        Load and initialize discovered plugins.
        
        Args:
            config: Configuration dictionary for plugins. If provided, it should be a dictionary
                   where keys are plugin names and values are plugin-specific configuration.
        """
        self.plugin_config = config or {}
        discovered_plugins = self.discover_plugins()
        
        for module_name, plugin_class in discovered_plugins:
            try:
                # Create plugin instance
                plugin = plugin_class()
                
                # Check for duplicate plugins
                if plugin.name in self.plugins:
                    self.logger.warning(f"Duplicate plugin found: {plugin.name}. Ignoring.")
                    continue
                    
                # Get plugin-specific config
                plugin_config = self.plugin_config.get(plugin.name, {})
                
                # Initialize the plugin
                plugin.initialize(plugin_config)
                
                # Store the plugin
                self.plugins[plugin.name] = plugin
                self.logger.info(f"Loaded plugin: {plugin.name} v{plugin.version}")
                
            except Exception as e:
                self.logger.error(f"Error loading plugin from {module_name}: {str(e)}")
    
    def get_plugin(self, name: str) -> Optional[PluginInterface]:
        """
        Get a plugin by name.
        
        Args:
            name: The name of the plugin to retrieve.
            
        Returns:
            The plugin instance or None if not found.
        """
        return self.plugins.get(name)
    
    def get_all_plugins(self) -> Dict[str, PluginInterface]:
        """Get all loaded plugins."""
        return self.plugins.copy()
    
    def shutdown_all(self) -> None:
        """Shutdown all plugins and clean up resources."""
        for name, plugin in self.plugins.items():
            try:
                self.logger.info(f"Shutting down plugin: {name}")
                plugin.shutdown()
            except Exception as e:
                self.logger.error(f"Error shutting down plugin {name}: {str(e)}")
        
        self.plugins.clear()
        self._loaded_modules.clear()
    
    def register_hook(self, hook_name: str, plugin_name: str, callback: Callable) -> None:
        """
        Register a plugin's hook callback.
        
        Args:
            hook_name: The name of the hook to register for
            plugin_name: The name of the plugin registering the hook
            callback: The callback function to call when the hook is triggered
        """
        if hook_name not in self._hooks:
            self._hooks[hook_name] = []
        
        self._hooks[hook_name].append((plugin_name, callback))
        self.logger.debug(f"Plugin {plugin_name} registered for hook: {hook_name}")
    
    def trigger_hook(self, hook_name: str, *args, **kwargs) -> List[Any]:
        """
        Trigger a hook and collect results from all registered callbacks.
        
        Args:
            hook_name: The name of the hook to trigger
            *args: Positional arguments to pass to callbacks
            **kwargs: Keyword arguments to pass to callbacks
            
        Returns:
            List of results from all callbacks
        """
        results = []
        
        if hook_name not in self._hooks:
            return results
        
        for plugin_name, callback in self._hooks[hook_name]:
            try:
                result = callback(*args, **kwargs)
                results.append(result)
            except Exception as e:
                self.logger.error(f"Error in hook {hook_name} for plugin {plugin_name}: {str(e)}")
        
        return results 
# PAT Project Analysis Tool Enhancement Plan

**Current State (June 2025)**

As of June 2025, the PAT codebase has implemented all core features described in this enhancement plan. The modular pipeline, tool integrations (mypy, pyright, ruff, bandit, coverage, flake8, black, isort, pydocstyle, dependency, call graph, overlays), overlay visualization, visualization index, chunked prompt/manifest, and CAW-aligned diagnostics are all present and working. Overlay stages (effect, protocol, graph) generate interactive visualizations and set the `visualization` field. Manifest and prompt chunking logic is robust, with actionable, non-redundant verification prompts. Timestamped output folders and a `latest` symlink are created for each run. Unified exclusion logic and config-driven tool enable/disable are present. 

Advanced features (TDA overlays, property-based testing, runtime/formal verification) are scaffolded but not yet fully implemented. Overlay analytics and visualization richness are planned for further enhancement.

| Feature/Stage                | Status (2025-06)         | Notes |
|------------------------------|:------------------------:|-------|
| Modular pipeline             |           ✅             | Fully modular, config-driven |
| All major tool integrations  |           ✅             | mypy, ruff, bandit, etc. present |
| Overlay stages (effect, protocol, graph) | ✅ (basic) | Visualizations generated, fields set |
| Visualization index          |           ✅             | `visualization_index.md` generated |
| Chunked prompt generation    |           ✅             | Manifest, verification, CAW guidance |
| Manifest for prompt chunks   |           ✅             | `manifest.json` with rich metadata |
| Verification prompts         |           ✅             | Actionable, diagnostics-rich |
| Metrics model (tool_results) |           ✅             | Project/FileMetrics store all outputs |
| Timestamped output folders   |           ✅             | Symlink to `latest` maintained |
| Grouped, CAW-aligned diagnostics in reports | ✅ | In reports and prompt chunks |
| Exclusion/configurability    |           ✅             | Unified logic, config.yaml |
| Protocol/effect/graph overlays |   ⏳ (Partial)          | Stubs, basic output, more to do |
| TDA overlays, property-based, runtime/formal verification | ⏳ | Stubs only, not full implementation |

---

**Last Updated:** April 20, 2025

## Purpose

This document outlines a comprehensive plan to enhance the PAT (Project Analysis Tool) for the Coversational project. The goal is to make PAT more powerful, user-friendly, and LLM-optimized, supporting both human and AI-driven codebase analysis, refactoring, and navigation.

**Related Files:**

- `PAT_analyze.py`, `PAT_analyze_all.py`, `PAT_output/`
- Project documentation: `docs/`, architectural records

**Dependencies:**

- Python 3.8+
- AST, NLP, and visualization libraries (to be specified per feature)

---

## Enhancement Goals

All core goals below are now implemented and working as of June 2025. Advanced goals (TDA overlays, property-based testing, runtime/formal verification) are scaffolded or planned.

1. **Deeper, More Flexible Analysis** ✅
2. **Human-Friendly Visualizations** ✅
3. **LLM-Optimized Output (Context Window Chunks)** ✅
4. **Verification and Rechecking Prompts** ✅
5. **Robust Exclusion and Configurability** ✅
6. **Modular, Extensible Architecture** ✅
7. **Advanced LLM/API Integration** ✅
8. **Automated Refactoring and Migration Guidance** ✅
9. **Test Quality and Coverage Analysis** ✅
10. **Security, Compliance, and Risk Analysis** ✅
11. **Incremental and Continuous Analysis** ✅
12. **User Experience and Accessibility** ✅
13. **Extensibility and Community Contributions** ✅
14. **Sample Outputs and User Stories** ✅
15. **Keyword-Centric Dependency and Connection Explorer** ✅
16. **Integration of Best-in-Class Python Analysis Tools** ✅

> **Note:** Overlay stages (protocol, effect, graph) are partially implemented (basic output and visualization, but not full analytics or advanced overlays). TDA overlays, property-based testing, and runtime/formal verification are scaffolded or planned.

---

## Step-by-Step Improvement Plan

### 1. Modularize PAT's Analysis Pipeline
- **Status:** ✅ **Completed (Q2 2025)**

### 2. Add Content Extraction and Summarization
- **Status:** ✅ **Completed (Q2 2025)**

### 3. Implement Robust Exclusion Patterns
- **Status:** ✅ **Completed (Q2 2025)**

### 4. LLM-Optimized Output: Chunked Prompt Generation
- **Status:** ✅ **Completed (Q2 2025)**

### 5. Insert Verification and Rechecking Prompts
- **Status:** ✅ **Completed (Q2 2025)**

### 6. Integrate Best-in-Class Python Analysis Tools
- **Status:** ✅ **Completed (Q2 2025)**

### 7. Expose Tool Outputs to Downstream Stages
- **Status:** ✅ **Completed (Q2 2025)**

### 7A. LLM Prompt Generation and Reporting: Grouped, CAW-Aligned Diagnostics
- **Status:** ✅ **Completed (Q2 2025)**

### 7B. Reporting: Grouped, CAW-Aligned Diagnostics and Recommendations
- **Status:** ✅ **Completed (Q2 2025)**

### 8. Overlay Stages (Protocol, Effect, Graph)
- **Status:** ⏳ **Partially Implemented (Q2 2025)**
  - Basic output and visualization present; advanced analytics and overlays planned.

### 9. TDA Overlays, Property-Based Testing, Runtime/Formal Verification
- **Status:** ⏳ **Scaffolded/Planned (Q2–Q3 2025)**

---

## Detailed Development Plan: Integration of Python Analysis Tools

To maximize the value of PAT for both human and LLM users, all static analysis, type checking, linting, security, and code quality tools will be integrated as early pipeline stages. Their outputs will be structured and made available to all downstream stages (visualization, reporting, LLM prompt generation).

### Unified Integration Roadmap

1. **Design Unified Analysis Stage Interface**
   - Define a standard interface for all tool integrations (input: file list, config; output: structured diagnostics in `FileMetrics`/`ProjectMetrics`).
   - Add config options to enable/disable each tool per run or via config file.

2. **Integrate Each Tool as a Modular Pipeline Stage**
   - For each tool:
     - Add a new pipeline stage class (e.g., `MypyStage`, `RuffStage`, etc.).
     - Implement logic to invoke the tool (subprocess or API), parse output, and store results in metrics objects.
     - Ensure robust error handling and clear logging.

3. **Tool-Specific Plans**

#### a. mypy/pyright (Type Checking & Coverage)

- **Integration:**
  - Add `MypyStage`/`PyrightStage` to pipeline after file discovery.
  - Run tool on discovered files, parse type errors and coverage.
  - Store results in `FileMetrics` (per-file errors, coverage) and `ProjectMetrics` (overall coverage).
- **Deliverables:**
  - Type error lists, type coverage stats, summary for LLM prompts.
- **Dependencies:**
  - mypy/pyright installed, config file support.
- **Testing:**
  - Unit tests for output parsing, integration tests for pipeline.

#### b. ruff/flake8/pylint (Linting, Style, Static Analysis)

- **Integration:**
  - Add `RuffStage`/`Flake8Stage`/`PylintStage` to pipeline.
  - Run tool, parse lint errors/warnings, store in metrics.
- **Deliverables:**
  - Linting diagnostics, code smell reports, summary overlays for visualization.
- **Dependencies:**
  - Tool installed, config file support.
- **Testing:**
  - Output parsing, error handling, integration with exclusion logic.

#### c. black/isort (Formatting, Import Sorting)

- **Integration:**
  - Add `BlackStage`/`IsortStage` (optional, not enforced by default).
  - Run in check mode, report formatting/import issues.
- **Deliverables:**
  - Formatting/import diagnostics, optional auto-fix suggestions.
- **Dependencies:**
  - Tool installed.
- **Testing:**
  - Output parsing, config respect.

#### d. bandit (Security Static Analysis)

- **Integration:**
  - Add `BanditStage` to pipeline.
  - Run on codebase, parse security warnings, store in metrics.
- **Deliverables:**
  - Security diagnostics, vulnerability overlays for visualization and LLM prompts.
- **Dependencies:**
  - bandit installed.
- **Testing:**
  - Output parsing, false positive handling.

#### e. coverage.py (Test Coverage)

- **Integration:**
  - Add `CoverageStage` to pipeline.
  - Run tests with coverage, parse per-file and overall stats.
- **Deliverables:**
  - Coverage reports, annotated code, summary for LLM prompts.
- **Dependencies:**
  - coverage.py installed, test suite present.
- **Testing:**
  - Output parsing, integration with test runner.

#### f. pydocstyle (Docstring Quality)

- **Integration:**
  - `PydocstyleStage` has been implemented and integrated into the pipeline.
  - Will run `pydocstyle` on the codebase, parse docstring issues, and store results in metrics.
- **Deliverables:**
  - Docstring diagnostics, summary for LLM prompts.
- **Status:** 🟢 **Complete**

#### g. pipdeptree/safety (Dependency, Security, License)

- **Integration:**
  - `DependencyStage` has been implemented and integrated into the pipeline.
  - Runs `pipdeptree` for dependency tree and `safety` for vulnerability scanning.
- **Deliverables:**
  - Dependency graph, security/license warnings, summary for LLM prompts.
- **Status:** 🟢 **Complete**

#### h. code2flow/pyan3 (Call Graph Visualization)

- **Integration:**
  - `CallGraphStage` will use both `code2flow` and `pyan3` to extract call graphs at function and module levels.
  - Outputs will be generated in both DOT and JSON formats for downstream processing.
- **Deliverables:**
  - Machine-readable call graph data (DOT, JSON) for further analysis.
  - (Optional) Static PNG/SVG for quick reference.
  - **Primary:** Interactive HTML visualization (using D3.js, Mermaid, or Cytoscape.js) to explore:
    - Hot spots (high-degree nodes)
    - Dense clusters
    - Orphaned/problematic nodes
    - Redundancies and similar logic
    - Cycles and architectural issues
    - Filtering, searching, zooming, and panning
- **Status:** ⏳ **Planned (Next)**

#### i. code2flow/pyan3 + Cytoscape.js (Interactive Call Graph Visualization)

- **Integration:**
  - `CallGraphStage` extracts call graph data (DOT/JSON) using pyan3.
  - `CallGraphVisualizerStage` now runs immediately after `CallGraphStage` in the pipeline.
  - Generates a self-contained `call_graph.html` (Cytoscape.js) for interactive exploration (zoom, pan, search, layout switching, node details).
  - The main report now links to `call_graph.html` as a key artifact.
- **Deliverables:**
  - Interactive HTML call graph visualization for human/LLM review.
  - All outputs are placed in the timestamped PAT_output directory for each run.
- **Status:** 🟢 **Complete**

4. **Expose Tool Outputs to Downstream Stages**
   - Update `FileMetrics`/`ProjectMetrics` to include all tool outputs.
   - Ensure visualization and LLM prompt stages can access and display diagnostics, overlays, and summaries.

5. **Testing and Validation**
   - Add unit/integration tests for each stage.
   - Validate outputs on sample and real codebases.

6. **Documentation and Usage Examples**
   - Update user docs to describe tool integration, config, and outputs.
   - Provide sample reports and LLM prompt outputs with diagnostics included.

### Step-by-Step Roadmap

1. Design and implement unified analysis stage interface and config system.
2. Integrate mypy/pyright as first tool, validate output flow.
3. Sequentially add ruff/flake8/pylint, bandit, coverage.py, and other tools as modular stages.
4. Update metrics models and downstream stages to consume tool outputs.
5. Add tests and documentation for each integration.
6. Release updated PAT with full tool integration and showcase outputs.

---

## Implementation Roadmap

1. **Q2 2025:**
   - Modularize analysis pipeline  ✅
   - Add robust exclusion/configuration 🟢/🟡
   - Begin content extraction and summarization  ✅
   - LLM chunked prompt generation & verification  ✅
2. **Q3 2025:**
   - Integrate mypy/pyright, ruff/flake8/pylint, bandit, coverage.py, etc. ⏳
   - Add interactive visualizations and architectural overlays
   - Insert verification/rechecking prompts  ✅
3. **Q4 2025:**
   - Refine LLM prompt optimization (tokenization, overlap, metadata)
   - Expand documentation and CI integration
   - Gather feedback, iterate, and plan next phase

---

## Progress Reporting Improvements (Planned Q2 2025)

### Motivation

- User feedback: Progress reporting for some stages (e.g., 'Running Flake8Stage: 0.0% (0/0) - 0.0s') is not informative, especially when step count is zero or unknown. This makes it hard to tell if the program is running or stuck.

### Planned Improvements

1. **Show "Working..." or "Running..." for Indeterminate Stages**
   - For stages with total_steps == 0, display a message like 'Running Flake8Stage... (elapsed: Xs)' instead of a percentage.
2. **Always Show Elapsed Time**
   - Even for indeterminate stages, show elapsed time so users can see if a stage is taking a long time.
3. **Log Subprocess Start/End**
   - For tool stages that run subprocesses, log when the subprocess starts and ends, and optionally show the command being run.
4. **Add a Spinner or Animated Indicator**
   - For long-running indeterminate stages, print a spinner or update the message periodically to reassure the user that the process is active.

### Action Items

- [ ] Update ProgressTracker to provide more informative output for indeterminate stages.
- [ ] Update ToolStage and pipeline to log subprocess start/end and show commands.
- [ ] Add a spinner or animation for long-running stages.

### Status in Roadmap

- This is the next immediate implementation step before further tool integrations (e.g., pipdeptree/safety, code2flow/pyan3).

---

### Updated Step-by-Step Roadmap (with Status)

| Step | Enhancement/Feature                                      | Status/Notes                | Prerequisites                |
|------|---------------------------------------------------------|-----------------------------|------------------------------|
| 1    | Modularize Analysis Pipeline                            | ✅ Done                     | -                            |
| 2    | Implement Robust Exclusion Patterns                     | 🟢 Structure, 🟡 Content    | 1                            |
| 3    | Add Content Extraction and Summarization                | ✅ Done                     | 1, 2                        |
| 4    | Unified Analysis Stage Interface & Config               | 🟢 In Progress              | 1, 2, 3                     |
| 5    | Integrate Static Analysis Tools (see above order)       | 🟢 Mypy, Ruff, Bandit, Coverage, Flake8, Black, Pydocstyle, Dependency: Complete | 4 |
|      |                                                         | ⏳ CallGraph: Next          |                              |
| 6    | Progress Reporting Improvements                         | 🟢 Complete                 | 5                            |
| 7    | Update Metrics Models                                   | ⏳ Planned                  | 6                            |
| 8    | Expose Tool Outputs to Downstream Stages                | ⏳ Planned                  | 7                            |
| 9    | Add/Update Visualization Stages                         | ⏳ Planned                  | 8                            |
| 10   | Implement LLM-Optimized Output (Chunked Prompt Gen.)    | ✅ Done                     | 8                            |
| 11   | Insert Verification and Rechecking Prompts              | ✅ Done                     | 10                           |
| 12   | Add Keyword-Centric Dependency/Connection Explorer      | ⏳ Planned                  | 9, 10                        |
| 13   | Implement Timestamped Output Folder for Each Run        | ✅ Done                     | 1                            |
| 14   | Implement Optional Comparison Between Last and Current Report | ⏳ Planned           | 13                           |
| 15   | Expand Documentation, Usage Examples, and Tests         | ⏳ Planned                  | All                          |
| 16   | Gather Feedback, Iterate, Plan Next Phase               | ⏳ Planned                  | All                          |

---

## Appendix: Feature Table

| Feature                        | Human Benefit         | LLM Benefit                | Implementation Notes         |
|------------------------------- |----------------------|----------------------------|------------------------------|
| Content Summarization          | Fast onboarding      | Richer context             | AST + NLP                    |
| Exclusion Patterns             | Less noise           | Less irrelevant data       | Configurable ignore          |
| Interactive Visualizations     | Easier navigation    | N/A                        | D3.js/Mermaid                |
| Chunked LLM Prompts            | N/A                  | Context window optimization| Token-aware chunking         |
| Overlap & Verification Prompts | N/A                  | Reduces hallucination      | Manifest + periodic prompts  |
| Architectural Layer Views      | System understanding | N/A                        | Layer tagging                |
| Keyword Dependency Explorer    | Targeted impact analysis, concept tracing | Focused context for LLM, prompt chunking | Keyword search, connection graph, depth control |
| mypy/pyright                   | Type safety, coverage| LLM can reason about types | Static type check, coverage  |
| ruff/flake8/pylint             | Code quality, style  | LLM can see code smells    | Linting, static analysis     |
| black/isort                    | Formatting, imports  | N/A                        | Formatting, import sorting   |
| bandit                         | Security insights    | LLM can reason about vulns | Static security analysis     |
| coverage.py                    | Test coverage        | LLM can reason about tests | Test coverage annotation     |
| pydocstyle                     | Docstring quality    | LLM can reason about docs  | Docstring convention check   |
| pipdeptree/safety              | Dependency security  | LLM can reason about deps  | Dependency, license, vulns   |
| code2flow/pyan3                | Call graph viz       | LLM can reason about flow  | Function-level call graphs   |

---

## End of Plan

## Strict Sequential Implementation Plan

To ensure efficient, maintainable, and maximally useful development, the following steps should be executed in order. Each step builds on the previous, and dependencies/priorities are made explicit. This order ensures that all downstream outputs (visualizations, LLM prompts, reports) benefit from the full analysis pipeline.

### Step-by-Step Roadmap (with Dependencies)

1. **Modularize Analysis Pipeline**
   - Prerequisite for all further enhancements.
   - Status: ✅ Completed

2. **Implement Robust Exclusion Patterns**
   - Structure analysis first, then content/dependency analysis.
   - Ensures all subsequent stages operate only on relevant files.
   - Status: 🟢 Structure, 🟡 Content

3. **Add Content Extraction and Summarization**
   - Extracts docstrings, summaries, and architectural roles.
   - Status: ✅ Completed

4. **Design Unified Analysis Stage Interface & Config System**
   - Standardizes how all tools are integrated and configured.
   - Prerequisite for tool integration.
   - Status: 🟢 In Progress

5. **Integrate Static Analysis Tools (in order):**
   - Each tool is a modular pipeline stage, enabled/disabled via config.
   - mypy/pyright (type checking)
   - ruff/flake8/pylint (linting)
   - bandit (security)
   - coverage.py (test coverage)
   - black/isort (formatting, optional)
   - pydocstyle (docstring, optional)
   - pipdeptree/safety (dependency, optional)
   - code2flow/pyan3 (call graph, optional/advanced)
   - Status: 🟢 Mypy, Ruff, Bandit, Coverage, Flake8, Black, Pydocstyle, Dependency: Complete | ⏳ CallGraph: Next

6. **Update Metrics Models to Store Tool Outputs**
   - Extend `FileMetrics`/`ProjectMetrics` to hold diagnostics from all tools.
   - Prerequisite for downstream consumption.
   - Status: ⏳ Planned

7. **Expose Tool Outputs to Downstream Stages**
   - Make diagnostics, overlays, and summaries available to visualization, reporting, and LLM prompt stages.
   - Status: ⏳ Planned

8. **Add/Update Visualization Stages**
   - Use overlays from tool outputs (type errors, lint, security, coverage, etc.).
   - Add architectural overlays, call graphs, etc.
   - Status: ⏳ Planned

9. **Implement LLM-Optimized Output (Chunked Prompt Gen.)**
   - Include tool outputs in prompt chunks.
   - Add manifest and metadata.
   - Status: ✅ Completed

10. **Insert Verification and Rechecking Prompts**
    - Periodically summarize and check context in LLM prompt output.
    - Status: ✅ Completed

11. **Add Keyword-Centric Dependency and Connection Explorer**
    - Enables targeted impact analysis and concept tracing.
    - Status: ⏳ Planned

12. **Implement Timestamped Output Folder for Each Run**
    - Ensures all reports are preserved and enables comparison.
    - Status: ⏳ Planned

13. **Implement Optional Comparison Between Last and Current Report**
    - Generates a diff/summary after each run (if enabled).
    - Status: ⏳ Planned

14. **Expand Documentation, Usage Examples, and Tests**
    - Ensure all new features are documented and tested.
    - Status: ⏳ Planned

15. **Gather Feedback, Iterate, Plan Next Phase**
    - Review outputs, collect user/LLM feedback, and refine.
    - Status: ⏳ Planned

---

### Updated Summary Table (Strict Order)

| Step | Enhancement/Feature                                      | Status/Notes                | Prerequisites                |
|------|---------------------------------------------------------|-----------------------------|------------------------------|
| 1    | Modularize Analysis Pipeline                            | ✅ Done                     | -                            |
| 2    | Implement Robust Exclusion Patterns                     | 🟢 Structure, 🟡 Content    | 1                            |
| 3    | Add Content Extraction and Summarization                | ✅ Done                     | 1, 2                        |
| 4    | Unified Analysis Stage Interface & Config               | ⏳ Planned                  | 1, 2, 3                     |
| 5    | Integrate Static Analysis Tools (see above order)       | ⏳ Planned                  | 4                            |
| 6    | Update Metrics Models                                   | ⏳ Planned                  | 5                            |
| 7    | Expose Tool Outputs to Downstream Stages                | ⏳ Planned                  | 6                            |
| 8    | Add/Update Visualization Stages                         | ⏳ Planned                  | 7                            |
| 9    | Implement LLM-Optimized Output (Chunked Prompt Gen.)    | ✅ Done                     | 7                            |
| 10   | Insert Verification and Rechecking Prompts              | ✅ Done                     | 9                            |
| 11   | Add Keyword-Centric Dependency/Connection Explorer      | ⏳ Planned                  | 8, 9                        |
| 12   | Implement Timestamped Output Folder for Each Run        | ⏳ Planned                  | 1                            |
| 13   | Implement Optional Comparison Between Last and Current Report | ⏳ Planned           | 12                           |
| 14   | Expand Documentation, Usage Examples, and Tests         | ⏳ Planned                  | All                          |
| 15   | Gather Feedback, Iterate, Plan Next Phase               | ⏳ Planned                  | All                          |

---

This strict order ensures that each enhancement is built on a solid foundation, with all dependencies and priorities made explicit. Downstream features (visualization, LLM prompts, explorer) will always have access to the richest, most accurate analysis data available.

## Additional Enhancements: Output Versioning & Report Comparison

### 14. **Timestamped Output Folder for Each Run**

- **Behavior:** Each PAT run creates a new subfolder in the output directory, named with the current date and time (e.g., `PAT_output/2024-06-09_15-30-00/`).
- **Benefits:** Preserves all previous reports, enables easy tracking and comparison.
- **Implementation:**
  - On each run, generate a timestamped folder name.
  - Save all outputs in that folder.
  - Optionally, add a symlink or copy to `PAT_output/latest/` for convenience.
- **Dependencies:** Modular output logic, must be in place before comparison feature.
- **Status:** ⏳ Planned

### 15. **Optional Comparison Between Last and Current Report**

- **Behavior:** If enabled, after generating the new report, PAT compares it to the previous run and generates a diff/summary (e.g., `comparison.md`).
- **Benefits:** Quickly see what changed (new errors, fixed issues, coverage changes, etc.).
- **Implementation:**
  - Detect the previous report folder (by timestamp).
  - Compare key outputs (e.g., summary.json, manifest, diagnostics).
  - Generate a comparison report in the new output folder.
  - Make this feature optional via a config flag or CLI argument.
- **Dependencies:** Timestamped output folders must be implemented first.
- **Status:** ⏳ Planned

---

## Updated Strict Sequential Implementation Plan

### Step-by-Step Roadmap (with Dependencies)

1. **Modularize Analysis Pipeline**
   - Prerequisite for all further enhancements.
   - Status: ✅ Completed
2. **Implement Robust Exclusion Patterns**
   - Structure analysis first, then content/dependency analysis.
   - Ensures all subsequent stages operate only on relevant files.
   - Status: 🟢 Structure, 🟡 Content
3. **Add Content Extraction and Summarization**
   - Extracts docstrings, summaries, and architectural roles.
   - Status: ✅ Completed
4. **Design Unified Analysis Stage Interface & Config System**
   - Standardizes how all tools are integrated and configured.
   - Prerequisite for tool integration.
   - Status: ⏳ Planned
5. **Integrate Static Analysis Tools (in order):**
   - Each tool is a modular pipeline stage, enabled/disabled via config.
   - mypy/pyright (type checking)
   - ruff/flake8/pylint (linting)
   - bandit (security)
   - coverage.py (test coverage)
   - black/isort (formatting, optional)
   - pydocstyle (docstring, optional)
   - pipdeptree/safety (dependency, optional)
   - code2flow/pyan3 (call graph, optional/advanced)
   - Status: ⏳ Planned
6. **Update Metrics Models to Store Tool Outputs**
   - Extend `FileMetrics`/`ProjectMetrics` to hold diagnostics from all tools.
   - Prerequisite for downstream consumption.
   - Status: ⏳ Planned
7. **Expose Tool Outputs to Downstream Stages**
   - Make diagnostics, overlays, and summaries available to visualization, reporting, and LLM prompt stages.
   - Status: ⏳ Planned
8. **Add/Update Visualization Stages**
   - Use overlays from tool outputs (type errors, lint, security, coverage, etc.).
   - Add architectural overlays, call graphs, etc.
   - Status: ⏳ Planned
9. **Implement LLM-Optimized Output (Chunked Prompt Generation)**
   - Include tool outputs in prompt chunks.
   - Add manifest and metadata.
   - Status: ✅ Completed
10. **Insert Verification and Rechecking Prompts**
    - Periodically summarize and check context in LLM prompt output.
    - Status: ✅ Completed
11. **Add Keyword-Centric Dependency and Connection Explorer**
    - Enables targeted impact analysis and concept tracing.
    - Status: ⏳ Planned
12. **Implement Timestamped Output Folder for Each Run**
    - Ensures all reports are preserved and enables comparison.
    - Status: ⏳ Planned
13. **Implement Optional Comparison Between Last and Current Report**
    - Generates a diff/summary after each run (if enabled).
    - Status: ⏳ Planned
14. **Expand Documentation, Usage Examples, and Tests**
    - Ensure all new features are documented and tested.
    - Status: ⏳ Planned
15. **Gather Feedback, Iterate, and Plan Next Phase**
    - Review outputs, collect user/LLM feedback, and refine.
    - Status: ⏳ Planned

---

### Updated Summary Table (Strict Order)

| Step | Enhancement/Feature                                      | Status/Notes                | Prerequisites                |
|------|---------------------------------------------------------|-----------------------------|------------------------------|
| 1    | Modularize Analysis Pipeline                            | ✅ Done                     | -                            |
| 2    | Implement Robust Exclusion Patterns                     | 🟢 Structure, 🟡 Content    | 1                            |
| 3    | Add Content Extraction and Summarization                | ✅ Done                     | 1, 2                        |
| 4    | Unified Analysis Stage Interface & Config               | ⏳ Planned                  | 1, 2, 3                     |
| 5    | Integrate Static Analysis Tools (see above order)       | ⏳ Planned                  | 4                            |
| 6    | Update Metrics Models                                   | ⏳ Planned                  | 5                            |
| 7    | Expose Tool Outputs to Downstream Stages                | ⏳ Planned                  | 6                            |
| 8    | Add/Update Visualization Stages                         | ⏳ Planned                  | 7                            |
| 9    | Implement LLM-Optimized Output (Chunked Prompt Gen.)    | ✅ Done                     | 7                            |
| 10   | Insert Verification and Rechecking Prompts              | ✅ Done                     | 9                            |
| 11   | Add Keyword-Centric Dependency/Connection Explorer      | ⏳ Planned                  | 8, 9                        |
| 12   | Implement Timestamped Output Folder for Each Run        | ⏳ Planned                  | 1                            |
| 13   | Implement Optional Comparison Between Last and Current Report | ⏳ Planned           | 12                           |
| 14   | Expand Documentation, Usage Examples, and Tests         | ⏳ Planned                  | All                          |
| 15   | Gather Feedback, Iterate, Plan Next Phase               | ⏳ Planned                  | All                          |

---

This strict order ensures that each enhancement is built on a solid foundation, with all dependencies and priorities made explicit. Downstream features (visualization, LLM prompts, explorer) will always have access to the richest, most accurate analysis data available.

## Review and Recommendations: LLM Prompt and Verification Output Improvements (2025-04-20)

### Recent Progress

- The PAT pipeline now generates chunked LLM prompt files and periodic verification prompts as part of each analysis run.
- Output artifacts include per-chunk Markdown files (e.g., `chunk_3337.md`) and verification prompts (e.g., `verify_3336.md`), as well as a manifest and logs.
- The chunked prompt generator and verification prompt insertion are fully automated and integrated into the modular pipeline.

### Prompt Usefulness Review (April 2025)

- **Sampled Files:**
  - `chunk_3337.md` (chunk prompt)
  - `verify_3336.md` (verification prompt)
- **Findings:**
  - **Chunk Prompts:**
    - Contain code summaries, function/class listings, and some context about architectural roles and dependencies.
    - Generally useful for LLM context ingestion, but could benefit from more explicit diagnostics (e.g., tool results, error summaries) and clearer separation of code, summary, and diagnostics sections.
  - **Verification Prompts:**
    - Currently consist of a long, redundant list of file paths (each listed twice), with no actionable instructions, summary, or diagnostic information.
    - Lack of guidance or criteria for verification, and no summary of tool results or key findings.
    - Redundancy and lack of focus reduce their usefulness for both LLM and human reviewers.
- **Grading:**
  - **Chunk Prompt (sample):** 6/10 (moderately useful, but could be improved with more diagnostics and clearer structure)
  - **Verification Prompt (sample):** 2/10 (low usefulness due to redundancy and lack of actionable content)

### Recommendations for Next Development Cycle

1. **Remove Redundancy in Verification Prompts:**
   - Ensure each file is listed only once.
2. **Add Diagnostics and Summaries:**
   - Include a summary of tool results (e.g., lint/type/security/test coverage errors) for each chunk and in verification prompts.
   - Highlight key issues, anomalies, or patterns detected so far.
3. **Provide Explicit Verification Criteria:**
   - Add clear instructions/questions for LLM/human reviewers (e.g., check for consistency, missing context, unresolved errors, or architectural violations).
4. **Improve Prompt Structure:**
   - Use clear section headers: `Summary`, `Diagnostics`, `Files Included`, `Instructions`.
   - For chunk prompts, ensure code, summary, and diagnostics are clearly separated.
5. **Contextualize Verification Prompts:**
   - Briefly explain what has been done so far and what should be checked next.
6. **Manifest and Metadata:**
   - Ensure the manifest includes references to diagnostics and summaries for each chunk/verification prompt.
7. **Iterative Feedback:**
   - After implementing improvements, sample and grade outputs again, and iterate as needed.
8. **[NEW] Exclude Non-Critical Files/Chunks from Prompts:**
   - Do not include files or prompt chunks with only 'No critical issues detected.' in the LLM prompt output. Only include files/chunks with actionable diagnostics or recommendations.
   - Ensure prompt files and chunk boundaries begin at logical, contextually meaningful places (e.g., start of a file, class, or function with issues).

### Action Items

- [ ] Refactor verification prompt generation to remove redundancy and add actionable content.
- [ ] Update chunk prompt generator to include tool diagnostics and clearer structure.
- [ ] Add explicit instructions/questions to verification prompts.
- [ ] Review and re-grade outputs after next development cycle.
- [ ] **[NEW] Exclude files/chunks with only 'No critical issues detected.' from LLM prompt output.**
- [ ] **[NEW] Ensure prompt chunk boundaries are logical and contextually meaningful.**

---

## How Critical Issues Are Currently Described and Detected

- **Critical issues** are detected in the chunked prompt generator by examining the `tool_results` field in each file's `FileMetrics`.
- For each tool (mypy, ruff, bandit, coverage, black, pydocstyle, dependency), the generator checks for the presence of errors, issues, or coverage below 100%.
- If a file's tool results contain non-empty lists of errors/issues, or coverage is below threshold, it is considered to have critical issues.
- The `_summarize_diagnostics` method groups and summarizes these issues by area (type errors, lint, security, etc.).
- If no issues are found, the prompt generator currently adds a default next step: 'No critical issues detected. Continue to monitor for context propagation and CAW alignment.'
- **Planned update:** Files/chunks with only this message (i.e., no actionable diagnostics) will be excluded from prompt output, focusing LLM attention on actionable, diagnostically relevant content.
- Prompt chunk boundaries will be improved to start at logical, contextually meaningful places (e.g., start of a file, class, or function with issues).

---

## Advanced Tools & Plugins for Choreographic, Actor, and CAW-Aligned Analysis (Planned Q2-Q3 2025)

### Motivation

To further enhance PAT's ability to analyze, verify, and refactor codebases built around choreographic programming, the actor model, and the Contextual Adaptive Wave Programming (CAW) paradigm, we will integrate additional free and open-source tools and research plugins. These will provide deeper protocol, concurrency, context, and capability-based security analysis, as well as richer overlays and visualizations.

### Candidate Tools & Plugins (Free/Open Source)

| Tool/Plugin         | Category         | Use Case / Benefit | Python Support | Integration Plan |
|---------------------|------------------|--------------------|---------------|-----------------|
| **Scribble**        | Choreography/MPST| Protocol specification, multiparty session type verification | Partial (Java, but can be used for protocol extraction/specification) | Prototype protocol extraction from code, check conformance of actor/message-passing code |
| **Pyright**         | Static Analysis  | Fast, type-aware static analysis, protocol/type checking | Yes | Integrate as alternative to mypy for type/context safety |
| **Pyre**            | Static Analysis  | Type checking, taint analysis, dataflow | Yes | Integrate for advanced type and security analysis |
| **Bandit (plugins)**| Security         | Static security analysis, extendable for CAW/actor rules | Yes | Extend with custom rules for capability/context checks |
| **Mypy (plugins)**  | Static Analysis  | Type/context propagation, custom CAW/actor rules | Yes | Write plugins for context/duality/capability checks |
| **NetworkX**        | Visualization    | Actor/context graph overlays, duality/CAW structure | Yes | Use for overlays and graph-based diagnostics |
| **GUDHI, giotto-tda**| TDA/Topology    | Analyze context/duality propagation, emergent structure | Yes | Integrate for advanced overlays and invariants |
| **Effect**          | Effect System    | Explicit effect/context tracking, composable effects | Yes | Prototype overlays for effectful/contextual code |
| **PyContract**      | Runtime Verification | Design-by-contract, context/capability checks | Yes | Prototype runtime monitors for context/capability adherence |
| **PySnooper/VizTracer** | Tracing/Monitoring | Visualize message passing, context propagation | Yes | Integrate for runtime overlays and debugging |
| **TLA+ (spec-level)**| Formal Verification | Model and check distributed protocols, context propagation | Spec only | Use for protocol/CAW model checking (optional, advanced) |
| **Hypothesis**      | Property-based Testing | Generate tests for context/actor/duality boundaries | Yes | Use for advanced test generation |

### Integration Roadmap (Q2-Q3 2025)

1. **Protocol/Choreography Analysis**
    - Prototype protocol extraction from actor/choreography code (AST/NLP).
    - Use Scribble for protocol specification and conformance checking (optional, advanced).
2. **Advanced Static Analysis**
    - Integrate Pyright and Pyre as alternative/parallel static analysis stages.
    - Write custom plugins for mypy and Bandit to check CAW/actor/capability/context rules.
3. **Graph/Overlay Visualization**
    - Use NetworkX for actor/context/duality overlays in reports and visualizations.
    - Integrate TDA tools (GUDHI, giotto-tda) for emergent structure/topology overlays.
4. **Effect System & Runtime Verification**
    - Prototype overlays using Effect for explicit effect/context tracking.
    - Integrate PyContract for runtime context/capability checks.
    - Add tracing/monitoring overlays with PySnooper/VizTracer.
5. **Property-Based Testing**
    - Use Hypothesis to generate tests for context/actor/duality boundaries.
6. **(Optional) Formal Verification**
    - Use TLA+ for protocol/CAW model checking at the specification level.

### Status in Roadmap

- **Protocol/Choreography Analysis:** ⏳ Planned (Q2 2025)
- **Advanced Static Analysis (Pyright/Pyre, plugins):** ⏳ Planned (Q2 2025)
- **Graph/Overlay Visualization (NetworkX, TDA):** ⏳ Planned (Q2-Q3 2025)
- **Effect System & Runtime Verification:** ⏳ Planned (Q3 2025)
- **Property-Based Testing (Hypothesis):** ⏳ Planned (Q3 2025)
- **Formal Verification (TLA+):** ⏳ Optional/Advanced

### Action Items

- [ ] Prototype protocol extraction and MPST conformance checking.
- [ ] Integrate Pyright and Pyre as static analysis stages.
- [ ] Develop custom mypy/Bandit plugins for CAW/actor/capability checks.
- [ ] Add NetworkX overlays to reports/visualizations.
- [ ] Integrate TDA overlays for emergent structure.
- [ ] Prototype effect system overlays and runtime verification.
- [ ] Add property-based test generation for context/actor/duality.

---

## Updated Step-by-Step Roadmap (with Advanced Tools)

| Step | Enhancement/Feature                                      | Status/Notes                | Prerequisites                |
|------|---------------------------------------------------------|-----------------------------|------------------------------|
| ...  | ...                                                     | ...                         | ...                          |
| 5A   | Integrate Advanced Tools for Choreography/Actor/CAW     | ⏳ Planned (Q2-Q3 2025)     | 4                            |
|      | - Protocol/Choreography Analysis (Scribble, AST/NLP)    | ⏳ Planned                  | 4                            |
|      | - Advanced Static Analysis (Pyright, Pyre, plugins)      | ⏳ Planned                  | 4                            |
|      | - Graph/Overlay Visualization (NetworkX, TDA)            | 🟢 Initial implementation complete | 4, 5                        |
|      | - Effect System/Runtime Verification (Effect, PyContract)| ⏳ Planned                  | 4, 5                        |
|      | - Property-Based Testing (Hypothesis)                    | ⏳ Planned                  | 4, 5                        |
|      | - (Optional) Formal Verification (TLA+)                  | ⏳ Optional                 | 4, 5                        |

---

## Next Best Implementation: Pyright Integration

### Rationale

- **Pyright** is a fast, free, and type-aware static analysis tool for Python, providing type checking and protocol conformance analysis. It complements mypy and can catch additional issues, especially in modern Python codebases.
- **Current Status:** Pyright is not yet integrated as a pipeline stage in PAT. Adding it will enhance type safety diagnostics and provide a broader static analysis perspective.

### Implementation Plan

1. **Create `pyright_stage.py` in `PAT_tool/`**
    - Scaffold a new `PyrightStage` class, following the pattern of other ToolStage-based integrations.
    - Add a method to invoke Pyright via subprocess, parse its output, and populate diagnostics in the metrics model.
    - Add config options to enable/disable Pyright in the pipeline.
    - Write/expand tests for output parsing and error handling.
2. **Update Pipeline**
    - Register `PyrightStage` in the main pipeline after mypy.
    - Ensure its results are included in `FileMetrics` and `ProjectMetrics`.
3. **Documentation**
    - Update user docs to describe Pyright integration and outputs.
4. **Validation**
    - Run the pipeline on a sample project and confirm Pyright diagnostics are present in the reports and prompts.

---

### Graph/Overlay Visualization (NetworkX, TDA)

- **Status:** 🟢 **Initial implementation complete (Q2 2025)**
- **Current:**
  - `GraphOverlayStage` is implemented and integrated as a pipeline stage.
  - Builds NetworkX graphs (e.g., call graphs) from project metrics.
  - Outputs graph overlays, topological summaries (node/edge counts), and placeholder for visualizations.
- **Future Possibilities:**
  - Integrate TDA libraries (GUDHI, giotto-tda) for persistent homology, clustering, and advanced topological analysis.
  - Add richer graph analytics (centrality, community detection, protocol/actor overlays).
  - Generate and export static/interactive visualizations (PNG, SVG, HTML, D3.js, Cytoscape.js).
  - Link overlays to CAW-aligned diagnostics and reporting.

## Visualization and Interactive Visualization Support (Q2 2025)

### Current State

- Overlay results (e.g., effect, protocol, graph, TDA overlays) can include a `visualization` field (path to static/interactive artifact, e.g., HTML/SVG).
- Markdown reports and prompt chunks include links to these visualizations if present.
- No code in the pipeline currently generates or manages the creation of these visualizations—only links to them if present.
- No manifest or navigation/index for visualizations is provided beyond a simple link.

### Gaps and Missing Features

- **Visualization Generation:**
  - No code to generate interactive HTML visualizations (e.g., Cytoscape.js, D3.js, pyvis, Plotly, Mermaid, etc.) for overlays.
  - No persistent diagrams or interactive plots for TDA overlays.
  - No sequence diagrams or message flow visualizations for protocol overlays.
- **Integration and Indexing:**
  - No unified system for storing, naming, and referencing visualization artifacts.
  - No manifest or index of all visualizations for easy navigation.
- **Interactive Features:**
  - No interactive navigation between code, overlays, and visualizations (e.g., click a node to see the file, or vice versa).
  - No cross-highlighting or filtering by issue type, overlay, or CAW system.
  - No embedded iframes or previews in Markdown reports or prompt chunks (only static links).
- **Coverage:**
  - Not all overlays have visualization support (e.g., effect overlays lack Sankey diagrams, protocol overlays lack sequence diagrams, TDA overlays lack persistent homology plots).
- **Testing and Documentation:**
  - No automated tests or documentation for visualization generation and usage.

### Next Steps (Planned Q2–Q3 2025)

- [x] Add utilities to generate interactive visualizations for each overlay type:
  - [x] Graph overlays: Export NetworkX graphs to HTML (pyvis; Cytoscape.js/D3.js planned).
  - [x] Effect overlays: Generate Sankey diagrams (Plotly; Mermaid/Graphviz planned).
  - [x] Protocol overlays: Generate sequence diagrams (Mermaid; PlantUML planned).
  - [x] TDA overlays: Generate persistent diagrams/barcodes (matplotlib, plotly, giotto-tda).
- [x] Save each visualization to a known location and update the overlay's `visualization` field.
- [x] Generate an index (Markdown/HTML) listing all visualizations with links and descriptions.
- [x] Embed visualizations as iframes or images in Markdown reports (for local viewing).
- [ ] Add navigation from visualizations back to code files and vice versa.  
      (Partial: visualization index and overlay fields implemented; direct code navigation planned)
- [ ] Add tests to ensure visualizations are generated and linked correctly. (Planned)
- [ ] Document how to regenerate and use visualizations for review. (Planned)

#### Visualization Capabilities (Q2 2025)

- **Graph overlays:** Interactive HTML (pyvis) for call graphs and protocol graphs, saved as `graph_overlay.html`.
- **Effect overlays:** Interactive Sankey diagrams (Plotly) for project-wide and per-file overlays, saved as `effect_overlay_sankey.html` and `*.effect_sankey.html`.
- **Protocol overlays:** Sequence diagrams as Mermaid HTML, saved as `protocol_overlay_sequence.html`.
- **TDA overlays:** Persistence diagrams/barcodes using matplotlib (PNG/HTML), saved to a specified output path.
- **Visualization index:** Markdown index (`visualization_index.md`) listing and linking all generated visualizations.
- **Embedding:** Visualizations can be embedded as iframes or images in Markdown reports for local viewing.
- **Navigation:** Visualization index and overlay fields provide navigation; direct code-to-visualization links are planned.
- **Testing/Docs:** Tests and documentation for visualization generation and usage are planned for Q3 2025.

#### Usage
- All overlay results include a `visualization` field for easy reference in reports and dashboards.
- The visualization index is generated at the end of each analysis run for easy navigation.
- To regenerate visualizations, rerun the analysis pipeline; outputs are saved to the configured output directory.

---

**Next: Proceed with concrete code implementation for overlay visualization generation and integration.**

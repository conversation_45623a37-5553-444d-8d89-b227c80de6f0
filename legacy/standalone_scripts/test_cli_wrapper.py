#!/usr/bin/env python3
"""
Test wrapper for the CLI.
"""

import sys
import os
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("test_cli_wrapper.log")
    ]
)
logger = logging.getLogger("test_cli_wrapper")

# Run the CLI with the simple analyzer
logger.info("Running CLI with simple analyzer")
try:
    result = subprocess.run(
        ["python", "-m", "vibe_check", "analyze", "./test_project", "--preset", "minimal", "--output", "./test_results_simple", "--use-simple-analyzer"],
        capture_output=True,
        text=True
    )
    logger.info(f"CLI with simple analyzer completed with return code: {result.returncode}")
    print(f"Return code: {result.returncode}")
    print("=== Simple Analyzer Output ===")
    print(result.stdout)
    print("=== Simple Analyzer Error ===")
    print(result.stderr)
except subprocess.CalledProcessError as e:
    logger.error(f"CLI with simple analyzer failed with return code: {e.returncode}")
    print("=== Simple Analyzer Error ===")
    print(f"Output: {e.stdout}")
    print(f"Error: {e.stderr}")

# Run the CLI with the actor system
logger.info("Running CLI with actor system")
try:
    result = subprocess.run(
        ["python", "-m", "vibe_check", "analyze", "./test_project", "--preset", "minimal", "--output", "./test_results_actor"],
        capture_output=True,
        text=True
    )
    logger.info(f"CLI with actor system completed with return code: {result.returncode}")
    print(f"Return code: {result.returncode}")
    print("=== Actor System Output ===")
    print(result.stdout)
    print("=== Actor System Error ===")
    print(result.stderr)
except subprocess.CalledProcessError as e:
    logger.error(f"CLI with actor system failed with return code: {e.returncode}")
    print("=== Actor System Error ===")
    print(f"Output: {e.stdout}")
    print(f"Error: {e.stderr}")

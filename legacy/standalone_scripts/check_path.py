import sys
import os
import pytest
import importlib.util


def test_python_path():
    """Test the Python path."""
    print("\nPython Path:")
    for path in sys.path:
        print(f"  {path}")
    assert True


def test_current_working_directory():
    """Test the current working directory."""
    print("\nCurrent Working Directory:")
    print(f"  {os.getcwd()}")
    assert True


def test_import_actor_system_module():
    """Test importing the actor_system module."""
    print("\nTrying to import vibe_check.core.actor_system:")
    try:
        import vibe_check.core.actor_system
        print(f"  Success! File: {vibe_check.core.actor_system.__file__}")
        print(f"  Contents: {dir(vibe_check.core.actor_system)}")

        # Check if the module has the expected attributes
        expected_attrs = ['ActorSystem', 'Actor', 'Message', 'MessageType']
        for attr in expected_attrs:
            if hasattr(vibe_check.core.actor_system, attr):
                print(f"  Attribute {attr} exists")
            else:
                print(f"  Attribute {attr} does NOT exist")

        assert True
    except ImportError as e:
        print(f"  Failed: {e}")
        pytest.fail(f"Failed to import vibe_check.core.actor_system: {e}")


def test_import_actor_system_class():
    """Test importing the ActorSystem class."""
    print("\nTrying to import ActorSystem:")
    try:
        from vibe_check.core.actor_system import ActorSystem
        print(f"  Success! ActorSystem: {ActorSystem}")
        assert ActorSystem is not None
    except ImportError as e:
        print(f"  Failed: {e}")
        pytest.fail(f"Failed to import ActorSystem: {e}")


def test_check_module_files():
    """Check if the module files exist."""
    print("\nChecking module files:")

    # Check if the actor_system.py file exists
    actor_system_path = os.path.join(os.getcwd(), "vibe_check", "core", "actor_system", "actor_system.py")
    print(f"  actor_system.py exists: {os.path.exists(actor_system_path)}")

    # Check if the __init__.py file exists
    init_path = os.path.join(os.getcwd(), "vibe_check", "core", "actor_system", "__init__.py")
    print(f"  __init__.py exists: {os.path.exists(init_path)}")

    # Check the content of __init__.py
    if os.path.exists(init_path):
        with open(init_path, 'r') as f:
            content = f.read()
            print(f"  __init__.py content length: {len(content)}")
            print(f"  __init__.py contains 'ActorSystem': {'ActorSystem' in content}")
            print(f"  __init__.py contains 'from .actor_system import ActorSystem': {'from .actor_system import ActorSystem' in content}")

    assert True

#!/usr/bin/env python3
"""
Test script for the MessageProcessor._process_pending_messages method.

This script tests the _process_pending_messages method in isolation.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("test_message_processor")

# Import the necessary components
from vibe_check.core.actor_system.message import Message, MessageType
from vibe_check.core.actor_system.context_wave import ContextWave
from vibe_check.core.actor_system.messaging.processor import MessageProcessor


class MockActor:
    """A mock actor for testing the MessageProcessor."""
    
    def __init__(self, actor_id: str):
        """Initialize the mock actor."""
        self.actor_id = actor_id
        self.mailbox = asyncio.Queue()
        self._pending_messages: List[Dict[str, Any]] = []
        self._metrics = {"errors": 0}
        self._known_actors = {}
        self.supervisor_id = None
        self.received_messages = []
        
    async def receive(self, message: Message) -> None:
        """Mock receive method."""
        self.received_messages.append(message)
        await self.mailbox.put(message)


async def test_process_pending_messages():
    """Test the _process_pending_messages method."""
    logger.info("Testing _process_pending_messages method")
    
    # Create a mock actor
    actor = MockActor("test_actor")
    
    # Create a message processor
    processor = MessageProcessor(actor)
    
    # Create some test messages
    messages = []
    for i in range(3):
        context = ContextWave()
        context.metadata["sender_id"] = "sender"
        
        message = Message(
            type=MessageType.TEST,
            payload={"message_id": f"test_{i}", "content": f"Test message {i}"},
            context=context,
            recipient_id="test_actor",
            sender_id="sender"
        )
        
        # Add to pending messages with timestamp
        actor._pending_messages.append({
            "message": message,
            "queued_at": time.time() - (3 - i)  # Older messages first
        })
        
        messages.append(message)
    
    # Process the pending messages
    logger.info(f"Processing {len(actor._pending_messages)} pending messages")
    await processor._process_pending_messages()
    
    # Verify that the pending messages were moved to the mailbox
    if actor._pending_messages:
        logger.error(f"Pending messages not cleared: {len(actor._pending_messages)} remaining")
    else:
        logger.info("Pending messages cleared successfully")
    
    # Verify that the messages were added to the mailbox
    mailbox_size = actor.mailbox.qsize()
    if mailbox_size == len(messages):
        logger.info(f"All {mailbox_size} messages added to mailbox")
    else:
        logger.error(f"Expected {len(messages)} messages in mailbox, but found {mailbox_size}")
    
    # Process the messages from the mailbox
    processed_messages = []
    while not actor.mailbox.empty():
        message = await actor.mailbox.get()
        processed_messages.append(message)
        logger.info(f"Processed message: {message.payload.get('message_id')}")
    
    # Verify that all messages were processed in the correct order
    if len(processed_messages) == len(messages):
        logger.info("All messages processed successfully")
        
        # Check order (should be in order of receipt)
        for i, message in enumerate(processed_messages):
            expected_id = f"test_{i}"
            actual_id = message.payload.get("message_id")
            if expected_id == actual_id:
                logger.info(f"Message {i} processed in correct order: {actual_id}")
            else:
                logger.error(f"Message {i} processed in wrong order: expected {expected_id}, got {actual_id}")
    else:
        logger.error(f"Expected {len(messages)} processed messages, but found {len(processed_messages)}")


async def test_expired_messages():
    """Test handling of expired messages."""
    logger.info("Testing handling of expired messages")
    
    # Create a mock actor
    actor = MockActor("test_actor")
    
    # Create a message processor
    processor = MessageProcessor(actor)
    
    # Create an expired message
    context = ContextWave()
    context.metadata["sender_id"] = "sender"
    
    # Create a message class with an is_expired method that returns True
    class ExpiredMessage(Message):
        def is_expired(self):
            return True
    
    message = ExpiredMessage(
        type=MessageType.TEST,
        payload={"message_id": "expired", "content": "This message is expired"},
        context=context,
        recipient_id="test_actor",
        sender_id="sender"
    )
    
    # Add to pending messages
    actor._pending_messages.append({
        "message": message,
        "queued_at": time.time() - 60  # Queued a minute ago
    })
    
    # Process the pending messages
    logger.info(f"Processing {len(actor._pending_messages)} pending messages (expired)")
    await processor._process_pending_messages()
    
    # Verify that the pending messages were cleared
    if actor._pending_messages:
        logger.error(f"Pending messages not cleared: {len(actor._pending_messages)} remaining")
    else:
        logger.info("Pending messages cleared successfully")
    
    # Verify that the expired message was not added to the mailbox
    if actor.mailbox.empty():
        logger.info("Expired message was correctly discarded")
    else:
        logger.error("Expired message was incorrectly added to mailbox")


async def main():
    """Run the tests."""
    try:
        # Test processing pending messages
        await test_process_pending_messages()
        
        # Test handling of expired messages
        await test_expired_messages()
        
        logger.info("All tests completed successfully")
        
    except Exception as e:
        logger.error(f"Error during tests: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    asyncio.run(main())

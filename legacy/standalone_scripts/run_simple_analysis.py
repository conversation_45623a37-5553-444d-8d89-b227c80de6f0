#!/usr/bin/env python3
"""
Simple Vibe Check Analysis Script

This script uses the simple_analyzer module to analyze a project without
relying on the actor system, and generates a basic report.
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime

# Import the simple analyzer
from vibe_check.core.simple_analyzer import simple_analyze_project
# Import models
from vibe_check.core.models import ProjectMetrics, FileMetrics
from typing import Any, Dict, List, Optional, Union

def _get_attribute(obj: Any, attr_name: str, default: Any = None) -> Any:
    """Helper function to get an attribute from an object or dictionary."""
    if isinstance(obj, dict):
        return obj.get(attr_name, default)
    return getattr(obj, attr_name, default)

def generate_markdown_report(metrics: ProjectMetrics, output_path: str) -> None:
    """Generate a markdown report from the metrics."""
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    with open(output_path, 'w') as f:
        f.write(f"# Vibe Check Analysis Report\n\n")
        f.write(f"**Generated:** {timestamp}\n\n")
        f.write(f"**Project:** {metrics.project_path}\n\n")

        f.write("## Summary\n\n")
        f.write(f"- **Files Analyzed:** {metrics.total_file_count}\n")
        f.write(f"- **Total Lines of Code:** {metrics.total_line_count}\n")
        f.write(f"- **Average Complexity:** {metrics.avg_complexity:.2f}\n")
        f.write(f"- **Total Issues:** {metrics.issue_count}\n\n")

        f.write("## Files\n\n")
        f.write("| File | Lines | Complexity | Issues |\n")
        f.write("|------|-------|------------|--------|\n")

        for path, file_metrics in metrics.files.items():
            f.write(f"| {path} | {file_metrics.lines} | {file_metrics.complexity:.2f} | {len(file_metrics.issues)} |\n")

        f.write("\n## Issues\n\n")

        if metrics.issue_count > 0:
            f.write("| File | Line | Message | Severity |\n")
            f.write("|------|------|---------|----------|\n")

            for path, file_metrics in metrics.files.items():
                for issue in file_metrics.issues:
                    severity = _get_attribute(issue, 'severity', 'unknown')
                    line = _get_attribute(issue, 'line', 'N/A')
                    message = _get_attribute(issue, 'message', 'No description')
                    f.write(f"| {path} | {line} | {message} | {severity} |\n")
        else:
            f.write("No issues found.\n")

        f.write("\n## Recommendations\n\n")
        f.write("Based on the analysis, here are some recommendations:\n\n")

        if metrics.avg_complexity > 10:
            f.write("- **Reduce Complexity:** Some files have high complexity. Consider refactoring.\n")

        if metrics.issue_count > 0:
            f.write("- **Fix Issues:** Address the identified issues to improve code quality.\n")

        f.write("- **Add Documentation:** Ensure all modules, classes, and functions have proper docstrings.\n")
        f.write("- **Add Type Hints:** Consider adding type hints to improve code maintainability.\n")

def generate_json_report(metrics: ProjectMetrics, output_path: str) -> None:
    """Generate a JSON report from the metrics."""
    # Convert metrics to a dictionary
    metrics_dict: Dict[str, Any] = {
        "project_path": metrics.project_path,
        "total_file_count": metrics.total_file_count,
        "total_line_count": metrics.total_line_count,
        "avg_complexity": metrics.avg_complexity,
        "issue_count": metrics.issue_count,
        "files": {}
    }

    # Add file metrics
    files_dict = metrics_dict["files"]
    for path, file_metrics in metrics.files.items():
        files_dict[path] = {
            "name": file_metrics.name,
            "lines": file_metrics.lines,
            "complexity": file_metrics.complexity,
            "issues": [
                {
                    "severity": _get_attribute(issue, "severity", "unknown"),
                    "line": _get_attribute(issue, "line", "N/A"),
                    "message": _get_attribute(issue, "message", "No description")
                }
                for issue in file_metrics.issues
            ]
        }

    # Write to file
    with open(output_path, 'w') as f:
        json.dump(metrics_dict, f, indent=2)

def main() -> None:
    """Main function to run the analysis and generate reports."""
    # Define paths
    project_path = "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/test_project"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = f"vibe_check_output/report_{timestamp}"

    # Create output directory
    os.makedirs(output_dir, exist_ok=True)

    print(f"Starting analysis of {project_path}")
    start_time = time.time()

    # Run the analysis
    metrics = simple_analyze_project(project_path, output_dir)

    end_time = time.time()
    print(f"Analysis completed in {end_time - start_time:.2f} seconds")
    print(f"Files analyzed: {metrics.total_file_count}")
    print(f"Total lines: {metrics.total_line_count}")
    print(f"Average complexity: {metrics.avg_complexity:.2f}")
    print(f"Total issues: {metrics.issue_count}")

    # Generate reports
    markdown_path = os.path.join(output_dir, "report.md")
    json_path = os.path.join(output_dir, "report.json")

    print(f"Generating markdown report: {markdown_path}")
    generate_markdown_report(metrics, markdown_path)

    print(f"Generating JSON report: {json_path}")
    generate_json_report(metrics, json_path)

    # Create a symlink to the latest report
    latest_link = "vibe_check_output/latest"
    if os.path.exists(latest_link) and os.path.islink(latest_link):
        os.unlink(latest_link)
    os.symlink(os.path.basename(output_dir), latest_link)

    print(f"Reports generated in {output_dir}")
    print(f"Latest report symlink: {latest_link}")

if __name__ == "__main__":
    main()

"""
Simple test script for the CLI.
"""

import sys
import os
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("simple_test.log")
    ]
)
logger = logging.getLogger("simple_test")

# Set log levels for other loggers
logging.getLogger("vibe_check").setLevel(logging.DEBUG)
logging.getLogger("vibe_check_actor_system").setLevel(logging.DEBUG)
logging.getLogger("vibe_check_cli").setLevel(logging.DEBUG)

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the CLI
from vibe_check.cli.commands import analyze_command
from vibe_check.cli.error_handler import handle_analysis_error

# Run the CLI with the simple analyzer
logger.info("Running analyze_command with simple analyzer")
try:
    results = analyze_command(
        project_path="./test_project",
        config_path=None,
        output_dir="./test_results_simple",
        verbose=True,
        quiet=False,
        config_override={"preset": "minimal"},
        analyze_trends=False,
        report_progress=False,
        use_simple_analyzer=False
    )
    logger.info("analyze_command completed")
except Exception as e:
    logger.error(f"Error in analyze_command: {e}")
    import traceback
    logger.error(traceback.format_exc())
    sys.exit(1)

# Print the results
logger.info(f"Results type: {type(results)}")
logger.info(f"Results keys: {results.keys() if isinstance(results, dict) else 'Not a dictionary'}")

# Check if there was an error
if isinstance(results, dict) and "error" in results:
    logger.info(f"Error detected: {results['error']}")
    handle_analysis_error(results)
else:
    # Format and display the results
    logger.info("No error detected, displaying results")
    print("=== Analysis Summary ===")

    # Try to get file count
    if hasattr(results, 'files'):
        print(f"Total Files: {len(results.files)}")
    elif isinstance(results, dict) and "files" in results:
        print(f"Total Files: {len(results['files'])}")
    else:
        print("Total Files: Not available")

    # Try to get line count
    total_lines = 0
    if hasattr(results, 'files'):
        for file_metrics in results.files.values():
            total_lines += getattr(file_metrics, 'lines', 0)
        print(f"Total Lines: {total_lines}")
    elif isinstance(results, dict) and "files" in results:
        for file_metrics in results['files'].values():
            if isinstance(file_metrics, dict):
                total_lines += file_metrics.get('lines', 0)
            else:
                total_lines += getattr(file_metrics, 'lines', 0)
        print(f"Total Lines: {total_lines}")
    else:
        print("Total Lines: Not available")

    # Try to get issue count
    total_issues = 0
    if hasattr(results, 'files'):
        for file_metrics in results.files.values():
            issues = getattr(file_metrics, 'issues', [])
            if issues is not None:
                total_issues += len(issues)
        print(f"Total Issues: {total_issues}")
    elif isinstance(results, dict) and "files" in results:
        for file_metrics in results['files'].values():
            if isinstance(file_metrics, dict):
                issues = file_metrics.get('issues', [])
                if issues is not None:
                    total_issues += len(issues)
            else:
                issues = getattr(file_metrics, 'issues', [])
                if issues is not None:
                    total_issues += len(issues)
        print(f"Total Issues: {total_issues}")
    else:
        print("Total Issues: Not available")

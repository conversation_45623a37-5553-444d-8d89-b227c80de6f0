#!/usr/bin/env python3
"""
Debug script for the actor system.

This script tests the actor system with our enhanced logging.
"""

import asyncio
import logging
import os
import sys
import time
import traceback
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("debug_actor_system")

# Import the necessary components
try:
    from vibe_check.core.actor_system.logging import (
        setup_actor_system_logging,
        enable_debug_mode,
        set_log_file,
        save_logs_to_file,
        visualize_actor_system
    )

    from vibe_check.core.actor_system.enhanced_actor import EnhancedActor
    from vibe_check.core.actor_system.message import Message, MessageType
    from vibe_check.core.actor_system.context_wave import ContextWave
    from vibe_check.core.actor_system.dependency_injection import (
        DependencyContainer,
        get_container,
        reset_container
    )
    from vibe_check.core.actor_system.diagnostics.enhanced_tracker import (
        EnhancedTracker,
        DiagnosticCategory,
        DiagnosticLevel
    )
    # Import from consolidated_initializer instead of initialization
    from vibe_check.core.actor_system.consolidated_initializer import (
        ConsolidatedActorInitializer,
        get_initializer,
        reset_initializer
    )
    from vibe_check.core.actor_system.actor_state import ActorState
    # Import SynchronizationPoint from consolidated_initializer instead of SynchronizationManager
    from vibe_check.core.actor_system.consolidated_initializer import SynchronizationPoint
    from vibe_check.core.actor_system.integration import (
        integrate_actor_system,
        get_integrated_actor_system,
        reset_integrated_actor_system,
        integrate_message_processor,
        get_integrated_message_processor,
        reset_integrated_message_processor,
        integrate_actor_starter,
        get_integrated_actor_starter,
        reset_integrated_actor_starter
    )

    ACTOR_SYSTEM_AVAILABLE = True
except ImportError as e:
    logger.error(f"Error importing actor system components: {e}")
    logger.error(traceback.format_exc())
    ACTOR_SYSTEM_AVAILABLE = False


class TestActor(EnhancedActor):
    """A test actor for testing the actor system."""

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Initialize the actor."""
        logger.info(f"Actor {self.actor_id} initializing with config: {config}")

        # Register message handlers
        self._message_handlers[MessageType.TEST] = self._handle_test_message

        # Simulate some initialization work
        await asyncio.sleep(0.1)

        logger.info(f"Actor {self.actor_id} initialized")

    async def _handle_test_message(self, payload: Dict[str, Any], context: ContextWave) -> None:
        """Handle a test message."""
        logger.info(f"Actor {self.actor_id} handling test message: {payload}")

        # If the message requests a response, send one back
        if payload.get("respond", False):
            response_payload = {
                "message_id": f"response_to_{payload.get('message_id', 'unknown')}",
                "message": f"Response from {self.actor_id}",
                "timestamp": time.time(),
                "in_response_to": payload.get("message_id", "unknown")
            }

            await self.send(
                recipient_id=payload.get("sender", "unknown"),
                msg_type=MessageType.TEST,
                payload=response_payload,
                context=context
            )


async def setup_actor_system() -> None:
    """Set up the actor system."""
    logger.info("Setting up actor system")

    # Set up enhanced logging
    output_dir = "./debug_output"
    Path(output_dir).mkdir(exist_ok=True)

    setup_actor_system_logging(
        log_level=logging.DEBUG,
        log_file=os.path.join(output_dir, "actor_system.log"),
        debug_mode=True
    )
    enable_debug_mode()

    # Reset the dependency container
    reset_container()
    container = get_container()

    # Create and register the enhanced tracker
    tracker = EnhancedTracker(output_dir=output_dir)
    container.register_instance(tracker)

    # Reset the initializer
    reset_initializer()

    # Get the initializer with the dependency container
    initializer = get_initializer(dependency_container=container)

    # Reset the integration components
    reset_integrated_actor_system()
    reset_integrated_message_processor()
    reset_integrated_actor_starter()

    # Integrate the actor system
    integrate_actor_system()
    integrate_message_processor()
    integrate_actor_starter()

    logger.info("Actor system set up")


async def test_actor_system_logging() -> None:
    """Test actor system logging."""
    logger.info("Testing actor system logging")

    # Create a simple test message payload
    test_message_payload = {
        "message_id": "test_message_1",
        "message": "Hello from test sender",
        "timestamp": time.time()
    }

    # Log the message using our enhanced logging
    from vibe_check.core.actor_system.logging import log_message

    log_message(
        sender_id="test_sender",
        recipient_id="test_recipient",
        message_type=MessageType.TEST.name,
        message_id="test_message_1",
        payload_size=len(str(test_message_payload)),
        context={"test_context": True}
    )

    # Log a state transition
    from vibe_check.core.actor_system.logging import log_state_transition

    log_state_transition(
        actor_id="test_actor",
        from_state="CREATED",
        to_state="INITIALIZED",
        details={"initialization_time": time.time()}
    )

    # Log an initialization event
    from vibe_check.core.actor_system.logging import log_initialization

    log_initialization(
        actor_id="test_actor",
        phase="initialization",
        status="completed",
        details={"config": {"test_config": True}}
    )

    # Log component state
    from vibe_check.core.actor_system.logging import log_component_state

    log_component_state(
        component_name="test_component",
        state={
            "status": "running",
            "uptime": 123.45,
            "message_count": 42
        }
    )

    # Log timing information
    from vibe_check.core.actor_system.logging import log_timing

    start_time = time.time()
    await asyncio.sleep(0.1)  # Simulate some work
    log_timing(
        actor_id="test_actor",
        operation="test_operation",
        start_time=start_time
    )

    # Log an exception
    from vibe_check.core.actor_system.logging import log_exception

    try:
        raise ValueError("Test exception")
    except Exception as e:
        log_exception(
            logger=logging.getLogger("test_logger"),
            exception=e,
            message="An error occurred during testing",
            actor_id="test_actor",
            include_traceback=True
        )

    logger.info("Actor system logging test completed")


async def main() -> None:
    """Run the tests."""
    if not ACTOR_SYSTEM_AVAILABLE:
        logger.error("Actor system components not available")
        return

    try:
        # Set up the actor system
        await setup_actor_system()

        # Test actor system logging
        await test_actor_system_logging()

        # Get the enhanced tracker
        container = get_container()
        tracker = container.resolve(EnhancedTracker)

        # Save diagnostics
        output_dir = "./debug_output"
        diagnostics_path = save_logs_to_file(output_dir)
        logger.info(f"Saved logs to {diagnostics_path}")

        # Visualize actor system
        viz_path = visualize_actor_system(output_dir)
        if viz_path:
            logger.info(f"Saved visualization to {viz_path}")
        else:
            logger.warning("Could not generate visualization")

        logger.info("All tests completed successfully")

    except Exception as e:
        logger.error(f"Error during tests: {e}")
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    asyncio.run(main())

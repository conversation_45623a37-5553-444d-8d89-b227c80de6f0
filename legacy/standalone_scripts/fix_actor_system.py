#!/usr/bin/env python3
"""
Fix Actor System Script

This script fixes issues with the Vibe Check actor system, specifically
addressing the problem with the FileActor constructor parameter mismatch.

Usage:
    python fix_actor_system.py

The script will create backup files of the modified files and apply the fixes.
"""

import os
import shutil
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

# Paths to files that need to be fixed
FILE_ACTOR_PATH = "vibe_check/core/actor_system/actors/file_actor.py"
ACTOR_SYSTEM_BUILDER_PATH = "vibe_check/core/orchestration/actor_system_builder.py"

def backup_file(file_path: str) -> None:
    """
    Create a backup of a file before modifying it.
    
    Args:
        file_path: Path to the file to backup
    """
    backup_path = f"{file_path}.bak"
    if not os.path.exists(backup_path):
        shutil.copy2(file_path, backup_path)
        print(f"Created backup: {backup_path}")

def fix_file_actor(file_path: str) -> None:
    """
    Fix the FileActor constructor to match what's expected in actor_system_builder.py.
    
    Args:
        file_path: Path to the file actor file
    """
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found")
        return
    
    backup_file(file_path)
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the constructor
    constructor_pattern = r"def __init__\(self, actor_system: Any, file_path: Optional\[Path\] = None,"
    constructor_match = re.search(constructor_pattern, content)
    
    if not constructor_match:
        print(f"Error: Could not find constructor in {file_path}")
        return
    
    # Replace the constructor
    updated_content = content.replace(
        "def __init__(self, actor_system: Any, file_path: Optional[Path] = None,",
        "def __init__(self, actor_id: str, file_path: Optional[Path] = None,"
    )
    
    # Find the super().__init__ call
    super_init_pattern = r"# Generate a unique actor ID if not provided\s+actor_id = f\"file_actor_{id\(self\)}\"\s+\s+super\(\).__init__\("
    super_init_match = re.search(super_init_pattern, content)
    
    if not super_init_match:
        print(f"Error: Could not find super().__init__ call in {file_path}")
        return
    
    # Replace the super().__init__ call
    updated_content = updated_content.replace(
        "# Generate a unique actor ID if not provided\n        actor_id = f\"file_actor_{id(self)}\"\n\n        super().__init__(",
        "super().__init__("
    )
    
    # Find the actor_system reference
    actor_system_pattern = r"# Store the actor system reference\s+self\.actor_system = actor_system"
    actor_system_match = re.search(actor_system_pattern, updated_content)
    
    if not actor_system_match:
        print(f"Error: Could not find actor_system reference in {file_path}")
        return
    
    # Remove the actor_system reference
    updated_content = updated_content.replace(
        "# Store the actor system reference\n        self.actor_system = actor_system",
        ""
    )
    
    # Fix the metadata initialization
    updated_content = updated_content.replace(
        "self.metadata = None",
        "self.metadata = {}"
    )
    
    # Fix the actor_id assignment in handle_init_analysis
    actor_id_pattern = r"# Get file ID from payload if provided \(for actor pool\)\s+file_id = payload\.get\(\"file_id\"\)\s+if file_id:\s+self\.actor_id = file_id"
    actor_id_match = re.search(actor_id_pattern, updated_content)
    
    if actor_id_match:
        updated_content = updated_content.replace(
            actor_id_match.group(0),
            "# Get file ID from payload if provided (for actor pool)\n        file_id = payload.get(\"file_id\")\n        # Note: We can't set actor_id directly as it's read-only\n        # Just log it for now\n        if file_id and file_id != self.actor_id:\n            logger.warning(f\"File ID mismatch: payload has {file_id}, actor has {self.actor_id}\")"
        )
    
    with open(file_path, 'w') as f:
        f.write(updated_content)
    
    print(f"Fixed {file_path}")

def main() -> None:
    """Main function to apply all fixes."""
    print("Applying fixes to Vibe Check Actor System...")
    
    # Fix the file actor
    fix_file_actor(FILE_ACTOR_PATH)
    
    print("\nAll fixes applied. Please restart Vibe Check to apply the changes.")
    print("To revert the changes, restore the .bak files.")

if __name__ == "__main__":
    main()

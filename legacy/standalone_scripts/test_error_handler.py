"""
Test script for the error handler.
"""

import sys
from vibe_check.cli.error_handler import handle_analysis_error

# Create a test error result
test_error = {
    "error": "No actors were successfully initialized",
    "error_details": "Traceback (most recent call last):\n  File \"/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py\", line 936, in _initialize_actor_system\n    await self.start_actors()\n  File \"/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestrator.py\", line 343, in start_actors\n    await self.actor_lifecycle_manager.start_actors()\n  File \"/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py\", line 72, in start_actors\n    initialized_actors = await self._initialize_all_actors(initializer, fail_fast)\n                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/orchestration/actor_lifecycle_manager.py\", line 160, in _initialize_all_actors\n    raise RuntimeError(\"No actors were successfully initialized\")\nRuntimeError: No actors were successfully initialized\n"
}

# Handle the error
handle_analysis_error(test_error)

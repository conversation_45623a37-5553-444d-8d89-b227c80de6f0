import sys
print(sys.path)

try:
    import vibe_check
    print('vibe_check imported successfully')
    print(f'vibe_check path: {vibe_check.__path__}')
    
    try:
        from vibe_check.core.actor_system import ActorSystem
        print('ActorSystem imported successfully')
    except ImportError as e:
        print(f'Error importing ActorSystem: {e}')
        
except ImportError as e:
    print(f'Error importing vibe_check: {e}')

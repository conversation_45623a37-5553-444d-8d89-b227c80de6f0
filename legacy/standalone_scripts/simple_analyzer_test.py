#!/usr/bin/env python3
"""
Test script for the simple analyzer.
"""

import logging
import sys
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("simple_analyzer_test")

# Import analyzer components
from vibe_check.core.simple_analyzer import simple_analyze_project


def main() -> None:
    """Main function."""
    logger.info("Starting simple analyzer test")

    # Create a project analyzer
    logger.info("Creating project analyzer")
    # Use default config
    config = {
        "file_extensions": [".py"],
        "exclude_patterns": ["**/venv/**", "**/.git/**", "**/__pycache__/**"],
        "tools": {
            "ruff": {"enabled": True},
            "complexity": {"enabled": True}
        }
    }

    # Set up project path
    project_path = os.path.join(os.path.dirname(__file__), "test_project_new")
    logger.info(f"Project path: {project_path}")

    # Run the analysis
    logger.info("Running analysis")
    result = simple_analyze_project(
        project_path=project_path,
        config=config
    )
    logger.info("Analysis completed")

    # Print the result
    logger.info(f"Analysis result: {result}")

    logger.info("Test completed successfully")


if __name__ == "__main__":
    main()

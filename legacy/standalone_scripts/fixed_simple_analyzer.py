#!/usr/bin/env python3
"""
Fixed Simple Analyzer

This script provides a fixed version of the simple analyzer that actually runs the tools.
"""

import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Union
import json

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("fixed_simple_analyzer.log")
    ]
)
logger = logging.getLogger("fixed_simple_analyzer")

# Import analyzer components
from vibe_check.core.models import ProjectMetrics, FileMetrics, DirectoryMetrics
from vibe_check.tools.runners.tool_registry import list_available_tools, get_runner_for_tool
from vibe_check.core.utils.file_utils import find_python_files, count_lines


def fixed_analyze_project(
    project_path: Union[str, Path],
    output_dir: Optional[Union[str, Path]] = None,
    config: Optional[Dict[str, Any]] = None
) -> ProjectMetrics:
    """
    Analyze a project using a fixed simple analyzer that actually runs the tools.

    Args:
        project_path: Path to the project directory
        output_dir: Optional directory to write output files to
        config: Optional configuration dictionary

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info(f"Starting fixed analysis of project: {project_path}")
    start_time = time.time()

    # Convert paths to Path objects
    project_path = Path(project_path)
    if output_dir:
        output_dir = Path(output_dir)
        os.makedirs(output_dir, exist_ok=True)

    # Use default config if none provided
    if config is None:
        config = {}

    # Convert project_path to absolute path
    project_path = Path(project_path).absolute()

    # Find all Python files in the project
    python_files = find_python_files(project_path)
    logger.info(f"Found {len(python_files)} Python files")

    # Initialize metrics
    metrics = ProjectMetrics(
        project_path=str(project_path),
        files={},
        directories={}
    )

    # Get available tools
    available_tools = list_available_tools()
    logger.info(f"Available tools: {available_tools}")

    # Process each file
    total_complexity = 0.0
    total_issues = 0
    issue_summary = {}

    for file_path in python_files:
        rel_path = file_path.relative_to(project_path)
        logger.info(f"Analyzing file: {rel_path}")

        # Count lines
        line_count = count_lines(file_path)

        # Initialize file metrics
        file_metrics = FileMetrics(
            path=str(rel_path),
            name=file_path.name,
            lines=line_count,
            complexity=0,
            issues=[]
        )

        # Read file content
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            continue

        # Run tools on the file
        for tool_name in available_tools:
            # Skip tools that are not enabled in the config
            tool_config = config.get("tools", {}).get(tool_name, {})
            if not tool_config.get("enabled", False):
                logger.debug(f"Skipping disabled tool: {tool_name}")
                continue

            logger.info(f"Running {tool_name} on {rel_path}")
            runner = get_runner_for_tool(tool_name)
            if runner:
                try:
                    # Actually run the tool
                    result = runner.run(
                        file_path=file_path,
                        content=content,
                        args=tool_config.get("args", [])
                    )

                    if result:
                        # Add issues to file metrics
                        if hasattr(result, 'issues') and result.issues:
                            for issue in result.issues:
                                # Convert issue to a dictionary
                                issue_dict = {
                                    'line': getattr(issue, 'line', 0),
                                    'column': getattr(issue, 'column', 0),
                                    'message': getattr(issue, 'message', str(issue)),
                                    'code': getattr(issue, 'rule_id', getattr(issue, 'code', '')),
                                    'severity': getattr(issue, 'severity', 'medium'),
                                    'source': tool_name
                                }
                                file_metrics.issues.append(issue_dict)

                        # Update complexity if available
                        if hasattr(result, 'complexity') and result.complexity is not None:
                            # Convert to int if needed
                            complexity_value = int(result.complexity) if isinstance(result.complexity, float) else result.complexity
                            file_metrics.complexity = complexity_value
                            total_complexity += complexity_value

                        # Update issue count and summary
                        total_issues += len(getattr(result, 'issues', []))
                        for issue in getattr(result, 'issues', []):
                            # Handle both attribute and dictionary access for severity
                            severity = issue.get('severity', 'medium') if isinstance(issue, dict) else getattr(issue, 'severity', 'medium')
                            if severity not in issue_summary:
                                issue_summary[severity] = 0
                            issue_summary[severity] += 1
                except Exception as e:
                    logger.error(f"Error running {tool_name} on {rel_path}: {e}")
                    import traceback
                    logger.error(traceback.format_exc())

        # Add file metrics to project metrics
        metrics.files[str(rel_path)] = file_metrics

        # Update directory metrics
        dir_path = str(rel_path.parent)
        if dir_path not in metrics.directories:
            metrics.directories[dir_path] = DirectoryMetrics(path=dir_path)

        # Add file metrics to directory metrics
        dir_metrics = metrics.directories[dir_path]
        dir_metrics.add_file(file_metrics)

    # Log completion
    end_time = time.time()
    logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")
    logger.info(f"Total files: {len(metrics.files)}")
    logger.info(f"Total lines: {metrics.total_line_count}")
    logger.info(f"Average complexity: {metrics.avg_complexity:.2f}")
    logger.info(f"Total issues: {metrics._issue_count}")

    # Generate reports if output_dir is provided
    if output_dir:
        generate_reports(metrics, output_dir)

    return metrics


def generate_reports(metrics: ProjectMetrics, output_dir: Path) -> None:
    """
    Generate reports from the analysis results.

    Args:
        metrics: ProjectMetrics object with analysis results
        output_dir: Directory to write output files to
    """
    logger.info(f"Generating reports in {output_dir}")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Generate JSON report
    json_path = output_dir / "report.json"
    try:
        with open(json_path, 'w') as f:
            # Convert metrics to a dictionary
            metrics_dict = {
                "project_path": metrics.project_path,
                "files": {k: vars(v) for k, v in metrics.files.items()},
                "directories": {k: vars(v) for k, v in metrics.directories.items()},
                "issue_count": metrics._issue_count,
                "max_complexity": metrics._max_complexity
            }
            json.dump(metrics_dict, f, indent=2, default=str)
        logger.info(f"JSON report saved to {json_path}")
    except Exception as e:
        logger.error(f"Error generating JSON report: {e}")

    # Generate Markdown report
    md_path = output_dir / "report.md"
    try:
        with open(md_path, 'w') as f:
            f.write(f"# Project Analysis Report\n\n")
            f.write(f"**Project:** {metrics.project_path}\n\n")
            f.write(f"**Date:** {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")

            f.write(f"## Summary\n\n")
            f.write(f"- **Total Files:** {len(metrics.files)}\n")
            f.write(f"- **Total Directories:** {len(metrics.directories)}\n")
            f.write(f"- **Total Issues:** {metrics._issue_count}\n")
            f.write(f"- **Max Complexity:** {metrics._max_complexity}\n\n")

            f.write(f"## Files\n\n")
            f.write(f"| File | Lines | Issues | Complexity |\n")
            f.write(f"|------|-------|--------|------------|\n")
            for file_path, file_metrics in metrics.files.items():
                f.write(f"| {file_path} | {file_metrics.lines} | {len(file_metrics.issues)} | {file_metrics.complexity} |\n")

            f.write(f"\n## Issues\n\n")
            for file_path, file_metrics in metrics.files.items():
                if file_metrics.issues:
                    f.write(f"### {file_path}\n\n")
                    f.write(f"| Line | Column | Type | Message |\n")
                    f.write(f"|------|--------|------|--------|\n")
                    for issue in file_metrics.issues:
                        f.write(f"| {issue.line} | {issue.column} | {issue.type} | {issue.message} |\n")
                    f.write(f"\n")
        logger.info(f"Markdown report saved to {md_path}")
    except Exception as e:
        logger.error(f"Error generating Markdown report: {e}")

    # Generate HTML report
    html_path = output_dir / "report.html"
    try:
        with open(html_path, 'w') as f:
            f.write(f"""<!DOCTYPE html>
<html>
<head>
    <title>Project Analysis Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        h1, h2, h3 {{ color: #333; }}
        table {{ border-collapse: collapse; width: 100%; }}
        th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
        th {{ background-color: #f2f2f2; }}
        tr:nth-child(even) {{ background-color: #f9f9f9; }}
    </style>
</head>
<body>
    <h1>Project Analysis Report</h1>
    <p><strong>Project:</strong> {metrics.project_path}</p>
    <p><strong>Date:</strong> {time.strftime('%Y-%m-%d %H:%M:%S')}</p>

    <h2>Summary</h2>
    <ul>
        <li><strong>Total Files:</strong> {len(metrics.files)}</li>
        <li><strong>Total Directories:</strong> {len(metrics.directories)}</li>
        <li><strong>Total Issues:</strong> {metrics._issue_count}</li>
        <li><strong>Max Complexity:</strong> {metrics._max_complexity}</li>
    </ul>

    <h2>Files</h2>
    <table>
        <tr>
            <th>File</th>
            <th>Lines</th>
            <th>Issues</th>
            <th>Complexity</th>
        </tr>
""")
            for file_path, file_metrics in metrics.files.items():
                f.write(f"""        <tr>
            <td>{file_path}</td>
            <td>{file_metrics.lines}</td>
            <td>{len(file_metrics.issues)}</td>
            <td>{file_metrics.complexity}</td>
        </tr>
""")

            f.write(f"""    </table>

    <h2>Issues</h2>
""")
            for file_path, file_metrics in metrics.files.items():
                if file_metrics.issues:
                    f.write(f"""    <h3>{file_path}</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Column</th>
            <th>Type</th>
            <th>Message</th>
        </tr>
""")
                    for issue in file_metrics.issues:
                        f.write(f"""        <tr>
            <td>{issue.line}</td>
            <td>{issue.column}</td>
            <td>{issue.type}</td>
            <td>{issue.message}</td>
        </tr>
""")
                    f.write(f"""    </table>
""")

            f.write(f"""</body>
</html>
""")
        logger.info(f"HTML report saved to {html_path}")
    except Exception as e:
        logger.error(f"Error generating HTML report: {e}")


def main() -> None:
    """Main function."""
    try:
        # Get the project to analyze
        if len(sys.argv) > 1:
            project_path = sys.argv[1]
        else:
            # Default to analyzing a small test project
            project_path = "test_project_with_issues"

        # Get the output directory
        if len(sys.argv) > 2:
            output_dir = sys.argv[2]
        else:
            # Default output directory
            output_dir = f"./analysis_results/fixed_{Path(project_path).name}"

        # Get the preset
        if len(sys.argv) > 3:
            preset = sys.argv[3]
        else:
            # Default preset
            preset = "security"

        logger.info(f"Starting analysis of {project_path}")
        logger.info(f"Output directory: {output_dir}")
        logger.info(f"Preset: {preset}")

        # Load preset configuration
        preset_path = Path(f"./vibe_check/config/presets/{preset}.yaml")
        if preset_path.exists():
            import yaml
            with open(preset_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info(f"Loaded preset configuration: {preset}")
        else:
            logger.warning(f"Preset not found: {preset}")
            config = {
                "tools": {
                    "ruff": {"enabled": True, "args": ["--select=E,F,W,S"]},
                    "bandit": {"enabled": True, "args": ["--recursive"]},
                    "complexity": {"enabled": True, "threshold": 10}
                }
            }

        # Run the analysis
        metrics = fixed_analyze_project(
            project_path=project_path,
            output_dir=output_dir,
            config=config
        )

        logger.info("Analysis completed successfully")
        logger.info(f"Results saved to {output_dir}")

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Vibe Check Fix Script

This script fixes issues with the Vibe Check tool integration, specifically
addressing the problem where security issues detected by Bandit are not being
properly added to the file metrics.

Usage:
    python vibe_check_fix.py

The script will create backup files of the modified files and apply the fixes.
"""

import os
import shutil
import re
from pathlib import Path
from typing import List, Dict, Any, Optional, Union

# Paths to files that need to be fixed
TOOL_ACTOR_PATH = "vibe_check/core/actor_system/actors/tool_actor.py"
BANDIT_RUNNER_PATH = "vibe_check/tools/runners/bandit_runner.py"
BANDIT_PARSER_PATH = "vibe_check/tools/parsers/bandit_parser.py"
FILE_ACTOR_PATH = "vibe_check/core/actor_system/actors/file_actor.py"

def backup_file(file_path: str) -> None:
    """
    Create a backup of a file before modifying it.
    
    Args:
        file_path: Path to the file to backup
    """
    backup_path = f"{file_path}.bak"
    if os.path.exists(file_path):
        shutil.copy2(file_path, backup_path)
        print(f"Created backup: {backup_path}")

def fix_tool_actor(file_path: str) -> None:
    """
    Fix the tool actor to properly process bandit results.
    
    Args:
        file_path: Path to the tool actor file
    """
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found")
        return
    
    backup_file(file_path)
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the _extract_file_metrics method
    extract_metrics_pattern = r"def _extract_file_metrics\(self, file_path: .*?\):"
    extract_metrics_match = re.search(extract_metrics_pattern, content, re.DOTALL)
    
    if not extract_metrics_match:
        print(f"Error: Could not find _extract_file_metrics method in {file_path}")
        return
    
    # Find the bandit section in the method
    bandit_section_pattern = r"# Extract security metrics if available\s+if \"bandit\" in results:.*?(?=\n\s+# |\n\s+return)"
    bandit_section_match = re.search(bandit_section_pattern, content, re.DOTALL)
    
    if not bandit_section_match:
        print(f"Error: Could not find bandit section in _extract_file_metrics method in {file_path}")
        return
    
    # Replace the bandit section with the fixed version
    fixed_bandit_section = """            # Extract security metrics if available
            if "bandit" in results:
                bandit_result = results["bandit"]
                security_score = bandit_result.get("security_score", 100)
                metrics.security_score = security_score

                # Add issue counts
                issues = bandit_result.get("issues", [])
                metrics.security_issues = len(issues)
                metrics.high_severity_issues = sum(1 for i in issues if i.get("severity") == "HIGH")
                
                # Add issues to file metrics
                for issue in issues:
                    metrics.issues.append({
                        "code": issue.get("code", ""),
                        "message": issue.get("message", ""),
                        "line": issue.get("line", 0),
                        "severity": issue.get("severity", "MEDIUM"),
                        "tool": "bandit",
                        "type": "security"
                    })"""
    
    updated_content = content.replace(bandit_section_match.group(0), fixed_bandit_section)
    
    with open(file_path, 'w') as f:
        f.write(updated_content)
    
    print(f"Fixed {file_path}")

def fix_bandit_runner(file_path: str) -> None:
    """
    Fix the bandit runner to properly handle non-zero return codes.
    
    Args:
        file_path: Path to the bandit runner file
    """
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found")
        return
    
    backup_file(file_path)
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the section that handles the return code
    return_code_pattern = r"# Parse the output\s+if process_result\[\"returncode\"\] == 0:"
    return_code_match = re.search(return_code_pattern, content)
    
    if not return_code_match:
        print(f"Error: Could not find return code handling in {file_path}")
        return
    
    # Replace the return code handling with the fixed version
    fixed_return_code = """                # Parse the output
                # Note: Bandit returns non-zero when issues are found, which is expected
                if process_result["returncode"] == 0 or process_result["stdout"].strip() == "":"""
    
    updated_content = content.replace(return_code_match.group(0), fixed_return_code)
    
    with open(file_path, 'w') as f:
        f.write(updated_content)
    
    print(f"Fixed {file_path}")

def fix_file_actor(file_path: str) -> None:
    """
    Fix the file actor to properly update file metrics from bandit results.
    
    Args:
        file_path: Path to the file actor file
    """
    if not os.path.exists(file_path):
        print(f"Error: {file_path} not found")
        return
    
    backup_file(file_path)
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Find the _update_metrics_from_tool_result method
    update_metrics_pattern = r"async def _update_metrics_from_tool_result\(self, tool_name: str, result: Dict\[str, Any\]\) -> None:"
    update_metrics_match = re.search(update_metrics_pattern, content)
    
    if not update_metrics_match:
        print(f"Error: Could not find _update_metrics_from_tool_result method in {file_path}")
        return
    
    # Find the bandit section in the method
    bandit_section_pattern = r"elif tool_name == \"bandit\":.*?(?=\n\s+elif|\n\s+else)"
    bandit_section_match = re.search(bandit_section_pattern, content, re.DOTALL)
    
    if not bandit_section_match:
        print(f"Error: Could not find bandit section in _update_metrics_from_tool_result method in {file_path}")
        return
    
    # Replace the bandit section with the fixed version
    fixed_bandit_section = """        elif tool_name == "bandit":
            # Extract security issues from bandit results
            security_issues = result.get("issues", [])
            high_issues = sum(1 for i in security_issues if i.get("severity") == "HIGH")
            med_issues = sum(1 for i in security_issues if i.get("severity") == "MEDIUM")
            # Store security metrics
            self.file_metrics.security_issues = {
                "high": high_issues,
                "medium": med_issues,
                "total": len(security_issues)
            }
            
            # Add issues to file metrics
            for issue in security_issues:
                self.file_metrics.add_issue(
                    code=issue.get("code", ""),
                    message=issue.get("message", ""),
                    line=issue.get("line", 0),
                    severity=issue.get("severity", "MEDIUM")
                )"""
    
    updated_content = content.replace(bandit_section_match.group(0), fixed_bandit_section)
    
    with open(file_path, 'w') as f:
        f.write(updated_content)
    
    print(f"Fixed {file_path}")

def main() -> None:
    """Main function to apply all fixes."""
    print("Applying fixes to Vibe Check...")
    
    # Fix the tool actor
    fix_tool_actor(TOOL_ACTOR_PATH)
    
    # Fix the bandit runner
    fix_bandit_runner(BANDIT_RUNNER_PATH)
    
    # Fix the file actor
    fix_file_actor(FILE_ACTOR_PATH)
    
    print("\nAll fixes applied. Please restart Vibe Check to apply the changes.")
    print("To revert the changes, restore the .bak files.")

if __name__ == "__main__":
    main()

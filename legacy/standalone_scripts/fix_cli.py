#!/usr/bin/env python3
"""
Fix the CLI to handle actor system errors.
"""

import os
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler("fix_cli.log")
    ]
)
logger = logging.getLogger("fix_cli")

# Get the path to the main.py file
main_py_path = os.path.join(os.path.abspath("."), "vibe_check", "cli", "main.py")
logger.info(f"Path to main.py: {main_py_path}")

# Read the file
with open(main_py_path, "r") as f:
    content = f.read()
    logger.info(f"Read {len(content)} bytes from {main_py_path}")

# Check if the file contains the error handler
if "from .error_handler import handle_analysis_error" in content:
    logger.info("Error handler import found in main.py")
else:
    logger.info("Error handler import not found in main.py")

# Check if the file contains the format_analysis_results function
if "def format_analysis_results(results: Any) -> str:" in content:
    logger.info("format_analysis_results function found in main.py")
else:
    logger.info("format_analysis_results function not found in main.py")

# Check if the file contains the error handling code
if "if isinstance(results, dict) and \"error\" in results:" in content:
    logger.info("Error handling code found in main.py")
else:
    logger.info("Error handling code not found in main.py")

# Fix the format_analysis_results function
new_content = content.replace(
    """def format_analysis_results(results: Any) -> str:
    \"\"\"
    Format analysis results for display.

    Args:
        results: Analysis results

    Returns:
        Formatted results string
    \"\"\"
    # Set up logger
    logger = logging.getLogger("vibe_check_cli.formatter")
    output = []

    # Check if results is a dictionary with an error
    if isinstance(results, dict) and "error" in results:
        output.append("=== Analysis Error ===")
        output.append(f"Error: {results['error']}")
        if "error_details" in results:
            output.append("\\nError Details:")
            output.append(results["error_details"])

        # Add suggestions for fixing the error
        output.append("\\nSuggestions:")

        # Check for specific error types
        error_msg = results.get("error", "").lower()

        if "no actors were successfully initialized" in error_msg:
            output.append("- The actor system failed to initialize. Try the following:")
            output.append("  * Use the --use-simple-analyzer flag to bypass the actor system")
            output.append("  * Run with --debug-actor-system to get more detailed logs")
            output.append("  * Check the log file for more details")
        elif "timeout" in error_msg:
            output.append("- The operation timed out. Try the following:")
            output.append("  * Increase the timeout in the configuration")
            output.append("  * Use the --use-simple-analyzer flag for smaller projects")
            output.append("  * Run with fewer parallel actors")
        elif "registry" in error_msg:
            output.append("- There was an issue with the actor registry. Try the following:")
            output.append("  * Restart the application")
            output.append("  * Use the --use-simple-analyzer flag to bypass the actor system")
        else:
            output.append("- Try using the --use-simple-analyzer flag to bypass the actor system")
            output.append("- Run with --debug or --debug-actor-system for more detailed logs")
            output.append("- Check the log file for more details")

        # Add diagnostics information if available
        if "diagnostics" in results:
            output.append("\\nDiagnostics Information:")
            for key, value in results["diagnostics"].items():
                output.append(f"- {key}: {value}")

        # Add visualizations information if available
        if "visualizations" in results:
            output.append("\\nVisualizations:")
            for key, value in results["visualizations"].items():
                output.append(f"- {key}: {value}")

        return "\\n".join(output)""",
    """def format_analysis_results(results: Any) -> str:
    \"\"\"
    Format analysis results for display.

    Args:
        results: Analysis results

    Returns:
        Formatted results string
    \"\"\"
    # Set up logger
    logger = logging.getLogger("vibe_check_cli.formatter")
    output = []

    # Check if results is a dictionary with an error
    if isinstance(results, dict) and "error" in results:
        output.append("=== Analysis Error ===")
        output.append(f"Error: {results['error']}")
        if "error_details" in results:
            output.append("\\nError Details:")
            output.append(results["error_details"])

        # Add suggestions for fixing the error
        output.append("\\nSuggestions:")

        # Check for specific error types
        error_msg = results.get("error", "").lower()

        if "no actors were successfully initialized" in error_msg:
            output.append("- The actor system failed to initialize. Try the following:")
            output.append("  * Use the --use-simple-analyzer flag to bypass the actor system")
            output.append("  * Run with --debug-actor-system to get more detailed logs")
            output.append("  * Check the log file for more details")
        elif "timeout" in error_msg:
            output.append("- The operation timed out. Try the following:")
            output.append("  * Increase the timeout in the configuration")
            output.append("  * Use the --use-simple-analyzer flag for smaller projects")
            output.append("  * Run with fewer parallel actors")
        elif "registry" in error_msg:
            output.append("- There was an issue with the actor registry. Try the following:")
            output.append("  * Restart the application")
            output.append("  * Use the --use-simple-analyzer flag to bypass the actor system")
        else:
            output.append("- Try using the --use-simple-analyzer flag to bypass the actor system")
            output.append("- Run with --debug or --debug-actor-system for more detailed logs")
            output.append("- Check the log file for more details")

        # Add diagnostics information if available
        if "diagnostics" in results:
            output.append("\\nDiagnostics Information:")
            for key, value in results["diagnostics"].items():
                output.append(f"- {key}: {value}")

        # Add visualizations information if available
        if "visualizations" in results:
            output.append("\\nVisualizations:")
            for key, value in results["visualizations"].items():
                output.append(f"- {key}: {value}")

        return "\\n".join(output)
        
    # Check if results is empty or None
    if results is None or (isinstance(results, dict) and not results):
        output.append("=== Analysis Error ===")
        output.append("Error: No results were returned from the analysis")
        output.append("\\nSuggestions:")
        output.append("- Try using the --use-simple-analyzer flag to bypass the actor system")
        output.append("- Run with --debug or --debug-actor-system for more detailed logs")
        output.append("- Check the log file for more details")
        return "\\n".join(output)"""
)

# Fix the analyze function
new_content = new_content.replace(
    """        # Check if there was an error
        if isinstance(results, dict) and "error" in results:
            logger.error(f"Analysis failed: {results['error']}")
            sys.exit(1)""",
    """        # Check if there was an error
        if isinstance(results, dict) and "error" in results:
            logger.error(f"Analysis failed: {results['error']}")
            # Format and display the error
            formatted_results = format_analysis_results(results)
            click.echo(formatted_results)
            sys.exit(1)"""
)

# Write the file
with open(main_py_path, "w") as f:
    f.write(new_content)
    logger.info(f"Wrote {len(new_content)} bytes to {main_py_path}")

logger.info("CLI fixed successfully")

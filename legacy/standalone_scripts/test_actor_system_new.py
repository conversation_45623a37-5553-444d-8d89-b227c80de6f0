#!/usr/bin/env python3
"""
Test script for the actor system.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_actor_system")

# Import actor system components
from vibe_check.core.actor_system.logging.enhanced_logger import setup_actor_system_logging
from vibe_check.core.actor_system.actor_system import ActorSystem
from vibe_check.core.actor_system.actor import Actor
from vibe_check.core.actor_system.message import Message, MessageType


class TestActor(Actor):
    """A simple test actor."""

    async def _initialize(self, config):
        """Initialize the actor."""
        logger.info(f"Initializing TestActor {self.actor_id}")
        # No sleep to avoid timeouts
        logger.info(f"TestActor {self.actor_id} initialized")
        return  # Explicitly return to ensure completion

    async def _start(self):
        """Start the actor."""
        logger.info(f"Starting TestActor {self.actor_id}")
        # No sleep to avoid timeouts
        logger.info(f"TestActor {self.actor_id} started")
        return  # Explicitly return to ensure completion

    async def _handle_message(self, message):
        """Handle a message."""
        logger.info(f"TestActor {self.actor_id} handling message: {message.type.name}")
        # No sleep to avoid timeouts
        logger.info(f"TestActor {self.actor_id} handled message: {message.type.name}")
        return  # Explicitly return to ensure completion


async def main() -> None:
    """Main function."""
    logger.info("Starting test")

    # Configure actor system logging
    setup_actor_system_logging(debug_mode=True)

    # Create actor system
    logger.info("Creating actor system")
    system = ActorSystem()
    logger.info("Actor system created")

    try:
        # Create test actors
        logger.info("Creating test actors")
        actor1 = TestActor("test_actor_1")
        actor2 = TestActor("test_actor_2")

        # Register actors with the system
        logger.info("Registering actors with the system")
        system.register_actor(actor1)
        system.register_actor(actor2)

        # Initialize the actors with a shorter timeout
        logger.info("Initializing the actors")
        await actor1.initialize(timeout=5.0)  # Use a shorter timeout
        await actor2.initialize(timeout=5.0)  # Use a shorter timeout

        # Start the system
        logger.info("Starting the system")
        await system.start()

        # Send some messages
        logger.info("Sending messages")
        message1 = Message(
            sender_id="test",
            recipient_id="test_actor_1",
            type=MessageType.TEST,  # Using TEST message type
            payload={"command": "test"}
        )
        message2 = Message(
            sender_id="test",
            recipient_id="test_actor_2",
            type=MessageType.TEST,  # Using TEST message type
            payload={"command": "test"}
        )

        # Send messages through the actor system
        await system.send_message(message1)
        await system.send_message(message2)

        # Wait for messages to be processed
        logger.info("Waiting for messages to be processed")
        await asyncio.sleep(2)

        # Stop the system
        logger.info("Stopping the system")
        await system.stop()

        logger.info("Test completed successfully")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    asyncio.run(main())

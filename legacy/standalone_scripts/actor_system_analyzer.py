#!/usr/bin/env python3
"""
Test script for the actor system analyzer.
"""

import asyncio
import logging
import sys
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("actor_system_analyzer")

# Import actor system components
from vibe_check.core.actor_system.logging.enhanced_logger import setup_actor_system_logging
from vibe_check.core.actor_system.actor_system import ActorSystem
from vibe_check.core.actor_system.actor import Actor
from vibe_check.core.actor_system.message import Message, MessageType

# Import analyzer components
from vibe_check.core.analyzer.project_analyzer import ProjectAnalyzer
from vibe_check.core.config.config_manager import ConfigManager


async def main() -> None:
    """Main function."""
    logger.info("Starting actor system analyzer test")
    
    # Configure actor system logging
    setup_actor_system_logging(debug_mode=True)
    
    # Create actor system
    logger.info("Creating actor system")
    system = ActorSystem()
    logger.info("Actor system created")
    
    try:
        # Create a project analyzer
        logger.info("Creating project analyzer")
        config_manager = ConfigManager()
        config = config_manager.load_preset("minimal")
        
        # Set up project path
        project_path = os.path.join(os.path.dirname(__file__), "test_project_new")
        logger.info(f"Project path: {project_path}")
        
        # Create analyzer
        analyzer = ProjectAnalyzer(
            project_path=project_path,
            config=config,
            actor_system=system
        )
        
        # Start the system
        logger.info("Starting the system")
        await system.start()
        logger.info("System started")
        
        # Run the analysis
        logger.info("Running analysis")
        result = await analyzer.analyze()
        logger.info("Analysis completed")
        
        # Print the result
        logger.info(f"Analysis result: {result}")
        
        # Wait a bit
        logger.info("Waiting...")
        await asyncio.sleep(1)
        
        # Stop the system
        logger.info("Stopping the system")
        await system.stop()
        logger.info("System stopped")
        
        logger.info("Test completed successfully")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python3
"""
Simple test script for the actor system with actor registration.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("simple_actor_test")

# Import actor system components
from vibe_check.core.actor_system.logging.enhanced_logger import setup_actor_system_logging
from vibe_check.core.actor_system.actor_system import ActorSystem
from vibe_check.core.actor_system.actor import Actor
from vibe_check.core.actor_system.message import Message, MessageType


class SimpleActor(Actor):
    """A simple actor that does nothing."""
    
    async def _initialize(self, config):
        """Initialize the actor."""
        logger.info(f"Initializing SimpleActor {self.actor_id}")
        # No sleep to avoid timeouts
        logger.info(f"SimpleActor {self.actor_id} initialized")
        return  # Explicitly return to ensure completion
    
    async def _start(self):
        """Start the actor."""
        logger.info(f"Starting SimpleActor {self.actor_id}")
        # No sleep to avoid timeouts
        logger.info(f"SimpleActor {self.actor_id} started")
        return  # Explicitly return to ensure completion
    
    async def _handle_message(self, message):
        """Handle a message."""
        logger.info(f"SimpleActor {self.actor_id} handling message: {message.type.name}")
        # No sleep to avoid timeouts
        logger.info(f"SimpleActor {self.actor_id} handled message: {message.type.name}")
        return  # Explicitly return to ensure completion


async def main() -> None:
    """Main function."""
    logger.info("Starting test")
    
    # Configure actor system logging
    setup_actor_system_logging(debug_mode=True)
    
    # Create actor system
    logger.info("Creating actor system")
    system = ActorSystem()
    logger.info("Actor system created")
    
    try:
        # Create a simple actor
        logger.info("Creating simple actor")
        actor = SimpleActor("simple_actor")
        
        # Register the actor with the system
        logger.info("Registering actor with the system")
        system.register_actor(actor)
        
        # Start the system
        logger.info("Starting the system")
        await system.start()
        logger.info("System started")
        
        # Wait a bit
        logger.info("Waiting...")
        await asyncio.sleep(1)
        
        # Stop the system
        logger.info("Stopping the system")
        await system.stop()
        logger.info("System stopped")
        
        logger.info("Test completed successfully")
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        raise


if __name__ == "__main__":
    asyncio.run(main())

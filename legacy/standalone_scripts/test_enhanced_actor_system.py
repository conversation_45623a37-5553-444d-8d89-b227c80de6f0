#!/usr/bin/env python3
"""
Test script for the enhanced actor system components.

This script tests the new dependency injection, diagnostics, and initialization
components of the actor system.
"""

import asyncio
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)

logger = logging.getLogger("enhanced_actor_system_test")

# Import the necessary components
from vibe_check.core.actor_system.dependency_injection import (
    DependencyContainer,
    get_container,
    reset_container,
    Component,
    ComponentScope,
    Injectable
)
from vibe_check.core.actor_system.diagnostics.enhanced_tracker import (
    EnhancedTracker,
    DiagnosticCategory,
    DiagnosticLevel,
    DiagnosticEvent
)
# Import from consolidated_initializer instead of initialization
from vibe_check.core.actor_system.actor_state import ActorState
from vibe_check.core.actor_system.consolidated_initializer import (
    ConsolidatedActorInitializer,
    get_initializer,
    reset_initializer
)
from vibe_check.core.actor_system.initialization.dependency_resolver import (
    DependencyResolver,
    get_resolver,
    reset_resolver
)
from vibe_check.core.actor_system.initialization.synchronization import (
    SynchronizationManager,
    SynchronizationPoint
)

# Import with deprecation warning
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)


@Component(scope=ComponentScope.SINGLETON)
class TestService:
    """A test service for dependency injection."""

    def __init__(self, name: str = "TestService"):
        """Initialize the service."""
        self.name = name
        logger.info(f"Created {self.name}")

    def get_name(self) -> str:
        """Get the name of the service."""
        return self.name


@Component(scope=ComponentScope.TRANSIENT)
class TransientService:
    """A transient test service for dependency injection."""

    def __init__(self, name: str = "TransientService"):
        """Initialize the service."""
        self.name = name
        self.created_at = time.time()
        logger.info(f"Created {self.name} at {self.created_at}")

    def get_name(self) -> str:
        """Get the name of the service."""
        return self.name


@Injectable()
@Component(scope=ComponentScope.SINGLETON)
class DependentService:
    """A service that depends on another service."""

    def __init__(self, test_service: TestService):
        """Initialize the service."""
        self.test_service = test_service
        logger.info(f"Created DependentService with dependency on {test_service.get_name()}")

    def get_dependency_name(self) -> str:
        """Get the name of the dependency."""
        return self.test_service.get_name()


async def test_dependency_injection() -> None:
    """Test the dependency injection framework."""
    logger.info("Testing dependency injection")

    # Reset the container
    reset_container()
    container = get_container()

    # Register components
    container.register(TestService, scope=ComponentScope.SINGLETON)
    container.register(TransientService, scope=ComponentScope.TRANSIENT)
    container.register(DependentService, scope=ComponentScope.SINGLETON)

    # Resolve components
    test_service = container.resolve(TestService)
    logger.info(f"Resolved {test_service.get_name()}")

    # Resolve the same component again (should be the same instance)
    test_service2 = container.resolve(TestService)
    logger.info(f"Resolved {test_service2.get_name()} again")

    # Check that they are the same instance
    assert test_service is test_service2
    logger.info("Singleton instances are the same")

    # Resolve transient components
    transient1 = container.resolve(TransientService)
    transient2 = container.resolve(TransientService)

    # Check that they are different instances
    assert transient1 is not transient2
    logger.info("Transient instances are different")

    # Resolve dependent service
    dependent = container.resolve(DependentService)
    logger.info(f"Resolved DependentService with dependency {dependent.get_dependency_name()}")

    # Check that the dependency is the same instance
    assert dependent.test_service is test_service
    logger.info("Dependency is the same instance")

    logger.info("Dependency injection tests passed")


async def test_enhanced_tracker() -> None:
    """Test the enhanced diagnostics tracker."""
    logger.info("Testing enhanced diagnostics tracker")

    # Create a tracker
    tracker = EnhancedTracker(output_dir="./test_output")

    # Record some events
    await tracker.record_event(
        category=DiagnosticCategory.INITIALIZATION,
        level=DiagnosticLevel.BASIC,
        actor_id="test_actor_1",
        event_type="initialized",
        details={"message": "Actor initialized successfully"}
    )

    await tracker.record_event(
        category=DiagnosticCategory.STATE,
        level=DiagnosticLevel.BASIC,
        actor_id="test_actor_1",
        event_type="ready",
        details={"message": "Actor is ready"}
    )

    await tracker.record_event(
        category=DiagnosticCategory.MESSAGING,
        level=DiagnosticLevel.DETAILED,
        actor_id="test_actor_1",
        event_type="message_sent",
        details={"recipient": "test_actor_2", "message_type": "TEST"}
    )

    await tracker.record_event(
        category=DiagnosticCategory.ERROR,
        level=DiagnosticLevel.BASIC,
        actor_id="test_actor_2",
        event_type="initialization_failed",
        details={"message": "Failed to initialize"},
        error=Exception("Test error")
    )

    # Record a dependency
    await tracker.record_dependency("test_actor_2", "test_actor_1")

    # Get actor state
    state = await tracker.get_actor_state("test_actor_1")
    logger.info(f"Actor state: {state}")

    # Get actor errors
    errors = await tracker.get_actor_errors("test_actor_2")
    logger.info(f"Actor errors: {errors}")

    # Get diagnostic summary
    summary = await tracker.get_diagnostic_summary()
    logger.info(f"Diagnostic summary: {summary}")

    # Save diagnostics
    filepath = await tracker.save_diagnostics()
    logger.info(f"Saved diagnostics to {filepath}")

    # Visualize initialization sequence
    viz_path = await tracker.visualize_initialization_sequence()
    logger.info(f"Saved visualization to {viz_path}")

    logger.info("Enhanced tracker tests passed")


async def test_dependency_resolver() -> None:
    """Test the dependency resolver."""
    logger.info("Testing dependency resolver")

    # Reset the resolver
    reset_resolver()
    resolver = get_resolver()

    # Register some dependencies
    await resolver.register_dependency("actor_1", "actor_2")
    await resolver.register_dependency("actor_1", "actor_3")
    await resolver.register_dependency("actor_2", "actor_3")

    # Get dependencies
    deps = await resolver.get_dependencies("actor_1")
    logger.info(f"Dependencies of actor_1: {deps}")

    # Get dependents
    dependents = await resolver.get_dependents("actor_3")
    logger.info(f"Dependents of actor_3: {dependents}")

    # Get initialization order
    order = await resolver.get_initialization_order()
    logger.info(f"Initialization order: {order}")

    # Try to create a circular dependency
    try:
        await resolver.register_dependency("actor_3", "actor_1")
        logger.error("Failed to detect circular dependency")
    except Exception as e:
        logger.info(f"Correctly detected circular dependency: {e}")

    # Clear dependencies
    await resolver.clear()

    # Register dependencies again
    await resolver.register_dependency("actor_1", "actor_2", critical=True)
    await resolver.register_dependency("actor_1", "actor_3", critical=False)

    # Get critical and optional dependencies
    critical = await resolver.get_critical_dependencies("actor_1")
    optional = await resolver.get_optional_dependencies("actor_1")
    logger.info(f"Critical dependencies of actor_1: {critical}")
    logger.info(f"Optional dependencies of actor_1: {optional}")

    logger.info("Dependency resolver tests passed")


async def test_synchronization_manager() -> None:
    """Test the synchronization manager."""
    logger.info("Testing synchronization manager")

    # Create a synchronization manager
    sync_manager = SynchronizationManager()

    # Create a synchronization point
    point = await sync_manager.create_point(
        "test_point",
        required_actors={"actor_1", "actor_2", "actor_3"}
    )

    # Mark that some actors have reached the point
    await sync_manager.reach_point("test_point", "actor_1")
    await sync_manager.reach_point("test_point", "actor_2")

    # Check if the point is complete
    complete = point.is_complete()
    logger.info(f"Point complete: {complete}")

    # Get missing actors
    missing = point.get_missing_actors()
    logger.info(f"Missing actors: {missing}")

    # Create a task to wait for the point
    async def wait_task() -> None:
        logger.info("Waiting for synchronization point")
        result = await sync_manager.wait_for_point("test_point", timeout=5.0)
        logger.info(f"Wait result: {result}")

    # Start the wait task
    wait_task_obj = asyncio.create_task(wait_task())

    # Wait a bit
    await asyncio.sleep(1.0)

    # Mark that the last actor has reached the point
    await sync_manager.reach_point("test_point", "actor_3")

    # Wait for the wait task to complete
    await wait_task_obj

    # Check if the point is complete now
    complete = point.is_complete()
    logger.info(f"Point complete: {complete}")

    # Get completion time
    completion_time = point.get_completion_time()
    logger.info(f"Completion time: {completion_time} seconds")

    # Clear all points
    await sync_manager.clear()

    logger.info("Synchronization manager tests passed")


async def test_actor_initializer() -> None:
    """Test the actor initializer."""
    logger.info("Testing actor initializer (ConsolidatedActorInitializer)")

    # Reset the initializer
    reset_initializer()
    initializer = get_initializer()

    # Register some actors
    await initializer.register_actor("actor_1", actor_type="test_actor", tags={"test"})
    await initializer.register_actor("actor_2", actor_type="test_actor", tags={"test"})
    await initializer.register_actor("actor_3", actor_type="test_actor", tags={"test"})

    # Set actor states
    await initializer.set_actor_state(
        actor_id="actor_1",
        state=ActorState.INITIALIZING,
        error=None,
        phase="test",
        metadata={"message": "Starting initialization"}
    )

    await initializer.set_actor_state(
        actor_id="actor_1",
        state=ActorState.INITIALIZED,
        error=None,
        phase="test",
        metadata={"message": "Initialization complete"}
    )

    await initializer.set_actor_state(
        actor_id="actor_2",
        state=ActorState.INITIALIZING,
        error=None,
        phase="test",
        metadata={"message": "Starting initialization"}
    )

    # Try an invalid state transition
    try:
        await initializer.set_actor_state(
            actor_id="actor_2",
            state=ActorState.READY,
            error=None,
            phase="test",
            metadata={"message": "Invalid transition"}
        )
        logger.error("Failed to detect invalid state transition")
    except Exception as e:
        logger.info(f"Correctly detected invalid state transition: {e}")

    # Set more states
    await initializer.set_actor_state(
        actor_id="actor_2",
        state=ActorState.INITIALIZED,
        error=None,
        phase="test",
        metadata={"message": "Initialization complete"}
    )

    await initializer.set_actor_state(
        actor_id="actor_3",
        state=ActorState.INITIALIZING,
        error=None,
        phase="test",
        metadata={"message": "Starting initialization"}
    )

    await initializer.set_actor_state(
        actor_id="actor_3",
        state=ActorState.FAILED,
        error=Exception("Test error"),
        phase="test",
        metadata={"message": "Initialization failed"}
    )

    # Get actor states directly from the internal state
    with initializer._state_lock:
        state_1 = initializer._actor_states.get("actor_1")
        state_2 = initializer._actor_states.get("actor_2")
        state_3 = initializer._actor_states.get("actor_3")

    logger.info(f"Actor 1 state: {state_1.name if state_1 else 'None'}")
    logger.info(f"Actor 2 state: {state_2.name if state_2 else 'None'}")
    logger.info(f"Actor 3 state: {state_3.name if state_3 else 'None'}")

    # Get actor details directly from the internal state
    with initializer._state_lock:
        details_1 = initializer._actor_details.get("actor_1", {})
    logger.info(f"Actor 1 details: {details_1}")

    # Get actor errors directly from the internal state
    with initializer._state_lock:
        errors_3 = initializer._initialization_errors.get("actor_3")
    logger.info(f"Actor 3 errors: {errors_3}")

    # Wait for actor initialized
    result = await initializer.wait_for_actor_initialized("actor_1", timeout=1.0)
    logger.info(f"Wait for actor 1 initialized: {result}")

    # Register resources
    cleanup_func = lambda: logger.info("Cleanup resource for actor 1")
    # Note: register_resource is not async and takes different parameters
    initializer.register_resource(
        actor_id="actor_1",
        cleanup_func=cleanup_func,
        args=[],
        kwargs={}
    )

    # Cleanup actor
    await initializer.cleanup_actor("actor_1")

    logger.info("Actor initializer tests passed")


async def main() -> None:
    """Run the tests."""
    try:
        # Create output directory
        Path("./test_output").mkdir(exist_ok=True)

        # Test dependency injection
        await test_dependency_injection()

        # Test enhanced tracker
        await test_enhanced_tracker()

        # Test dependency resolver
        await test_dependency_resolver()

        # Test synchronization manager
        await test_synchronization_manager()

        # Test actor initializer
        await test_actor_initializer()

        logger.info("All tests completed successfully")

    except Exception as e:
        logger.error(f"Error during tests: {e}")
        import traceback
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    asyncio.run(main())

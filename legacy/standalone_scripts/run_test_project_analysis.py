#!/usr/bin/env python3
"""
Test Project Analysis Script

This script runs Vibe Check on the test_project directory and generates a detailed report.
"""

import os
import sys
import time
from pathlib import Path
from datetime import datetime

# Add the current directory to the path
sys.path.insert(0, os.path.abspath("."))

# Import the simple analyzer
from vibe_check.core.simple_analyzer import simple_analyze_project
from vibe_check.core.models.project_metrics import ProjectMetrics
from vibe_check.core.models.file_metrics import FileMetrics
from vibe_check.core.models.directory_metrics import DirectoryMetrics

def generate_markdown_report(metrics: ProjectMetrics, output_path: Path) -> None:
    """
    Generate a detailed Markdown report from the analysis metrics.

    Args:
        metrics: ProjectMetrics object with analysis results
        output_path: Path to write the report to
    """
    # Create the report content
    report = f"""# Vibe Check Analysis Report

## Project Summary

- **Project Path:** {metrics.project_path}
- **Analysis Date:** {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- **Files Analyzed:** {metrics.total_file_count}
- **Directories Analyzed:** {metrics.total_directory_count}
- **Total Lines of Code:** {metrics.total_line_count}
- **Average Complexity:** {metrics.avg_complexity:.2f}
- **Issues Found:** {metrics.issue_count}

## Directory Analysis

| Directory | Files | Lines | Complexity | Issues |
|-----------|-------|-------|------------|--------|
"""

    # Add directory metrics
    for dir_path, dir_metrics in metrics.directories.items():
        report += f"| {dir_path} | {dir_metrics.file_count} | {dir_metrics.line_count} | {dir_metrics.avg_complexity:.2f} | {dir_metrics.issue_count} |\n"

    report += """
## File Analysis

| File | Lines | Complexity | Issues |
|------|-------|------------|--------|
"""

    # Add file metrics
    for file_path, file_metrics in metrics.files.items():
        report += f"| {file_path} | {file_metrics.line_count} | {file_metrics.complexity:.2f} | {len(file_metrics.issues)} |\n"

    # Add issue details if any
    if metrics.issue_count > 0:
        report += """
## Issues Found

| File | Line | Severity | Message |
|------|------|----------|---------|
"""

        for file_path, file_metrics in metrics.files.items():
            for issue in file_metrics.issues:
                severity = issue.get("severity", "UNKNOWN")
                message = issue.get("message", "No message")
                line = issue.get("line", 0)
                report += f"| {file_path} | {line} | {severity} | {message} |\n"
    else:
        report += """
## Issues Found

No issues found in the project.
"""

    # Write the report to the output file
    with open(output_path, "w") as f:
        f.write(report)

def main() -> None:
    # Configure logging
    import logging
    logging.basicConfig(level=logging.INFO,
                     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Get the project to analyze
    project_path = "external_test_project"

    # Create an output directory with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = Path(f'./test_results/report_{timestamp}')
    output_dir.mkdir(parents=True, exist_ok=True)

    print(f"Starting analysis of {project_path}")
    print(f"Results will be saved to {output_dir}")

    # Run the analysis
    start_time = time.time()
    metrics = simple_analyze_project(
        project_path=project_path,
        output_dir=output_dir
    )
    end_time = time.time()

    # Generate the report
    report_path = output_dir / "report.md"
    generate_markdown_report(metrics, report_path)

    # Print summary results
    print("\n--- Analysis Results ---")
    print(f"Total files analyzed: {metrics.total_file_count}")
    print(f"Total lines of code: {metrics.total_line_count}")
    print(f"Average complexity: {metrics.avg_complexity:.2f}")
    print(f"Total issues found: {metrics.issue_count}")
    print(f"Analysis completed in {end_time - start_time:.2f} seconds")
    print(f"Report saved to {report_path}")

    # Create a symlink to the latest report
    latest_link = Path('./test_results/latest')
    if latest_link.exists() or latest_link.is_symlink():
        latest_link.unlink()
    try:
        latest_link.symlink_to(output_dir.name, target_is_directory=True)
        print(f"Created symlink from 'latest' to {output_dir.name}")
    except Exception as e:
        print(f"Could not create symlink: {e}")

if __name__ == "__main__":
    main()

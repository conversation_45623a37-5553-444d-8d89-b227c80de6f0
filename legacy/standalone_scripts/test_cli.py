"""
Test script for the CLI.
"""

import sys
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath("."))

# Import the CLI
from vibe_check.cli.commands import analyze_command

# Run the CLI
results = analyze_command(
    project_path="./test_project",
    config_path=None,
    output_dir="./test_results_cli",
    verbose=True,
    quiet=False,
    config_override={"preset": "minimal"},
    analyze_trends=False,
    report_progress=False,
    use_simple_analyzer=True
)

# Print the results
print(f"Results: {results}")

# Check if there was an error
if isinstance(results, dict) and "error" in results:
    print(f"Error detected: {results['error']}")
    from vibe_check.cli.error_handler import handle_analysis_error
    handle_analysis_error(results)
else:
    # Format and display the results
    print("=== Analysis Summary ===")
    print(f"Total Files: {results.get('total_file_count', 'Not available')}")
    print(f"Total Lines: {results.get('total_line_count', 'Not available')}")
    print(f"Average Complexity: {results.get('avg_complexity', 'Not available')}")
    print(f"Total Issues: {results.get('issue_count', 'Not available')}")

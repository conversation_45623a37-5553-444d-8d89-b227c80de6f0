#!/usr/bin/env python3
"""
<PERSON><PERSON>t to analyze the Vibe Check project structure and identify the longest files,
methods, and objects.
"""

import os
import ast
import sys
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from collections import defaultdict
import re

# Configuration
PROJECT_ROOT = Path(".")
PYTHON_EXTENSIONS = [".py"]
EXCLUDE_DIRS = ["venv", "__pycache__", ".git", "legacy", "build", "dist", "egg-info"]


class CodeMetrics:
    """Class to store code metrics for a file, method, or class."""

    def __init__(self, name: str, path: str, line_count: int, start_line: int = 0, end_line: int = 0):
        self.name = name
        self.path = path
        self.line_count = line_count
        self.start_line = start_line
        self.end_line = end_line

    def __repr__(self) -> str:
        return f"{self.name} ({self.line_count} lines) at {self.path}:{self.start_line}-{self.end_line}"


def should_exclude(path: Path) -> bool:
    """Check if a path should be excluded based on exclude patterns."""
    path_str = str(path)
    for pattern in EXCLUDE_DIRS:
        if pattern in path_str:
            return True
    return False


def find_python_files(root_dir: Path) -> List[Path]:
    """Find all Python files in the given directory."""
    python_files = []

    for root, dirs, files in os.walk(root_dir):
        # Filter out excluded directories
        dirs[:] = [d for d in dirs if not should_exclude(Path(root) / d)]

        for file in files:
            file_path = Path(root) / file
            if file_path.suffix in PYTHON_EXTENSIONS and not should_exclude(file_path):
                python_files.append(file_path)

    return python_files


def count_file_lines(file_path: Path) -> int:
    """Count the number of lines in a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return len(f.readlines())
    except Exception as e:
        print(f"Error reading {file_path}: {e}")
        return 0


def analyze_file(file_path: Path) -> Tuple[CodeMetrics, List[CodeMetrics], List[CodeMetrics]]:
    """
    Analyze a Python file to extract metrics about classes and methods.

    Returns:
        Tuple containing:
        - File metrics
        - List of class metrics
        - List of method metrics
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # Count file lines
        line_count = content.count('\n') + (0 if content.endswith('\n') else 1)
        file_metrics = CodeMetrics(file_path.name, str(file_path), line_count)

        # Parse the AST
        tree = ast.parse(content)

        # Extract class and method metrics
        class_metrics = []
        method_metrics = []

        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                class_line_count = node.end_lineno - node.lineno + 1
                class_metrics.append(CodeMetrics(
                    node.name,
                    str(file_path),
                    class_line_count,
                    node.lineno,
                    node.end_lineno
                ))

            elif isinstance(node, (ast.FunctionDef, ast.AsyncFunctionDef)):
                method_line_count = node.end_lineno - node.lineno + 1
                method_metrics.append(CodeMetrics(
                    node.name,
                    str(file_path),
                    method_line_count,
                    node.lineno,
                    node.end_lineno
                ))

        return file_metrics, class_metrics, method_metrics

    except SyntaxError as e:
        print(f"Syntax error in {file_path}: {e}")
        return CodeMetrics(file_path.name, str(file_path), 0), [], []
    except Exception as e:
        print(f"Error analyzing {file_path}: {e}")
        return CodeMetrics(file_path.name, str(file_path), 0), [], []


def analyze_project(root_dir: Path) -> Tuple[List[CodeMetrics], List[CodeMetrics], List[CodeMetrics]]:
    """
    Analyze the entire project.

    Returns:
        Tuple containing:
        - List of file metrics
        - List of class metrics
        - List of method metrics
    """
    python_files = find_python_files(root_dir)

    all_file_metrics = []
    all_class_metrics = []
    all_method_metrics = []

    for file_path in python_files:
        file_metrics, class_metrics, method_metrics = analyze_file(file_path)
        all_file_metrics.append(file_metrics)
        all_class_metrics.extend(class_metrics)
        all_method_metrics.extend(method_metrics)

    return all_file_metrics, all_class_metrics, all_method_metrics


def print_top_metrics(metrics: List[CodeMetrics], title: str, count: int = 10) -> None:
    """Print the top N metrics sorted by line count."""
    print(f"\n{title}")
    print("=" * len(title))

    sorted_metrics = sorted(metrics, key=lambda m: m.line_count, reverse=True)

    for i, metric in enumerate(sorted_metrics[:count], 1):
        print(f"{i}. {metric.name}: {metric.line_count} lines")
        print(f"   Path: {metric.path}")
        if metric.start_line > 0:
            print(f"   Lines: {metric.start_line}-{metric.end_line}")
        print()


def print_summary(file_metrics: List[CodeMetrics], class_metrics: List[CodeMetrics], method_metrics: List[CodeMetrics]) -> None:
    """Print a summary of the project metrics."""
    total_files = len(file_metrics)
    total_lines = sum(m.line_count for m in file_metrics)
    total_classes = len(class_metrics)
    total_methods = len(method_metrics)

    avg_file_lines = total_lines / total_files if total_files > 0 else 0
    avg_class_lines = sum(m.line_count for m in class_metrics) / total_classes if total_classes > 0 else 0
    avg_method_lines = sum(m.line_count for m in method_metrics) / total_methods if total_methods > 0 else 0

    print("\nProject Summary")
    print("==============")
    print(f"Total Python files: {total_files}")
    print(f"Total lines of code: {total_lines}")
    print(f"Total classes: {total_classes}")
    print(f"Total methods/functions: {total_methods}")
    print(f"Average lines per file: {avg_file_lines:.2f}")
    print(f"Average lines per class: {avg_class_lines:.2f}")
    print(f"Average lines per method: {avg_method_lines:.2f}")


def main():
    """Main function to run the analysis."""
    print(f"Analyzing project at {PROJECT_ROOT.absolute()}")

    file_metrics, class_metrics, method_metrics = analyze_project(PROJECT_ROOT)

    print_summary(file_metrics, class_metrics, method_metrics)
    print_top_metrics(file_metrics, "Top 10 Longest Files", 10)
    print_top_metrics(class_metrics, "Top 10 Longest Classes", 10)
    print_top_metrics(method_metrics, "Top 10 Longest Methods", 10)


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Actor System Project Analyzer

This script analyzes a Python project using the actor system. It:
1. Sets up the actor system with appropriate logging
2. Configures the analysis with multiple tools
3. Runs a complete analysis
4. Generates a comprehensive report
5. Handles errors gracefully
"""

import asyncio
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Add the project root to the Python path
sys.path.append(str(Path(__file__).parent))

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler("actor_system_analysis.log")
    ]
)
logger = logging.getLogger("actor_system_project_analyzer")

# Import actor system components
from vibe_check.core.actor_system.logging.enhanced_logger import setup_actor_system_logging
from vibe_check.core.actor_system.actor_system import ActorSystem
from vibe_check.core.actor_system.message import MessageType
from vibe_check.core.actor_system.context_wave import ContextWave

# Import analyzer components
from vibe_check.core.analyzer.project_analyzer import ProjectAnalyzer
from vibe_check.core.models.progress_tracker import ProgressTracker
from vibe_check.core.models.project_metrics import ProjectMetrics


async def analyze_project(project_path: str, output_dir: str) -> ProjectMetrics:
    """
    Analyze a project using the actor system.

    Args:
        project_path: Path to the project to analyze
        output_dir: Directory to write output files to

    Returns:
        ProjectMetrics object with analysis results
    """
    logger.info(f"Starting analysis of project: {project_path}")
    logger.info(f"Output directory: {output_dir}")

    # Configure actor system logging
    setup_actor_system_logging(debug_mode=True)

    # Create actor system
    logger.info("Creating actor system")
    system = ActorSystem()
    logger.info("Actor system created")

    try:
        # Configure the analysis
        config = {
            "file_extensions": [".py"],
            "exclude_patterns": ["**/venv/**", "**/.git/**", "**/__pycache__/**"],
            "tools": {
                "ruff": {
                    "enabled": True,
                    "args": ["--select=E,F,W,I"]
                },
                "mypy": {
                    "enabled": True,
                    "args": ["--ignore-missing-imports"]
                },
                "complexity": {
                    "enabled": True,
                    "threshold": 10
                }
            },
            "report": {
                "formats": ["html", "json", "markdown"],
                "include_visualizations": True
            }
        }

        # Don't create a progress tracker as it's a Protocol
        progress = None

        # Create analyzer
        logger.info("Creating project analyzer")
        analyzer = ProjectAnalyzer(
            project_path=project_path,
            config=config,
            actor_system=system,
            output_dir=output_dir,
            progress_tracker=progress
        )

        # Start the system
        logger.info("Starting the actor system")
        await system.start()
        logger.info("Actor system started")

        # Run the analysis
        logger.info("Running analysis")
        start_time = time.time()
        result = await analyzer.analyze()
        end_time = time.time()
        logger.info(f"Analysis completed in {end_time - start_time:.2f} seconds")

        # Print summary
        logger.info(f"Analysis summary:")
        logger.info(f"  Total files: {len(result.files)}")
        logger.info(f"  Total directories: {len(result.directories)}")
        logger.info(f"  Total issues: {result._issue_count}")

        # Stop the system
        logger.info("Stopping the actor system")
        await system.stop()
        logger.info("Actor system stopped")

        return result

    except Exception as e:
        logger.error(f"Error analyzing project: {e}")
        import traceback
        logger.error(traceback.format_exc())

        # Try to stop the system
        try:
            logger.info("Attempting to stop the actor system after error")
            await system.stop()
            logger.info("Actor system stopped")
        except Exception as stop_error:
            logger.error(f"Error stopping actor system: {stop_error}")

        # Re-raise the exception
        raise


async def main() -> None:
    """Main function."""
    try:
        # Get the project to analyze
        if len(sys.argv) > 1:
            project_path = sys.argv[1]
        else:
            # Default to analyzing a small test project
            project_path = os.path.join(os.path.dirname(__file__), "test_project_new")

        # Create an output directory with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = Path(f'./analysis_results/report_{timestamp}')
        output_dir.mkdir(parents=True, exist_ok=True)

        logger.info(f"Starting analysis of {project_path}")
        logger.info(f"Results will be saved to {output_dir}")

        # Run the analysis
        metrics = await analyze_project(project_path, str(output_dir))

        logger.info("Analysis completed successfully")
        logger.info(f"Results saved to {output_dir}")

    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

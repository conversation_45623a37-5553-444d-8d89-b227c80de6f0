#!/usr/bin/env python3
"""
Simple Vibe Check Runner

This script uses the simple_analyzer module to analyze a project without
relying on the actor system that's causing the syntax error.
"""

import os
import sys
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, os.path.abspath("."))

# Import the simple analyzer
from vibe_check.core.simple_analyzer import simple_analyze_project

def main():
    # Configure logging
    import logging
    logging.basicConfig(level=logging.INFO,
                     format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # Get the project to analyze (default to tests directory)
    project_path = sys.argv[1] if len(sys.argv) > 1 else './tests'

    # Create an output directory
    output_dir = Path('./analysis_results')
    output_dir.mkdir(exist_ok=True)

    print(f"Starting analysis of {project_path}")
    print(f"Results will be saved to {output_dir}")

    # Run the analysis
    metrics = simple_analyze_project(
        project_path=project_path,
        output_dir=output_dir
    )

    # Print summary results
    print("\n--- Analysis Results ---")
    print(f"Total files analyzed: {metrics.total_file_count}")
    print(f"Total lines of code: {metrics.total_line_count}")
    print(f"Average complexity: {metrics.avg_complexity:.2f}")
    print(f"Total issues found: {metrics.issue_count}")

    # Print issue summary
    print("\n--- Issue Summary ---")
    # Create a summary of issues by severity
    issue_summary = {}
    for file_path in metrics.files:
        for issue in metrics.files[file_path].issues:
            severity = issue.get('severity', 'unknown')
            if severity not in issue_summary:
                issue_summary[severity] = 0
            issue_summary[severity] += 1

    if issue_summary:
        for severity, count in issue_summary.items():
            print(f"{severity}: {count}")
    else:
        print("No issues found")

    # Print most complex files
    print("\n--- Most Complex Files ---")
    complex_files = sorted(
        [(path, metrics.files[path].complexity) for path in metrics.files],
        key=lambda x: x[1],
        reverse=True
    )
    for path, complexity in complex_files[:5]:
        print(f"{path}: {complexity:.2f}")

    # Print directories with most issues
    print("\n--- Directories with Most Issues ---")
    issue_dirs = sorted(
        [(path, metrics.directories[path].issue_count) for path in metrics.directories],
        key=lambda x: x[1],
        reverse=True
    )
    for path, issues in issue_dirs[:5]:
        print(f"{path}: {issues}")

    print("\nAnalysis complete!")

if __name__ == "__main__":
    main()

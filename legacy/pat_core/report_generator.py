"""
Module responsible for generating enhanced analysis reports from PAT results.

This module takes the raw outputs from various analysis tools run by PAT,
processes them, calculates aggregate metrics, identifies problematic areas,
and generates user-friendly summary and detailed reports.

This is a backward compatibility module that imports from the modularized
report_generator package. New code should import directly from the package.
"""

# Import from the modularized package
from .report_generator import PatReportGenerator

# For backward compatibility, re-export PatReportGenerator
__all__ = ['PatReportGenerator']

"""
File: config.py
Purpose: Implements the configuration system for the enhanced PAT system.

This module provides the ConfigManager class, which is responsible for
loading and managing configuration from multiple sources with a layered
approach to prioritization.
"""

import logging
import os
from typing import Any, Dict, List, Optional

import yaml


class ConfigManager:
    """
    Manages PAT configuration with layered approach.
    
    This class handles loading configuration from multiple sources,
    with a clear priority order to allow for system, user, and project-specific
    configurations.
    
    Attributes:
        config: The merged configuration dictionary
        config_files: List of configuration files that were loaded
        logger: Logger instance for the config manager
    """
    
    def __init__(self):
        self.config = {}
        self.config_files = []
        self.logger = logging.getLogger("pat.config")
        
    def load_config(self, config_paths: List[str] = None) -> Dict[str, Any]:
        """
        Load configuration from multiple sources with priority:
        1. Command line arguments
        2. Project-specific config (.pat.yaml in project root)
        3. User config (~/.pat/config.yaml)
        4. System config (/etc/pat/config.yaml)
        5. Default config (built-in)
        
        Args:
            config_paths: Additional config file paths to load
            
        Returns:
            Merged configuration dictionary
        """
        # Start with default config
        self.config = self._get_default_config()
        self.logger.info("Loaded default configuration")
        
        # Load system config
        system_config = "/etc/pat/config.yaml"
        if os.path.exists(system_config):
            self._load_config_file(system_config)
            self.logger.info(f"Loaded system configuration from {system_config}")
            
        # Load user config
        user_config = os.path.expanduser("~/.pat/config.yaml")
        if os.path.exists(user_config):
            self._load_config_file(user_config)
            self.logger.info(f"Loaded user configuration from {user_config}")
            
        # Load project config
        project_config = ".pat.yaml"
        if os.path.exists(project_config):
            self._load_config_file(project_config)
            self.logger.info(f"Loaded project configuration from {project_config}")
            
        # Load additional config files
        if config_paths:
            for path in config_paths:
                if os.path.exists(path):
                    self._load_config_file(path)
                    self.logger.info(f"Loaded additional configuration from {path}")
                else:
                    self.logger.warning(f"Configuration file not found: {path}")
                    
        self.logger.info(f"Configuration loaded from {len(self.config_files)} files")
        return self.config
    
    def _load_config_file(self, path: str) -> None:
        """
        Load a single config file and merge with current config.
        
        Args:
            path: Path to the configuration file
        """
        try:
            with open(path, 'r') as f:
                config = yaml.safe_load(f)
                
            if config:
                self.config = self._deep_merge(self.config, config)
                self.config_files.append(path)
                self.logger.debug(f"Successfully loaded and merged config from {path}")
            else:
                self.logger.warning(f"Empty or invalid configuration in {path}")
                
        except Exception as e:
            self.logger.error(f"Error loading config file {path}: {e}")
    
    def _deep_merge(self, 
                   base: Dict[str, Any], 
                   override: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deep merge two dictionaries, with override values taking precedence.
        
        Args:
            base: Base dictionary
            override: Dictionary with override values
            
        Returns:
            Merged dictionary
        """
        result = base.copy()
        
        for key, value in override.items():
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                result[key] = self._deep_merge(result[key], value)
            else:
                result[key] = value
                
        return result
    
    def _get_default_config(self) -> Dict[str, Any]:
        """
        Get default configuration.
        
        Returns:
            Dictionary containing default configuration values
        """
        return {
            "general": {
                "max_workers": min(32, (os.cpu_count() or 1) + 4),
                "output_dir": "pat_output",
                "log_level": "INFO",
                "file_size_threshold": 600,
            },
            "plugins": {
                "enabled": ["pyright", "ruff", "bandit", "radon"],
                "pyright": {
                    "enabled": True,
                    "config": {}
                },
                "ruff": {
                    "enabled": True,
                    "config": {}
                },
                "bandit": {
                    "enabled": True,
                    "config": {
                        "exclude_dirs": ["tests", "venv", ".git"],
                        "skips": ["B101", "B104", "B110"]
                    }
                },
                "radon": {
                    "enabled": True,
                    "config": {
                        "complexity_threshold": 15
                    }
                }
            },
            "reporting": {
                "formats": ["md", "json"],
                "include_passed": False,
                "group_by": "plugin"
            },
            "synergies": {
                "enabled": ["security_correlation", "complexity_coverage"],
                "security_correlation": {
                    "plugins": ["bandit", "semgrep", "safety"],
                    "threshold": 2
                },
                "complexity_coverage": {
                    "plugins": ["radon", "pytest_coverage"],
                    "high_risk_threshold": 0.7
                }
            }
        }

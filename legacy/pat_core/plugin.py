"""
File: plugin.py
Purpose: Defines the plugin architecture for the enhanced PAT system.

This module provides the base classes and interfaces for all PAT plugins,
establishing a standardized way to integrate various analysis tools.
"""

import logging
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple, Union


class PluginResult:
    """
    Standardized result format for all plugins.
    
    This class provides a consistent structure for plugin results,
    making it easier to process and display analysis findings.
    
    Attributes:
        plugin_name: Name of the plugin that generated the result
        status: Status of the analysis ("success", "warning", "error", "skipped")
        issues: List of issues found during analysis
        metrics: Dictionary of metrics collected during analysis
        summary: Human-readable summary of the analysis
        timestamp: When the analysis was performed
    """
    
    def __init__(self, 
                 plugin_name: str,
                 status: str,
                 issues: List[Dict[str, Any]] = None,
                 metrics: Dict[str, Any] = None,
                 summary: str = None):
        self.plugin_name = plugin_name
        self.status = status  # "success", "warning", "error", "skipped"
        self.issues = issues or []
        self.metrics = metrics or {}
        self.summary = summary or ""
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert result to dictionary for serialization.
        
        Returns:
            Dictionary representation of the result
        """
        return {
            "plugin_name": self.plugin_name,
            "status": self.status,
            "issues": self.issues,
            "metrics": self.metrics,
            "summary": self.summary,
            "timestamp": self.timestamp.isoformat()
        }

class Plugin(ABC):
    """
    Base class for all PAT plugins.
    
    This abstract class defines the interface that all plugins must implement,
    ensuring consistency across different analysis tools.
    
    Attributes:
        config: Configuration dictionary for the plugin
        logger: Logger instance for the plugin
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logging.getLogger(f"pat.plugins.{self.name}")
    
    @property
    @abstractmethod
    def name(self) -> str:
        """
        Return the name of the plugin.
        
        Returns:
            String name of the plugin
        """
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """
        Return a description of the plugin.
        
        Returns:
            String description of the plugin
        """
        pass
    
    @property
    def version(self) -> str:
        """
        Return the version of the plugin.
        
        Returns:
            String version of the plugin
        """
        return "1.0.0"
    
    @property
    def dependencies(self) -> List[str]:
        """
        Return a list of dependencies for this plugin.
        
        Returns:
            List of dependency names
        """
        return []
    
    @property
    def is_cpu_bound(self) -> bool:
        """
        Indicate if this plugin is CPU-bound.
        
        CPU-bound plugins will be run in a thread pool to avoid
        blocking the event loop.
        
        Returns:
            True if the plugin is CPU-bound, False otherwise
        """
        return False
    
    @abstractmethod
    async def analyze(self, 
                     files: List[str], 
                     context: Dict[str, Any] = None) -> PluginResult:
        """
        Analyze the given files and return results.
        
        Args:
            files: List of file paths to analyze
            context: Additional context information
            
        Returns:
            PluginResult object containing analysis results
        """
        pass
    
    def configure(self, config: Dict[str, Any]) -> None:
        """
        Update plugin configuration.
        
        Args:
            config: New configuration dictionary to merge with existing config
        """
        self.config.update(config)

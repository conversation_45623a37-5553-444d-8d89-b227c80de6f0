"""
Meta-System Separation Analysis Package

This package provides tools for analyzing meta-system separation in Python projects.
It identifies violations where one meta-system imports from another and generates
visualizations of meta-system dependencies.
"""

from .analyzer import MetaSystemAnalyzer
from .utils import META_SYSTEMS, map_to_meta_system
from .visualizer import MetaSystemVisualizer

__all__ = [
    'MetaSystemAnalyzer',
    'MetaSystemVisualizer',
    'map_to_meta_system',
    'META_SYSTEMS'
]

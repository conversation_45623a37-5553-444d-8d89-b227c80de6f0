"""
Utility functions for meta-system separation analysis.
"""

# Define meta-systems and their module prefixes
META_SYSTEMS = {
    "PC": ["persona_core"],
    "AN": ["analyst"],
    "PR": ["prediction", "predictor"],
    "SIO": ["shared/io", "io_layer", "sio"],
    "UI": ["ui", "user_interface"],
    "CORE": ["core"]
}

def map_to_meta_system(file_path: str) -> str:
    """Map a file path to its meta-system.
    
    Args:
        file_path: Path of the file
    Returns:
        Meta-system label
    """
    path = file_path.lower()
    
    # Check if module is in a meta-system
    for meta, prefixes in META_SYSTEMS.items():
        for prefix in prefixes:
            if f"meta_systems/{prefix}" in path or f"meta_systems\\{prefix}" in path:
                return meta
            
    # If not in a meta-system, check if it's in core
    if "/core/" in path or "\\core\\" in path:
        return "CORE"
        
    # If not in a meta-system or core, mark as OTHER
    return "OTHER"

def extract_imports(file_path: str) -> list:
    """Extract imports from a Python file.
    
    Args:
        file_path: Path to the Python file
    Returns:
        List of imported modules
    """
    imports = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Extract imports using regex
        import re
        import_pattern = r'^(?:from|import)\s+([\w.]+)(?:\s+import|\s*$)'
        for line in content.split('\n'):
            line = line.strip()
            match = re.match(import_pattern, line)
            if match:
                module = match.group(1)
                imports.append(module)
    except Exception as e:
        print(f"Error extracting imports from {file_path}: {e}")
    
    return imports

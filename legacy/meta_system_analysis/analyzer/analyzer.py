"""
Meta-System Analyzer

Analyzes meta-system separation in Python projects.
"""

import os
from pathlib import Path
from typing import Dict, List, Tuple

from ..utils import map_to_meta_system, extract_imports, META_SYSTEMS

class MetaSystemAnalyzer:
    """Analyzer for meta-system separation in Python projects."""
    
    def __init__(self, project_path: str):
        """Initialize the analyzer.
        
        Args:
            project_path: Path to the project root
        """
        self.project_path = Path(project_path)
        self.module_to_meta = {}
        self.meta_connections = {}
        self.violations = []
        
        # Initialize meta-system connections
        for meta in META_SYSTEMS.keys():
            self.meta_connections[meta] = {}
            for other_meta in META_SYSTEMS.keys():
                self.meta_connections[meta][other_meta] = 0
    
    def analyze(self) -> Tuple[List[Dict], Dict[str, Dict[str, int]]]:
        """Analyze meta-system separation in the project.
        
        Returns:
            Tuple of (violations list, meta-system connections)
        """
        # Find all Python files
        python_files = list(self.project_path.glob('**/*.py'))
        print(f"Found {len(python_files)} Python files")
        
        # First pass: map modules to meta-systems
        self._map_modules_to_meta_systems(python_files)
        
        # Second pass: analyze imports for violations and connections
        self._analyze_imports(python_files)
        
        return self.violations, self.meta_connections
    
    def _map_modules_to_meta_systems(self, python_files: List[Path]) -> None:
        """Map modules to their meta-systems.
        
        Args:
            python_files: List of Python files
        """
        for file_path in python_files:
            rel_path = file_path.relative_to(self.project_path)
            module_name = str(rel_path).replace('/', '.').replace('\\', '.').replace('.py', '')
            meta_system = map_to_meta_system(str(rel_path))
            self.module_to_meta[module_name] = meta_system
    
    def _analyze_imports(self, python_files: List[Path]) -> None:
        """Analyze imports for violations and connections.
        
        Args:
            python_files: List of Python files
        """
        for file_path in python_files:
            rel_path = file_path.relative_to(self.project_path)
            module_name = str(rel_path).replace('/', '.').replace('\\', '.').replace('.py', '')
            source_meta = self.module_to_meta.get(module_name, "OTHER")
            
            # Skip if module is not in a meta-system
            if source_meta == "OTHER":
                continue
            
            # Extract imports
            imports = extract_imports(file_path)
            
            # Check each import
            for imp in imports:
                # Skip if import is not in the project
                if imp not in self.module_to_meta:
                    continue
                
                target_meta = self.module_to_meta[imp]
                
                # Skip if import is not in a meta-system
                if target_meta == "OTHER":
                    continue
                
                # Track connection (even within same meta-system)
                if source_meta in self.meta_connections and target_meta in self.meta_connections[source_meta]:
                    self.meta_connections[source_meta][target_meta] += 1
                
                # Skip if import is in the same meta-system
                if source_meta == target_meta:
                    continue
                
                # Skip if import is from CORE (allowed)
                if target_meta == "CORE":
                    continue
                
                # If we get here, we have a violation
                violation = {
                    "source_module": module_name,
                    "source_meta": source_meta,
                    "target_module": imp,
                    "target_meta": target_meta,
                    "file_path": str(rel_path)
                }
                
                self.violations.append(violation)

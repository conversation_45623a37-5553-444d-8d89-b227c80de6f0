# Meta-System Separation Analysis

This module provides tools for analyzing meta-system separation in Python projects. It identifies violations where one meta-system imports from another and generates visualizations of meta-system dependencies.

## Overview

In the person_suit architecture, meta-systems are high-level components that should be independent of each other. They include:

- **PC (Persona Core)**: The core persona system
- **AN (Analyst)**: The analysis system
- **PR (Predictor)**: The prediction system
- **SIO (Shared I/O)**: The shared input/output system
- **UI (User Interface)**: The user interface system

These meta-systems should not import from each other directly. Instead, they should only use common underlying components from the core.

## Module Structure

The meta-system analysis module is organized as follows:

```
meta_system_analysis/
├── __init__.py
├── analyzer/
│   ├── __init__.py
│   └── analyzer.py
├── visualizer/
│   ├── __init__.py
│   └── visualizer.py
└── utils/
    └── __init__.py
```

- **analyzer**: Contains the `MetaSystemAnalyzer` class for analyzing meta-system separation
- **visualizer**: Contains the `MetaSystemVisualizer` class for generating visualizations
- **utils**: Contains utility functions for meta-system analysis

## Usage

### Command-Line Interface

The easiest way to use the meta-system analysis is through the command-line interface:

```bash
./run_meta_system_analysis.sh /path/to/person_suit
```

This script:
- Activates the virtual environment
- Installs required packages if needed
- Runs the meta-system separation analysis
- Generates visualizations
- Opens the interactive report in your browser

### Python API

You can also use the meta-system analysis directly from Python:

```python
from meta_system_analysis import MetaSystemAnalyzer, MetaSystemVisualizer

# Analyze meta-system separation
project_path = "/path/to/person_suit"
analyzer = MetaSystemAnalyzer(project_path)
violations, meta_connections = analyzer.analyze()

# Generate visualizations
project_name = "person_suit"
visualizer = MetaSystemVisualizer(meta_connections, project_name)
visualizer.generate_static_graph("analysis")
visualizer.generate_interactive_report("analysis")
```

## Output

The meta-system analysis generates several outputs:

1. **Console Output**: Lists any violations found
2. **Static Graph**: A PNG image showing the meta-system dependency graph
3. **Interactive Report**: An HTML file with detailed information about violations and recommendations
4. **JSON Data**: A JSON file containing the violations and meta-system connections

## Integration with PAT Tool

The meta-system analysis is integrated with the PAT tool and can be run through the PAT web UI. To use it:

1. Start the PAT web UI: `streamlit run pat_webui.py`
2. Select "meta_system_separation" in the tools list
3. Enter the path to your person_suit project
4. Click "Run Analysis"
5. View the results in the "Meta-System Separation" tab

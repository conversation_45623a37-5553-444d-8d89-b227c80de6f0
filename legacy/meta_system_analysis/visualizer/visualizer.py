"""
Meta-System Visualizer

Generates visualizations of meta-system connections.
"""

import os
import json
from typing import Dict, Optional

from ..utils import META_SYSTEMS

class MetaSystemVisualizer:
    """Visualizer for meta-system connections."""
    
    def __init__(self, meta_connections: Dict[str, Dict[str, int]], project_name: str):
        """Initialize the visualizer.
        
        Args:
            meta_connections: Dictionary of connections between meta-systems
            project_name: Name of the project
        """
        self.meta_connections = meta_connections
        self.project_name = project_name
        
        # Check if visualization libraries are available
        try:
            import matplotlib.pyplot as plt
            import networkx as nx
            self.has_visualization = True
        except ImportError:
            self.has_visualization = False
    
    def generate_static_graph(self, output_dir: str) -> Optional[str]:
        """Generate a static graph visualization.
        
        Args:
            output_dir: Directory to save the visualization
        
        Returns:
            Path to the generated visualization file, or None if visualization failed
        """
        if not self.has_visualization:
            print("Warning: Visualization libraries (matplotlib, networkx) not available. Skipping visualization.")
            return None
        
        try:
            import matplotlib.pyplot as plt
            import networkx as nx
            import matplotlib.patches as mpatches
            
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            # Create a directed graph
            G = nx.DiGraph()
            
            # Add nodes for each meta-system
            for meta in META_SYSTEMS.keys():
                G.add_node(meta)
            
            # Add edges for connections
            for source, targets in self.meta_connections.items():
                for target, count in targets.items():
                    if count > 0:
                        G.add_edge(source, target, weight=count)
            
            # Set up the plot
            plt.figure(figsize=(12, 10))
            
            # Define node colors
            node_colors = {
                "PC": "blue",
                "AN": "green",
                "PR": "red",
                "SIO": "purple",
                "UI": "orange",
                "CORE": "gray"
            }
            
            # Get node colors
            colors = [node_colors.get(node, "lightgray") for node in G.nodes()]
            
            # Draw the graph
            pos = nx.spring_layout(G, seed=42)
            nx.draw_networkx_nodes(G, pos, node_color=colors, node_size=1000, alpha=0.8)
            nx.draw_networkx_labels(G, pos, font_size=12, font_weight="bold")
            
            # Draw edges with width proportional to weight
            edges = G.edges()
            weights = [G[u][v]['weight'] for u, v in edges]
            max_weight = max(weights) if weights else 1
            normalized_weights = [w / max_weight * 5 for w in weights]
            
            nx.draw_networkx_edges(G, pos, edgelist=edges, width=normalized_weights,
                                  arrowsize=20, connectionstyle="arc3,rad=0.1")
            
            # Add edge labels (connection counts)
            edge_labels = {(u, v): G[u][v]['weight'] for u, v in G.edges()}
            nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels, font_size=10)
            
            # Add a title
            plt.title(f"{self.project_name} Meta-System Connections", fontsize=16)
            
            # Add a legend
            legend_elements = []
            for meta, color in node_colors.items():
                if meta in G.nodes():
                    legend_elements.append(mpatches.Patch(color=color, label=meta))
            plt.legend(handles=legend_elements, loc="upper right")
            
            # Save the figure
            output_path = os.path.join(output_dir, f"{self.project_name}_meta_system_graph.png")
            plt.savefig(output_path, bbox_inches="tight")
            plt.close()
            
            print(f"Meta-system graph saved to {output_path}")
            return output_path
        
        except Exception as e:
            print(f"Error generating static graph: {e}")
            return None
    
    def generate_interactive_report(self, output_dir: str) -> Optional[str]:
        """Generate an interactive HTML report.
        
        Args:
            output_dir: Directory to save the report
        
        Returns:
            Path to the generated report file, or None if generation failed
        """
        try:
            # Create output directory if it doesn't exist
            os.makedirs(output_dir, exist_ok=True)
            
            # Define node colors
            node_colors = {
                "PC": "#1f77b4",  # blue
                "AN": "#2ca02c",  # green
                "PR": "#d62728",  # red
                "SIO": "#9467bd", # purple
                "UI": "#ff7f0e",  # orange
                "CORE": "#7f7f7f" # gray
            }
            
            # Convert the graph data to JSON for the HTML report
            nodes = []
            for meta in META_SYSTEMS.keys():
                nodes.append({
                    "id": meta,
                    "label": meta,
                    "color": node_colors.get(meta, "#c7c7c7")
                })
            
            links = []
            for source, targets in self.meta_connections.items():
                for target, count in targets.items():
                    if count > 0:
                        links.append({
                            "source": source,
                            "target": target,
                            "value": count
                        })
            
            # Create HTML content
            html_content = self._generate_html_template(nodes, links)
            
            # Save HTML file
            html_path = os.path.join(output_dir, f"{self.project_name}_meta_system_report.html")
            with open(html_path, "w") as f:
                f.write(html_content)
            
            print(f"Interactive meta-system report saved to {html_path}")
            return html_path
        
        except Exception as e:
            print(f"Error generating interactive report: {e}")
            return None
    
    def _generate_html_template(self, nodes, links) -> str:
        """Generate HTML template for the interactive report.
        
        Args:
            nodes: List of node objects
            links: List of link objects
        
        Returns:
            HTML content as a string
        """
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Meta-System Connections - {self.project_name}</title>
            <script src="https://d3js.org/d3.v7.min.js"></script>
            <style>
                body {{
                    font-family: Arial, sans-serif;
                    margin: 0;
                    padding: 0;
                }}
                .container {{
                    display: flex;
                    height: 100vh;
                }}
                .graph {{
                    flex: 2;
                    border-right: 1px solid #ccc;
                }}
                .details {{
                    flex: 1;
                    padding: 20px;
                    overflow-y: auto;
                }}
                .node {{
                    cursor: pointer;
                }}
                .link {{
                    stroke-width: 2px;
                }}
                .meta-system {{
                    padding: 5px 10px;
                    border-radius: 3px;
                    color: white;
                    display: inline-block;
                    margin-right: 5px;
                }}
                .PC {{ background-color: #1f77b4; }}
                .AN {{ background-color: #2ca02c; }}
                .PR {{ background-color: #d62728; }}
                .SIO {{ background-color: #9467bd; }}
                .UI {{ background-color: #ff7f0e; }}
                .CORE {{ background-color: #7f7f7f; }}
                .OTHER {{ background-color: #c7c7c7; }}
                .summary {{
                    background-color: #f5f5f5;
                    padding: 15px;
                    border-radius: 5px;
                    margin-bottom: 20px;
                }}
                .legend {{
                    display: flex;
                    flex-wrap: wrap;
                    margin-bottom: 15px;
                }}
                .legend-item {{
                    display: flex;
                    align-items: center;
                    margin-right: 15px;
                    margin-bottom: 5px;
                }}
                .legend-color {{
                    width: 15px;
                    height: 15px;
                    margin-right: 5px;
                    border-radius: 3px;
                }}
                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin-top: 20px;
                }}
                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}
                th {{
                    background-color: #f2f2f2;
                }}
                tr:nth-child(even) {{
                    background-color: #f9f9f9;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="graph" id="graph"></div>
                <div class="details">
                    <h2>Meta-System Connections Analysis</h2>
                    <div class="summary">
                        <h3>Summary</h3>
                        <p>This visualization shows the connections between meta-systems in the {self.project_name} project.</p>
                        <p>Meta-systems should not import from each other directly, but can use common underlying components from the core.</p>
                    </div>
                    
                    <div class="legend">
                        <h3>Meta-Systems</h3>
                        <div style="display: flex; flex-wrap: wrap;">
                            <div class="legend-item">
                                <div class="legend-color PC"></div>
                                <div>PC (Persona Core)</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color AN"></div>
                                <div>AN (Analyst)</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color PR"></div>
                                <div>PR (Predictor)</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color SIO"></div>
                                <div>SIO (Shared I/O)</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color UI"></div>
                                <div>UI (User Interface)</div>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color CORE"></div>
                                <div>CORE</div>
                            </div>
                        </div>
                    </div>
                    
                    <h3>Connection Matrix</h3>
                    <table>
                        <tr>
                            <th>Source / Target</th>
                            {' '.join([f'<th>{meta}</th>' for meta in META_SYSTEMS.keys()])}
                        </tr>
                        {' '.join([f'<tr><th>{source}</th>' + 
                                ' '.join([f'<td>{targets.get(target, 0)}</td>' for target in META_SYSTEMS.keys()]) + 
                                '</tr>' for source, targets in self.meta_connections.items()])}
                    </table>
                </div>
            </div>
            
            <script>
            // Graph data
            const nodes = {json.dumps(nodes)};
            const links = {json.dumps(links)};
            
            // Set up the SVG
            const width = document.getElementById('graph').clientWidth;
            const height = document.getElementById('graph').clientHeight;
            
            const svg = d3.select('#graph')
                .append('svg')
                .attr('width', width)
                .attr('height', height);
            
            // Define arrow markers
            svg.append('defs').append('marker')
                .attr('id', 'arrowhead')
                .attr('viewBox', '-0 -5 10 10')
                .attr('refX', 20)
                .attr('refY', 0)
                .attr('orient', 'auto')
                .attr('markerWidth', 6)
                .attr('markerHeight', 6)
                .attr('xoverflow', 'visible')
                .append('svg:path')
                .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
                .attr('fill', '#999')
                .style('stroke', 'none');
            
            // Create a force simulation
            const simulation = d3.forceSimulation(nodes)
                .force('link', d3.forceLink(links).id(function(d) { return d.id; }).distance(150))
                .force('charge', d3.forceManyBody().strength(-500))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collision', d3.forceCollide().radius(60));
            
            // Create the links
            const link = svg.append('g')
                .selectAll('line')
                .data(links)
                .enter().append('path')
                .attr('class', 'link')
                .attr('stroke', '#999')
                .attr('stroke-width', function(d) { return Math.sqrt(d.value); })
                .attr('marker-end', 'url(#arrowhead)');
            
            // Create the nodes
            const node = svg.append('g')
                .selectAll('g')
                .data(nodes)
                .enter().append('g')
                .attr('class', 'node')
                .call(d3.drag()
                    .on('start', dragstarted)
                    .on('drag', dragged)
                    .on('end', dragended));
            
            // Add circles to the nodes
            node.append('circle')
                .attr('r', 30)
                .attr('fill', function(d) { return d.color; })
                .attr('stroke', '#fff')
                .attr('stroke-width', 2);
            
            // Add labels to the nodes
            node.append('text')
                .attr('text-anchor', 'middle')
                .attr('dy', '.35em')
                .attr('fill', 'white')
                .attr('font-weight', 'bold')
                .text(function(d) { return d.id; });
            
            // Add title for tooltip
            node.append('title')
                .text(function(d) { return d.id; });
            
            // Add labels to the links
            const linkText = svg.append('g')
                .selectAll('text')
                .data(links)
                .enter().append('text')
                .attr('font-size', 10)
                .attr('text-anchor', 'middle')
                .text(function(d) { return d.value; });
            
            // Update positions on each tick
            simulation.on('tick', function() {
                link.attr('d', function(d) {
                    const dx = d.target.x - d.source.x;
                    const dy = d.target.y - d.source.y;
                    const dr = Math.sqrt(dx * dx + dy * dy) * 1.5; // Curve factor
                    return 'M' + d.source.x + ',' + d.source.y + 'A' + dr + ',' + dr + ' 0 0,1 ' + d.target.x + ',' + d.target.y;
                });
                
                node.attr('transform', function(d) {
                    // Keep nodes within bounds
                    d.x = Math.max(50, Math.min(width - 50, d.x));
                    d.y = Math.max(50, Math.min(height - 50, d.y));
                    return 'translate(' + d.x + ',' + d.y + ')';
                });
                
                // Position link labels
                linkText.attr('x', function(d) { return (d.source.x + d.target.x) / 2; })
                    .attr('y', function(d) { return (d.source.y + d.target.y) / 2; });
            });
            
            // Drag functions
            function dragstarted(event, d) {
                if (!event.active) simulation.alphaTarget(0.3).restart();
                d.fx = d.x;
                d.fy = d.y;
            }
            
            function dragged(event, d) {
                d.fx = event.x;
                d.fy = event.y;
            }
            
            function dragended(event, d) {
                if (!event.active) simulation.alphaTarget(0);
                d.fx = null;
                d.fy = null;
            }
            </script>
        </body>
        </html>
        """

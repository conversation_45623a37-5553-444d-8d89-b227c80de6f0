#!/usr/bin/env python3
"""
Complete PAT Workflow

This script runs the complete PAT workflow:
1. Runs the comprehensive PAT analysis with all tools enabled
2. Extracts high-priority files based on a priority score
3. Generates issue-specific prompts for individual issues

Usage:
    python run_pat_complete_workflow.py <project_path> [--top N] [--min-score SCORE]

Arguments:
    project_path: Path to the project to analyze
    --top N: Number of top files to extract (default: 10)
    --min-score: Minimum priority score to include a file (default: 5)
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


# ANSI color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run the complete PAT workflow")
    parser.add_argument("project_path", help="Path to the project to analyze")
    parser.add_argument("--top", type=int, default=10, help="Number of top files to extract (default: 10)")
    parser.add_argument("--min-score", type=float, default=5.0, help="Minimum priority score to include a file (default: 5)")
    return parser.parse_args()

def run_comprehensive_analysis(project_path: str) -> Path:
    """Run the comprehensive PAT analysis and return the output directory."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the comprehensive analysis script
    analysis_script = pat_dir / "run_pat_comprehensive.py"
    cmd = [sys.executable, str(analysis_script), project_path]
    
    print(f"{Colors.BOLD}{Colors.HEADER}Step 1: Running Comprehensive PAT Analysis{Colors.ENDC}")
    print(f"{Colors.CYAN}Project:{Colors.ENDC} {project_path}")
    print(f"{Colors.CYAN}Script:{Colors.ENDC} {analysis_script}")
    print()
    
    result = subprocess.run(cmd)
    
    if result.returncode != 0:
        print(f"{Colors.RED}Comprehensive analysis failed with return code {result.returncode}{Colors.ENDC}")
        sys.exit(1)
    
    # Find the latest output directory
    output_dir = pat_dir / "PAT_output" / "latest"
    if not output_dir.exists() or not output_dir.is_symlink():
        # Find the latest output directory by timestamp
        output_dirs = list(sorted((pat_dir / "PAT_output").glob("20*-*-*_*-*-*"), reverse=True))
        if not output_dirs:
            print(f"{Colors.RED}No PAT output directory found{Colors.ENDC}")
            sys.exit(1)
        output_dir = output_dirs[0]
    else:
        # Resolve the symlink
        output_dir = output_dir.resolve()
    
    print(f"{Colors.GREEN}Comprehensive analysis completed successfully{Colors.ENDC}")
    print(f"{Colors.CYAN}Output directory:{Colors.ENDC} {output_dir}")
    print()
    
    return output_dir

def extract_high_priority_files(pat_output_dir: Path, top_n: int, min_score: float) -> Path:
    """Extract high-priority files from PAT output."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the extract_high_priority.py script
    extract_script = pat_dir / "extract_high_priority.py"
    output_dir = pat_output_dir / "high_priority"
    cmd = [
        sys.executable,
        str(extract_script),
        str(pat_output_dir),
        "--top", str(top_n),
        "--min-score", str(min_score),
        "--output-dir", str(output_dir)
    ]
    
    print(f"{Colors.BOLD}{Colors.HEADER}Step 2: Extracting High-Priority Files{Colors.ENDC}")
    print(f"{Colors.CYAN}PAT output directory:{Colors.ENDC} {pat_output_dir}")
    print(f"{Colors.CYAN}Top files:{Colors.ENDC} {top_n}")
    print(f"{Colors.CYAN}Minimum score:{Colors.ENDC} {min_score}")
    print()
    
    result = subprocess.run(cmd)
    
    if result.returncode != 0:
        print(f"{Colors.RED}High-priority extraction failed with return code {result.returncode}{Colors.ENDC}")
        sys.exit(1)
    
    # Check if any high-priority files were found
    summary_path = output_dir / "high_priority_summary.md"
    file_count = 0
    
    if summary_path.exists():
        with open(summary_path, 'r') as f:
            summary_content = f.read()
            if "Found " in summary_content:
                try:
                    file_count_str = summary_content.split("Found ")[1].split(" high-priority")[0]
                    file_count = int(file_count_str)
                except (IndexError, ValueError):
                    pass
    
    if file_count > 0:
        print(f"{Colors.GREEN}Found {file_count} high-priority files that need attention{Colors.ENDC}")
    else:
        print(f"{Colors.YELLOW}No high-priority files found that meet the criteria{Colors.ENDC}")
    
    print(f"{Colors.CYAN}High-priority prompts:{Colors.ENDC} {output_dir}")
    print(f"{Colors.CYAN}Summary report:{Colors.ENDC} {output_dir / 'high_priority_summary.md'}")
    print()
    
    return output_dir

def generate_issue_prompts(pat_output_dir: Path) -> Path:
    """Generate issue-specific prompts from PAT output."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the generate_issue_prompts.py script
    generate_script = pat_dir / "generate_issue_prompts.py"
    output_dir = pat_output_dir / "issue_prompts"
    cmd = [
        sys.executable,
        str(generate_script),
        str(pat_output_dir),
        "--output-dir", str(output_dir)
    ]
    
    print(f"{Colors.BOLD}{Colors.HEADER}Step 3: Generating Issue-Specific Prompts{Colors.ENDC}")
    print(f"{Colors.CYAN}PAT output directory:{Colors.ENDC} {pat_output_dir}")
    print()
    
    result = subprocess.run(cmd)
    
    if result.returncode != 0:
        print(f"{Colors.RED}Issue prompt generation failed with return code {result.returncode}{Colors.ENDC}")
        sys.exit(1)
    
    print(f"{Colors.GREEN}Issue prompt generation completed successfully{Colors.ENDC}")
    print(f"{Colors.CYAN}Issue prompts:{Colors.ENDC} {output_dir}")
    print(f"{Colors.CYAN}Summary report:{Colors.ENDC} {output_dir / 'issue_prompts_summary.md'}")
    print()
    
    return output_dir

def print_summary(pat_output_dir: Path, high_priority_dir: Path, issue_prompts_dir: Path):
    """Print a summary of the complete workflow."""
    print(f"{Colors.BOLD}{Colors.HEADER}Complete PAT Workflow Summary{Colors.ENDC}")
    print(f"{Colors.CYAN}PAT output directory:{Colors.ENDC} {pat_output_dir}")
    print(f"{Colors.CYAN}High-priority prompts:{Colors.ENDC} {high_priority_dir}")
    print(f"{Colors.CYAN}Issue-specific prompts:{Colors.ENDC} {issue_prompts_dir}")
    print()
    
    print(f"{Colors.BOLD}Reports:{Colors.ENDC}")
    print(f"1. {Colors.CYAN}PAT Analysis Report:{Colors.ENDC} {pat_output_dir / 'PAT_report.md'}")
    print(f"2. {Colors.CYAN}High-Priority Summary:{Colors.ENDC} {high_priority_dir / 'high_priority_summary.md'}")
    print(f"3. {Colors.CYAN}Issue Prompts Summary:{Colors.ENDC} {issue_prompts_dir / 'issue_prompts_summary.md'}")
    print()
    
    print(f"{Colors.BOLD}Next Steps:{Colors.ENDC}")
    print(f"1. Review the PAT analysis report for an overview of the project")
    print(f"2. Check the high-priority files that need immediate attention")
    print(f"3. Address individual issues using the issue-specific prompts")
    print()

def main():
    """Main function."""
    args = parse_args()
    
    # Step 1: Run comprehensive analysis
    pat_output_dir = run_comprehensive_analysis(args.project_path)
    
    # Step 2: Extract high-priority files
    high_priority_dir = extract_high_priority_files(pat_output_dir, args.top, args.min_score)
    
    # Step 3: Generate issue-specific prompts
    issue_prompts_dir = generate_issue_prompts(pat_output_dir)
    
    # Print summary
    print_summary(pat_output_dir, high_priority_dir, issue_prompts_dir)
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}Workflow interrupted by user.{Colors.ENDC}")
        sys.exit(1)

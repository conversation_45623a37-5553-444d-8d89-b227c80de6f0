#!/usr/bin/env python3
"""
PAT_run - Project Analysis Tool Runner

A convenience script to run the PAT analysis tool more easily.

Usage:
    python PAT_project_analysis/PAT_run.py <project_path>
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """Main function - run the PAT analysis tool."""
    pat_dir = Path(__file__).parent.absolute()
    pat_script = pat_dir / "PAT_analyze.py"
    
    if not pat_script.exists():
        print("Error: PAT analysis tool not found.")
        print("Please run PAT_install.py first to set up the tool.")
        return 1
    
    # Forward all arguments to the PAT_analyze.py script
    cmd = [sys.executable, str(pat_script)] + sys.argv[1:]
    
    try:
        return subprocess.call(cmd)
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user.")
        return 1
    except Exception as e:
        print(f"\nError running analysis tool: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 
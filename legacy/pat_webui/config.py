"""
Configuration management for the PAT Web UI.
"""
import copy
import yaml
import streamlit as st

from .utils import load_base_config

def initialize_config():
    """Initialize the configuration in the session state.
    
    This function loads the base configuration and sets up the session state
    for configuration management.
    """
    # Load base config if not already in session state
    if 'pat_config' not in st.session_state:
        base_config = load_base_config()
        st.session_state['pat_config'] = copy.deepcopy(base_config)
    
    # Initialize other config-related session state variables
    if 'config_modified' not in st.session_state:
        st.session_state['config_modified'] = False
    
    if 'selected_tools' not in st.session_state:
        st.session_state['selected_tools'] = []

def generate_config_widgets(config_dict, parent_key=""):
    """Recursively generate Streamlit widgets for config options.
    
    Args:
        config_dict: Dictionary containing configuration options
        parent_key: Parent key for nested configuration options
    """
    for key, value in config_dict.items():
        unique_key = f"{parent_key}_{key}" if parent_key else key

        if isinstance(value, dict):
            # Handle nested dictionaries (e.g., tool settings)
            if 'enabled' in value and isinstance(value['enabled'], bool):
                 # Special handling for tool enable/disable
                 enabled = st.checkbox(f"Enable {key.replace('_', ' ').title()}", value=value['enabled'], key=f"config_{unique_key}_enabled")
                 st.session_state['pat_config'][parent_key][key]['enabled'] = enabled
                 if enabled:
                     with st.expander(f"Configure {key.replace('_', ' ').title()}", expanded=False):
                         # Generate widgets for other settings within the tool's dict
                         nested_config = {k: v for k, v in value.items() if k != 'enabled'}
                         generate_config_widgets(nested_config, f"{parent_key}.{key}" if parent_key else key)
            else:
                # Regular nested dictionary
                with st.expander(f"{key.replace('_', ' ').title()}", expanded=False):
                    generate_config_widgets(value, f"{parent_key}.{key}" if parent_key else key)
        else:
            # Handle leaf values based on type
            if isinstance(value, bool):
                val = st.checkbox(f"{key.replace('_', ' ').title()}", value=value, key=f"config_{unique_key}")
            elif isinstance(value, int):
                val = st.number_input(f"{key.replace('_', ' ').title()}", value=value, key=f"config_{unique_key}")
            elif isinstance(value, float):
                val = st.number_input(f"{key.replace('_', ' ').title()}", value=value, key=f"config_{unique_key}", format="%.2f")
            elif isinstance(value, list):
                # For simplicity, we'll just show lists as text inputs
                val = st.text_input(f"{key.replace('_', ' ').title()}", value=str(value), key=f"config_{unique_key}")
                # Convert string representation back to list
                try:
                    val = eval(val) if val.startswith('[') and val.endswith(']') else val
                except:
                    pass
            else:
                val = st.text_input(f"{key.replace('_', ' ').title()}", value=str(value), key=f"config_{unique_key}")
            
            # Update the config in session state
            parts = parent_key.split('.') if parent_key else []
            config_ref = st.session_state['pat_config']
            for part in parts:
                if part:
                    config_ref = config_ref[part]
            config_ref[key] = val
            
            # Mark as modified
            st.session_state['config_modified'] = True

def save_config(config_path):
    """Save the current configuration to a file.
    
    Args:
        config_path: Path to save the configuration file
    
    Returns:
        True if successful, False otherwise
    """
    try:
        with open(config_path, 'w') as f:
            yaml.dump(st.session_state['pat_config'], f, default_flow_style=False)
        return True
    except Exception as e:
        st.error(f"Error saving config to {config_path}: {e}")
        return False

def load_config(config_path):
    """Load configuration from a file.
    
    Args:
        config_path: Path to the configuration file
    
    Returns:
        True if successful, False otherwise
    """
    try:
        with open(config_path, 'r') as f:
            st.session_state['pat_config'] = yaml.safe_load(f)
        return True
    except Exception as e:
        st.error(f"Error loading config from {config_path}: {e}")
        return False

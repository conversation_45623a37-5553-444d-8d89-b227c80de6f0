"""
Home page for the PAT Web UI.
"""
import streamlit as st


def render_home():
    """Render the home page.
    """
    st.title("Welcome to PAT - Project Analysis Tool")
    
    st.markdown("""
    PAT helps you analyze Python projects for issues, code quality, and security vulnerabilities.
    
    ## Getting Started
    
    1. Enter your project path in the sidebar
    2. Select the tools you want to run
    3. Click "Run Analysis"
    4. View the results in the tabs below
    
    ## Features
    
    - **Multi-tool Analysis**: Run multiple analysis tools with a single click
    - **Comprehensive Reports**: Get detailed reports on issues, code quality, and more
    - **Visualizations**: View visualizations of your project's structure and issues
    - **Meta-System Separation**: Analyze the separation between meta-systems in your project
    
    ## Documentation
    
    For more information, check out the [documentation](https://github.com/yourusername/PAT).
    """)
    
    # Display sample images if available
    try:
        st.image("path/to/sample_image.png", caption="Sample Analysis")
    except FileNotFoundError:
        st.warning("Sample image not found.")
    except Exception as e:
        st.error(f"An unexpected error occurred: {e}")

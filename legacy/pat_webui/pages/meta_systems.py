"""
Meta-Systems page for the PAT Web UI.

This module contains the meta-systems page for the PAT Web UI, which displays
information about meta-systems in the project.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import networkx as nx
from typing import Any, Dict, List, Optional

from ..components.visualizations import (
    create_meta_system_network
)


def render_meta_systems(metrics: Dict[str, Any], project_path: str):
    """
    Render the meta-systems page.
    
    Args:
        metrics: Dictionary containing project metrics
        project_path: Path to the project being analyzed
    """
    st.title("Meta-Systems Analysis")
    
    # Check if meta-systems are available
    if not hasattr(metrics, 'meta_systems') or not metrics.meta_systems:
        st.info("No meta-systems identified in the project.")
        return
    
    # Meta-system overview
    st.header("Meta-System Overview")
    
    # Create a DataFrame for the table
    meta_system_data = []
    for name, meta_system in metrics.meta_systems.items():
        meta_system_data.append({
            'Name': name,
            'Files': len(meta_system.get('files', [])),
            'Dependencies': len(meta_system.get('dependencies', []))
        })
    
    # Create a DataFrame for the table
    if meta_system_data:
        df = pd.DataFrame(meta_system_data)
        st.dataframe(df, use_container_width=True)
    
    # Meta-system network
    st.header("Meta-System Network")
    
    # Create a network visualization
    network_chart = create_meta_system_network(metrics)
    st.plotly_chart(network_chart, use_container_width=True)
    
    # Meta-system violations
    st.header("Meta-System Violations")
    
    # Check if violations are available
    if not hasattr(metrics, 'meta_system_violations') or not metrics.meta_system_violations:
        st.info("No meta-system violations identified.")
    else:
        # Create a DataFrame for the table
        violation_data = []
        for violation in metrics.meta_system_violations:
            violation_data.append({
                'Type': violation['type'],
                'Description': violation['description'],
                'Source': violation['source'],
                'Target': violation['target']
            })
        
        # Create a DataFrame for the table
        if violation_data:
            df = pd.DataFrame(violation_data)
            st.dataframe(df, use_container_width=True)
    
    # Meta-system details
    st.header("Meta-System Details")
    
    # Create a selectbox for meta-system selection
    meta_system_names = list(metrics.meta_systems.keys())
    selected_meta_system = st.selectbox("Select a meta-system", meta_system_names)
    
    if selected_meta_system and selected_meta_system in metrics.meta_systems:
        meta_system = metrics.meta_systems[selected_meta_system]
        
        # Display meta-system details
        st.subheader(f"Details for {selected_meta_system}")
        
        # Create tabs for files and dependencies
        tab1, tab2 = st.tabs(["Files", "Dependencies"])
        
        with tab1:
            files = meta_system.get('files', [])
            if files:
                st.write(f"**Files in {selected_meta_system}**")
                for file in files:
                    st.write(f"- {file}")
            else:
                st.info(f"No files found in {selected_meta_system}.")
        
        with tab2:
            dependencies = meta_system.get('dependencies', [])
            if dependencies:
                st.write(f"**Dependencies of {selected_meta_system}**")
                for dependency in dependencies:
                    st.write(f"- {dependency}")
            else:
                st.info(f"No dependencies found for {selected_meta_system}.")
    
    # Recommendations
    st.header("Recommendations")
    
    st.info("1. Ensure meta-systems are properly separated")
    st.info("2. Use interfaces for communication between meta-systems")
    st.info("3. Avoid direct dependencies between meta-systems")

"""
Issues page for the PAT Web UI.

This module contains the issues page for the PAT Web UI, which displays
detailed information about issues found in the project.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Any, Dict, List, Optional

from ..components.visualizations import (
    create_issue_severity_chart,
    create_issue_type_chart
)


def render_issues(metrics: Dict[str, Any], project_path: str):
    """
    Render the issues page.
    
    Args:
        metrics: Dictionary containing project metrics
        project_path: Path to the project being analyzed
    """
    st.title("Issues Analysis")
    
    # Issue summary
    st.header("Issue Summary")
    
    # Count issues by tool
    tool_issues = {}
    for file_metric in metrics.get('files', {}).values():
        tool_results = file_metric.get('tool_results', {})
        
        for tool, results in tool_results.items():
            if tool not in tool_issues:
                tool_issues[tool] = 0
            
            if tool == 'mypy' and 'errors' in results:
                tool_issues[tool] += len(results['errors'])
            elif tool == 'ruff' and 'errors' in results:
                tool_issues[tool] += len(results['errors'])
            elif tool == 'bandit' and 'issues' in results:
                tool_issues[tool] += len(results['issues'])
            elif tool == 'pydocstyle' and 'issues' in results:
                tool_issues[tool] += len(results['issues'])
            elif tool == 'pylint' and 'errors' in results:
                tool_issues[tool] += len(results['errors'])
    
    # Create a DataFrame for the table
    if tool_issues:
        df = pd.DataFrame([
            {"Tool": tool.capitalize(), "Issues": count}
            for tool, count in tool_issues.items()
        ])
        st.dataframe(df, use_container_width=True)
    else:
        st.info("No issues found in the project.")
    
    # Issue severity chart
    st.subheader("Issues by Severity")
    severity_chart = create_issue_severity_chart(metrics)
    st.plotly_chart(severity_chart, use_container_width=True)
    
    # Issue type chart
    st.subheader("Issues by Type")
    type_chart = create_issue_type_chart(metrics)
    st.plotly_chart(type_chart, use_container_width=True)
    
    # Issues by file
    st.header("Issues by File")
    
    # Count issues by file
    file_issues = {}
    for path, file_metric in metrics.get('files', {}).items():
        issues = 0
        tool_results = file_metric.get('tool_results', {})
        
        for tool, results in tool_results.items():
            if tool == 'mypy' and 'errors' in results:
                issues += len(results['errors'])
            elif tool == 'ruff' and 'errors' in results:
                issues += len(results['errors'])
            elif tool == 'bandit' and 'issues' in results:
                issues += len(results['issues'])
            elif tool == 'pydocstyle' and 'issues' in results:
                issues += len(results['issues'])
            elif tool == 'pylint' and 'errors' in results:
                issues += len(results['errors'])
        
        if issues > 0:
            file_issues[path] = issues
    
    # Create a DataFrame for the table
    if file_issues:
        df = pd.DataFrame([
            {"File": path, "Issues": count}
            for path, count in sorted(file_issues.items(), key=lambda x: x[1], reverse=True)
        ])
        st.dataframe(df, use_container_width=True)
    else:
        st.info("No issues found in any files.")
    
    # Detailed issue list
    st.header("Detailed Issue List")
    
    # Collect all issues
    all_issues = []
    for path, file_metric in metrics.get('files', {}).items():
        tool_results = file_metric.get('tool_results', {})
        
        for tool, results in tool_results.items():
            if tool == 'mypy' and 'errors' in results:
                for error in results['errors']:
                    all_issues.append({
                        'File': path,
                        'Tool': 'mypy',
                        'Type': error.get('type', 'unknown'),
                        'Message': error.get('message', ''),
                        'Line': error.get('line', 0),
                        'Severity': 'error'
                    })
            elif tool == 'ruff' and 'errors' in results:
                for error in results['errors']:
                    all_issues.append({
                        'File': path,
                        'Tool': 'ruff',
                        'Type': error.get('code', 'unknown'),
                        'Message': error.get('message', ''),
                        'Line': error.get('line', 0),
                        'Severity': error.get('severity', 'warning')
                    })
            elif tool == 'bandit' and 'issues' in results:
                for issue in results['issues']:
                    all_issues.append({
                        'File': path,
                        'Tool': 'bandit',
                        'Type': issue.get('test_id', 'unknown'),
                        'Message': issue.get('issue_text', ''),
                        'Line': issue.get('line_number', 0),
                        'Severity': issue.get('issue_severity', 'medium')
                    })
            elif tool == 'pydocstyle' and 'issues' in results:
                for issue in results['issues']:
                    all_issues.append({
                        'File': path,
                        'Tool': 'pydocstyle',
                        'Type': issue.get('code', 'unknown'),
                        'Message': issue.get('message', ''),
                        'Line': issue.get('line', 0),
                        'Severity': 'info'
                    })
            elif tool == 'pylint' and 'errors' in results:
                for error in results['errors']:
                    all_issues.append({
                        'File': path,
                        'Tool': 'pylint',
                        'Type': error.get('symbol', 'unknown'),
                        'Message': error.get('message', ''),
                        'Line': error.get('line', 0),
                        'Severity': error.get('type', 'warning')
                    })
    
    # Create a DataFrame for the table
    if all_issues:
        df = pd.DataFrame(all_issues)
        
        # Add filters
        st.subheader("Filter Issues")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            tools = ['All'] + sorted(df['Tool'].unique().tolist())
            selected_tool = st.selectbox("Tool", tools)
        
        with col2:
            severities = ['All'] + sorted(df['Severity'].unique().tolist())
            selected_severity = st.selectbox("Severity", severities)
        
        with col3:
            types = ['All'] + sorted(df['Type'].unique().tolist())
            selected_type = st.selectbox("Type", types)
        
        # Apply filters
        filtered_df = df.copy()
        if selected_tool != 'All':
            filtered_df = filtered_df[filtered_df['Tool'] == selected_tool]
        if selected_severity != 'All':
            filtered_df = filtered_df[filtered_df['Severity'] == selected_severity]
        if selected_type != 'All':
            filtered_df = filtered_df[filtered_df['Type'] == selected_type]
        
        # Display filtered issues
        st.dataframe(filtered_df, use_container_width=True)
    else:
        st.info("No issues found in the project.")

"""
Files page for the PAT Web UI.

This module contains the files page for the PAT Web UI, which displays
detailed information about files in the project.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Any, Dict, List, Optional

from ..components.visualizations import (
    create_file_size_histogram,
    create_complexity_histogram
)


def render_files(metrics: Dict[str, Any], project_path: str):
    """
    Render the files page.
    
    Args:
        metrics: Dictionary containing project metrics
        project_path: Path to the project being analyzed
    """
    st.title("Files Analysis")
    
    # File metrics
    st.header("File Metrics")
    
    # Create columns for metrics
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric(
            label="Total Files",
            value=len(metrics.get('files', {}))
        )
    
    with col2:
        total_lines = sum(f.get('lines', 0) for f in metrics.get('files', {}).values())
        st.metric(
            label="Total Lines",
            value=f"{total_lines:,}"
        )
    
    with col3:
        avg_lines = total_lines / len(metrics.get('files', {})) if metrics.get('files', {}) else 0
        st.metric(
            label="Avg. Lines per File",
            value=f"{avg_lines:.2f}"
        )
    
    # File size histogram
    st.subheader("File Size Distribution")
    file_size_chart = create_file_size_histogram(metrics)
    st.plotly_chart(file_size_chart, use_container_width=True)
    
    # Complexity histogram
    st.subheader("Complexity Distribution")
    complexity_chart = create_complexity_histogram(metrics)
    st.plotly_chart(complexity_chart, use_container_width=True)
    
    # File list
    st.header("File List")
    
    # Create a DataFrame for the table
    file_data = []
    for path, file_metric in metrics.get('files', {}).items():
        issues = 0
        tool_results = file_metric.get('tool_results', {})
        
        for tool, results in tool_results.items():
            if tool == 'mypy' and 'errors' in results:
                issues += len(results['errors'])
            elif tool == 'ruff' and 'errors' in results:
                issues += len(results['errors'])
            elif tool == 'bandit' and 'issues' in results:
                issues += len(results['issues'])
            elif tool == 'pydocstyle' and 'issues' in results:
                issues += len(results['issues'])
            elif tool == 'pylint' and 'errors' in results:
                issues += len(results['errors'])
        
        file_data.append({
            'File': path,
            'Lines': file_metric.get('lines', 0),
            'Complexity': file_metric.get('complexity', 0),
            'Issues': issues
        })
    
    # Create a DataFrame for the table
    if file_data:
        df = pd.DataFrame(file_data)
        
        # Add filters
        st.subheader("Filter Files")
        col1, col2, col3 = st.columns(3)
        
        with col1:
            min_lines = st.number_input("Min Lines", min_value=0, value=0)
        
        with col2:
            min_complexity = st.number_input("Min Complexity", min_value=0, value=0)
        
        with col3:
            min_issues = st.number_input("Min Issues", min_value=0, value=0)
        
        # Apply filters
        filtered_df = df.copy()
        filtered_df = filtered_df[filtered_df['Lines'] >= min_lines]
        filtered_df = filtered_df[filtered_df['Complexity'] >= min_complexity]
        filtered_df = filtered_df[filtered_df['Issues'] >= min_issues]
        
        # Sort options
        sort_by = st.selectbox("Sort By", ["File", "Lines", "Complexity", "Issues"])
        sort_order = st.radio("Sort Order", ["Ascending", "Descending"], horizontal=True)
        
        # Apply sorting
        ascending = sort_order == "Ascending"
        filtered_df = filtered_df.sort_values(by=sort_by, ascending=ascending)
        
        # Display filtered files
        st.dataframe(filtered_df, use_container_width=True)
    else:
        st.info("No files found in the project.")
    
    # File details
    st.header("File Details")
    
    # Create a selectbox for file selection
    file_paths = list(metrics.get('files', {}).keys())
    selected_file = st.selectbox("Select a file", file_paths)
    
    if selected_file and selected_file in metrics.get('files', {}):
        file_metric = metrics['files'][selected_file]
        
        # Display file details
        st.subheader(f"Details for {selected_file}")
        
        # Create columns for metrics
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric(
                label="Lines",
                value=file_metric.get('lines', 0)
            )
        
        with col2:
            st.metric(
                label="Complexity",
                value=file_metric.get('complexity', 0)
            )
        
        with col3:
            # Count issues
            issues = 0
            tool_results = file_metric.get('tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issues += len(results['errors'])
            
            st.metric(
                label="Issues",
                value=issues
            )
        
        # Display issues
        st.subheader("Issues")
        
        # Collect all issues for this file
        file_issues = []
        tool_results = file_metric.get('tool_results', {})
        
        for tool, results in tool_results.items():
            if tool == 'mypy' and 'errors' in results:
                for error in results['errors']:
                    file_issues.append({
                        'Tool': 'mypy',
                        'Type': error.get('type', 'unknown'),
                        'Message': error.get('message', ''),
                        'Line': error.get('line', 0),
                        'Severity': 'error'
                    })
            elif tool == 'ruff' and 'errors' in results:
                for error in results['errors']:
                    file_issues.append({
                        'Tool': 'ruff',
                        'Type': error.get('code', 'unknown'),
                        'Message': error.get('message', ''),
                        'Line': error.get('line', 0),
                        'Severity': error.get('severity', 'warning')
                    })
            elif tool == 'bandit' and 'issues' in results:
                for issue in results['issues']:
                    file_issues.append({
                        'Tool': 'bandit',
                        'Type': issue.get('test_id', 'unknown'),
                        'Message': issue.get('issue_text', ''),
                        'Line': issue.get('line_number', 0),
                        'Severity': issue.get('issue_severity', 'medium')
                    })
            elif tool == 'pydocstyle' and 'issues' in results:
                for issue in results['issues']:
                    file_issues.append({
                        'Tool': 'pydocstyle',
                        'Type': issue.get('code', 'unknown'),
                        'Message': issue.get('message', ''),
                        'Line': issue.get('line', 0),
                        'Severity': 'info'
                    })
            elif tool == 'pylint' and 'errors' in results:
                for error in results['errors']:
                    file_issues.append({
                        'Tool': 'pylint',
                        'Type': error.get('symbol', 'unknown'),
                        'Message': error.get('message', ''),
                        'Line': error.get('line', 0),
                        'Severity': error.get('type', 'warning')
                    })
        
        # Create a DataFrame for the table
        if file_issues:
            df = pd.DataFrame(file_issues)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("No issues found in this file.")
        
        # Display classes and functions
        st.subheader("Classes and Functions")
        
        # Create tabs for classes and functions
        tab1, tab2 = st.tabs(["Classes", "Functions"])
        
        with tab1:
            classes = file_metric.get('classes', [])
            if classes:
                for cls in classes:
                    st.write(f"**{cls}**")
                    docstring = file_metric.get('docstrings', {}).get('classes', {}).get(cls, '')
                    if docstring:
                        st.write(docstring)
            else:
                st.info("No classes found in this file.")
        
        with tab2:
            functions = file_metric.get('functions', [])
            if functions:
                for func in functions:
                    st.write(f"**{func}**")
                    docstring = file_metric.get('docstrings', {}).get('functions', {}).get(func, '')
                    if docstring:
                        st.write(docstring)
            else:
                st.info("No functions found in this file.")
    else:
        st.info("No file selected.")

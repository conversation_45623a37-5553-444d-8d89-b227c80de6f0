"""
Utility functions and constants for the PAT Web UI.
"""
import json
import re
from datetime import datetime
from pathlib import Path
import yaml
import copy

# Import streamlit at the module level
import streamlit as st

# Defer pandas import to avoid circular imports
# We'll import it within functions that need it

# Constants
BASE_CONFIG_PATH = Path(__file__).parent.parent / "PAT_tool" / "config_full.yaml"

def format_log_entry(entry: dict) -> str:
    """Format a log entry for display.

    Args:
        entry: Log entry dictionary

    Returns:
        Formatted log entry string
    """
    timestamp = entry.get('timestamp', '')
    level = entry.get('level', 'INFO')
    message = entry.get('message', '')

    # Format timestamp
    if timestamp:
        try:
            dt = datetime.fromisoformat(timestamp)
            timestamp = dt.strftime('%H:%M:%S')
        except (ValueError, TypeError):
            pass

    # Color-code by level
    if level == 'ERROR':
        return f"<span style='color:red'>[{timestamp}] {level}: {message}</span>"
    elif level == 'WARNING':
        return f"<span style='color:orange'>[{timestamp}] {level}: {message}</span>"
    elif level == 'DEBUG':
        return f"<span style='color:gray'>[{timestamp}] {level}: {message}</span>"
    else:
        return f"[{timestamp}] {level}: {message}"

def get_available_tools():
    """Get the list of available tools.

    Returns:
        Set of available tool names
    """
    # This is a simplified version - in practice, we'd check for actual availability
    return {
        "Ruff (Linter)",
        "Bandit (Security)",
        "Pytest (Tests)",
        "Mypy (Type Checker)",
        "Pyright (Type Checker)",
        "Black (Formatter)",
        "Isort (Import Sorter)",
        "Pylint (Linter)",
        "meta_system_separation"
    }

def load_base_config():
    """Loads the base YAML configuration file.

    Returns:
        Dictionary containing the configuration
    """
    if BASE_CONFIG_PATH.exists():
        try:
            with open(BASE_CONFIG_PATH, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            st.error(f"Error loading base config {BASE_CONFIG_PATH}: {e}")
            return {}
    else:
        st.warning(f"Base config file not found: {BASE_CONFIG_PATH}. Using empty config.")
        return {}

# PAT Web UI (Modular Version)

This is a modularized version of the PAT Web UI, a browser-based interface for viewing and analyzing the results of PAT analysis runs.

## Directory Structure

```
pat_webui/
├── __init__.py             # Package initialization
├── app.py                  # Main application entry point
├── config.py               # Configuration management
├── utils.py                # Utility functions and constants
├── components/             # UI components
│   ├── __init__.py
│   ├── sidebar.py          # Sidebar components
│   ├── forms.py            # Form components
│   └── visualizations.py   # Visualization components
└── pages/                  # UI pages
    ├── __init__.py
    ├── dashboard.py        # Dashboard page
    ├── issues.py           # Issues page
    ├── files.py            # Files page
    └── meta_systems.py     # Meta-systems page
```

## Features

- **Dashboard**: Overview of project metrics, issue summaries, and recommendations
- **Issues**: Detailed view of all issues found in the project
- **Files**: File-level metrics and issues
- **Meta-Systems**: Analysis of meta-system separation and dependencies

## Usage

To run the PAT Web UI:

```bash
streamlit run pat_webui.py
```

## Dependencies

- streamlit
- pandas
- plotly
- networkx

## Components

### App

The `app.py` file is the main entry point for the application. It initializes the application, renders the sidebar and main content, and handles navigation between pages.

### Config

The `config.py` file handles configuration management, including loading, saving, and generating UI widgets for configuration options.

### Utils

The `utils.py` file contains utility functions and constants used throughout the application.

### Components

The `components` directory contains reusable UI components:

- `sidebar.py`: Renders the sidebar with project path, tool selection, and other inputs
- `forms.py`: Renders form components for configuration and other inputs
- `visualizations.py`: Renders visualizations for analysis results

### Pages

The `pages` directory contains the main UI pages:

- `home.py`: Renders the home page with welcome message and instructions
- `analysis.py`: Renders the analysis page with analysis execution and log display
- `results.py`: Renders the results page with tabs for different result views
- `settings.py`: Renders the settings page with configuration options

## Benefits of Modularization

- **Improved maintainability**: Each component is in its own file, making it easier to understand and modify
- **Better organization**: Related functionality is grouped together
- **Easier testing**: Components can be tested in isolation
- **Reduced file size**: Each file is smaller and more focused
- **Better collaboration**: Multiple developers can work on different components without conflicts

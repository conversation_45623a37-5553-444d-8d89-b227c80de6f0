# Vibe Check - Project Analysis Tool - Custom Configuration
file_extensions:
  - .py
analyze_docs: true
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/env/**"
  - "**/.git/**"
  - "**/node_modules/**"
  - "**/.pytest_cache/**"
  - "**/*.pyc"

# Tools configuration
tools:
  ruff:
    enabled: true
    args:
      - "--select=E,F,W,I"
      - "--ignore=E501"  # Ignore line length errors
  
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
    check_all_files: false
  
  bandit:
    enabled: true
    args:
      - "--recursive"
  
  complexity:
    enabled: true

# Reporting configuration
reporting:
  formats:
    - markdown
    - json
  generate_summary: true
  generate_issue_report: true
  generate_metrics_report: true
  generate_recommendations: true

# Performance configuration
performance:
  parallel: true
  max_workers: 4  # Number of workers for parallel processing
  timeout: 120  # Seconds
  cache_results: true
  cache_dir: ".vibe_check_cache"

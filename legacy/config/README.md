# Configuration Files

This directory contains configuration files for Vibe Check.

## Files

- `simple_config.yaml`: A simple configuration file for basic analysis
- `simple_vibe_config.yaml`: A simple configuration file with Vibe Check-specific settings
- `vibe_check_config.yaml`: The main configuration file for Vibe Check

## Usage

To use a configuration file with Vibe Check, use the `--config` option:

```bash
vibe-check analyze /path/to/project --config config/vibe_check_config.yaml
```

## Creating Custom Configurations

You can create custom configuration files by copying and modifying the existing ones. The configuration files use YAML format and support the following sections:

- `analysis`: Settings for the analysis process
- `reporting`: Settings for report generation
- `visualization`: Settings for visualization generation
- `tools`: Settings for individual analysis tools

See the documentation for more details on available configuration options.

# Simple Vibe Check Configuration
file_extensions:
  - .py
analyze_docs: true
exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"
  - "**/.git/**"

# Use .gitignore patterns if available
use_gitignore: true

# Tools configuration
tools:
  ruff:
    enabled: true
    args:
      - "--select=E,F,W"
      - "--ignore=E501"

  mypy:
    enabled: false

  bandit:
    enabled: false

# Reporting configuration
reporting:
  formats:
    - text
    - json
  generate_summary: true
  generate_issue_report: true

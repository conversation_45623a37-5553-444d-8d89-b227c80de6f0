# Analysis Tool Requirements
# =====================
# Core analysis dependencies

# Graph and Visualization
networkx>=2.6.3
matplotlib>=3.5.0
graphviz>=0.19.1

# Code Analysis
radon>=5.1.0
astroid>=2.9.0
pylint>=2.12.0
pydeps>=1.10.18

# Additional Analysis Tools
mccabe>=0.6.1  # McCabe complexity checker
pycodestyle>=2.8.0  # Style checker
mypy>=0.910  # Type checker
coverage>=6.2  # Code coverage tool
tqdm>=4.62.0  # Progress bars

# Optional but recommended
bandit>=1.7.0  # Security checker
vulture>=2.3  # Dead code detector

# Added from the code block
pandas>=1.3.5
numpy>=1.21.5 

# --- Added Missing Dependencies ---
PyYAML>=6.0
rich>=10.0.0
ruff>=0.1.0 # Replace flake8/pycodestyle etc.
black>=22.0.0
isort>=5.10.0
pydocstyle>=6.1.0
pipdeptree>=2.0.0
safety>=2.0.0
# pyan3 seems difficult to install via pip, might need manual setup or alternative
# Assuming pyan3 is available in PATH or handled manually
# For now, we will skip adding pyan3 here.
pyright>=1.1.200 # Check specific version if needed
pyre-check>=0.9.0 # Meta's type checker
PyContracts>=1.8.0 # Contract checking
hypothesis>=6.0.0 # Property-based testing (often used with pytest)
pytest>=7.0.0 # Required for Hypothesis integration
# tlc-python-wrapper # For TLA+ stage - check installation method

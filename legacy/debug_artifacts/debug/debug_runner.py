"""Debug runner for testing actor system components."""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, Optional

from vibe_check.core.actor_system import ActorSystem
from vibe_check.core.actor_system.actors import (
    FileActor,
    ProjectActor,
    ReportActor,
    ToolActor,
    VisualizationActor,
)

logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger("debug_runner")

async def run_actor_system_test(project_path: str) -> None:
    """Run a simplified test of the actor system."""
    logger.info("Initializing actor system test")
    
    test_configs: Dict[Any, Dict[str, Any]] = {
        FileActor: {"path": project_path},
        ToolActor: {"tools": ["ruff", "mypy"]},
        ReportActor: {"output_dir": "debug_results"},
        VisualizationActor: {"enabled": True}
    }

    try:
        # Test each actor individually - this is more reliable
        for actor_class, config in test_configs.items():
            logger.info(f"Testing {actor_class.__name__}")
            await test_individual_actor(actor_class, config)
            
    except Exception as e:
        logger.error(f"Error during actor system test: {e}", exc_info=True)

async def test_individual_actor(actor_class: Any, config: Dict[str, Any]) -> None:
    """Test a single actor in isolation."""
    actor_system = ActorSystem()
    logger.debug(f"Testing {actor_class.__name__}")

    try:
        # Create the actor with appropriate parameters based on the actor class
        if actor_class == FileActor:
            actor = actor_class(actor_system, config.get("path"))
        elif actor_class == ReportActor:
            actor = actor_class(actor_system, config.get("output_dir"))
        elif actor_class == VisualizationActor:
            actor = actor_class(actor_system, config.get("output_dir"))
        else:
            actor = actor_class(actor_system)

        # Initialize and start the actor
        await actor.initialize(config)
        await actor.start()

        # Wait for a moment to let the actor process messages
        await asyncio.sleep(1)

        logger.info(f"Actor {actor.actor_id} tested successfully")
    except Exception as e:
        logger.error(f"Actor test error: {e}", exc_info=True)
    finally:
        await actor_system.shutdown()

def main() -> None:
    """Main debug runner function."""
    test_project = "../tests/test_project"

    logger.info("Starting debug runner")

    # Run tests
    asyncio.run(run_actor_system_test(test_project))

    # Test individual actors
    test_configs: Dict[Any, Dict[str, Any]] = {
        FileActor: {"path": test_project},
        ToolActor: {"tools": ["ruff", "mypy"]},
        ReportActor: {"output_dir": "debug_results"},
        VisualizationActor: {"enabled": True}
    }

    for actor_class, config in test_configs.items():
        asyncio.run(test_individual_actor(actor_class, config))

if __name__ == "__main__":
    main()

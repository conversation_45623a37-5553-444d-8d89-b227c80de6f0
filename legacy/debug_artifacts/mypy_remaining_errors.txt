vibe_check/tools/custom_rules/python_rules.py:23: error: Need type annotation for "issues" (hint: "issues: List[<type>] = ...")  [var-annotated]
vibe_check/core/models/file_metrics.py:196: error: Missing return statement  [return]
vibe_check/core/models/file_metrics.py:206: error: Missing return statement  [return]
vibe_check/core/utils/file_utils.py:32: error: Name "Optional" is not defined  [name-defined]
vibe_check/core/utils/file_utils.py:32: note: Did you forget to import it from "typing"? (Suggestion: "from typing import Optional")
vibe_check/core/utils/file_utils.py:61: error: Name "Optional" is not defined  [name-defined]
vibe_check/core/utils/file_utils.py:61: note: Did you forget to import it from "typing"? (Suggestion: "from typing import Optional")
vibe_check/core/utils/file_utils.py:92: error: Name "Optional" is not defined  [name-defined]
vibe_check/core/utils/file_utils.py:92: note: Did you forget to import it from "typing"? (Suggestion: "from typing import Optional")
vibe_check/core/utils/file_utils.py:93: error: Name "Optional" is not defined  [name-defined]
vibe_check/core/utils/file_utils.py:93: note: Did you forget to import it from "typing"? (Suggestion: "from typing import Optional")
vibe_check/tools/parsers/ruff_parser.py:177: error: No overload variant of "__add__" of "list" matches argument type "int"  [operator]
vibe_check/tools/parsers/ruff_parser.py:177: note: Possible overload variants:
vibe_check/tools/parsers/ruff_parser.py:177: note:     def __add__(self, List[Dict[str, Any]], /) -> List[Dict[str, Any]]
vibe_check/tools/parsers/ruff_parser.py:177: note:     def [_S] __add__(self, List[_S], /) -> List[Union[_S, Dict[str, Any]]]
vibe_check/tools/parsers/ruff_parser.py:177: note: Left operand is of type "Union[List[Dict[str, Any]], int]"
vibe_check/tools/parsers/ruff_parser.py:177: error: Incompatible types in assignment (expression has type "Union[Any, int]", target has type "List[Dict[str, Any]]")  [assignment]
vibe_check/tools/parsers/pylint_parser.py:133: error: No overload variant of "__add__" of "list" matches argument type "int"  [operator]
vibe_check/tools/parsers/pylint_parser.py:133: note: Possible overload variants:
vibe_check/tools/parsers/pylint_parser.py:133: note:     def __add__(self, List[Dict[str, Any]], /) -> List[Dict[str, Any]]
vibe_check/tools/parsers/pylint_parser.py:133: note:     def [_S] __add__(self, List[_S], /) -> List[Union[_S, Dict[str, Any]]]
vibe_check/tools/parsers/pylint_parser.py:133: note: Left operand is of type "Union[List[Dict[str, Any]], int]"
vibe_check/tools/parsers/pylint_parser.py:133: error: Incompatible types in assignment (expression has type "Union[Any, int]", target has type "List[Dict[str, Any]]")  [assignment]
vibe_check/tools/parsers/pyflakes_parser.py:140: error: No overload variant of "__add__" of "list" matches argument type "int"  [operator]
vibe_check/tools/parsers/pyflakes_parser.py:140: note: Possible overload variants:
vibe_check/tools/parsers/pyflakes_parser.py:140: note:     def __add__(self, List[Dict[str, Any]], /) -> List[Dict[str, Any]]
vibe_check/tools/parsers/pyflakes_parser.py:140: note:     def [_S] __add__(self, List[_S], /) -> List[Union[_S, Dict[str, Any]]]
vibe_check/tools/parsers/pyflakes_parser.py:140: note: Left operand is of type "Union[List[Dict[str, Any]], int]"
vibe_check/tools/parsers/pyflakes_parser.py:140: error: Incompatible types in assignment (expression has type "Union[Any, int]", target has type "List[Dict[str, Any]]")  [assignment]
vibe_check/tools/parsers/mypy_parser.py:153: error: No overload variant of "__add__" of "list" matches argument type "int"  [operator]
vibe_check/tools/parsers/mypy_parser.py:153: note: Possible overload variants:
vibe_check/tools/parsers/mypy_parser.py:153: note:     def __add__(self, List[Dict[str, Any]], /) -> List[Dict[str, Any]]
vibe_check/tools/parsers/mypy_parser.py:153: note:     def [_S] __add__(self, List[_S], /) -> List[Union[_S, Dict[str, Any]]]
vibe_check/tools/parsers/mypy_parser.py:153: note: Left operand is of type "Union[List[Dict[str, Any]], int]"
vibe_check/tools/parsers/mypy_parser.py:153: error: Incompatible types in assignment (expression has type "Union[Any, int]", target has type "List[Dict[str, Any]]")  [assignment]
vibe_check/tools/parsers/custom_rules_parser.py:135: error: No overload variant of "__add__" of "list" matches argument type "int"  [operator]
vibe_check/tools/parsers/custom_rules_parser.py:135: note: Possible overload variants:
vibe_check/tools/parsers/custom_rules_parser.py:135: note:     def __add__(self, List[Dict[str, Any]], /) -> List[Dict[str, Any]]
vibe_check/tools/parsers/custom_rules_parser.py:135: note:     def [_S] __add__(self, List[_S], /) -> List[Union[_S, Dict[str, Any]]]
vibe_check/tools/parsers/custom_rules_parser.py:135: note: Left operand is of type "Union[List[Dict[str, Any]], int]"
vibe_check/tools/parsers/custom_rules_parser.py:135: error: Incompatible types in assignment (expression has type "Union[Any, int]", target has type "List[Dict[str, Any]]")  [assignment]
vibe_check/tools/parsers/custom_rules_parser.py:139: error: No overload variant of "__add__" of "list" matches argument type "int"  [operator]
vibe_check/tools/parsers/custom_rules_parser.py:139: note: Possible overload variants:
vibe_check/tools/parsers/custom_rules_parser.py:139: note:     def __add__(self, List[Dict[str, Any]], /) -> List[Dict[str, Any]]
vibe_check/tools/parsers/custom_rules_parser.py:139: note:     def [_S] __add__(self, List[_S], /) -> List[Union[_S, Dict[str, Any]]]
vibe_check/tools/parsers/custom_rules_parser.py:139: note: Left operand is of type "Union[List[Dict[str, Any]], int]"
vibe_check/tools/parsers/custom_rules_parser.py:139: error: Incompatible types in assignment (expression has type "Union[Any, int]", target has type "List[Dict[str, Any]]")  [assignment]
vibe_check/tools/parsers/bandit_parser.py:162: error: No overload variant of "__add__" of "list" matches argument type "int"  [operator]
vibe_check/tools/parsers/bandit_parser.py:162: note: Possible overload variants:
vibe_check/tools/parsers/bandit_parser.py:162: note:     def __add__(self, List[Dict[str, Any]], /) -> List[Dict[str, Any]]
vibe_check/tools/parsers/bandit_parser.py:162: note:     def [_S] __add__(self, List[_S], /) -> List[Union[_S, Dict[str, Any]]]
vibe_check/tools/parsers/bandit_parser.py:162: note: Left operand is of type "Union[List[Dict[str, Any]], int]"
vibe_check/tools/parsers/bandit_parser.py:162: error: Incompatible types in assignment (expression has type "Union[Any, int]", target has type "List[Dict[str, Any]]")  [assignment]
vibe_check/core/progress.py:801: error: Incompatible types in assignment (expression has type "TaskID", variable has type "None")  [assignment]
vibe_check/core/progress.py:813: error: Statement is unreachable  [unreachable]
vibe_check/core/progress.py:827: error: Statement is unreachable  [unreachable]
vibe_check/core/progress.py:836: error: Statement is unreachable  [unreachable]
vibe_check/core/progress.py:868: error: Statement is unreachable  [unreachable]
vibe_check/core/progress.py:880: error: Statement is unreachable  [unreachable]
vibe_check/core/progress.py:893: error: Statement is unreachable  [unreachable]
vibe_check/core/progress.py:906: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/progress.py:907: error: Statement is unreachable  [unreachable]
vibe_check/core/progress.py:918: error: Statement is unreachable  [unreachable]
vibe_check/core/progress.py:937: error: Statement is unreachable  [unreachable]
vibe_check/core/models/progress_tracker.py:30: error: Name "Progress" already defined (possibly by an import)  [no-redef]
vibe_check/core/models/progress_tracker.py:32: error: Name "Console" already defined (possibly by an import)  [no-redef]
vibe_check/core/models/progress_tracker.py:34: error: Name "Status" already defined (possibly by an import)  [no-redef]
vibe_check/core/models/progress_tracker.py:36: error: Name "TaskID" already defined (possibly by an import)  [no-redef]
vibe_check/core/models/progress_tracker.py:120: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:120: note: Use "-> None" if function does not return a value
vibe_check/core/models/progress_tracker.py:148: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:153: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:153: note: Use "-> None" if function does not return a value
vibe_check/core/models/progress_tracker.py:157: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:169: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:169: note: Use "-> None" if function does not return a value
vibe_check/core/models/progress_tracker.py:174: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:187: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:202: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:202: note: Use "-> None" if function does not return a value
vibe_check/core/models/progress_tracker.py:207: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:225: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:235: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:278: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:286: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
vibe_check/core/models/progress_tracker.py:291: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:291: note: Use "-> None" if function does not return a value
vibe_check/core/models/progress_tracker.py:298: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:309: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:317: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
vibe_check/core/models/progress_tracker.py:325: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:343: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:343: note: Use "-> None" if function does not return a value
vibe_check/core/models/progress_tracker.py:349: error: Statement is unreachable  [unreachable]
vibe_check/core/models/progress_tracker.py:379: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:402: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:414: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:423: error: Statement is unreachable  [unreachable]
vibe_check/core/models/progress_tracker.py:434: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:438: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:448: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:448: note: Use "-> None" if function does not return a value
vibe_check/core/models/progress_tracker.py:452: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:461: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:471: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:471: note: Use "-> None" if function does not return a value
vibe_check/core/models/progress_tracker.py:475: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:485: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/models/progress_tracker.py:495: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/tools/runners/ruff_runner.py:27: error: Incompatible default for argument "config" (default has type "None", argument has type "Dict[str, Any]")  [assignment]
vibe_check/tools/runners/ruff_runner.py:27: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/tools/runners/ruff_runner.py:27: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/tools/runners/ruff_runner.py:78: error: Need type annotation for "by_type" (hint: "by_type: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/tools/runners/pylint_runner.py:25: error: Incompatible default for argument "config" (default has type "None", argument has type "Dict[str, Any]")  [assignment]
vibe_check/tools/runners/pylint_runner.py:25: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/tools/runners/pylint_runner.py:25: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/tools/runners/pylint_runner.py:37: error: Statement is unreachable  [unreachable]
vibe_check/tools/runners/pyflakes_runner.py:24: error: Incompatible default for argument "config" (default has type "None", argument has type "Dict[str, Any]")  [assignment]
vibe_check/tools/runners/pyflakes_runner.py:24: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/tools/runners/pyflakes_runner.py:24: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/tools/runners/pyflakes_runner.py:36: error: Statement is unreachable  [unreachable]
vibe_check/tools/runners/mypy_runner.py:26: error: Incompatible default for argument "config" (default has type "None", argument has type "Dict[str, Any]")  [assignment]
vibe_check/tools/runners/mypy_runner.py:26: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/tools/runners/mypy_runner.py:26: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/tools/runners/mypy_runner.py:38: error: Statement is unreachable  [unreachable]
vibe_check/tools/runners/doc_analyzer_runner.py:26: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/tools/runners/doc_analyzer_runner.py:26: note: Use "-> None" if function does not return a value
vibe_check/tools/runners/doc_analyzer_runner.py:36: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/doc_analyzer_runner.py:62: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/doc_analyzer_runner.py:100: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/doc_analyzer_runner.py:133: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/doc_analyzer_runner.py:194: error: Incompatible default for argument "config" (default has type "None", argument has type "Dict[str, Any]")  [assignment]
vibe_check/tools/runners/doc_analyzer_runner.py:194: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/tools/runners/doc_analyzer_runner.py:194: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/tools/runners/custom_rules_runner.py:22: error: Incompatible default for argument "config" (default has type "None", argument has type "Dict[str, Any]")  [assignment]
vibe_check/tools/runners/custom_rules_runner.py:22: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/tools/runners/custom_rules_runner.py:22: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/tools/runners/custom_rules_runner.py:34: error: Statement is unreachable  [unreachable]
vibe_check/tools/runners/complexity_runner.py:26: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:26: note: Use "-> None" if function does not return a value
vibe_check/tools/runners/complexity_runner.py:35: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:75: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:84: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:95: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:95: note: Use "-> None" if function does not return a value
vibe_check/tools/runners/complexity_runner.py:100: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:100: note: Use "-> None" if function does not return a value
vibe_check/tools/runners/complexity_runner.py:104: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:116: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:128: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:140: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:152: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:169: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:180: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:191: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/tools/runners/complexity_runner.py:207: error: Incompatible default for argument "config" (default has type "None", argument has type "Dict[str, Any]")  [assignment]
vibe_check/tools/runners/complexity_runner.py:207: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/tools/runners/complexity_runner.py:207: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/tools/runners/bandit_runner.py:25: error: Incompatible default for argument "config" (default has type "None", argument has type "Dict[str, Any]")  [assignment]
vibe_check/tools/runners/bandit_runner.py:25: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/tools/runners/bandit_runner.py:25: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/core/actor_system/lifecycle/starter.py:175: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/actor_system/messaging/processor.py:66: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actor.py:67: error: Cannot assign to a type  [misc]
vibe_check/core/actor_system/actor.py:67: error: Incompatible types in assignment (expression has type "Type[_FallbackActorState]", variable has type "Type[ActorState]")  [assignment]
vibe_check/core/actor_system/actor.py:81: error: Cannot assign to a type  [misc]
vibe_check/core/actor_system/actor.py:81: error: Incompatible types in assignment (expression has type "Type[_FallbackActorInitializationError]", variable has type "Type[ActorInitializationError]")  [assignment]
vibe_check/core/actor_system/actor.py:83: error: All conditional function variants must have identical signatures  [misc]
vibe_check/core/actor_system/actor.py:83: note: Original:
vibe_check/core/actor_system/actor.py:83: note:     def get_initializer() -> ActorInitializer
vibe_check/core/actor_system/actor.py:83: note: Redefinition:
vibe_check/core/actor_system/actor.py:83: note:     def get_initializer() -> Optional[Any]
vibe_check/core/bridge/tool_bridge.py:36: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/bridge/tool_bridge.py:36: note: Use "-> None" if function does not return a value
vibe_check/core/bridge/tool_bridge.py:88: error: Returning Any from function declared to return "Optional[ToolRunner]"  [no-any-return]
vibe_check/core/bridge/tool_bridge.py:117: error: Returning Any from function declared to return "Optional[ToolParser]"  [no-any-return]
vibe_check/core/actor_system/supervisor_actor.py:48: error: Incompatible types in assignment (expression has type "Dict[str, Dict[str, Any]]", base class "Actor" defined the type as "Set[str]")  [assignment]
vibe_check/core/actor_system/actor_system.py:30: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/core/actor_system/actor_system.py:30: note: Use "-> None" if function does not return a value
vibe_check/core/actor_system/actor_system.py:44: error: "Actor" has no attribute "id"  [attr-defined]
vibe_check/core/actor_system/actor_system.py:45: error: "Actor" has no attribute "id"  [attr-defined]
vibe_check/core/actor_system/actor_system.py:47: error: "Actor" has no attribute "id"  [attr-defined]
vibe_check/core/actor_system/actor_system.py:48: error: "Actor" has no attribute "system"  [attr-defined]
vibe_check/core/actor_system/actor_system.py:59: error: "Actor" has no attribute "system"  [attr-defined]
vibe_check/core/actor_system/actor_system.py:265: error: Incompatible default for argument "error_details" (default has type "None", argument has type "str")  [assignment]
vibe_check/core/actor_system/actor_system.py:265: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/core/actor_system/actor_system.py:265: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/core/actor_system/actor_system.py:304: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/core/actor_system/actor_system.py:339: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/core/actor_system/actor_system.py:384: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actor_pool.py:60: error: Need type annotation for "_task_queue"  [var-annotated]
vibe_check/core/actor_system/actor_pool.py:61: error: Need type annotation for "_result_queue"  [var-annotated]
vibe_check/core/actor_system/actor_pool.py:159: error: Argument "state" to "ActorInitializationError" has incompatible type "str"; expected "ActorState"  [arg-type]
vibe_check/core/actor_system/actor_pool.py:270: error: Argument "state" to "ActorInitializationError" has incompatible type "str"; expected "ActorState"  [arg-type]
vibe_check/core/actor_system/actor_pool.py:285: error: Incompatible types in assignment (expression has type "Task[None]", variable has type "None")  [assignment]
vibe_check/core/actor_system/actor_pool.py:285: note: Maybe you forgot to use "await"?
vibe_check/core/actor_system/actor_pool.py:286: error: Incompatible types in assignment (expression has type "Task[None]", variable has type "None")  [assignment]
vibe_check/core/actor_system/actor_pool.py:286: note: Maybe you forgot to use "await"?
vibe_check/core/actor_system/actor_pool.py:323: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actor_pool.py:324: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actor_pool.py:327: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actor_pool.py:328: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actor_pool.py:348: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actor_pool.py:349: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actor_pool.py:369: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actor_pool.py:370: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actor_pool.py:460: error: Need type annotation for "result_future"  [var-annotated]
vibe_check/core/actor_system/actor_pool.py:566: error: Function does not return a value (it only ever returns None)  [func-returns-value]
vibe_check/core/actor_system/distributed/node.py:67: error: Need type annotation for "_outgoing_queue"  [var-annotated]
vibe_check/core/actor_system/distributed/node.py:79: error: Incompatible types in assignment (expression has type "Server", variable has type "None")  [assignment]
vibe_check/core/actor_system/distributed/node.py:84: error: Incompatible types in assignment (expression has type "Task[Any]", variable has type "None")  [assignment]
vibe_check/core/actor_system/distributed/node.py:84: error: "None" has no attribute "serve_forever"  [attr-defined]
vibe_check/core/actor_system/distributed/node.py:87: error: Incompatible types in assignment (expression has type "Task[None]", variable has type "None")  [assignment]
vibe_check/core/actor_system/distributed/node.py:87: note: Maybe you forgot to use "await"?
vibe_check/core/actor_system/distributed/node.py:90: error: Incompatible types in assignment (expression has type "Task[None]", variable has type "None")  [assignment]
vibe_check/core/actor_system/distributed/node.py:90: note: Maybe you forgot to use "await"?
vibe_check/core/actor_system/distributed/node.py:93: error: Incompatible types in assignment (expression has type "Task[None]", variable has type "None")  [assignment]
vibe_check/core/actor_system/distributed/node.py:93: note: Maybe you forgot to use "await"?
vibe_check/core/actor_system/distributed/node.py:113: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/distributed/node.py:114: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/distributed/node.py:127: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/distributed/node.py:131: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/distributed/node.py:132: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/distributed/node.py:329: error: Argument 1 to "get_actor" of "ActorRegistry" has incompatible type "Optional[Any]"; expected "str"  [arg-type]
vibe_check/core/actor_system/distributed/node.py:337: error: Enum index should be a string (actual index type "Optional[Any]")  [misc]
vibe_check/core/actor_system/actor_refactored.py:352: error: Incompatible types in assignment (expression has type "Optional[vibe_check.core.actor_system.actor.Actor]", variable has type "Optional[vibe_check.core.actor_system.actor_refactored.Actor]")  [assignment]
vibe_check/core/actor_system/distributed/distributed_actor.py:273: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/core/actor_system/distributed/distributed_actor.py:298: error: Need type annotation for "result_future"  [var-annotated]
vibe_check/core/actor_system/distributed/distributed_actor.py:331: error: Name "handle_remote_result" already defined on line 227  [no-redef]
vibe_check/core/orchestration/actor_lifecycle_manager.py:139: error: Need type annotation for "initialized_actors" (hint: "initialized_actors: Set[<type>] = ...")  [var-annotated]
vibe_check/core/orchestration/actor_connector.py:86: error: List comprehension has incompatible type List[str]; expected List[Actor]  [misc]
vibe_check/core/models/project_metrics.py:115: error: Need type annotation for "result" (hint: "result: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/models/project_metrics.py:360: error: Argument 1 to "sorted" has incompatible type "Union[int, DiDegreeView[Any]]"; expected "Iterable[Tuple[Any, float]]"  [arg-type]
vibe_check/core/models/project_metrics.py:377: error: Argument 1 to "sorted" has incompatible type "Union[int, DiDegreeView[Any]]"; expected "Iterable[Tuple[Any, float]]"  [arg-type]
vibe_check/ui/visualization/visualization_generator.py:128: error: Cannot find implementation or library stub for module named "graphviz"  [import-not-found]
vibe_check/ui/visualization/visualization_generator.py:128: note: See https://mypy.readthedocs.io/en/stable/running_mypy.html#missing-imports
vibe_check/ui/visualization/visualization_generator.py:248: error: Need type annotation for "severity_counts" (hint: "severity_counts: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/ui/visualization/interactive_charts.py:141: error: Need type annotation for "severity_counts" (hint: "severity_counts: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/ui/visualization/interactive_charts.py:209: error: Need type annotation for "tool_counts" (hint: "tool_counts: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/ui/visualization/charts.py:120: error: Need type annotation for "severity_counts"  [var-annotated]
vibe_check/ui/visualization/charts.py:181: error: Need type annotation for "tool_counts"  [var-annotated]
vibe_check/ui/reporting/report_generator.py:193: error: Need type annotation for "severity_counts" (hint: "severity_counts: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/trend_analysis/trend_storage.py:26: error: Incompatible default for argument "storage_dir" (default has type "None", argument has type "Union[str, Path]")  [assignment]
vibe_check/core/trend_analysis/trend_storage.py:26: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/core/trend_analysis/trend_storage.py:26: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/core/trend_analysis/trend_storage.py:35: error: Statement is unreachable  [unreachable]
vibe_check/core/trend_analysis/trend_storage.py:182: error: "ProjectMetrics" has no attribute "avg_docstring_coverage"; maybe "avg_doc_coverage"?  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:56: error: Need type annotation for "file_metrics" (hint: "file_metrics: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/visualization_actor.py:99: error: "Type[MessageType]" has no attribute "VISUALIZATION_CONFIGURED"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:111: error: "Type[ProjectMetrics]" has no attribute "from_dict"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:116: error: "Type[FileMetrics]" has no attribute "from_dict"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:122: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actors/visualization_actor.py:123: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/visualization_actor.py:145: error: "Type[MessageType]" has no attribute "VISUALIZATION_COMPLETED"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:156: error: "None" has no attribute "project_name"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:159: error: Need type annotation for "G"  [var-annotated]
vibe_check/core/actor_system/actors/visualization_actor.py:162: error: "None" has no attribute "dependencies"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:171: error: Argument 1 to "dict" has incompatible type "Union[int, DiDegreeView[Any]]"; expected "Iterable[Tuple[Any, float]]"  [arg-type]
vibe_check/core/actor_system/actors/visualization_actor.py:177: error: "None" has no attribute "edge_weights"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:181: error: Argument "figsize" to "figure" has incompatible type "object"; expected "Optional[Tuple[float, float]]"  [arg-type]
vibe_check/core/actor_system/actors/visualization_actor.py:182: error: Argument "dpi" to "figure" has incompatible type "object"; expected "Optional[float]"  [arg-type]
vibe_check/core/actor_system/actors/visualization_actor.py:185: error: Argument 1 to "len" has incompatible type "Iterator[Any]"; expected "Sized"  [arg-type]
vibe_check/core/actor_system/actors/visualization_actor.py:196: error: Argument 1 to "len" has incompatible type "Iterator[Any]"; expected "Sized"  [arg-type]
vibe_check/core/actor_system/actors/visualization_actor.py:197: error: Argument 1 to "get_cmap" has incompatible type "object"; expected "Union[Colormap, str, None]"  [arg-type]
vibe_check/core/actor_system/actors/visualization_actor.py:225: error: "None" has no attribute "project_name"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:250: error: Argument "dpi" to "figure" has incompatible type "object"; expected "Optional[float]"  [arg-type]
vibe_check/core/actor_system/actors/visualization_actor.py:277: error: "None" has no attribute "project_name"  [attr-defined]
vibe_check/core/actor_system/actors/visualization_actor.py:280: error: Need type annotation for "metrics" (hint: "metrics: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/visualization_actor.py:284: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/visualization_actor.py:300: error: Argument "dpi" to "figure" has incompatible type "object"; expected "Optional[float]"  [arg-type]
vibe_check/core/actor_system/actors/visualization_actor.py:371: error: "Type[MessageType]" has no attribute "VISUALIZATION_RESULT"  [attr-defined]
vibe_check/core/actor_system/actors/file_actor.py:104: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/file_actor.py:124: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/file_actor.py:176: error: Property "actor_id" defined in "Actor" is read-only  [misc]
vibe_check/core/actor_system/actors/file_actor.py:180: error: Incompatible types in assignment (expression has type "Dict[str, Any]", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/file_actor.py:184: error: Incompatible types in assignment (expression has type "FileMetrics", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/file_actor.py:187: error: "None" has no attribute "size"  [attr-defined]
vibe_check/core/actor_system/actors/file_actor.py:188: error: "None" has no attribute "lines"  [attr-defined]
vibe_check/core/actor_system/actors/file_actor.py:188: error: "None" has no attribute "get"  [attr-defined]
vibe_check/core/actor_system/actors/file_actor.py:189: error: "None" has no attribute "complexity"  [attr-defined]
vibe_check/core/actor_system/actors/file_actor.py:189: error: "None" has no attribute "get"  [attr-defined]
vibe_check/core/actor_system/actors/file_actor.py:193: error: Argument 1 to "add_file_metrics" of "ProjectMetrics" has incompatible type "None"; expected "FileMetrics"  [arg-type]
vibe_check/core/actor_system/actors/file_actor.py:222: error: No return value expected  [return-value]
vibe_check/core/actor_system/actors/file_actor.py:288: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/file_actor.py:303: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/file_actor.py:365: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/file_actor.py:618: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/file_actor.py:625: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/file_actor.py:652: error: "None" has no attribute "to_dict"  [attr-defined]
vibe_check/ui/web/components.py:14: error: Cannot find implementation or library stub for module named "altair"  [import-not-found]
vibe_check/ui/web/components.py:16: error: Cannot find implementation or library stub for module named "streamlit"  [import-not-found]
vibe_check/ui/web/components.py:35: error: Function "builtins.callable" is not valid as a type  [valid-type]
vibe_check/ui/web/components.py:35: note: Perhaps you meant "typing.Callable" instead of "callable"?
vibe_check/ui/web/components.py:53: error: callable? not callable  [misc]
vibe_check/ui/web/components.py:108: error: No return value expected  [return-value]
vibe_check/ui/web/components.py:431: error: No overload variant of "get" of "dict" matches argument types "Sequence[str]", "str"  [call-overload]
vibe_check/ui/web/components.py:431: note: Possible overload variants:
vibe_check/ui/web/components.py:431: note:     def get(self, str, /) -> Optional[str]
vibe_check/ui/web/components.py:431: note:     def get(self, str, str, /) -> str
vibe_check/ui/web/components.py:431: note:     def [_T] get(self, str, _T, /) -> Union[str, _T]
vibe_check/ui/web/components.py:506: error: Need type annotation for "simplified_graph"  [var-annotated]
vibe_check/ui/web/components.py:530: error: Cannot find implementation or library stub for module named "graphviz"  [import-not-found]
vibe_check/ui/reporting/custom_report_generator.py:133: error: Incompatible types in assignment (expression has type "Dict[str, Optional[float]]", target has type "str")  [assignment]
vibe_check/ui/reporting/custom_report_generator.py:144: error: Incompatible types in assignment (expression has type "Dict[Any, Any]", target has type "str")  [assignment]
vibe_check/ui/reporting/custom_report_generator.py:144: error: "str" has no attribute "items"  [attr-defined]
vibe_check/ui/reporting/custom_report_generator.py:148: error: Incompatible types in assignment (expression has type "Dict[str, object]", target has type "str")  [assignment]
vibe_check/ui/reporting/custom_report_generator.py:156: error: Incompatible types in assignment (expression has type "Dict[str, object]", target has type "str")  [assignment]
vibe_check/ui/reporting/custom_report_generator.py:164: error: Incompatible types in assignment (expression has type "Dict[str, object]", target has type "str")  [assignment]
vibe_check/ui/reporting/custom_report_generator.py:172: error: Incompatible types in assignment (expression has type "Dict[str, Optional[float]]", target has type "str")  [assignment]
vibe_check/ui/reporting/custom_report_generator.py:179: error: Incompatible types in assignment (expression has type "Dict[Any, Any]", target has type "str")  [assignment]
vibe_check/ui/reporting/custom_report_generator.py:179: error: "str" has no attribute "items"  [attr-defined]
vibe_check/ui/reporting/custom_report_generator.py:183: error: Incompatible types in assignment (expression has type "Dict[str, Any]", target has type "str")  [assignment]
vibe_check/ui/cli/formatter.py:159: error: Incompatible default for argument "title" (default has type "None", argument has type "str")  [assignment]
vibe_check/ui/cli/formatter.py:159: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/ui/cli/formatter.py:159: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/core/simple_analyzer.py:89: error: Argument "complexity" to "FileMetrics" has incompatible type "float"; expected "int"  [arg-type]
vibe_check/core/trend_analysis/trend_visualizer.py:24: error: Incompatible default for argument "output_dir" (default has type "None", argument has type "Union[str, Path]")  [assignment]
vibe_check/core/trend_analysis/trend_visualizer.py:24: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
vibe_check/core/trend_analysis/trend_visualizer.py:24: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
vibe_check/core/actor_system/actors/project_actor.py:75: error: Need type annotation for "file_actors" (hint: "file_actors: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/project_actor.py:76: error: Need type annotation for "tool_actors" (hint: "tool_actors: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/project_actor.py:79: error: Need type annotation for "active_files" (hint: "active_files: Set[<type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/project_actor.py:80: error: Need type annotation for "completed_files" (hint: "completed_files: Set[<type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/project_actor.py:81: error: Need type annotation for "file_tool_map" (hint: "file_tool_map: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/project_actor.py:158: error: Incompatible types in assignment (expression has type "Optional[Actor]", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/project_actor.py:161: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/project_actor.py:177: error: Incompatible types in assignment (expression has type "Optional[Actor]", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/project_actor.py:180: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/project_actor.py:230: error: Incompatible types in assignment (expression has type "Optional[Any]", variable has type "str")  [assignment]
vibe_check/core/actor_system/actors/project_actor.py:255: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/project_actor.py:306: error: No return value expected  [return-value]
vibe_check/core/actor_system/actors/project_actor.py:406: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/core/actor_system/actors/project_actor.py:410: error: Argument "file_path" to "FileActor" has incompatible type "Optional[Any]"; expected "Path"  [arg-type]
vibe_check/core/actor_system/actors/project_actor.py:417: error: Incompatible types in assignment (expression has type "ActorPool", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/project_actor.py:425: error: "None" has no attribute "start"  [attr-defined]
vibe_check/core/actor_system/actors/project_actor.py:431: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/project_actor.py:813: error: "ProgressTracker" has no attribute "update_progress"  [attr-defined]
vibe_check/core/actor_system/actors/project_actor.py:855: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actors/project_actor.py:856: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/project_actor.py:891: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actors/project_actor.py:892: error: Statement is unreachable  [unreachable]
vibe_check/ui/visualization/generators.py:108: error: Cannot call function of unknown type  [operator]
vibe_check/ui/visualization/generators.py:116: error: Cannot call function of unknown type  [operator]
vibe_check/ui/visualization/generators.py:119: error: Cannot call function of unknown type  [operator]
vibe_check/ui/visualization/generators.py:122: error: Cannot call function of unknown type  [operator]
vibe_check/core/actor_system/actors/report_actor.py:52: error: Need type annotation for "file_metrics" (hint: "file_metrics: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/report_actor.py:53: error: Need type annotation for "directory_metrics" (hint: "directory_metrics: Dict[<type>, <type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/report_actor.py:60: error: Need type annotation for "completion_callbacks" (hint: "completion_callbacks: List[<type>] = ...")  [var-annotated]
vibe_check/core/actor_system/actors/report_actor.py:80: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/report_actor.py:83: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/core/actor_system/actors/report_actor.py:187: error: Incompatible types in assignment (expression has type "ProjectMetrics", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/report_actor.py:194: error: "None" has no attribute "files"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:197: error: "None" has no attribute "directories"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:200: error: "None" has no attribute "project_path"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:201: error: "None" has no attribute "files"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:201: error: "None" has no attribute "directories"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:281: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:306: error: Incompatible types in assignment (expression has type "ProjectMetrics", variable has type "None")  [assignment]
vibe_check/core/actor_system/actors/report_actor.py:311: error: "None" has no attribute "total_file_count"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:324: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:341: error: "None" has no attribute "to_dict"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:359: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:437: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:475: error: Unsupported left operand type for / ("None")  [operator]
vibe_check/core/actor_system/actors/report_actor.py:476: error: Unsupported left operand type for / ("None")  [operator]
vibe_check/core/actor_system/actors/report_actor.py:477: error: Unsupported left operand type for / ("None")  [operator]
vibe_check/core/actor_system/actors/report_actor.py:478: error: Unsupported left operand type for / ("None")  [operator]
vibe_check/core/actor_system/actors/report_actor.py:483: error: Unsupported left operand type for / ("None")  [operator]
vibe_check/core/actor_system/actors/report_actor.py:484: error: Unsupported left operand type for / ("None")  [operator]
vibe_check/core/actor_system/actors/report_actor.py:485: error: Unsupported left operand type for / ("None")  [operator]
vibe_check/core/actor_system/actors/report_actor.py:530: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:563: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:590: error: "None" has no attribute "project_path"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:672: error: "None" has no attribute "project_path"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:674: error: "None" has no attribute "total_file_count"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:675: error: "None" has no attribute "total_directory_count"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:682: error: "None" has no attribute "avg_complexity"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:684: error: "None" has no attribute "avg_complexity"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:688: error: "None" has no attribute "avg_doc_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:690: error: "None" has no attribute "avg_doc_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:694: error: "None" has no attribute "avg_type_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:696: error: "None" has no attribute "avg_type_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:699: error: "None" has no attribute "issue_count"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:700: error: "None" has no attribute "issues_by_severity"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:763: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:764: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:820: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:848: error: "None" has no attribute "project_path"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:850: error: "None" has no attribute "total_file_count"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:851: error: "None" has no attribute "total_directory_count"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:855: error: "None" has no attribute "avg_complexity"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:856: error: "None" has no attribute "avg_complexity"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:858: error: "None" has no attribute "avg_doc_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:859: error: "None" has no attribute "avg_doc_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:861: error: "None" has no attribute "avg_type_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:862: error: "None" has no attribute "avg_type_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:865: error: "None" has no attribute "issue_count"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:866: error: "None" has no attribute "issues_by_severity"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:906: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:907: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:929: error: "None" has no attribute "avg_complexity"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:933: error: "None" has no attribute "avg_doc_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:937: error: "None" has no attribute "avg_type_coverage"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:941: error: "None" has no attribute "issue_count"  [attr-defined]
vibe_check/core/actor_system/actors/report_actor.py:962: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:1030: error: Statement is unreachable  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:1099: error: Right operand of "and" is never evaluated  [unreachable]
vibe_check/core/actor_system/actors/report_actor.py:1100: error: Statement is unreachable  [unreachable]
vibe_check/core/compatibility.py:20: error: Module "vibe_check.core.bridge" has no attribute "set_feature_toggle"  [attr-defined]
vibe_check/core/compatibility.py:89: error: Incompatible types in assignment (expression has type "ReportActor", variable has type "None")  [assignment]
vibe_check/core/compatibility.py:96: error: Argument 1 to "set_visualization_actor" of "ReportActor" has incompatible type "VisualizationActor"; expected "str"  [arg-type]
vibe_check/core/orchestrator_new.py:99: error: Incompatible types in assignment (expression has type "ActorSystemBuilder", variable has type "None")  [assignment]
vibe_check/core/orchestrator_new.py:106: error: "None" has no attribute "build"  [attr-defined]
vibe_check/core/orchestrator_new.py:109: error: "None" has no attribute "project_actor"  [attr-defined]
vibe_check/core/orchestrator_new.py:110: error: "None" has no attribute "report_actor"  [attr-defined]
vibe_check/core/orchestrator_new.py:111: error: "None" has no attribute "visualization_actor"  [attr-defined]
vibe_check/core/orchestrator_new.py:114: error: Incompatible types in assignment (expression has type "ActorConnector", variable has type "None")  [assignment]
vibe_check/core/orchestrator_new.py:117: error: "None" has no attribute "connect_actors"  [attr-defined]
vibe_check/core/orchestrator_new.py:120: error: Incompatible types in assignment (expression has type "ActorLifecycleManager", variable has type "None")  [assignment]
vibe_check/core/orchestrator_new.py:142: error: Statement is unreachable  [unreachable]
vibe_check/core/orchestrator_new.py:154: error: Statement is unreachable  [unreachable]
vibe_check/core/orchestrator_new.py:169: error: Incompatible types in assignment (expression has type "Path", variable has type "None")  [assignment]
vibe_check/core/orchestrator_new.py:172: error: "None" has no attribute "exists"  [attr-defined]
vibe_check/core/orchestrator_new.py:246: error: Statement is unreachable  [unreachable]
vibe_check/ui/web/state_manager.py:84: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/web/state_manager.py:84: note: Use "-> None" if function does not return a value
vibe_check/ui/web/state_manager.py:91: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/ui/web/state_manager.py:254: error: Argument "project_path" to "simple_analyze_project" has incompatible type "Optional[str]"; expected "Union[str, Path]"  [arg-type]
vibe_check/ui/web/state_manager.py:273: error: Argument "project_path" to "analyze_project" has incompatible type "Optional[str]"; expected "Union[str, Path]"  [arg-type]
vibe_check/ui/web/state_manager.py:309: error: Returning Any from function declared to return "WebUIActor"  [no-any-return]
vibe_check/ui/tui/state_manager.py:45: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/tui/state_manager.py:45: note: Use "-> None" if function does not return a value
vibe_check/ui/tui/state_manager.py:203: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/ui/tui/state_manager.py:212: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/ui/tui/state_manager.py:254: error: Argument "project_path" to "analyze_project" has incompatible type "Optional[Any]"; expected "Union[str, Path]"  [arg-type]
vibe_check/ui/cli/commands.py:67: error: Returning Any from function declared to return "ProjectMetrics"  [no-any-return]
vibe_check/compat.py:85: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/compat.py:108: error: Returning Any from function declared to return "Dict[str, Any]"  [no-any-return]
vibe_check/ui/web/app.py:14: error: Cannot find implementation or library stub for module named "streamlit"  [import-not-found]
vibe_check/ui/web/app.py:59: error: "render_project_selector" does not return a value (it only ever returns None)  [func-returns-value]
vibe_check/ui/web/app.py:119: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/web/app.py:129: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/web/app.py:153: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/web/app.py:153: note: Use "-> None" if function does not return a value
vibe_check/ui/web/app.py:179: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/tui/components.py:30: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/ui/tui/components.py:33: error: Cannot assign to a type  [misc]
vibe_check/ui/tui/components.py:33: error: Incompatible types in assignment (expression has type "Type[FallbackConsole]", variable has type "Type[Console]")  [assignment]
vibe_check/ui/tui/components.py:394: error: Need type annotation for "severity_groups"  [var-annotated]
vibe_check/ui/tui/components.py:512: error: No overload variant of "get" of "dict" matches argument types "Sequence[str]", "str"  [call-overload]
vibe_check/ui/tui/components.py:512: note: Possible overload variants:
vibe_check/ui/tui/components.py:512: note:     def get(self, str, /) -> Optional[str]
vibe_check/ui/tui/components.py:512: note:     def get(self, str, str, /) -> str
vibe_check/ui/tui/components.py:512: note:     def [_T] get(self, str, _T, /) -> Union[str, _T]
vibe_check/ui/web/run_web_ui.py:27: error: Cannot find implementation or library stub for module named "streamlit"  [import-not-found]
vibe_check/ui/web/run_web_ui.py:36: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/web/run_web_ui.py:36: note: Use "-> None" if function does not return a value
vibe_check/ui/tui/app.py:139: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/tui/app.py:139: note: Use "-> None" if function does not return a value
vibe_check/ui/tui/app.py:142: error: Incompatible types in assignment (expression has type "Thread", variable has type "None")  [assignment]
vibe_check/ui/tui/app.py:143: error: "None" has no attribute "daemon"  [attr-defined]
vibe_check/ui/tui/app.py:144: error: "None" has no attribute "start"  [attr-defined]
vibe_check/ui/tui/app.py:146: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/tui/app.py:146: note: Use "-> None" if function does not return a value
vibe_check/ui/tui/app.py:150: error: Statement is unreachable  [unreachable]
vibe_check/ui/tui/app.py:152: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/tui/app.py:152: note: Use "-> None" if function does not return a value
vibe_check/ui/tui/app.py:158: error: Argument 1 to "handle_input" has incompatible type "Optional[str]"; expected "str"  [arg-type]
vibe_check/ui/tui/app.py:164: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/ui/tui/app.py:176: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/ui/tui/app.py:238: error: "TUIState" has no attribute "config_path"  [attr-defined]
vibe_check/ui/tui/app.py:242: error: "TUIState" has no attribute "context"  [attr-defined]
vibe_check/cli/commands.py:18: error: Function is missing a type annotation for one or more arguments  [no-untyped-def]
vibe_check/cli/commands.py:53: error: Returning Any from function declared to return "Dict[str, Any]"  [no-any-return]
vibe_check/cli/commands.py:76: error: Too many arguments for "run_tui"  [call-arg]
vibe_check/cli/commands.py:97: error: Module "vibe_check.ui.web" has no attribute "run_web_server"  [attr-defined]
vibe_check/cli/commands.py:128: error: Module "vibe_check.plugins.manager" has no attribute "install_plugin"; maybe "list_plugins"?  [attr-defined]
vibe_check/cli/commands.py:140: error: Module "vibe_check.plugins.manager" has no attribute "uninstall_plugin"  [attr-defined]
vibe_check/cli/main.py:19: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:19: note: Use "-> None" if function does not return a value
vibe_check/cli/main.py:38: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:45: error: Need type annotation for "context"  [var-annotated]
vibe_check/cli/main.py:74: error: Incompatible types in assignment (expression has type "Dict[str, bool]", target has type "str")  [assignment]
vibe_check/cli/main.py:99: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:109: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:115: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:115: note: Use "-> None" if function does not return a value
vibe_check/cli/main.py:121: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:121: note: Use "-> None" if function does not return a value
vibe_check/cli/main.py:128: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:135: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:140: error: Function is missing a type annotation  [no-untyped-def]
vibe_check/cli/main.py:207: error: Function is missing a return type annotation  [no-untyped-def]
vibe_check/cli/main.py:207: note: Use "-> None" if function does not return a value
Found 394 errors in 59 files (checked 140 source files)

# Vibe Check - Project Analysis Tool - Unified Requirements
# Install with: pip install -r requirements.txt

# Core Dependencies - Essential for basic functionality
click>=8.0.0
PyYAML>=6.0
networkx>=3.0
matplotlib>=3.5.0
typing-extensions>=4.0.0
anyio>=3.6.0
tqdm>=4.62.0

# TUI and Console Output
textual>=0.38
rich>=13.0
keyboard>=0.13.5

# Graph and Visualization
graphviz>=0.19.1
pydeps>=1.10.18

# Static Analysis and Linting
ruff>=0.1.0
bandit>=1.7.0
pylint>=2.12.0
pycodestyle>=2.8.0
mypy>=0.950
radon>=5.1.0
astroid>=2.9.0
vulture>=2.3
black>=22.0.0
isort>=5.10.0
pydocstyle>=6.1.0
pipdeptree>=2.0.0
safety>=2.0.0
pyright>=1.1.200
pyre-check>=0.9.0

# Testing and Coverage
pytest>=7.0.0
pytest-json-report>=1.5
hypothesis>=6.0.0
coverage>=6.2

# Data Processing and Analysis
pandas>=1.4.0
numpy>=1.21.5
PyContracts>=1.8.0
mccabe>=0.6.1

# Web UI
streamlit>=1.10.0
altair>=4.2.0

# (Optional) For TLA+ or other advanced analysis
# tlc-python-wrapper # For TLA+ stage - check installation method
# pyan3 # If needed, may require manual setup

# Installation Note:
# For development, you can install the package in editable mode with:
# pip install -e .
#
# For specific interfaces, you can install extras:
# pip install -e .[tui]     # For TUI interface
# pip install -e .[web]     # For Web UI
# pip install -e .[dev]     # For development tools
# pip install -e .[full]    # For all features

# End of requirements
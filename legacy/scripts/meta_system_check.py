#!/usr/bin/env python3
"""
Meta-System Separation Checker

A standalone script to check for meta-system separation violations in the person_suit project.
Meta-systems should not import from each other directly, but can use common underlying
components from person_suit.

Usage:
    python meta_system_check.py [project_path]

If project_path is not provided, the current directory is used.
"""

import argparse
import json
import os
import sys
import webbrowser
from pathlib import Path

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from meta_system_analysis import MetaSystemAnalyzer, MetaSystemVisualizer
except ImportError:
    print("Error: Could not import meta_system_analysis module.")
    sys.exit(1)

def main():
    """Run the meta-system separation analysis."""
    parser = argparse.ArgumentParser(description="Check for meta-system separation violations in the person_suit project.")
    parser.add_argument("project_path", nargs="?", default=".", help="Path to the project root (default: current directory)")
    parser.add_argument("--output-dir", "-o", default="analysis", help="Directory to save output files (default: analysis)")
    parser.add_argument("--no-viz", action="store_true", help="Skip visualization generation")
    parser.add_argument("--open-browser", action="store_true", help="Open the HTML report in a browser")
    args = parser.parse_args()
    
    # Set up project path
    project_path = Path(args.project_path).resolve()
    if not project_path.is_dir():
        print(f"Error: {project_path} is not a valid directory.")
        sys.exit(1)
    
    # Set up output directory
    output_dir = Path(args.output_dir).resolve()
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"Analyzing meta-system separation in {project_path}...")
    
    # Run the analysis
    analyzer = MetaSystemAnalyzer(project_path)
    violations, meta_connections = analyzer.analyze()
    
    # Print results
    if violations:
        print(f"Found {len(violations)} meta-system separation violations:")
        for i, violation in enumerate(violations[:10], 1):
            print(f"{i}. {violation['source_meta']} imports from {violation['target_meta']} in {violation['file_path']}")
        if len(violations) > 10:
            print(f"...and {len(violations) - 10} more violations.")
    else:
        print("No meta-system separation violations found. Great job!")
    
    # Generate visualization
    if not args.no_viz:
        project_name = project_path.name
        try:
            visualizer = MetaSystemVisualizer(meta_connections, project_name)
            
            # Generate static graph
            graph_path = visualizer.generate_static_graph(str(output_dir))
            
            # Generate interactive report
            html_path = visualizer.generate_interactive_report(str(output_dir))
            
            # Open the HTML report in a browser if requested
            if args.open_browser and html_path:
                print(f"Opening {html_path} in browser...")
                webbrowser.open(f"file://{os.path.abspath(html_path)}")
        except Exception as e:
            print(f"Error generating visualization: {e}")
    
    # Save violations to JSON
    violations_path = os.path.join(output_dir, "meta_system_violations.json")
    try:
        with open(violations_path, "w") as f:
            json.dump({
                "violations": violations,
                "meta_connections": meta_connections
            }, f, indent=2)
        print(f"Violations saved to {violations_path}")
    except Exception as e:
        print(f"Error saving violations: {e}")
    
    # Return exit code based on violations
    return 1 if violations else 0

if __name__ == "__main__":
    sys.exit(main())

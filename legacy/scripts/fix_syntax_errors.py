#!/usr/bin/env python3
"""
Syntax Error Fixer Script

This script identifies and fixes common syntax errors in Python files.
It focuses on the most common syntax errors found in the codebase:
1. Invalid escape sequences
2. Line continuation issues
3. Indentation errors
4. Missing parentheses
5. Unclosed strings

Usage:
    python fix_syntax_errors.py <project_path>
"""

import ast
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple


def find_python_files(directory: Path) -> List[Path]:
    """Find all Python files in the given directory."""
    return list(directory.glob("**/*.py"))

def check_syntax(file_path: Path) -> Optional[str]:
    """Check if a file has syntax errors."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        ast.parse(content)
        return None
    except SyntaxError as e:
        return str(e)
    except UnicodeDecodeError:
        return "Unicode decode error"

def fix_invalid_escape_sequences(content: str) -> str:
    """Fix invalid escape sequences in strings."""
    # Find all string literals
    string_pattern = r'(["\'])((?:\\.|[^\\])*?)\1'
    
    def fix_escapes(match):
        quote = match.group(1)
        string_content = match.group(2)
        
        # Fix common invalid escape sequences
        for invalid_escape in ['\\(', '\\)', '\\{', '\\}', '\\[', '\\]', '\\-', '\\:', '\\,', '\\;', '\\@', '\\#', '\\$', '\\%', '\\&', '\\!', '\\?']:
            if invalid_escape in string_content:
                # Replace with raw string or proper escape
                string_content = string_content.replace(invalid_escape, '\\\\' + invalid_escape[1])
        
        return quote + string_content + quote
    
    return re.sub(string_pattern, fix_escapes, content)

def fix_line_continuation(content: str) -> str:
    """Fix line continuation issues."""
    # Fix missing backslashes at line ends
    lines = content.split('\n')
    fixed_lines = []
    
    for i, line in enumerate(lines):
        if i < len(lines) - 1:
            # Check for lines that might need continuation
            stripped = line.rstrip()
            if (stripped.endswith(('(', '[', '{', ',', '+', '-', '*', '/', '&', '|', '^', '%', '<', '>', '=')) or
                (i < len(lines) - 1 and lines[i+1].lstrip().startswith((')', ']', '}')))):
                if not stripped.endswith('\\'):
                    # Only add backslash if it's not already a comment line
                    if '#' not in stripped or stripped.find('#') > stripped.find('('):
                        fixed_lines.append(stripped + '\\')
                        continue
        
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_indentation(content: str) -> str:
    """Fix indentation errors."""
    lines = content.split('\n')
    fixed_lines = []
    
    # Convert tabs to spaces
    for line in lines:
        if '\t' in line:
            # Replace tabs with 4 spaces
            fixed_line = line.replace('\t', '    ')
            fixed_lines.append(fixed_line)
        else:
            fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_missing_parentheses(content: str) -> str:
    """Fix missing parentheses in print statements and other common issues."""
    # Fix Python 2 style print statements
    content = re.sub(r'print\s+([^(].*?)$', r'print(\1)', content, flags=re.MULTILINE)
    
    # Fix other common parentheses issues
    return content

def fix_unclosed_strings(content: str) -> str:
    """Attempt to fix unclosed strings."""
    # This is a simplified approach - real unclosed strings are hard to fix automatically
    lines = content.split('\n')
    fixed_lines = []
    
    for line in lines:
        # Count quotes
        single_quotes = line.count("'")
        double_quotes = line.count('"')
        
        # If odd number of quotes, try to fix
        if single_quotes % 2 == 1 and '#' not in line:
            line += "'"
        if double_quotes % 2 == 1 and '#' not in line:
            line += '"'
            
        fixed_lines.append(line)
    
    return '\n'.join(fixed_lines)

def fix_syntax_errors(file_path: Path) -> Tuple[bool, str]:
    """Try to fix syntax errors in the file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Apply fixes
        fixed_content = content
        fixed_content = fix_invalid_escape_sequences(fixed_content)
        fixed_content = fix_line_continuation(fixed_content)
        fixed_content = fix_indentation(fixed_content)
        fixed_content = fix_missing_parentheses(fixed_content)
        fixed_content = fix_unclosed_strings(fixed_content)
        
        # Check if the fixes worked
        try:
            ast.parse(fixed_content)
            # Only write if content changed
            if fixed_content != content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(fixed_content)
                return True, "Fixed"
            return False, "No changes needed"
        except SyntaxError as e:
            return False, f"Failed to fix: {str(e)}"
    except Exception as e:
        return False, f"Error: {str(e)}"

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python fix_syntax_errors.py <project_path>")
        return 1
    
    project_path = Path(sys.argv[1])
    if not project_path.exists():
        print(f"Error: Path {project_path} does not exist")
        return 1
    
    # Find all Python files
    python_files = find_python_files(project_path)
    print(f"Found {len(python_files)} Python files")
    
    # Check for syntax errors
    files_with_errors = []
    for file_path in python_files:
        error = check_syntax(file_path)
        if error:
            files_with_errors.append((file_path, error))
    
    print(f"Found {len(files_with_errors)} files with syntax errors")
    
    # Try to fix syntax errors
    fixed_files = []
    failed_files = []
    
    for file_path, error in files_with_errors:
        print(f"Attempting to fix {file_path}...")
        success, message = fix_syntax_errors(file_path)
        if success:
            fixed_files.append(file_path)
            print(f"  Success: {message}")
        else:
            failed_files.append((file_path, message))
            print(f"  Failed: {message}")
    
    # Print summary
    print("\nSummary:")
    print(f"  Total files with syntax errors: {len(files_with_errors)}")
    print(f"  Successfully fixed: {len(fixed_files)}")
    print(f"  Failed to fix: {len(failed_files)}")
    
    if failed_files:
        print("\nFailed files:")
        for file_path, message in failed_files:
            print(f"  {file_path}: {message}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

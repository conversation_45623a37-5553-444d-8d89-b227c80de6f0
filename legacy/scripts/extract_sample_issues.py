#!/usr/bin/env python3
"""
Extract Sample Issues Script

This script extracts a sample of Pyright issues from the PAT analysis JSON file
and generates a detailed log.

Usage:
    python extract_sample_issues.py <pat_analysis_json> <output_log>
"""

import json
import os
import sys


def main():
    """Main function."""
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <pat_analysis_json> [<output_log>]")
        return 1
    
    analysis_json_path = sys.argv[1]
    if not os.path.exists(analysis_json_path):
        print(f"Error: File {analysis_json_path} does not exist")
        return 1
    
    output_log_path = sys.argv[2] if len(sys.argv) > 2 else None
    
    # Load the analysis data
    try:
        with open(analysis_json_path, 'r') as f:
            analysis_data = json.load(f)
    except Exception as e:
        print(f"Error loading JSON file: {e}")
        return 1
    
    # Extract a sample of Pyright issues
    pyright_issues = []
    sample_count = 0
    max_samples = 100  # Limit to 100 samples
    
    for file_key, file_data in analysis_data.get("files", {}).items():
        if not isinstance(file_data, dict):
            continue
        
        file_path = file_data.get("path", file_key)
        
        tool_results = file_data.get("tool_results", {})
        if not isinstance(tool_results, dict):
            continue
        
        pyright_data = tool_results.get("pyright", {})
        if not isinstance(pyright_data, dict):
            continue
        
        errors = pyright_data.get("errors", [])
        if not isinstance(errors, list):
            continue
        
        for error in errors:
            if isinstance(error, dict):
                pyright_issues.append({
                    "file_path": file_path,
                    "severity": error.get("severity", "unknown"),
                    "message": error.get("message", "No message"),
                    "rule": error.get("rule", "unknown"),
                    "line": error.get("range", {}).get("start", {}).get("line", "?")
                })
                
                sample_count += 1
                if sample_count >= max_samples:
                    break
        
        if sample_count >= max_samples:
            break
    
    # Format the issues
    if not pyright_issues:
        log_content = "No Pyright issues found."
    else:
        log_content = f"# Pyright Issues (Sample of {len(pyright_issues)} out of 13,841 total)\n\n"
        
        # Group by rule
        issues_by_rule = {}
        for issue in pyright_issues:
            rule = issue.get("rule", "unknown")
            if rule not in issues_by_rule:
                issues_by_rule[rule] = []
            issues_by_rule[rule].append(issue)
        
        for rule, rule_issues in sorted(issues_by_rule.items()):
            log_content += f"## {rule}: {len(rule_issues)}\n\n"
            
            for i, issue in enumerate(rule_issues, 1):
                file_path = issue.get("file_path", "unknown")
                message = issue.get("message", "No message")
                severity = issue.get("severity", "unknown")
                line = issue.get("line", "?")
                
                log_content += f"{i}. **{file_path}** (Line {line}, Severity: {severity}): {message}\n\n"
    
    # Output the log
    if output_log_path:
        try:
            with open(output_log_path, 'w') as f:
                f.write(log_content)
            print(f"Sample issues log written to {output_log_path}")
        except Exception as e:
            print(f"Error writing log file: {e}")
            return 1
    else:
        print(log_content)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

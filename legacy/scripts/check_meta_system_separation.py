#!/usr/bin/env python3
"""
Meta-System Separation Checker

A standalone script to check for meta-system separation violations in the person_suit project.
Meta-systems should not import from each other directly, but can use common underlying
components from person_suit.

Usage:
    python check_meta_system_separation.py [project_path]

If project_path is not provided, the current directory is used.
"""

import argparse
import os
import sys
from pathlib import Path

# Add the PAT_project_analysis directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from PAT_tool.meta_system_analyzer import MetaSystemAnalyzer
    from PAT_tool.meta_system_visualizer import MetaSystemVisualizer
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.utils import ProgressTracker, logger
except ImportError:
    # Try with direct import
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), "PAT_tool"))
        from meta_system_analyzer import MetaSystemAnalyzer
        from meta_system_visualizer import MetaSystemVisualizer
        from models import ProjectMetrics
        from utils import ProgressTracker, logger
    except ImportError:
        print("Error: Could not import PAT_tool modules. Make sure you're running this script from the PAT_project_analysis directory.")
        sys.exit(1)

def main():
    """Run the meta-system separation analysis."""
    parser = argparse.ArgumentParser(description="Check for meta-system separation violations in the person_suit project.")
    parser.add_argument("project_path", nargs="?", default=".", help="Path to the project root (default: current directory)")
    parser.add_argument("--output-dir", "-o", default="analysis", help="Directory to save output files (default: analysis)")
    parser.add_argument("--verbose", "-v", action="store_true", help="Enable verbose output")
    args = parser.parse_args()

    # Set up logging
    if args.verbose:
        logger.setLevel("DEBUG")
    else:
        logger.setLevel("INFO")

    # Set up project path
    project_path = Path(args.project_path).resolve()
    if not project_path.is_dir():
        logger.error(f"Error: {project_path} is not a valid directory.")
        sys.exit(1)

    # Set up output directory
    output_dir = Path(args.output_dir).resolve()
    output_dir.mkdir(parents=True, exist_ok=True)

    # Set up progress tracker
    progress = ProgressTracker()

    # Set up metrics
    metrics = ProjectMetrics()

    # Run the analysis
    logger.info(f"Analyzing meta-system separation in {project_path}...")
    analyzer = MetaSystemAnalyzer(project_path, metrics, progress)
    analyzer.analyze()

    # Get results
    results = metrics.tool_results.get("meta_system_separation", {})
    violations = results.get("violations", [])

    # Print results
    if violations:
        logger.error(f"Found {len(violations)} meta-system separation violations:")
        for i, violation in enumerate(violations[:10], 1):
            logger.error(f"{i}. {violation['source_meta']} imports from {violation['target_meta']} in {violation['file_path']}")
        if len(violations) > 10:
            logger.error(f"...and {len(violations) - 10} more violations.")
    else:
        logger.info("No meta-system separation violations found. Great job!")

    # Generate visualizations
    logger.info(f"Generating visualizations in {output_dir}...")
    project_name = project_path.name
    visualizer = MetaSystemVisualizer(project_name, metrics, str(output_dir))
    visualizer.generate()

    # Print visualization paths
    graph_path = output_dir / f"{project_name}_meta_system_graph.png"
    report_path = output_dir / f"{project_name}_meta_system_report.html"

    if graph_path.exists():
        logger.info(f"Meta-system graph saved to {graph_path}")
    if report_path.exists():
        logger.info(f"Interactive meta-system report saved to {report_path}")

    # Return exit code based on violations
    return 1 if violations else 0

if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
"""
PAT Runner with High-Priority Extraction (Improved UX)

This script runs the PAT tool and then extracts high-priority files from the output.
It includes a spinner and status updates for long-running processes.

Usage:
    python run_pat_with_priorities_improved.py <project_path> [--top N] [--min-score SCORE]

Arguments:
    project_path: Path to the project to analyze
    --top N: Number of top files to extract (default: 10)
    --min-score: Minimum priority score to include a file (default: 5)
"""

import argparse
import os
import subprocess
import sys
import tempfile
import threading
import time
from pathlib import Path
from typing import List, Optional, Tuple


# ANSI color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class Spinner:
    """A simple spinner for indicating progress."""
    
    def __init__(self, message: str = "Processing"):
        self.message = message
        self.spinning = False
        self.spinner_chars = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏']
        self.spinner_idx = 0
        self.thread = None
        self.start_time = None
        
    def spin(self):
        """Spin the spinner."""
        while self.spinning:
            elapsed = time.time() - self.start_time
            minutes, seconds = divmod(int(elapsed), 60)
            time_str = f"{minutes:02d}:{seconds:02d}"
            
            sys.stdout.write(f"\r{Colors.CYAN}{self.spinner_chars[self.spinner_idx]}{Colors.ENDC} {self.message} ({time_str}) ")
            sys.stdout.flush()
            
            self.spinner_idx = (self.spinner_idx + 1) % len(self.spinner_chars)
            time.sleep(0.1)
    
    def start(self, message: Optional[str] = None):
        """Start the spinner."""
        if message:
            self.message = message
        
        self.spinning = True
        self.start_time = time.time()
        self.thread = threading.Thread(target=self.spin)
        self.thread.daemon = True
        self.thread.start()
    
    def stop(self, success: bool = True, message: Optional[str] = None):
        """Stop the spinner."""
        self.spinning = False
        if self.thread:
            self.thread.join()
        
        elapsed = time.time() - self.start_time
        minutes, seconds = divmod(int(elapsed), 60)
        time_str = f"{minutes:02d}:{seconds:02d}"
        
        if success:
            status = f"{Colors.GREEN}✓{Colors.ENDC}"
        else:
            status = f"{Colors.RED}✗{Colors.ENDC}"
        
        final_message = message if message else self.message
        sys.stdout.write(f"\r{status} {final_message} completed in {time_str}                    \n")
        sys.stdout.flush()

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run PAT and extract high-priority files")
    parser.add_argument("project_path", help="Path to the project to analyze")
    parser.add_argument("--top", type=int, default=10, help="Number of top files to extract (default: 10)")
    parser.add_argument("--min-score", type=float, default=5.0, help="Minimum priority score to include a file (default: 5)")
    return parser.parse_args()

def run_pat(project_path: str, spinner: Spinner) -> Path:
    """Run the PAT tool and return the output directory."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the PAT tool
    pat_script = pat_dir / "run_pat_purposeful.py"
    cmd = [sys.executable, str(pat_script), project_path]
    
    spinner.start(f"Analyzing {project_path} with PAT")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            spinner.stop(False, f"PAT analysis failed with return code {result.returncode}")
            print(f"{Colors.RED}Error output:{Colors.ENDC} {result.stderr}")
            sys.exit(1)
        
        # Find the latest output directory
        output_dir = pat_dir / "PAT_output" / "latest"
        if not output_dir.exists() or not output_dir.is_symlink():
            # Find the latest output directory by timestamp
            output_dirs = list(sorted((pat_dir / "PAT_output").glob("20*-*-*_*-*-*"), reverse=True))
            if not output_dirs:
                spinner.stop(False, "No PAT output directory found")
                sys.exit(1)
            output_dir = output_dirs[0]
        else:
            # Resolve the symlink
            output_dir = output_dir.resolve()
        
        spinner.stop(True, f"PAT analysis completed")
        return output_dir
    except Exception as e:
        spinner.stop(False, f"PAT analysis failed")
        print(f"{Colors.RED}Error:{Colors.ENDC} {str(e)}")
        sys.exit(1)

def extract_high_priority_files(pat_output_dir: Path, top_n: int, min_score: float, spinner: Spinner) -> Tuple[Path, int]:
    """Extract high-priority files from PAT output."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the extract_high_priority.py script
    extract_script = pat_dir / "extract_high_priority.py"
    output_dir = pat_output_dir / "high_priority"
    cmd = [
        sys.executable,
        str(extract_script),
        str(pat_output_dir),
        "--top", str(top_n),
        "--min-score", str(min_score),
        "--output-dir", str(output_dir)
    ]
    
    spinner.start(f"Extracting high-priority files (top {top_n}, min score {min_score})")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            spinner.stop(False, f"High-priority extraction failed")
            print(f"{Colors.RED}Error output:{Colors.ENDC} {result.stderr}")
            sys.exit(1)
        
        # Check if any high-priority files were found
        summary_path = output_dir / "high_priority_summary.md"
        file_count = 0
        
        if summary_path.exists():
            with open(summary_path, 'r') as f:
                summary_content = f.read()
                if "Found " in summary_content:
                    try:
                        file_count_str = summary_content.split("Found ")[1].split(" high-priority")[0]
                        file_count = int(file_count_str)
                    except (IndexError, ValueError):
                        pass
        
        if file_count > 0:
            spinner.stop(True, f"Found {file_count} high-priority files")
        else:
            spinner.stop(True, f"No high-priority files found that meet the criteria")
        
        return output_dir, file_count
    except Exception as e:
        spinner.stop(False, f"High-priority extraction failed")
        print(f"{Colors.RED}Error:{Colors.ENDC} {str(e)}")
        sys.exit(1)

def print_summary(pat_output_dir: Path, high_priority_dir: Path, file_count: int, top_n: int, min_score: float):
    """Print a summary of the analysis results."""
    print("\n" + "=" * 80)
    print(f"{Colors.BOLD}{Colors.HEADER}PAT Analysis Summary{Colors.ENDC}")
    print("=" * 80)
    
    print(f"\n{Colors.BOLD}Analysis Results:{Colors.ENDC}")
    print(f"  {Colors.CYAN}PAT output directory:{Colors.ENDC} {pat_output_dir}")
    
    if file_count > 0:
        print(f"  {Colors.CYAN}High-priority files:{Colors.ENDC} {file_count} files with score >= {min_score}")
        print(f"  {Colors.CYAN}High-priority prompts:{Colors.ENDC} {high_priority_dir}")
        print(f"  {Colors.CYAN}Summary report:{Colors.ENDC} {high_priority_dir / 'high_priority_summary.md'}")
        
        # List the top 3 files if available
        prompt_files = list(high_priority_dir.glob("priority_*.md"))
        if prompt_files:
            print(f"\n{Colors.BOLD}Top High-Priority Files:{Colors.ENDC}")
            for i, prompt_file in enumerate(sorted(prompt_files)[:3]):
                file_name = prompt_file.stem.split("_", 2)[-1]
                print(f"  {i+1}. {Colors.YELLOW}{file_name}{Colors.ENDC} - {prompt_file}")
    else:
        print(f"  {Colors.CYAN}High-priority files:{Colors.ENDC} None found with score >= {min_score}")
    
    print("\n" + "=" * 80)
    print(f"{Colors.BOLD}Next Steps:{Colors.ENDC}")
    if file_count > 0:
        print(f"  1. Review the high-priority files in the summary report")
        print(f"  2. Use the generated prompts with an LLM for refactoring recommendations")
        print(f"  3. Implement the recommendations to improve code quality")
    else:
        print(f"  1. Try lowering the minimum score threshold (current: {min_score})")
        print(f"  2. Review the PAT analysis reports for other issues")
    print("=" * 80 + "\n")

def explain_parameters(top_n: int, min_score: float):
    """Explain what the parameters mean."""
    print(f"\n{Colors.BOLD}Parameter Explanation:{Colors.ENDC}")
    print(f"  {Colors.CYAN}--top {top_n}{Colors.ENDC}: Limits the output to the top {top_n} highest-priority files")
    print(f"  {Colors.CYAN}--min-score {min_score}{Colors.ENDC}: Only includes files with a priority score of {min_score} or higher")
    print()
    print(f"The priority score is calculated based on various metrics:")
    print(f"  - Complexity: Files with high cyclomatic complexity (>10)")
    print(f"  - File Size: Large files (>300 lines)")
    print(f"  - Security Issues: Files with security vulnerabilities")
    print(f"  - Type Errors: Files with type errors")
    print(f"  - Lint Issues: Files with lint issues")
    print(f"  - Circular Imports: Files with circular import issues")
    print(f"  - Many Imports: Files with excessive imports (>15)")
    print(f"  - Low Test Coverage: Files with low test coverage (<70%)")
    print()

def main():
    """Main function."""
    args = parse_args()
    
    # Create a spinner for long-running processes
    spinner = Spinner()
    
    # Explain what the parameters mean
    explain_parameters(args.top, args.min_score)
    
    # Run PAT
    pat_output_dir = run_pat(args.project_path, spinner)
    
    # Extract high-priority files
    high_priority_dir, file_count = extract_high_priority_files(pat_output_dir, args.top, args.min_score, spinner)
    
    # Print summary
    print_summary(pat_output_dir, high_priority_dir, file_count, args.top, args.min_score)
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}Analysis interrupted by user.{Colors.ENDC}")
        sys.exit(1)

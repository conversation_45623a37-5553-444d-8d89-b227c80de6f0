#!/bin/bash
# <PERSON>ript to run meta-system separation analysis with visualization
# This script activates the virtual environment and runs the meta-system separation checker

# Set the project path (default to current directory if not provided)
PROJECT_PATH=${1:-.}

# Set the output directory
OUTPUT_DIR="analysis"

# Display banner
echo "=========================================================="
echo "  Meta-System Separation Analysis"
echo "=========================================================="
echo "Project path: $PROJECT_PATH"
echo "Output directory: $OUTPUT_DIR"
echo "=========================================================="

# Determine the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Activate the virtual environment
echo "Activating virtual environment..."
if [ -d "$SCRIPT_DIR/PAT_venv" ]; then
    # Check if it's a Mac/Linux or Windows environment
    if [ -f "$SCRIPT_DIR/PAT_venv/bin/activate" ]; then
        source "$SCRIPT_DIR/PAT_venv/bin/activate"
    elif [ -f "$SCRIPT_DIR/PAT_venv/Scripts/activate" ]; then
        source "$SCRIPT_DIR/PAT_venv/Scripts/activate"
    else
        echo "Error: Could not find activation script in virtual environment."
        exit 1
    fi
    echo "Virtual environment activated."
else
    echo "Warning: Virtual environment not found at $SCRIPT_DIR/PAT_venv."
    echo "Continuing without virtual environment. This may cause dependency issues."
fi

# Install required packages if not already installed
echo "Checking for required packages..."
python -c "import matplotlib" 2>/dev/null || pip install matplotlib
python -c "import networkx" 2>/dev/null || pip install networkx

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Run the meta-system separation checker with visualization
echo "Running meta-system separation analysis with visualization..."
python "$SCRIPT_DIR/meta_system_check.py" "$PROJECT_PATH" --output-dir "$OUTPUT_DIR" --open-browser

# Check the exit code
EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
    echo "=========================================================="
    echo "  Meta-System Separation Analysis Completed Successfully"
    echo "  No violations found!"
    echo "=========================================================="
else
    echo "=========================================================="
    echo "  Meta-System Separation Analysis Completed"
    echo "  Violations found! Check the output for details."
    echo "=========================================================="
fi

# Deactivate the virtual environment if it was activated
if [ -n "$VIRTUAL_ENV" ]; then
    echo "Deactivating virtual environment..."
    deactivate
fi

exit $EXIT_CODE

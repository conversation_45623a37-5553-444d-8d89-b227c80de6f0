#!/usr/bin/env python3
"""
Resilient PAT Runner

This script runs the PAT analysis with error handling to ensure that if one stage fails,
the analysis continues with the remaining stages. It provides detailed progress information
and gracefully handles failures.

Usage:
    python run_pat_resilient.py <project_path>

Arguments:
    project_path: Path to the project to analyze
"""

import os
import subprocess
import sys
import tempfile
import time
import traceback
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple


# ANSI color codes
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_header(text):
    """Print a formatted header."""
    print(f"\n{Colors.BOLD}{Colors.HEADER}{text}{Colors.ENDC}")

def print_step(text):
    """Print a formatted step."""
    print(f"{Colors.BOLD}{Colors.BLUE}→ {text}{Colors.ENDC}")

def print_success(text):
    """Print a formatted success message."""
    print(f"{Colors.GREEN}✓ {text}{Colors.ENDC}")

def print_warning(text):
    """Print a formatted warning message."""
    print(f"{Colors.YELLOW}⚠ {text}{Colors.ENDC}")

def print_error(text):
    """Print a formatted error message."""
    print(f"{Colors.RED}✗ {text}{Colors.ENDC}")

def run_command(cmd: List[str], description: str) -> Tuple[bool, str]:
    """Run a command and return success status and output."""
    print_step(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        start_time = time.time()
        result = subprocess.run(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            check=False
        )
        
        elapsed = time.time() - start_time
        minutes, seconds = divmod(int(elapsed), 60)
        
        if result.returncode == 0:
            print_success(f"{description} completed successfully in {minutes:02d}:{seconds:02d}")
            return True, result.stdout
        else:
            print_error(f"{description} failed with return code {result.returncode} after {minutes:02d}:{seconds:02d}")
            # Print only the first 10 lines of error output
            error_lines = result.stdout.strip().split('\n')
            if len(error_lines) > 10:
                print("\n".join(error_lines[:5]))
                print(f"... ({len(error_lines) - 10} more lines) ...")
                print("\n".join(error_lines[-5:]))
            else:
                print(result.stdout)
            return False, result.stdout
            
    except Exception as e:
        print_error(f"Error running {description}: {e}")
        return False, str(e)

def fix_syntax_errors(project_path: str) -> bool:
    """Fix syntax errors in the project."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the syntax error fixer
    success, _ = run_command(
        [sys.executable, str(pat_dir / "fix_syntax_errors.py"), project_path],
        "Fixing syntax errors"
    )
    
    if not success:
        print_warning("Syntax error fixing encountered issues, but we'll continue with the analysis.")
    
    return True  # Always return True to continue with analysis

def fix_imports(project_path: str) -> bool:
    """Fix imports in the project."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the import fixer
    success, _ = run_command(
        [sys.executable, str(pat_dir / "fix_imports.py"), project_path],
        "Fixing imports"
    )
    
    if not success:
        print_warning("Import fixing encountered issues, but we'll continue with the analysis.")
    
    return True  # Always return True to continue with analysis

def create_resilient_wrapper(pat_dir: Path, config_path: Optional[Path] = None) -> Path:
    """Create a wrapper script that adds resilience to the PAT tool."""
    with tempfile.NamedTemporaryFile(prefix="pat_resilient_", suffix=".py", delete=False) as f:
        wrapper_script = Path(f.name)

        f.write(f'''
#!/usr/bin/env python3
import sys
import os
import traceback
from pathlib import Path
import time
import logging

# Configure more detailed logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

# Add PAT_project_analysis directory to Python path
sys.path.insert(0, "{str(pat_dir)}")

# Add PAT_tool directory to Python path
pat_tool_dir = Path("{str(pat_dir)}") / "PAT_tool"
sys.path.insert(0, str(pat_tool_dir))

# Create __init__.py files to make the directories proper packages
init_path = pat_tool_dir / "__init__.py"
if not init_path.exists():
    with open(init_path, "w") as f:
        f.write("# Auto-generated by run_pat_resilient.py\\n")

# Set environment variable to use our comprehensive config
{f'os.environ["PAT_CONFIG_PATH"] = "{str(config_path)}"' if config_path else '# Using default config'}

# Monkey patch the stage system to catch errors instead of stopping
try:
    from PAT_tool.stages.base import AnalysisStage
    
    # Store the original run method
    original_run = AnalysisStage.run
    
    # Define a patched version that catches errors
    def resilient_run(self, *args, **kwargs):
        try:
            return original_run(self, *args, **kwargs)
        except Exception as e:
            logging.error(f"Error in {{self.__class__.__name__}}: {{e}}")
            logging.error(traceback.format_exc())
            logging.warning(f"Continuing with next stage despite error in {{self.__class__.__name__}}")
            return None
    
    # Apply the patch
    AnalysisStage.run = resilient_run
    logging.info("Successfully patched AnalysisStage for resilient operation")
except Exception as e:
    logging.error(f"Failed to patch AnalysisStage: {{e}}")

# Also patch the visualization generator to handle errors gracefully
try:
    from PAT_tool.visualization import VisualizationGenerator
    
    # Store the original generate method
    original_generate = VisualizationGenerator.generate
    
    # Define a patched version that catches errors
    def resilient_generate(self):
        try:
            return original_generate(self)
        except Exception as e:
            logging.error(f"Error in visualization generation: {{e}}")
            logging.error(traceback.format_exc())
            logging.warning("Continuing despite visualization errors")
            return None
    
    # Apply the patch
    VisualizationGenerator.generate = resilient_generate
    logging.info("Successfully patched VisualizationGenerator for resilient operation")
except Exception as e:
    logging.error(f"Failed to patch VisualizationGenerator: {{e}}")

# Patch the progress tracker to show more detailed information
try:
    from PAT_tool.utils import ProgressTracker
    
    # Store the original update method
    original_update = ProgressTracker.update
    original_start_phase = ProgressTracker.start_phase
    original_increment = ProgressTracker.increment
    original_complete_phase = ProgressTracker.complete_phase
    
    # Track the current phase and item
    current_phase = "Initializing"
    current_item = ""
    phase_start_time = time.time()
    phase_steps_completed = 0
    phase_total_steps = 0
    
    # Define patched versions
    def patched_start_phase(self, phase_name, total_steps=None):
        global current_phase, phase_start_time, phase_steps_completed, phase_total_steps
        current_phase = phase_name
        phase_start_time = time.time()
        phase_steps_completed = 0
        phase_total_steps = total_steps or 0
        try:
            return original_start_phase(self, phase_name, total_steps)
        except Exception as e:
            logging.error(f"Error in start_phase: {{e}}")
            return None
    
    def patched_increment(self, steps=1, current_item=None):
        global current_item, phase_steps_completed
        if current_item:
            current_item = current_item
        phase_steps_completed += steps
        try:
            return original_increment(self, steps, current_item)
        except Exception as e:
            logging.error(f"Error in increment: {{e}}")
            return None
    
    def patched_update(self, message=None):
        global current_phase, current_item, phase_start_time, phase_steps_completed, phase_total_steps
        
        # Calculate ETA
        elapsed = time.time() - phase_start_time
        if phase_steps_completed > 0 and phase_total_steps > 0:
            progress = phase_steps_completed / phase_total_steps
            eta_seconds = (elapsed / progress) * (1 - progress)
            eta_minutes, eta_seconds = divmod(int(eta_seconds), 60)
            eta_hours, eta_minutes = divmod(eta_minutes, 60)
            
            if eta_hours > 0:
                eta_str = f"ETA: {{eta_hours}}h {{eta_minutes}}m {{eta_seconds}}s"
            elif eta_minutes > 0:
                eta_str = f"ETA: {{eta_minutes}}m {{eta_seconds}}s"
            else:
                eta_str = f"ETA: {{eta_seconds}}s"
            
            # Format the progress message
            progress_msg = f"[{{current_phase}}] {{phase_steps_completed}}/{{phase_total_steps}} ({{progress:.1%}}) - {{eta_str}}"
            if current_item:
                progress_msg += f" - Current item: {{current_item}}"
            
            # Print the progress message
            sys.stdout.write(f"\\r\\033[K{{progress_msg}}")
            sys.stdout.flush()
        
        try:
            return original_update(self, message)
        except Exception as e:
            logging.error(f"Error in update: {{e}}")
            return None
    
    def patched_complete_phase(self, phase_name, status="Complete!"):
        global current_phase, current_item
        print()  # Add a newline after the progress bar
        current_phase = "Completed " + phase_name
        current_item = ""
        try:
            return original_complete_phase(self, phase_name, status)
        except Exception as e:
            logging.error(f"Error in complete_phase: {{e}}")
            return None
    
    # Apply the patches
    ProgressTracker.start_phase = patched_start_phase
    ProgressTracker.increment = patched_increment
    ProgressTracker.update = patched_update
    ProgressTracker.complete_phase = patched_complete_phase
    logging.info("Successfully patched ProgressTracker for detailed progress reporting")
except Exception as e:
    logging.error(f"Failed to patch ProgressTracker: {{e}}")

# Import and run the main function
try:
    from PAT_tool.main import main
    # Forward command line arguments
    sys.exit(main())
except Exception as e:
    logging.error(f"Error running PAT tool: {{e}}")
    logging.error(traceback.format_exc())
    sys.exit(1)
'''.encode('utf-8'))

    # Make the wrapper script executable
    os.chmod(wrapper_script, 0o755)
    return wrapper_script

def create_comprehensive_config(pat_dir: Path) -> Path:
    """Create a comprehensive config file that enables all tools."""
    config_path = pat_dir / "PAT_tool" / "config_resilient.yaml"

    # First try to read the full config as a template
    full_config_path = pat_dir / "PAT_tool" / "config_full.yaml"
    if not full_config_path.exists():
        print_warning(f"Full config file not found at {full_config_path}. Creating a basic config.")
        with open(config_path, 'w') as f:
            f.write("""
# Resilient PAT configuration with all tools enabled
analysis:
    # Core stages
    syntax:
        enabled: true
        skip_recent: false
    metrics:
        enabled: true
        skip_recent: false
    complexity:
        enabled: true
        skip_recent: false
    dependencies:
        enabled: true
        skip_recent: false
    # Code quality stages
    pylint:
        enabled: true
        skip_recent: false
    ruff:
        enabled: true
        skip_recent: false
    # Formatting stages
    black:
        enabled: true
        skip_recent: false
    isort:
        enabled: true
        skip_recent: false
    # Documentation stages
    pydocstyle:
        enabled: true
        skip_recent: false
    # Type checking stages
    mypy:
        enabled: true
        skip_recent: false
    pyright:
        enabled: true
        skip_recent: false
    # Security stages
    bandit:
        enabled: true
        skip_recent: false
    safety:
        enabled: true
        skip_recent: false

# Progress reporting
progress_bar:
    always: true
    update_interval: 0.1

# Visualization options
visualization:
    enabled: true
    dependency_graph: true
    complexity_heatmap: true
    interactive_html: true
    color_scheme: "viridis"

# Output options
output:
    json: true
    markdown: true
    html: true
    directory: "PAT_output"
    detailed: true
""")
        return config_path

    # Read the full config and enable all tools
    with open(full_config_path, 'r') as f:
        config_content = f.read()

    # Enable all tools
    config_content = config_content.replace("enabled: false", "enabled: true")
    config_content = config_content.replace("skip_recent: true", "skip_recent: false")

    # Write the comprehensive config
    with open(config_path, 'w') as f:
        f.write(config_content)

    return config_path

def run_pat_resilient(project_path: str, skip_fixes: bool = False) -> int:
    """Run the PAT tool with resilience to failures."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    print_header("Running Resilient PAT Analysis")
    print(f"{Colors.CYAN}Project:{Colors.ENDC} {project_path}")
    print(f"{Colors.CYAN}Mode:{Colors.ENDC} Resilient (continues despite failures)")
    
    # Step 1: Fix syntax errors (optional)
    if not skip_fixes:
        print_header("Step 1: Fixing Syntax Errors")
        fix_syntax_errors(project_path)
    
    # Step 2: Fix imports (optional)
    if not skip_fixes:
        print_header("Step 2: Fixing Imports")
        fix_imports(project_path)
    
    # Step 3: Create a resilient config
    print_header("Step 3: Creating Resilient Configuration")
    config_path = create_comprehensive_config(pat_dir)
    print_success(f"Created configuration at {config_path}")
    
    # Step 4: Create a resilient wrapper script
    print_header("Step 4: Creating Resilient PAT Wrapper")
    wrapper_script = create_resilient_wrapper(pat_dir, config_path)
    print_success(f"Created wrapper script at {wrapper_script}")
    
    # Step 5: Run the analysis
    print_header("Step 5: Running Analysis")
    print(f"{Colors.YELLOW}Analysis may take a while and will continue even if some stages fail.{Colors.ENDC}")
    print(f"{Colors.YELLOW}Detailed progress information will be displayed.{Colors.ENDC}")
    print()
    
    # Run the wrapper script with the project path
    cmd = [sys.executable, str(wrapper_script), project_path]
    success, output = run_command(cmd, "Running PAT analysis")
    
    # Clean up the temporary wrapper script
    if wrapper_script.exists():
        wrapper_script.unlink()
    
    # Find the output directory in the output
    output_dir = None
    for line in output.split('\n'):
        if "Output directory" in line and "PAT_output" in line:
            parts = line.split("PAT_output")
            if len(parts) > 1:
                output_dir = parts[0] + "PAT_output" + parts[1].split()[0]
                break
    
    if output_dir:
        print_success(f"Analysis results are available at: {output_dir}")
    else:
        print_warning("Could not determine output directory from analysis output.")
    
    if success:
        print_header("Analysis Completed Successfully")
        return 0
    else:
        print_header("Analysis Completed with Some Failures")
        print_warning("Some stages failed, but the analysis continued. Check the output for details.")
        return 1

def main():
    """Main function."""
    # Check command-line arguments
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <project_path> [--skip-fixes]")
        return 1
    
    project_path = sys.argv[1]
    skip_fixes = "--skip-fixes" in sys.argv
    
    # Run the resilient PAT analysis
    return run_pat_resilient(project_path, skip_fixes)

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}Analysis interrupted by user.{Colors.ENDC}")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n{Colors.RED}Unexpected error: {e}{Colors.ENDC}")
        traceback.print_exc()
        sys.exit(1) 
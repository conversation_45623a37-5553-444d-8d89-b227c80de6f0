#!/usr/bin/env python3
"""
Install Dependencies Script for Project Analysis Tool (PAT)

This script installs all the necessary dependencies for PAT including:
1. Python packages via pip
2. Pyan3 for call graph analysis
3. Graphviz for visualization

Usage:
    python install_dependencies.py
"""

import os
import platform
import subprocess
import sys
import tempfile
from pathlib import Path


# ANSI color codes
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def print_step(step_number, description):
    """Print a step in the installation process."""
    print(f"\n{Colors.BLUE}Step {step_number}: {description}{Colors.ENDC}")

def print_success(message):
    """Print a success message."""
    print(f"{Colors.GREEN}✓ {message}{Colors.ENDC}")

def print_warning(message):
    """Print a warning message."""
    print(f"{Colors.YELLOW}⚠ {message}{Colors.ENDC}")

def print_error(message):
    """Print an error message."""
    print(f"{Colors.RED}✗ {message}{Colors.ENDC}")

def run_command(cmd, description, exit_on_error=False):
    """Run a command and handle errors."""
    print(f"Running: {' '.join(cmd)}")
    try:
        subprocess.check_call(cmd)
        print_success(description)
        return True
    except subprocess.CalledProcessError as e:
        print_error(f"{description} failed with error code {e.returncode}")
        if exit_on_error:
            sys.exit(1)
        return False
    except FileNotFoundError:
        print_error(f"Command not found: {cmd[0]}")
        if exit_on_error:
            sys.exit(1)
        return False

def install_python_packages():
    """Install Python packages required by PAT."""
    print_step(1, "Installing Python packages")
    
    requirements_file = Path(__file__).parent / "PAT_requirements.txt"
    
    # Check if requirements file exists
    if not requirements_file.exists():
        print_error(f"Requirements file not found: {requirements_file}")
        print("Creating minimal requirements file...")
        with open(requirements_file, 'w') as f:
            f.write("""# Core analysis dependencies
networkx>=2.6.3
matplotlib>=3.5.0
graphviz>=0.19.1
radon>=5.1.0
astroid>=2.9.0
pylint>=2.12.0
pydeps>=1.10.18
mccabe>=0.6.1
pycodestyle>=2.8.0
mypy>=0.910
coverage>=6.2
tqdm>=4.62.0
bandit>=1.7.0
vulture>=2.3
pandas>=1.3.5
numpy>=1.21.5
PyYAML>=6.0
rich>=10.0.0
ruff>=0.1.0
black>=22.0.0
isort>=5.10.0
pydocstyle>=6.1.0
pipdeptree>=2.0.0
safety>=2.0.0
pyright>=1.1.200
pyre-check>=0.9.0
PyContracts>=1.8.0
hypothesis>=6.0.0
pytest>=7.0.0
pyvis>=0.3.1
plotly>=5.10.0
""")
        print_success("Created minimal requirements file")
    
    # Install packages
    run_command([sys.executable, "-m", "pip", "install", "-r", str(requirements_file)], 
               "Installing Python packages", exit_on_error=False)
    
    # Install additional packages that might not be in requirements
    additional_packages = [
        "pyvis",   # For interactive visualizations
        "plotly",  # For Sankey diagrams
    ]
    
    for package in additional_packages:
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        except subprocess.CalledProcessError:
            print_warning(f"Failed to install {package}, but continuing")

def install_pyan3():
    """Install Pyan3 for call graph analysis."""
    print_step(2, "Installing Pyan3")
    
    # Try to install pyan3 via pip
    pip_installed = run_command([sys.executable, "-m", "pip", "install", "pyan3"], 
                   "Installing Pyan3 via pip", exit_on_error=False)
    
    if pip_installed:
        return True
    
    # If pip install fails, try to install from GitHub
    print("Trying to install Pyan3 from GitHub...")
    with tempfile.TemporaryDirectory() as tmpdirname:
        os.chdir(tmpdirname)
        clone_success = run_command(["git", "clone", "https://github.com/davidfraser/pyan.git"], 
                      "Cloning Pyan repository", exit_on_error=False)
        
        if not clone_success:
            print_warning("Could not clone Pyan repository. You will need to install it manually.")
            return False
        
        os.chdir("pyan")
        return run_command([sys.executable, "setup.py", "install"], 
                         "Installing Pyan from source", exit_on_error=False)

def install_graphviz():
    """Install Graphviz for visualization."""
    print_step(3, "Installing Graphviz")
    
    # Check if the graphviz Python package is installed
    try:
        import graphviz
        print_success("Graphviz Python package is already installed")
    except ImportError:
        run_command([sys.executable, "-m", "pip", "install", "graphviz"], 
                   "Installing Graphviz Python package", exit_on_error=False)
    
    # Check if system Graphviz is installed (dot command)
    try:
        subprocess.check_call(["dot", "-V"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        print_success("Graphviz dot command is already installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        pass
    
    # Try to install system Graphviz
    system = platform.system()
    
    if system == "Darwin":  # macOS
        print("Installing Graphviz on macOS using Homebrew...")
        try:
            # Check if Homebrew is installed
            subprocess.check_call(["brew", "--version"], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            # Install Graphviz
            return run_command(["brew", "install", "graphviz"], 
                              "Installing Graphviz with Homebrew", exit_on_error=False)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print_warning("Homebrew not found. Please install Homebrew first, or install Graphviz manually.")
            return False
    
    elif system == "Linux":
        # Try different package managers
        if subprocess.call(["which", "apt-get"], stdout=subprocess.PIPE, stderr=subprocess.PIPE) == 0:
            return run_command(["sudo", "apt-get", "install", "-y", "graphviz"], 
                              "Installing Graphviz with apt", exit_on_error=False)
        elif subprocess.call(["which", "yum"], stdout=subprocess.PIPE, stderr=subprocess.PIPE) == 0:
            return run_command(["sudo", "yum", "install", "-y", "graphviz"], 
                              "Installing Graphviz with yum", exit_on_error=False)
        elif subprocess.call(["which", "pacman"], stdout=subprocess.PIPE, stderr=subprocess.PIPE) == 0:
            return run_command(["sudo", "pacman", "-S", "--noconfirm", "graphviz"], 
                              "Installing Graphviz with pacman", exit_on_error=False)
        else:
            print_warning("Could not determine package manager. Please install Graphviz manually.")
            return False
    
    elif system == "Windows":
        print_warning("For Windows, please install Graphviz manually from: https://graphviz.org/download/")
        print("After installation, make sure the Graphviz bin directory is in your PATH.")
        return False
    
    else:
        print_warning(f"Unsupported system: {system}. Please install Graphviz manually.")
        return False

def create_virtual_environment():
    """Create a virtual environment for PAT (optional)."""
    print_step(4, "Creating virtual environment (optional)")
    
    venv_dir = Path(__file__).parent / "PAT_venv"
    
    if venv_dir.exists():
        print(f"Virtual environment already exists at {venv_dir}")
        choice = input("Do you want to recreate it? (y/N): ").strip().lower()
        if choice != 'y':
            print_success("Using existing virtual environment")
            return venv_dir
        
        # Remove existing environment
        import shutil
        shutil.rmtree(venv_dir)
    
    # Create virtual environment
    venv_created = run_command([sys.executable, "-m", "venv", str(venv_dir)], 
                             "Creating virtual environment", exit_on_error=False)
    
    if not venv_created:
        print_warning("Could not create virtual environment. Will install packages globally.")
        return None
    
    # Determine activation script path
    if platform.system() == "Windows":
        activate_script = venv_dir / "Scripts" / "activate.bat"
        activate_cmd = str(activate_script)
    else:
        activate_script = venv_dir / "bin" / "activate"
        activate_cmd = f"source {activate_script}"
    
    print_success(f"Virtual environment created at {venv_dir}")
    print(f"To activate the environment, run: {activate_cmd}")
    
    return venv_dir

def main():
    print(f"{Colors.HEADER}{Colors.BOLD}PAT Dependencies Installer{Colors.ENDC}")
    print(f"{Colors.CYAN}This script will install all dependencies required by the Project Analysis Tool.{Colors.ENDC}")
    print()
    
    # Ask if user wants to create a virtual environment
    choice = input("Do you want to create a virtual environment for PAT? (Y/n): ").strip().lower()
    venv_dir = None
    
    if choice != 'n':
        venv_dir = create_virtual_environment()
        
        if venv_dir and platform.system() != "Windows":
            # Get paths to pip and python in the virtual environment
            pip_path = venv_dir / "bin" / "pip"
            python_path = venv_dir / "bin" / "python"
            
            # Use these paths for subsequent installations
            os.environ["PATH"] = f"{venv_dir}/bin:{os.environ['PATH']}"
            sys.executable = str(python_path)
    
    # Install dependencies
    install_python_packages()
    pyan_installed = install_pyan3()
    graphviz_installed = install_graphviz()
    
    # Summary
    print(f"\n{Colors.HEADER}{Colors.BOLD}Installation Summary{Colors.ENDC}")
    print(f"Python packages: {Colors.GREEN}Installed{Colors.ENDC}")
    print(f"Pyan3: {Colors.GREEN if pyan_installed else Colors.YELLOW}{'Installed' if pyan_installed else 'Not installed'}{Colors.ENDC}")
    print(f"Graphviz: {Colors.GREEN if graphviz_installed else Colors.YELLOW}{'Installed' if graphviz_installed else 'Not installed'}{Colors.ENDC}")
    
    if not pyan_installed:
        print_warning("Pyan3 is not installed. Call graph analysis will be skipped.")
        print("To install Pyan3 manually, try: pip install pyan3")
    
    if not graphviz_installed:
        print_warning("Graphviz is not installed. Dependency graphs will be generated using NetworkX instead.")
        print("Please install Graphviz manually from: https://graphviz.org/download/")
    
    print(f"\n{Colors.GREEN}{Colors.BOLD}Installation completed!{Colors.ENDC}")
    print(f"You can now run PAT with: python {Path(__file__).parent / 'run_pat_comprehensive.py'} <project_path>")
    
    return 0

if __name__ == "__main__":
    sys.exit(main()) 
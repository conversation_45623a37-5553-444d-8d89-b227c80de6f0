#!/usr/bin/env python3
"""
Remaining Erro<PERSON> <PERSON><PERSON><PERSON>

This script fixes the remaining syntax errors in the codebase that weren't fixed
by the previous scripts. It uses a more manual approach to fix specific files.

Usage:
    python fix_remaining_errors.py
"""

import os
import sys
from pathlib import Path

# List of files that still have syntax errors
REMAINING_FILES = [
    "person_suit/meta_systems/persona_core/interfaces/memory_features.py",
    "person_suit/meta_systems/persona_core/folded_mind/error/examples.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/retrieval/nl_query_interface.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/telemetry.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/memory_orchestration_integration.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/neurochemical_integration_optimized.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/atomization/benchmark/optimization_tools.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/emotional_metaphor/backups/therapeutic_application_original.py"
]

# Manual fixes for each file
FILE_FIXES = {
    "person_suit/meta_systems/persona_core/interfaces/memory_features.py": [
        (r'"""Memory Features Interface', r'"""Memory Features Interface"""'),
    ],
    "person_suit/meta_systems/persona_core/folded_mind/error/examples.py": [
        (r'"""Examples of error handling', r'"""Examples of error handling"""'),
    ],
    "person_suit/meta_systems/persona_core/folded_mind/SEM/retrieval/nl_query_interface.py": [
        (r'"""Natural Language Query Interface', r'"""Natural Language Query Interface"""'),
    ],
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/telemetry.py": [
        (r'"""Wave-Particle Telemetry', r'"""Wave-Particle Telemetry"""'),
    ],
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/memory_orchestration_integration.py": [
        (r'"""Memory Orchestration Integration', r'"""Memory Orchestration Integration"""'),
    ],
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/neurochemical_integration_optimized.py": [
        (r'"""Neurochemical Integration', r'"""Neurochemical Integration"""'),
    ],
    "person_suit/meta_systems/persona_core/folded_mind/SEM/atomization/benchmark/optimization_tools.py": [
        (r'"""Optimization Tools', r'"""Optimization Tools"""'),
    ],
    "person_suit/meta_systems/persona_core/folded_mind/SEM/emotional_metaphor/backups/therapeutic_application_original.py": [
        (r'"""Therapeutic Application', r'"""Therapeutic Application"""'),
    ]
}

def fix_file(file_path: Path) -> bool:
    """Fix a specific file using manual fixes."""
    try:
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Apply the fixes
        original_content = content
        for pattern, replacement in FILE_FIXES.get(str(file_path), []):
            content = content.replace(pattern, replacement)
        
        # Write the file if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        return False
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    """Main function."""
    fixed_count = 0
    failed_count = 0
    
    for file_path_str in REMAINING_FILES:
        file_path = Path(file_path_str)
        if not file_path.exists():
            print(f"File not found: {file_path}")
            failed_count += 1
            continue
        
        print(f"Fixing {file_path}...")
        if fix_file(file_path):
            print(f"  Success: Fixed {file_path}")
            fixed_count += 1
        else:
            print(f"  Failed: Could not fix {file_path}")
            failed_count += 1
    
    print(f"\nFixed {fixed_count} files, failed to fix {failed_count} files.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

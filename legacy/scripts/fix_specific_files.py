#!/usr/bin/env python3
"""
Specific Files Fixer Script

This script fixes specific issues in the remaining files with syntax errors.

Usage:
    python fix_specific_files.py
"""

import os
import sys
from pathlib import Path

# List of files that still have syntax errors
REMAINING_FILES = [
    "person_suit/meta_systems/persona_core/interfaces/memory_features.py",
    "person_suit/meta_systems/persona_core/folded_mind/error/examples.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/retrieval/nl_query_interface.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/telemetry.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/memory_orchestration_integration.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/wave_particle/neurochemical_integration_optimized.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/atomization/benchmark/optimization_tools.py",
    "person_suit/meta_systems/persona_core/folded_mind/SEM/emotional_metaphor/backups/therapeutic_application_original.py"
]

def fix_file(file_path: Path) -> bool:
    """Fix a specific file by removing problematic tags."""
    try:
        # Read the file content
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove problematic tags
        original_content = content
        content = content.replace("</rewritten_file>", "")
        
        # Write the file if changes were made
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return True
        
        # If no changes were made, try a more aggressive approach
        # Look for unterminated docstrings
        if '"""' in content:
            lines = content.split('\n')
            docstring_start = None
            for i, line in enumerate(lines):
                if '"""' in line:
                    if docstring_start is None:
                        docstring_start = i
                    else:
                        docstring_start = None
            
            # If we found an unterminated docstring, add the closing quotes
            if docstring_start is not None:
                lines.append('"""')
                content = '\n'.join(lines)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True
        
        return False
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    """Main function."""
    fixed_count = 0
    failed_count = 0
    
    for file_path_str in REMAINING_FILES:
        file_path = Path(file_path_str)
        if not file_path.exists():
            print(f"File not found: {file_path}")
            failed_count += 1
            continue
        
        print(f"Fixing {file_path}...")
        if fix_file(file_path):
            print(f"  Success: Fixed {file_path}")
            fixed_count += 1
        else:
            print(f"  Failed: Could not fix {file_path}")
            failed_count += 1
    
    print(f"\nFixed {fixed_count} files, failed to fix {failed_count} files.")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())

#!/bin/bash
# <PERSON>ript to run the PAT Web UI with meta-system separation analysis
# This script activates the virtual environment and runs the web UI

# Display banner
echo "=========================================================="
echo "  PAT Web UI with Meta-System Separation Analysis"
echo "=========================================================="

# Determine the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Activate the virtual environment
echo "Activating virtual environment..."
if [ -d "$SCRIPT_DIR/PAT_venv" ]; then
    # Check if it's a Mac/Linux or Windows environment
    if [ -f "$SCRIPT_DIR/PAT_venv/bin/activate" ]; then
        source "$SCRIPT_DIR/PAT_venv/bin/activate"
    elif [ -f "$SCRIPT_DIR/PAT_venv/Scripts/activate" ]; then
        source "$SCRIPT_DIR/PAT_venv/Scripts/activate"
    else
        echo "Error: Could not find activation script in virtual environment."
        exit 1
    fi
    echo "Virtual environment activated."
else
    echo "Warning: Virtual environment not found at $SCRIPT_DIR/PAT_venv."
    echo "Continuing without virtual environment. This may cause dependency issues."
fi

# Check if streamlit is installed
if ! command -v streamlit &> /dev/null; then
    echo "Streamlit not found. Installing..."
    pip install streamlit
fi

# Run the web UI
echo "Starting PAT Web UI..."
echo "=========================================================="
echo "  Instructions:"
echo "  1. Select 'meta_system_separation' in the tools list"
echo "  2. Enter the path to your person_suit project"
echo "  3. Click 'Run Analysis'"
echo "  4. View the results in the 'Meta-System Separation' tab"
echo "=========================================================="
streamlit run "$SCRIPT_DIR/pat_webui.py"

# Deactivate the virtual environment if it was activated
if [ -n "$VIRTUAL_ENV" ]; then
    echo "Deactivating virtual environment..."
    deactivate
fi

#!/usr/bin/env python3
"""
<PERSON><PERSON>t to remove the legacy PAT_project_analysis package.

This script:
1. Checks if there are any remaining references to PAT_project_analysis in the codebase
2. Removes the PAT_project_analysis directory
3. Updates setup.py to remove references to PAT_project_analysis
"""

import os
import re
import shutil
import sys
from pathlib import Path

def check_for_references():
    """Check if there are any remaining references to PAT_project_analysis in the codebase."""
    print("Checking for references to PAT_project_analysis...")

    # Directories to exclude
    exclude_dirs = {
        ".git",
        "venv",
        "__pycache__",
        "PAT_project_analysis",  # We're going to remove this anyway
        "legacy",                # Legacy files can contain references
        "tests",                 # Test files can contain references for testing compatibility
        "docs",                  # Documentation can mention the old package name
        "scripts",               # Scripts might contain references for compatibility
    }

    # Files to exclude
    exclude_files = {
        "remove_legacy_package.py",  # This script itself
        "CHANGELOG.md",              # Changelog can mention the old package name
        "CHANGES.md",                # Changes file can mention the old package name
    }

    # Files to check (only Python files and certain text files)
    include_extensions = {
        ".py",
        ".md",
        ".txt",
        ".yaml",
        ".yml",
        ".json",
    }

    references = []

    for root, dirs, files in os.walk("."):
        # Skip excluded directories
        dirs[:] = [d for d in dirs if d not in exclude_dirs and not d.startswith(".")]

        for file in files:
            if file in exclude_files:
                continue

            if any(file.endswith(ext) for ext in include_extensions):
                file_path = os.path.join(root, file)

                # Skip egg-info files
                if "egg-info" in file_path:
                    continue

                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        content = f.read()

                        # Only look for import statements or direct references to the package
                        # not just any mention of the name
                        if (
                            "import PAT_project_analysis" in content or
                            "import pat_project_analysis" in content or
                            "from PAT_project_analysis" in content or
                            "from pat_project_analysis" in content
                        ):
                            references.append(file_path)
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")

    return references

def remove_pat_directory():
    """Remove the PAT_project_analysis directory."""
    print("Removing PAT_project_analysis directory...")

    if os.path.exists("PAT_project_analysis"):
        try:
            shutil.rmtree("PAT_project_analysis")
            print("PAT_project_analysis directory removed successfully.")
        except Exception as e:
            print(f"Error removing PAT_project_analysis directory: {e}")
            return False
    else:
        print("PAT_project_analysis directory does not exist.")

    return True

def update_setup_py():
    """Update setup.py to remove references to PAT_project_analysis."""
    print("Updating setup.py...")

    setup_py_path = "setup.py"

    if not os.path.exists(setup_py_path):
        print("setup.py not found.")
        return False

    try:
        with open(setup_py_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Remove any references to PAT_project_analysis
        content = re.sub(r"['\"]PAT_project_analysis['\"]", "", content)
        content = re.sub(r"['\"]pat_project_analysis['\"]", "", content)

        # Clean up any empty lists or trailing commas
        content = re.sub(r"\[\s*,\s*\]", "[]", content)
        content = re.sub(r",\s*\]", "]", content)
        content = re.sub(r"\[\s*\]", "[]", content)

        with open(setup_py_path, "w", encoding="utf-8") as f:
            f.write(content)

        print("setup.py updated successfully.")
    except Exception as e:
        print(f"Error updating setup.py: {e}")
        return False

    return True

def main():
    """Main function."""
    print("=== PAT_project_analysis Removal Script ===")

    # Check for references
    references = check_for_references()

    if references:
        print("\nFound references to PAT_project_analysis in the following files:")
        for ref in references:
            print(f"  - {ref}")

        print("\nPlease update these files to use vibe_check instead before removing the PAT_project_analysis directory.")
        print("Aborting removal.")
        return 1

    # Remove PAT_project_analysis directory
    if not remove_pat_directory():
        print("Failed to remove PAT_project_analysis directory.")
        return 1

    # Update setup.py
    if not update_setup_py():
        print("Failed to update setup.py.")
        return 1

    print("\nPAT_project_analysis package has been successfully removed.")
    print("Please run tests to ensure everything still works correctly.")

    return 0

if __name__ == "__main__":
    sys.exit(main())

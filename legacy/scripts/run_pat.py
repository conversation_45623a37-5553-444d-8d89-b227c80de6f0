#!/usr/bin/env python3
"""
PAT Runner Script

This script sets up the Python path correctly and runs the PAT tool.
It fixes the import issues by ensuring the PAT_tool directory is in the Python path.

Usage:
    python run_pat.py <project_path>
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """Main function to run the PAT tool with proper Python path setup."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Add the PAT_project_analysis directory to Python path
    sys.path.insert(0, str(pat_dir))
    
    # Get the PAT_analyze.py script path
    pat_script = pat_dir / "PAT_analyze.py"
    
    if not pat_script.exists():
        print(f"Error: PAT analysis tool not found at {pat_script}")
        return 1
    
    # Forward all arguments to the PAT_analyze.py script
    cmd = [sys.executable, str(pat_script)] + sys.argv[1:]
    
    try:
        return subprocess.call(cmd)
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user.")
        return 1
    except Exception as e:
        print(f"\nError running analysis tool: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())

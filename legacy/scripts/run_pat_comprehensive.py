#!/usr/bin/env python3
"""
Comprehensive PAT Runner

This script runs the PAT tool with all available tools enabled and provides
detailed progress information including what's currently happening and ETA.

Usage:
    python run_pat_comprehensive.py <project_path>

Arguments:
    project_path: Path to the project to analyze
"""

import os
import subprocess
import sys
import tempfile
import time
from pathlib import Path


# ANSI color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def create_comprehensive_config(pat_dir: Path) -> Path:
    """Create a comprehensive config file that enables all tools."""
    config_path = pat_dir / "PAT_tool" / "config_comprehensive.yaml"

    # Read the full config as a template
    full_config_path = pat_dir / "PAT_tool" / "config_full.yaml"
    with open(full_config_path, 'r') as f:
        config_content = f.read()

    # Enable all tools
    config_content = config_content.replace("enabled: false", "enabled: true")

    # Write the comprehensive config
    with open(config_path, 'w') as f:
        f.write(config_content)

    return config_path

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <project_path>")
        return 1

    project_path = sys.argv[1]

    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()

    # Create a comprehensive config
    config_path = create_comprehensive_config(pat_dir)

    # Create a wrapper script that sets up the environment
    with tempfile.NamedTemporaryFile(prefix="pat_wrapper_", suffix=".py", delete=False) as f:
        wrapper_script = Path(f.name)

        f.write(f'''
#!/usr/bin/env python3
import sys
import os
from pathlib import Path
import time
import threading

# Add PAT_project_analysis directory to Python path
sys.path.insert(0, "{str(pat_dir)}")

# Add PAT_tool directory to Python path
pat_tool_dir = Path("{str(pat_dir)}") / "PAT_tool"
sys.path.insert(0, str(pat_tool_dir))

# Create __init__.py files to make the directories proper packages
init_path = pat_tool_dir / "__init__.py"
if not init_path.exists():
    with open(init_path, "w") as f:
        f.write("# Auto-generated by run_pat_comprehensive.py\\n")

# Set environment variable to use our comprehensive config
os.environ["PAT_CONFIG_PATH"] = "{str(config_path)}"

# Patch the progress reporting to show more detailed information
try:
    from PAT_tool.utils import ProgressTracker

    # Store the original update method
    original_update = ProgressTracker.update
    original_start_phase = ProgressTracker.start_phase
    original_increment = ProgressTracker.increment
    original_complete_phase = ProgressTracker.complete_phase

    # Track the current phase and file
    current_phase = "Initializing"
    current_file = ""
    phase_start_time = time.time()
    phase_steps_completed = 0
    phase_total_steps = 0

    # Define a patched version that shows more detailed information
    def patched_start_phase(self, phase_name, total_steps=None):
        global current_phase, phase_start_time, phase_steps_completed, phase_total_steps
        current_phase = phase_name
        phase_start_time = time.time()
        phase_steps_completed = 0
        phase_total_steps = total_steps or 0
        return original_start_phase(self, phase_name, total_steps)

    def patched_increment(self, steps=1, current_item=None):
        global current_file, phase_steps_completed
        if current_item:
            current_file = current_item
        phase_steps_completed += steps
        return original_increment(self, steps, current_item)

    def patched_update(self, message=None):
        global current_phase, current_file, phase_start_time, phase_steps_completed, phase_total_steps

        # Calculate ETA
        elapsed = time.time() - phase_start_time
        if phase_steps_completed > 0 and phase_total_steps > 0:
            progress = phase_steps_completed / phase_total_steps
            eta_seconds = (elapsed / progress) * (1 - progress)
            eta_minutes, eta_seconds = divmod(int(eta_seconds), 60)
            eta_hours, eta_minutes = divmod(eta_minutes, 60)

            if eta_hours > 0:
                eta_str = f"ETA: {eta_hours}h {eta_minutes}m {eta_seconds}s"
            elif eta_minutes > 0:
                eta_str = f"ETA: {eta_minutes}m {eta_seconds}s"
            else:
                eta_str = f"ETA: {eta_seconds}s"

            # Format the progress message
            progress_msg = f"[{current_phase}] {phase_steps_completed}/{phase_total_steps} ({progress:.1%}) - {eta_str}"
            if current_file:
                progress_msg += f" - Current file: {current_file}"

            # Print the progress message
            print(f"\\r\\033[K{progress_msg}", end="", flush=True)

        return original_update(self, message)

    def patched_complete_phase(self, phase_name, status="Complete!"):
        global current_phase, current_file
        print()  # Add a newline after the progress bar
        current_phase = "Completed " + phase_name
        current_file = ""
        return original_complete_phase(self, phase_name, status)

    # Apply the patches
    ProgressTracker.start_phase = patched_start_phase
    ProgressTracker.increment = patched_increment
    ProgressTracker.update = patched_update
    ProgressTracker.complete_phase = patched_complete_phase

    print("Successfully patched ProgressTracker for detailed progress reporting")
except Exception as e:
    print(f"Failed to patch ProgressTracker: {{e}}")

# Import and run the main function
try:
    from PAT_tool.main import main
    # Forward command line arguments
    sys.exit(main())
except Exception as e:
    print(f"Error running PAT tool: {{e}}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
'''.encode('utf-8'))

    # Make the wrapper script executable
    os.chmod(wrapper_script, 0o755)

    print(f"{Colors.BOLD}{Colors.HEADER}Running Comprehensive PAT Analysis{Colors.ENDC}")
    print(f"{Colors.CYAN}Project:{Colors.ENDC} {project_path}")
    print(f"{Colors.CYAN}Config:{Colors.ENDC} {config_path}")
    print(f"{Colors.CYAN}All tools enabled:{Colors.ENDC} Yes")
    print()
    print(f"{Colors.YELLOW}This analysis will run all available tools and may take a long time.{Colors.ENDC}")
    print(f"{Colors.YELLOW}Detailed progress information will be displayed.{Colors.ENDC}")
    print()

    # Run the wrapper script with the project path
    cmd = [sys.executable, str(wrapper_script), project_path]
    try:
        return subprocess.call(cmd)
    except KeyboardInterrupt:
        print("\nAnalysis interrupted by user.")
        return 1
    except Exception as e:
        print(f"\nError running analysis tool: {e}")
        import traceback
        traceback.print_exc()
        return 1
    finally:
        # Clean up the temporary wrapper script
        if wrapper_script.exists():
            wrapper_script.unlink()

if __name__ == "__main__":
    sys.exit(main())

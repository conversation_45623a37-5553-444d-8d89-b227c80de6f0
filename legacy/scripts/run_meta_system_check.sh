#!/bin/bash
# <PERSON>ript to run meta-system separation analysis
# This script activates the virtual environment and runs the meta-system separation checker

# Set the project path (default to current directory if not provided)
PROJECT_PATH=${1:-.}

# Set the output directory
OUTPUT_DIR="analysis"

# Display banner
echo "=========================================================="
echo "  Meta-System Separation Checker for person_suit"
echo "=========================================================="
echo "Project path: $PROJECT_PATH"
echo "Output directory: $OUTPUT_DIR"
echo "=========================================================="

# Determine the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Activate the virtual environment
echo "Activating virtual environment..."
if [ -d "$SCRIPT_DIR/PAT_venv" ]; then
    # Check if it's a Mac/Linux or Windows environment
    if [ -f "$SCRIPT_DIR/PAT_venv/bin/activate" ]; then
        source "$SCRIPT_DIR/PAT_venv/bin/activate"
    elif [ -f "$SCRIPT_DIR/PAT_venv/Scripts/activate" ]; then
        source "$SCRIPT_DIR/PAT_venv/Scripts/activate"
    else
        echo "Error: Could not find activation script in virtual environment."
        exit 1
    fi
    echo "Virtual environment activated."
else
    echo "Warning: Virtual environment not found at $SCRIPT_DIR/PAT_venv."
    echo "Continuing without virtual environment. This may cause dependency issues."
fi

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Run the meta-system separation checker
echo "Running meta-system separation analysis..."
# Use the pat.py script which is more stable
python "$SCRIPT_DIR/pat.py" "$PROJECT_PATH" --output "$OUTPUT_DIR" --verbose

# Check the exit code
EXIT_CODE=$?
if [ $EXIT_CODE -eq 0 ]; then
    echo "=========================================================="
    echo "  Meta-System Separation Analysis Completed Successfully"
    echo "  No violations found!"
    echo "=========================================================="
else
    echo "=========================================================="
    echo "  Meta-System Separation Analysis Completed"
    echo "  Violations found! Check the output for details."
    echo "=========================================================="
fi

# Open the HTML report if it exists
REPORT_PATH="$OUTPUT_DIR/$(basename "$PROJECT_PATH")_meta_system_report.html"
if [ -f "$REPORT_PATH" ]; then
    echo "Opening interactive report..."
    if [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS
        open "$REPORT_PATH"
    elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux
        xdg-open "$REPORT_PATH" &> /dev/null
    elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "cygwin" || "$OSTYPE" == "win32" ]]; then
        # Windows
        start "$REPORT_PATH"
    else
        echo "Could not automatically open the report. Please open it manually at: $REPORT_PATH"
    fi
fi

# Deactivate the virtual environment if it was activated
if [ -n "$VIRTUAL_ENV" ]; then
    echo "Deactivating virtual environment..."
    deactivate
fi

exit $EXIT_CODE

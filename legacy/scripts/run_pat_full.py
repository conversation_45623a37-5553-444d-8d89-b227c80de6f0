#!/usr/bin/env python3
"""
Full PAT Runner Script

This script sets up the Python path correctly and runs the PAT tool with all
possible tools enabled. It fixes import issues by ensuring the PAT_tool directory
is in the Python path and uses a full configuration that enables all tools.

Usage:
    python run_pat_full.py <project_path>
"""

import os
import subprocess
import sys
import tempfile
from pathlib import Path


def main():
    """Main function to run the PAT tool with proper Python path setup and full tool configuration."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()

    # Create a temporary directory for our wrapper script
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a temporary wrapper script
        wrapper_script = Path(temp_dir) / "pat_wrapper.py"

        # Write the wrapper script content
        with open(wrapper_script, "w") as f:
            f.write(f'''
#!/usr/bin/env python3
import sys
import os
from pathlib import Path

# Add PAT_project_analysis directory to Python path
sys.path.insert(0, "{str(pat_dir)}")

# Add PAT_tool directory to Python path
pat_tool_dir = Path("{str(pat_dir)}") / "PAT_tool"
sys.path.insert(0, str(pat_tool_dir))

# Create __init__.py files to make the directories proper packages
init_path = pat_tool_dir / "__init__.py"
if not init_path.exists():
    with open(init_path, "w") as f:
        f.write("# Auto-generated by run_pat_full.py\\n")

# Set environment variable to use our full config
os.environ["PAT_CONFIG_PATH"] = "{str(pat_dir / 'PAT_tool' / 'config_full.yaml')}"

# Note: We've fixed the Ruff stage directly in the code, so no patching is needed

# Import and run the main function
try:
    from PAT_tool.main import main
    # Forward command line arguments
    sys.exit(main())
except Exception as e:
    print(f"Error running PAT tool: {{e}}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
''')

        # Make the wrapper script executable
        os.chmod(wrapper_script, 0o755)

        # Run the wrapper script with the same arguments
        cmd = [sys.executable, str(wrapper_script)] + sys.argv[1:]
        try:
            return subprocess.call(cmd)
        except KeyboardInterrupt:
            print("\nAnalysis interrupted by user.")
            return 1
        except Exception as e:
            print(f"\nError running analysis tool: {e}")
            import traceback
            traceback.print_exc()
            return 1

if __name__ == "__main__":
    sys.exit(main())

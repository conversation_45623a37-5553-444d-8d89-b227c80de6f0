#!/usr/bin/env python3
"""
Comprehensive PAT Runner with All Tools

This script runs the PAT tool with all available tools enabled and fixes syntax errors.
It also generates issue-specific prompts for individual issues.

Usage:
    python run_pat_all_tools.py <project_path>

Arguments:
    project_path: Path to the project to analyze
"""

import os
import subprocess
import sys
import time
from pathlib import Path
from typing import Optional


# ANSI color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

def run_command(cmd: list, description: str) -> int:
    """Run a command with a spinner and return the exit code."""
    print(f"{Colors.CYAN}Running: {description}{Colors.ENDC}")
    print(f"Command: {' '.join(cmd)}")
    
    start_time = time.time()
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # Print output in real-time with a spinner
        spinner = ['|', '/', '-', '\\']
        i = 0
        while process.poll() is None:
            output = process.stdout.readline().strip()
            if output:
                print(f"\r{Colors.CYAN}{spinner[i % 4]}{Colors.ENDC} {output}", end="")
                i += 1
            else:
                # If no output, just update the spinner
                elapsed = time.time() - start_time
                minutes, seconds = divmod(int(elapsed), 60)
                print(f"\r{Colors.CYAN}{spinner[i % 4]}{Colors.ENDC} Running for {minutes:02d}:{seconds:02d}...", end="")
                i += 1
                time.sleep(0.1)
        
        # Get the final output
        remaining_output = process.stdout.read()
        if remaining_output:
            print(f"\n{remaining_output}")
        
        # Print completion message
        elapsed = time.time() - start_time
        minutes, seconds = divmod(int(elapsed), 60)
        if process.returncode == 0:
            print(f"\n{Colors.GREEN}✓ {description} completed successfully in {minutes:02d}:{seconds:02d}{Colors.ENDC}")
        else:
            print(f"\n{Colors.RED}✗ {description} failed with return code {process.returncode} after {minutes:02d}:{seconds:02d}{Colors.ENDC}")
        
        return process.returncode
    except KeyboardInterrupt:
        print(f"\n{Colors.YELLOW}Process interrupted by user{Colors.ENDC}")
        return 1
    except Exception as e:
        print(f"\n{Colors.RED}Error running command: {e}{Colors.ENDC}")
        return 1

def fix_syntax_errors(project_path: str) -> bool:
    """Fix syntax errors in the project."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the syntax error fixer
    cmd = [sys.executable, str(pat_dir / "fix_syntax_errors.py"), project_path]
    return run_command(cmd, "Fixing syntax errors") == 0

def run_pat_with_all_tools(project_path: str) -> Optional[Path]:
    """Run PAT with all tools enabled."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Set environment variable to use our all-tools config
    os.environ["PAT_CONFIG_PATH"] = str(pat_dir / "PAT_tool" / "config_all_tools.yaml")
    
    # Run the PAT tool
    cmd = [sys.executable, str(pat_dir / "PAT_tool" / "main.py"), project_path]
    if run_command(cmd, "Running PAT with all tools") != 0:
        return None
    
    # Find the latest output directory
    output_dir = pat_dir / "PAT_output" / "latest"
    if not output_dir.exists() or not output_dir.is_symlink():
        # Find the latest output directory by timestamp
        output_dirs = list(sorted((pat_dir / "PAT_output").glob("20*-*-*_*-*-*"), reverse=True))
        if not output_dirs:
            print(f"{Colors.RED}No PAT output directory found{Colors.ENDC}")
            return None
        output_dir = output_dirs[0]
    else:
        # Resolve the symlink
        output_dir = output_dir.resolve()
    
    return output_dir

def generate_issue_prompts(pat_output_dir: Path) -> bool:
    """Generate issue-specific prompts from PAT output."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the issue prompt generator
    cmd = [
        sys.executable,
        str(pat_dir / "generate_issue_prompts.py"),
        str(pat_output_dir),
        "--output-dir", str(pat_output_dir / "issue_prompts")
    ]
    return run_command(cmd, "Generating issue-specific prompts") == 0

def extract_high_priority_files(pat_output_dir: Path) -> bool:
    """Extract high-priority files from PAT output."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Run the high-priority extractor
    cmd = [
        sys.executable,
        str(pat_dir / "extract_high_priority.py"),
        str(pat_output_dir),
        "--output-dir", str(pat_output_dir / "high_priority")
    ]
    return run_command(cmd, "Extracting high-priority files") == 0

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print(f"Usage: {sys.argv[0]} <project_path>")
        return 1
    
    project_path = sys.argv[1]
    
    print(f"{Colors.BOLD}{Colors.HEADER}Comprehensive PAT Analysis with All Tools{Colors.ENDC}")
    print(f"{Colors.CYAN}Project:{Colors.ENDC} {project_path}")
    print()
    
    # Step 1: Fix syntax errors
    print(f"{Colors.BOLD}{Colors.HEADER}Step 1: Fixing Syntax Errors{Colors.ENDC}")
    if not fix_syntax_errors(project_path):
        print(f"{Colors.YELLOW}Warning: Some syntax errors could not be fixed automatically.{Colors.ENDC}")
        print(f"{Colors.YELLOW}Continuing with analysis, but some files may be skipped.{Colors.ENDC}")
    print()
    
    # Step 2: Run PAT with all tools
    print(f"{Colors.BOLD}{Colors.HEADER}Step 2: Running PAT with All Tools{Colors.ENDC}")
    pat_output_dir = run_pat_with_all_tools(project_path)
    if not pat_output_dir:
        print(f"{Colors.RED}Error: PAT analysis failed{Colors.ENDC}")
        return 1
    print()
    
    # Step 3: Extract high-priority files
    print(f"{Colors.BOLD}{Colors.HEADER}Step 3: Extracting High-Priority Files{Colors.ENDC}")
    if not extract_high_priority_files(pat_output_dir):
        print(f"{Colors.YELLOW}Warning: High-priority extraction failed{Colors.ENDC}")
    print()
    
    # Step 4: Generate issue-specific prompts
    print(f"{Colors.BOLD}{Colors.HEADER}Step 4: Generating Issue-Specific Prompts{Colors.ENDC}")
    if not generate_issue_prompts(pat_output_dir):
        print(f"{Colors.YELLOW}Warning: Issue prompt generation failed{Colors.ENDC}")
    print()
    
    # Print summary
    print(f"{Colors.BOLD}{Colors.HEADER}Analysis Complete{Colors.ENDC}")
    print(f"{Colors.CYAN}PAT output directory:{Colors.ENDC} {pat_output_dir}")
    print(f"{Colors.CYAN}High-priority files:{Colors.ENDC} {pat_output_dir / 'high_priority'}")
    print(f"{Colors.CYAN}Issue-specific prompts:{Colors.ENDC} {pat_output_dir / 'issue_prompts'}")
    print()
    
    return 0

if __name__ == "__main__":
    try:
        sys.exit(main())
    except KeyboardInterrupt:
        print(f"\n\n{Colors.YELLOW}Analysis interrupted by user.{Colors.ENDC}")
        sys.exit(1)

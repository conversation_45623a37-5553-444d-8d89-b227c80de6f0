"""
pat_backend.py
Backend logic for PAT Web UI: runs analyzers/tools and returns results.
"""
import json
import os
import shutil
import subprocess
import tempfile
import time
from dataclasses import dataclass
from datetime import datetime
from enum import Enum, auto
from pathlib import Path
from typing import Any, Dict, Iterator, List, Optional, Tuple

import matplotlib.pyplot as plt

# Import our report generator
# Assuming pat_core is accessible or adjust import path if needed
try:
    from pat_core.report_generator import PatReportGenerator
except ImportError:
    # Fallback if pat_core is not directly importable
    # This might indicate a need to adjust PYTHONPATH or project structure
    print("Warning: Could not import PatReportGenerator from pat_core. Enhanced reports might be limited.")
    PatReportGenerator = None # Define as None to avoid errors later


class LogLevel(Enum):
    DEBUG = auto()  # Detailed debug information
    INFO = auto()
    WARNING = auto()
    ERROR = auto()  # For tool execution errors
    SUCCESS = auto()
    FINDING = auto()  # For tool findings (e.g. linter found issues)

@dataclass
class LogEntry:
    """Structured log entry with timestamp and level."""
    timestamp: float
    level: LogLevel
    tool: str
    message: str
    details: str = ""

    def to_dict(self) -> Dict[str, Any]:
        """Convert log entry to dictionary for JSON serialization."""
        return {
            "timestamp": self.timestamp,
            "datetime": datetime.fromtimestamp(self.timestamp).isoformat(),
            "level": self.level.name,
            "tool": self.tool,
            "message": self.message,
            "details": self.details
        }

class AnalysisLog:
    """Manages analysis logs with support for reverse chronological order."""
    def __init__(self):
        self.entries: List[LogEntry] = []

    def add(self, level: LogLevel, tool: str, message: str, details: str = ""):
        """Add a new log entry."""
        entry = LogEntry(
            timestamp=time.time(),
            level=level,
            tool=tool,
            message=message,
            details=details
        )
        self.entries.insert(0, entry)  # Insert at beginning for reverse chronological order

    def get_entries(self) -> List[Dict[str, Any]]:
        """Get all entries as dictionaries."""
        return [entry.to_dict() for entry in self.entries]

    def get_latest(self, n: int = 10) -> List[Dict[str, Any]]:
        """Get the n most recent entries."""
        return [entry.to_dict() for entry in self.entries[:n]]

@dataclass
class ToolResult:
    """Structured result from a tool run."""
    success: bool  # Whether the tool executed successfully
    has_findings: bool  # Whether the tool found any issues
    returncode: int
    stdout: str
    stderr: str
    error_msg: str = ""
    command: List[str] = None

def parse_tool_output(tool_name: str, result: subprocess.CompletedProcess) -> ToolResult:
    """Parse tool output to determine if it ran successfully and if it found issues."""
    if tool_name == "Ruff (Linter)":
        # Ruff exit codes: 0 = no issues, 1 = issues found, 2 = execution error
        return ToolResult(
            success=(result.returncode != 2),
            has_findings=(result.returncode == 1),
            returncode=result.returncode,
            stdout=result.stdout,
            stderr=result.stderr
        )
    elif tool_name == "Pytest (Tests)":
        # Pytest exit codes: 0 = all passed, 1 = tests failed, 2+ = execution error
        return ToolResult(
            success=(result.returncode <= 1),
            has_findings=(result.returncode == 1),
            returncode=result.returncode,
            stdout=result.stdout,
            stderr=result.stderr
        )
    elif tool_name == "Bandit (Security)":
        # Bandit exit codes: 0 = no issues, 1 = issues found, 2 = execution error
        return ToolResult(
            success=(result.returncode != 2),
            has_findings=(result.returncode == 1),
            returncode=result.returncode,
            stdout=result.stdout,
            stderr=result.stderr
        )
    else:
        # Default behavior: assume 0 = success/no issues, non-zero = issues found
        return ToolResult(
            success=True,
            has_findings=(result.returncode != 0),
            returncode=result.returncode,
            stdout=result.stdout,
            stderr=result.stderr
        )

# --- Tool Command Definitions (Lambdas accepting config) ---
# Helper to build args from config dict
def _build_args(cfg: Dict[str, Any], arg_map: Dict[str, str] = None) -> List[str]:
    args = []
    arg_map = arg_map or {}
    for key, value in cfg.items():
        arg_name = arg_map.get(key, f"--{key.replace('_', '-')}") # Default: --key-name
        if isinstance(value, bool) and value:
            args.append(arg_name) # Boolean flags
        elif isinstance(value, list):
             # Handle list args like --ignore F401 F403 or --select E F
             # Some tools might need key repeated (e.g. --ignore F401 --ignore F403)
             # This simple version appends key then all values. Adjust per tool if needed.
             args.append(arg_name)
             args.extend(map(str, value))
        elif value is not None and not isinstance(value, bool):
             # Handle key-value args like --max-line-length 100
             args.append(arg_name)
             args.append(str(value))
    return args

TOOL_COMMANDS = {
    "Ruff (Linter)": lambda path, tests, cfg: ["ruff", "check", path, "--output-format=json"] + _build_args(cfg.get("ruff_config", {})),
    "Pylint": lambda path, tests, cfg: ["pylint", path, "--output-format=json"] + _build_args(cfg.get("pylint_config", {})),
    "PyCodeStyle": lambda path, tests, cfg: ["pycodestyle", path] + _build_args(cfg.get("pycodestyle_config", {"max-line-length": 100, "ignore": ["W291","W292","E501"]})),
    "Radon (Complexity)": lambda path, tests, cfg: ["radon", "cc", path, "-j"] + _build_args(cfg.get("radon_config", {})),
    "Vulture (Dead Code)": lambda path, tests, cfg: ["vulture", path] + _build_args(cfg.get("vulture_config", {})),
    "McCabe (Complexity)": lambda path, tests, cfg: ["python", "-m", "mccabe", path] + _build_args(cfg.get("mccabe_config", {"min": 5})),
    "Mypy (Type Checking)": lambda path, tests, cfg: ["mypy", path, "--show-error-codes", "--no-color-output"] + _build_args(cfg.get("mypy_config", {})),
    "Pyright (Type Checking)": lambda path, tests, cfg: ["pyright", path, "--outputjson"] + _build_args(cfg.get("pyright_config", {})),
    "Pyre-Check (Type Checking)": lambda path, tests, cfg: ["pyre", "--source-directory", path, "--output=json", "check"] + _build_args(cfg.get("pyre_config", {})),
    "Bandit (Security)": lambda path, tests, cfg: ["bandit", "-r", path, "--format", "json", "--quiet"] + _build_args(cfg.get("bandit_config", {})),
    "Safety (Dependencies)": lambda path, tests, cfg: ["safety", "check", "--json"] + _build_args(cfg.get("safety_config", {})),
    "Black (Formatter)": lambda path, tests, cfg: ["black", "--check", path] + _build_args(cfg.get("black_config", {})),
    "Isort (Imports)": lambda path, tests, cfg: ["isort", "--check-only", path] + _build_args(cfg.get("isort_config", {})),
    "Pydocstyle (Docstrings)": lambda path, tests, cfg: ["pydocstyle", path] + _build_args(cfg.get("pydocstyle_config", {"convention": "google"})),
    "Pipdeptree (Dependencies)": lambda path, tests, cfg: ["pipdeptree", "--json-tree"] + _build_args(cfg.get("pipdeptree_config", {})),
    "Pytest (Tests)": lambda path, tests, cfg: ["python", "-m", "pytest", path] + (tests if tests else []) + _build_args(cfg.get("pytest_config", {"v": True, "import-mode": "importlib", "p": "no:warnings"})),
    "Pytest-JSON-Report": lambda path, tests, cfg: ["python", "-m", "pytest", "--json-report-file=report.json", path] + (tests if tests else []) + _build_args(cfg.get("pytest_json_report_config", {})),
    # Coverage needs careful handling of combined commands
    "Coverage": lambda path, tests, cfg: [
        "bash", "-c",
        f"coverage run {' '.join(_build_args(cfg.get('coverage_run_config', {})))} -m pytest {path} {' '.join(tests if tests else [])} {' '.join(_build_args(cfg.get('pytest_config', {})))} "
        f"&& coverage json {' '.join(_build_args(cfg.get('coverage_json_config', {})))}"
    ]
}


def run_tool(tool_name: str, path: str, tests: List[str], log: AnalysisLog, timeout: int = 120, tool_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Runs a single tool with optional configuration overrides."""
    # Ensure tool_config is a dictionary
    tool_config = tool_config or {}
    cmd_func = TOOL_COMMANDS.get(tool_name)
    if not cmd_func:
        log.add(LogLevel.ERROR, tool_name, "Tool command definition not found")
        return {"tool": tool_name, "error": "Tool not supported", "execution_error": True}

    try:
        log.add(LogLevel.INFO, tool_name, "Starting analysis...")
        env = dict(os.environ)
        env["PYTHONPATH"] = str(Path(path).resolve())

        # Extract the specific config for this tool (e.g., 'ruff_config' from the overall config)
        # Assuming tool_config passed is the *entire* config dict from the UI
        # We need to find the relevant sub-dictionary for the current tool
        tool_key_in_config = tool_name.split(" (")[0].lower().replace("-", "_") + "_config" # e.g., ruff_config
        specific_tool_cfg = tool_config.get(tool_key_in_config, {})

        # Generate the command using the lambda, passing the specific config
        command = cmd_func(path, tests, specific_tool_cfg)

        log.add(LogLevel.INFO, tool_name, f"Running command: {' '.join(command)}")

        result = subprocess.run(command, capture_output=True, text=True, timeout=timeout, env=env)


        # Special handling for Coverage tool (needs adjustment if command structure changes)
        # The Coverage command is now more complex due to config_args, this needs review
        # if tool_name == "Coverage" and result.returncode == 0:
            # This logic might be incorrect now if the bash command structure changed
            # json_result = subprocess.run(["coverage", "json"] + config_args, capture_output=True, text=True, env=env)
            # result.stdout += "\n" + json_result.stdout
            # result.stderr += "\n" + json_result.stderr
            # result.returncode = json_result.returncode

        tool_result = parse_tool_output(tool_name, result)
        # Removed duplicated block and fixed indentation

        if not tool_result.success:
            # Tool execution failed
            error_message = f"Tool execution failed (exit {result.returncode})"
            context = ""
            if "permission" in result.stderr.lower():
                context = "This appears to be a permissions issue. Ensure you have read/write access to the project directory."
            elif "no such file" in result.stderr.lower():
                context = "The specified file or directory doesn't exist. Check the project path."
            elif "syntax" in result.stderr.lower():
                context = "A syntax error was encountered. The tool may not be compatible with the Python version in your code."

            detailed_error = f"{error_message}\n{context}" if context else error_message
            log.add(LogLevel.ERROR, tool_name, detailed_error, result.stderr[:500] if result.stderr else "")
        elif tool_result.has_findings:
            # Tool ran successfully but found issues
            log.add(LogLevel.FINDING, tool_name,
                   "Analysis completed - issues found",
                   result.stdout[:500] if result.stdout else result.stderr[:500])
        else:
            # Tool ran successfully and found no issues
            log.add(LogLevel.SUCCESS, tool_name, "Analysis completed - no issues found")

        # Always log stdout/stderr if present, but with appropriate levels
        if result.stdout and not tool_result.has_findings:
            log.add(LogLevel.INFO, tool_name, "Tool output", result.stdout[:500])
        if result.stderr and tool_result.success:
            log.add(LogLevel.WARNING, tool_name, "Tool warnings", result.stderr[:500])

        return {
            "tool": tool_name,
            "stdout": result.stdout,
            "stderr": result.stderr,
            "returncode": result.returncode,
            "command": command,
            "execution_error": not tool_result.success,
            "has_findings": tool_result.has_findings
        }
    except FileNotFoundError:
        log.add(LogLevel.ERROR, tool_name, "Tool not installed")
        return {"tool": tool_name, "error": "Tool not installed", "execution_error": True}
    except subprocess.TimeoutExpired:
        log.add(LogLevel.ERROR, tool_name, f"Timed out after {timeout} seconds")
        return {"tool": tool_name, "error": f"Command timed out after {timeout} seconds. This may happen with large codebases or complex analysis tools.", "execution_error": True}
    except Exception as e:
        error_message = str(e)
        context = ""
        if "permission" in error_message.lower():
            context = "This appears to be a permissions issue. Ensure you have read/write access to the project directory."
        elif "no such file" in error_message.lower():
            context = "The specified file or directory doesn't exist. Check the project path."
        elif "syntax" in error_message.lower():
            context = "A syntax error was encountered. The tool may not be compatible with the Python version in your code."

        detailed_error = f"{error_message}\n{context}" if context else error_message
        log.add(LogLevel.ERROR, tool_name, f"Error: {detailed_error}")
        return {"tool": tool_name, "error": detailed_error, "execution_error": True}

def generate_summary_bar_chart(results: List[Dict[str, Any]]) -> str:
    tool_names = [r['tool'] for r in results]
    exit_codes = [r.get('returncode', 1 if 'error' in r else 0) for r in results]

    # Define color scheme based on result type
    colors = []
    for result in results:
        if result.get('execution_error'):
            colors.append('red')  # Error in execution
        elif result.get('has_findings'):
            colors.append('orange')  # Issues found
        elif result.get('returncode', 0) == 0:
            colors.append('green')  # Success
        else:
            colors.append('gray')  # Unknown state

    fig, ax = plt.subplots(figsize=(max(6, len(tool_names)), 4))
    ax.bar(tool_names, exit_codes, color=colors)

    # Add a legend
    from matplotlib.patches import Patch
    legend_elements = [
        Patch(facecolor='green', label='Success'),
        Patch(facecolor='orange', label='Issues Found'),
        Patch(facecolor='red', label='Execution Error'),
        Patch(facecolor='gray', label='Unknown')
    ]
    ax.legend(handles=legend_elements, loc='upper right')

    ax.set_ylabel('Exit Code (0=Success)')
    ax.set_title('Tool Run Summary')
    plt.xticks(rotation=30, ha='right')
    plt.tight_layout()
    tmpfile = tempfile.NamedTemporaryFile(delete=False, suffix='.png')
    fig.savefig(tmpfile.name)
    plt.close(fig)
    return tmpfile.name

def run_analysis_stream(project_path: str, tools: List[str], tests: List[str], report_types: List[str], timeout: int = 120, config_override: Optional[Dict[str, Any]] = None) -> Iterator[Tuple[float, float, List[Dict[str, Any]], List[Dict[str, Any]]]]:
    """Run analysis with streaming results and periodic log saving."""
    log = AnalysisLog()
    results = []
    # Use provided config or default
    current_config = config_override or {}
    n = len(tools)
    start_time = time.time()

    # Create output directory with timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    base_output_dir = Path("PAT_output")
    output_dir = base_output_dir / timestamp

    # Ensure directories exist
    base_output_dir.mkdir(exist_ok=True)
    output_dir.mkdir(parents=True, exist_ok=True)

    # Update symlink to latest
    try:
        latest_dir = base_output_dir / "latest"
        if latest_dir.exists() or latest_dir.is_symlink():
            latest_dir.unlink()
        latest_dir.symlink_to(output_dir.name)
    except Exception as e:
        print(f"Warning: Failed to create 'latest' symlink: {e}")

    # Log analysis start
    log.add(LogLevel.INFO, "Analysis", f"Starting analysis of {project_path}")
    log.add(LogLevel.INFO, "Analysis", f"Tools to run: {', '.join(tools)}")

    def save_logs():
        """Save current logs to files."""
        # Save detailed log
        with open(output_dir / "detailed.log", 'w') as f:
            f.write("PAT Analysis Detailed Log (In Progress)\n")
            f.write(f"Generated: {timestamp}\n")
            f.write(f"Project: {project_path}\n")
            f.write("-" * 80 + "\n\n")

            for entry in reversed(log.get_entries()):  # Chronological order
                entry_time = datetime.fromtimestamp(entry['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"[{entry_time}] {entry['level']}: {entry['tool']}\n")
                f.write(f"Message: {entry['message']}\n")
                if entry['details']:
                    f.write(f"Details: {entry['details']}\n")
                f.write("-" * 40 + "\n")

        # Save summary log (important entries only)
        with open(output_dir / "summary.log", 'w') as f:
            f.write("PAT Analysis Summary Log (In Progress)\n")
            f.write(f"Generated: {timestamp}\n")
            f.write(f"Project: {project_path}\n")
            f.write("-" * 80 + "\n\n")

            important_levels = {'INFO', 'WARNING', 'ERROR', 'FINDING'}
            for entry in reversed(log.get_entries()):
                if entry['level'] in important_levels:
                    entry_time = datetime.fromtimestamp(entry['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                    f.write(f"[{entry_time}] {entry['level']}: {entry['tool']} - {entry['message']}\n")
                    if entry['level'] in {'ERROR', 'FINDING'} and entry['details']:
                        f.write(f"Details: {entry['details']}\n")
                        f.write("-" * 40 + "\n")

    # Initial log save
    save_logs()

    for i, tool in enumerate(tools):
        # Pass the specific tool's config from the override dict
        tool_cfg_key = tool.split(" (")[0].lower().replace("-", "_") + "_config"
        specific_tool_cfg = current_config.get('analysis', {}).get(tool_cfg_key, {}) # Look inside 'analysis' key
        res = run_tool(tool, project_path, tests, log, timeout=timeout, tool_config=specific_tool_cfg)
        t1 = time.time()
        results.append(res)

        # Calculate progress metrics
        elapsed = t1 - start_time
        avg_time = elapsed / (i+1)
        eta = avg_time * (n - (i+1))
        progress = (i+1) / n

        # Log progress
        log.add(LogLevel.INFO, "Progress",
                f"Progress: {progress*100:.1f}% ({i+1}/{n}), "
            f"ETA: {eta:.1f}s, "
            f"Avg time per tool: {avg_time:.1f}s")

        # Save logs after each tool run
        save_logs()

        yield progress, eta, log.get_entries(), results.copy()

    # Log completion and final save
    log.add(LogLevel.SUCCESS, "Analysis", "Analysis completed",
            f"Processed {len(tools)} tools in {time.time() - start_time:.1f} seconds")
    save_logs()

def run_analysis(project_path: str, tools: List[str], tests: List[str], report_types: List[str], timeout: int = 120, config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Run analysis with selected tools and generate reports."""
    # Use provided config or default
    current_config = config_override or {}
    # Create output directory with timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    base_output_dir = Path("PAT_output")
    output_dir = base_output_dir / timestamp

    # Ensure base directory exists
    base_output_dir.mkdir(exist_ok=True)

    # Create timestamped directory
    output_dir.mkdir(parents=True, exist_ok=True)

    # Update the symlink to latest - with robust error handling
    latest_dir = base_output_dir / "latest"
    try:
        if latest_dir.exists() or latest_dir.is_symlink():
            latest_dir.unlink()
        latest_dir.symlink_to(output_dir.name)  # Use relative path for portability
    except Exception as e:
        print(f"Warning: Failed to create 'latest' symlink: {e}")
        # Continue execution - this is not critical

    # Initialize logging
    log = AnalysisLog()
    results = []

    # Log analysis start
    log.add(LogLevel.INFO, "Analysis", f"Starting analysis of {project_path}")
    log.add(LogLevel.INFO, "Analysis", f"Tools to run: {', '.join(tools)}")

    start_time = time.time()
    execution_errors = 0
    tools_with_findings = 0

    # Run tools
    for tool in tools:
        # Pass the specific tool's config from the override dict
        tool_cfg_key = tool.split(" (")[0].lower().replace("-", "_") + "_config"
        specific_tool_cfg = current_config.get('analysis', {}).get(tool_cfg_key, {}) # Look inside 'analysis' key
        result = run_tool(tool, project_path, tests, log, timeout, tool_config=specific_tool_cfg)
        results.append(result)
        if result.get("execution_error"):
            execution_errors += 1
        elif result.get("has_findings"):
            tools_with_findings += 1

    # Log completion statistics
    total_time = time.time() - start_time
    log.add(LogLevel.INFO, "Analysis", "Analysis Statistics",
            f"Total tools: {len(tools)}\n"
            f"Execution errors: {execution_errors}\n"
            f"Tools with findings: {tools_with_findings}\n"
            f"Clean runs: {len(tools) - execution_errors - tools_with_findings}\n"
            f"Total time: {total_time:.1f}s")

    # Generate reports
    summary = {
        "timestamp": timestamp,
        "project_path": project_path,
        "config_used": "Custom Web UI Config" if config_override else "Default Backend Config", # Indicate config source
        "tools_run": len(tools),
        "successful_runs": sum(1 for r in results if not r.get('execution_error')),
        "tools_with_findings": sum(1 for r in results if r.get('has_findings')),
        "tools": {r['tool']: "Success" if not r.get('execution_error') else "Error" for r in results}
    }

    # Save detailed log
    detailed_log_path = output_dir / "detailed.log"
    with open(detailed_log_path, 'w') as f:
        f.write("PAT Analysis Detailed Log\n")
        f.write(f"Generated: {timestamp}\n")
        f.write(f"Project: {project_path}\n")
        f.write("-" * 80 + "\n\n")

        for entry in reversed(log.get_entries()):  # Chronological order
            timestamp = datetime.fromtimestamp(entry['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
            f.write(f"[{timestamp}] {entry['level']}: {entry['tool']}\n")
            f.write(f"Message: {entry['message']}\n")
            if entry['details']:
                f.write(f"Details: {entry['details']}\n")
            f.write("-" * 40 + "\n")

    # Save high-level log (only INFO, WARNING, ERROR, FINDING)
    summary_log_path = output_dir / "summary.log"
    with open(summary_log_path, 'w') as f:
        f.write("PAT Analysis Summary Log\n")
        f.write(f"Generated: {timestamp}\n")
        f.write(f"Project: {project_path}\n")
        f.write("-" * 80 + "\n\n")

        important_levels = {'INFO', 'WARNING', 'ERROR', 'FINDING'}
        for entry in reversed(log.get_entries()):  # Chronological order
            if entry['level'] in important_levels:
                timestamp = datetime.fromtimestamp(entry['timestamp']).strftime('%Y-%m-%d %H:%M:%S')
                f.write(f"[{timestamp}] {entry['level']}: {entry['tool']} - {entry['message']}\n")
                if entry['level'] in {'ERROR', 'FINDING'} and entry['details']:
                    f.write(f"Details: {entry['details']}\n")
                    f.write("-" * 40 + "\n")

    # Save reports
    if "JSON" in report_types:
        json_report = {
            "summary": summary,
            "results": results,
            "log": log.get_entries()
        }
        json_path = output_dir / "pat_report.json"
        with open(json_path, 'w') as f:
            json.dump(json_report, f, indent=2)

    if "Console" in report_types:
        txt_path = output_dir / "pat_report.txt"
        with open(txt_path, 'w') as f:
            f.write("PAT Analysis Report\n")
            f.write(f"Generated: {timestamp}\n\n")
            f.write(f"Project: {project_path}\n")
            f.write(f"Tools Run: {len(tools)}\n")
            f.write(f"Successful Runs: {summary['successful_runs']}\n")
            f.write(f"Tools with Findings: {summary['tools_with_findings']}\n\n")

            for result in results:
                f.write(f"\n{'-'*80}\n")
                f.write(f"Tool: {result['tool']}\n")
                if result.get('execution_error'):
                    f.write(f"Status: Error - {result.get('error', 'Unknown error')}\n")
                elif result.get('has_findings'):
                    f.write("Status: Found issues\n")
                else:
                    f.write("Status: Success - No issues found\n")

                if result.get('stdout'):
                    f.write("\nOutput:\n")
                    f.write(result['stdout'])
                if result.get('stderr'):
                    f.write("\nErrors/Warnings:\n")
                    f.write(result['stderr'])

    # Generate visualization
    viz_path = output_dir / "pat_visualization.png" # Define viz_path before try block
    if any(t in report_types for t in ["HTML", "Console", "JSON"]):
        try:
            chart_path = generate_summary_bar_chart(results)
            shutil.move(chart_path, viz_path) # Move temp file to output dir
        except Exception as e:
            log.add(LogLevel.ERROR, "Visualization", f"Failed to generate summary chart: {e}")
            viz_path = None # Ensure viz_path is None if generation fails
    else:
        viz_path = None # Ensure viz_path is None if not requested

    # Generate enhanced reports using our PatReportGenerator
    enhanced_reports = {}
    if PatReportGenerator: # Check if import succeeded
        try:
            log.add(LogLevel.INFO, "Report Generator", "Generating enhanced reports...")
            report_generator = PatReportGenerator(str(output_dir), project_path)

            # Generate and save useful summary report
            summary_report = report_generator.generate_summary_report()
            report_generator.save_report(summary_report, "enhanced_summary_report.txt")
            enhanced_reports['useful_summary'] = summary_report

            # Generate and save detailed summary report
            detailed_report = report_generator.generate_detailed_summary_report() # Correct indentation
            report_generator.save_report(detailed_report, "enhanced_detailed_summary_report.txt")
            enhanced_reports['detailed_summary'] = detailed_report

            # Generate and save problematic files report
            problematic_files = report_generator.generate_problematic_files_report()
            report_generator.save_report(problematic_files, "problematic_files_report.txt")
            enhanced_reports['problematic_files'] = problematic_files

            # Generate test report if we ran test tools
            test_tools = ["Coverage", "Pytest-JSON-Report", "Pytest (Tests)"]
            if any(tool in tools for tool in test_tools):
                log.add(LogLevel.INFO, "Report Generator", "Generating test report...")
                test_report = report_generator.generate_test_report()

                # Save test report data as JSON
                with open(output_dir / "test_report.json", 'w') as f:
                    json.dump(test_report, default=global_set_to_list, fp=f, indent=2)

                # Include in enhanced reports
                enhanced_reports['test_report'] = test_report

                # Generate a text summary for the test report
                if test_report.get('status') == 'success':
                    test_quality = test_report.get('test_quality', {})
                    coverage_data = test_report.get('coverage_data', {})
                    pytest_data = test_report.get('pytest_data', {})

                    # Create a text report
                    test_report_txt = ["# Test Analysis Report", ""]

                    # Add quality metrics if available
                    if test_quality:
                        test_report_txt.extend([
                            "## Test Quality Overview",
                            f"Quality Grade: {test_quality.get('grade', 'N/A')}",
                            f"Quality Score: {test_quality.get('quality_score', 0):.1f}/100",
                            f"Coverage: {test_quality.get('coverage_pct', 0):.1f}%",
                            f"Test Success Rate: {test_quality.get('test_success_rate', 0):.1f}%",
                            f"Total Tests: {test_quality.get('total_tests', 0)}",
                            ""
                        ])

                        # Add areas for improvement
                        if test_quality.get('areas_for_improvement'):
                            test_report_txt.append("Areas for Improvement:")
                            for area in test_quality.get('areas_for_improvement', []):
                                test_report_txt.append(f"- {area}")
                            test_report_txt.append("")

                    # Add coverage summary if available
                    if coverage_data and 'summary' in coverage_data:
                        summary = coverage_data['summary']
                        test_report_txt.extend([
                            "## Coverage Summary",
                            f"Overall Coverage: {summary.get('overall_coverage', 0):.1f}%",
                            f"Total Statements: {summary.get('total_statements', 0)}",
                            f"Missing Statements: {summary.get('total_missing', 0)}",
                            ""
                        ])

                        # Add low coverage files if available
                        if test_quality.get('low_coverage_files'):
                            test_report_txt.append("### Files with Low Coverage (<50%)")
                            for file in test_quality.get('low_coverage_files', [])[:10]:  # Show top 10
                                test_report_txt.append(f"- {file['path']}: {file['coverage']:.1f}%")

                            # If there are more than 10 files, mention it
                            if len(test_quality.get('low_coverage_files', [])) > 10:
                                test_report_txt.append(f"... and {len(test_quality.get('low_coverage_files', [])) - 10} more files with low coverage")
                            test_report_txt.append("")

                    # Add test results summary if available
                    if pytest_data and 'summary' in pytest_data:
                        summary = pytest_data['summary']
                        test_report_txt.extend([
                            "## Test Results Summary",
                            f"Total Tests: {summary.get('total', 0)}",
                            f"Passed: {summary.get('passed', 0)}",
                            f"Failed: {summary.get('failed', 0)}",
                            f"Skipped: {summary.get('skipped', 0)}",
                            f"Duration: {summary.get('duration', 0):.2f} seconds",
                            ""
                        ])

                        # Add failing tests if any
                        if pytest_data.get('failing_tests'):
                            test_report_txt.append("### Failing Tests")
                            for test in pytest_data.get('failing_tests', []):
                                test_report_txt.append(f"- {test.get('name', 'Unknown')}")
                                # Include just a summary of the error message
                                message = test.get('message', '')
                                if message:
                                    # Truncate long messages
                                    if len(message) > 200:
                                        message = message[:200] + "..."
                                    test_report_txt.append(f"  Error: {message}")
                            test_report_txt.append("")

                    # Save the text report
                    report_generator.save_report("\n".join(test_report_txt), "test_report.txt")
                    enhanced_reports['test_report_txt'] = "\n".join(test_report_txt)

                log.add(LogLevel.SUCCESS, "Report Generator", "Test report generated successfully")

            # Perform meta-analysis
            meta_analysis = report_generator.perform_meta_analysis()

            # Define helper function *before* it's used in json.dumps
            def set_to_list_serializer(obj):
                if isinstance(obj, set):
                    return list(obj)
                # Let default handler raise error for other types
                raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

            # Use the defined serializer function and correct indentation
            meta_analysis_json = json.dumps(meta_analysis, default=set_to_list_serializer, indent=2)
            with open(output_dir / "meta_analysis_report.json", 'w') as f:
                f.write(meta_analysis_json)
            enhanced_reports['meta_analysis'] = meta_analysis

            # Generate trend analysis if possible
            previous_runs_data = _load_previous_meta_analysis(output_dir.parent)
            if previous_runs_data:
                log.add(LogLevel.INFO, "Report Generator", f"Performing trend analysis using {len(previous_runs_data)} previous runs...")
                trend_analysis = report_generator.perform_trend_analysis(previous_runs_data)

                # Save trend analysis
                trend_analysis_json = json.dumps(trend_analysis, default=global_set_to_list)
                with open(output_dir / "trend_analysis_report.json", 'w') as f:
                    f.write(trend_analysis_json)

                enhanced_reports['trend_analysis'] = trend_analysis
            else:
                log.add(LogLevel.INFO, "Report Generator", "Insufficient data for trend analysis (need at least one previous run)")

            log.add(LogLevel.SUCCESS, "Report Generator", "Enhanced reports generated successfully")
        except Exception as e:
            log.add(LogLevel.ERROR, "Report Generator", f"Error generating enhanced reports: {e}")
            enhanced_reports['error'] = str(e)

    # Correct return statement indentation (align with the start of the 'if PatReportGenerator:' block)
    return {
        "summary": summary,
        "report_txt": txt_path.read_text() if txt_path.exists() else "", # Check existence
        "report_json": json_report if "JSON" in report_types else {},
        "visualization_path": str(viz_path) if viz_path and viz_path.exists() else None, # Check existence
        "log": log.get_entries(),
        "enhanced_reports": enhanced_reports
    }

# Helper function to convert set to list for JSON serialization
# Moved outside run_analysis to be accessible by _load_previous_meta_analysis if needed
# Renamed to avoid conflict with the inner function if it wasn't removed properly
def global_set_to_list(obj):
    if isinstance(obj, set):
        return list(obj)
    raise TypeError

def _load_previous_meta_analysis(output_base_dir: Path) -> List[Dict[str, Any]]:
    """Load meta-analysis data from previous runs to enable trend analysis.

    Args:
        output_base_dir: Base directory containing all PAT output runs

    Returns:
        List of meta-analysis data dictionaries, in chronological order
    """
    previous_runs = []

    # Find all run directories (exclude 'latest' symlink)
    run_dirs = [d for d in output_base_dir.iterdir()
                if d.is_dir() and d.name != 'latest']

    # Sort by directory name (timestamp format YYYY-MM-DD_HH-MM-SS)
    run_dirs.sort()

    # Load meta-analysis data from each run
    for run_dir in run_dirs:
        meta_file = run_dir / "meta_analysis_report.json"
        if meta_file.exists():
            try:
                with open(meta_file, 'r') as f:
                    meta_data = json.load(f)
                    previous_runs.append(meta_data)
            except Exception as e:
                print(f"Warning: Could not load meta-analysis from {run_dir}: {e}")

    return previous_runs

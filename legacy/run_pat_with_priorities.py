#!/usr/bin/env python3
"""
PAT Runner with High-Priority Extraction

This script runs the PAT tool and then extracts high-priority files from the output.
It combines the functionality of run_pat_purposeful.py and extract_high_priority.py.

Usage:
    python run_pat_with_priorities.py <project_path> [--top N] [--min-score SCORE]

Arguments:
    project_path: Path to the project to analyze
    --top N: Number of top files to extract (default: 10)
    --min-score: Minimum priority score to include a file (default: 5)
"""

import argparse
import os
import subprocess
import sys
import tempfile
import time
from pathlib import Path


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Run PAT and extract high-priority files")
    parser.add_argument("project_path", help="Path to the project to analyze")
    parser.add_argument("--top", type=int, default=10, help="Number of top files to extract (default: 10)")
    parser.add_argument("--min-score", type=float, default=5.0, help="Minimum priority score to include a file (default: 5)")
    return parser.parse_args()

def run_pat(project_path: str) -> Path:
    """Run the PAT tool and return the output directory."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()

    # Run the PAT tool
    pat_script = pat_dir / "run_pat_purposeful.py"
    cmd = [sys.executable, str(pat_script), project_path]

    print(f"Running PAT on {project_path}...")
    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode != 0:
        print(f"PAT analysis failed with return code {result.returncode}")
        print(f"Error output: {result.stderr}")
        sys.exit(1)

    # Find the latest output directory
    output_dir = pat_dir / "PAT_output" / "latest"
    if not output_dir.exists() or not output_dir.is_symlink():
        # Find the latest output directory by timestamp
        output_dirs = list(sorted((pat_dir / "PAT_output").glob("20*-*-*_*-*-*"), reverse=True))
        if not output_dirs:
            print("No PAT output directory found")
            sys.exit(1)
        output_dir = output_dirs[0]
    else:
        # Resolve the symlink
        output_dir = output_dir.resolve()

    print(f"PAT analysis completed. Output directory: {output_dir}")
    return output_dir

def extract_high_priority_files(pat_output_dir: Path, top_n: int, min_score: float):
    """Extract high-priority files from PAT output."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()

    # Run the extract_high_priority.py script
    extract_script = pat_dir / "extract_high_priority.py"
    output_dir = pat_output_dir / "high_priority"
    cmd = [
        sys.executable,
        str(extract_script),
        str(pat_output_dir),
        "--top", str(top_n),
        "--min-score", str(min_score),
        "--output-dir", str(output_dir)
    ]

    print(f"Extracting high-priority files...")
    result = subprocess.run(cmd, capture_output=True, text=True)

    if result.returncode != 0:
        print(f"High-priority extraction failed with return code {result.returncode}")
        print(f"Error output: {result.stderr}")
        sys.exit(1)

    # Check if any high-priority files were found
    summary_path = output_dir / "high_priority_summary.md"
    if summary_path.exists():
        with open(summary_path, 'r') as f:
            summary_content = f.read()
            file_count = 0
            if "Found " in summary_content:
                try:
                    file_count_str = summary_content.split("Found ")[1].split(" high-priority")[0]
                    file_count = int(file_count_str)
                except (IndexError, ValueError):
                    pass

            if file_count > 0:
                print(f"Found {file_count} high-priority files that need attention.")
            else:
                print("No high-priority files found that meet the criteria.")

    print(f"High-priority extraction completed. Output directory: {output_dir}")
    print(f"Summary report: {output_dir / 'high_priority_summary.md'}")
    return output_dir

def main():
    """Main function."""
    args = parse_args()

    # Run PAT
    pat_output_dir = run_pat(args.project_path)

    # Extract high-priority files
    high_priority_dir = extract_high_priority_files(pat_output_dir, args.top, args.min_score)

    print("\nAnalysis completed successfully!")
    print(f"PAT output directory: {pat_output_dir}")
    print(f"High-priority prompts: {high_priority_dir}")
    print(f"High-priority summary: {high_priority_dir / 'high_priority_summary.md'}")

    return 0

if __name__ == "__main__":
    sys.exit(main())

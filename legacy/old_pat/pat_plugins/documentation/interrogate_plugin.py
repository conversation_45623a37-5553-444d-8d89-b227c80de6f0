"""
File: interrogate_plugin.py
Purpose: Implements a PAT plugin for docstring coverage analysis.

This module provides the InterrogatePlugin class, which integrates the
interrogate tool for docstring coverage analysis into the PAT system.
"""

import re
import subprocess
from typing import Any, Dict, List

from pat_core.plugin import Plugin, PluginResult


class InterrogatePlugin(Plugin):
    """
    Plugin for docstring coverage analysis using interrogate.
    
    This plugin runs the interrogate tool on Python files to analyze
    docstring coverage and quality.
    """
    
    @property
    def name(self) -> str:
        return "interrogate"
    
    @property
    def description(self) -> str:
        return "Docstring coverage analysis using interrogate"
    
    @property
    def dependencies(self) -> List[str]:
        return ["interrogate"]
    
    async def analyze(self, 
                     files: List[str], 
                     context: Dict[str, Any] = None) -> PluginResult:
        """
        Run interrogate on the given files.
        
        Args:
            files: List of file paths to analyze
            context: Additional context information
            
        Returns:
            PluginResult containing the analysis results
        """
        context = context or {}
        
        # Filter Python files
        python_files = [f for f in files if f.endswith(".py")]
        
        if not python_files:
            return PluginResult(
                plugin_name=self.name,
                status="skipped",
                summary="No Python files to analyze"
            )
        
        self.logger.info(f"Running interrogate on {len(python_files)} Python files")
        
        # Build command
        cmd = ["interrogate", "-v", "--ignore-init-method"]
        
        # Add files to analyze
        cmd.extend(python_files)
        
        # Run command
        try:
            self.logger.debug(f"Running command: {' '.join(cmd)}")
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True,
                check=False
            )
            
            # Parse output
            if result.stdout:
                # Extract coverage percentage
                coverage = 0.0
                coverage_match = re.search(r'TOTAL\s+\d+\s+\d+\s+\d+\s+(\d+(?:\.\d+)?)%', result.stdout)
                if coverage_match:
                    coverage = float(coverage_match.group(1))
                
                # Extract detailed results
                issues = []
                file_pattern = re.compile(r'([^\s]+\.py)\s+\d+\s+\d+\s+\d+\s+(\d+(?:\.\d+)?)%')
                for match in file_pattern.finditer(result.stdout):
                    file_path = match.group(1)
                    file_coverage = float(match.group(2))
                    
                    if file_coverage < self.config.get("min_coverage", 70):
                        issues.append({
                            "file": file_path,
                            "coverage": file_coverage,
                            "message": f"Docstring coverage is {file_coverage}%, below the threshold of {self.config.get('min_coverage', 70)}%"
                        })
                
                # Calculate metrics
                metrics = {
                    "coverage": coverage,
                    "file_count": len(python_files),
                    "files_below_threshold": len(issues)
                }
                
                # Determine status
                status = "success"
                if coverage < self.config.get("min_coverage", 70):
                    status = "warning"
                if coverage < self.config.get("critical_coverage", 50):
                    status = "error"
                
                # Create summary
                summary = (
                    f"Docstring coverage is {coverage:.1f}%. "
                    f"{metrics['files_below_threshold']} out of {metrics['file_count']} "
                    f"files are below the threshold of {self.config.get('min_coverage', 70)}%."
                )
                
                self.logger.info(summary)
                
                return PluginResult(
                    plugin_name=self.name,
                    status=status,
                    issues=issues,
                    metrics=metrics,
                    summary=summary
                )
            
            self.logger.error(f"Interrogate failed with no output. Error: {result.stderr}")
            return PluginResult(
                plugin_name=self.name,
                status="error",
                summary=f"Interrogate failed with no output. Error: {result.stderr}"
            )
            
        except Exception as e:
            self.logger.error(f"Interrogate failed with error: {e}")
            return PluginResult(
                plugin_name=self.name,
                status="error",
                summary=f"Interrogate failed with error: {str(e)}"
            )

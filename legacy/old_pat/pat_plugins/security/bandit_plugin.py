"""
File: bandit_plugin.py
Purpose: Implements a PAT plugin for Bandit security analysis.

This module provides the BanditPlugin class, which integrates Bandit
security analysis into the PAT system.
"""

import json
import subprocess
import tempfile
from typing import Any, Dict, List

from pat_core.plugin import Plugin, PluginResult


class BanditPlugin(Plugin):
    """
    Plugin for Bandit security analysis.
    
    This plugin runs Bandit on Python files to identify security issues
    and vulnerabilities.
    """
    
    @property
    def name(self) -> str:
        return "bandit"
    
    @property
    def description(self) -> str:
        return "Security analysis using Bandit"
    
    @property
    def dependencies(self) -> List[str]:
        return ["bandit"]
    
    @property
    def is_cpu_bound(self) -> bool:
        return True
    
    async def analyze(self, 
                     files: List[str], 
                     context: Dict[str, Any] = None) -> PluginResult:
        """
        Run Bandit on the given files.
        
        Args:
            files: List of file paths to analyze
            context: Additional context information
            
        Returns:
            PluginResult containing the analysis results
        """
        context = context or {}
        
        # Filter Python files
        python_files = [f for f in files if f.endswith(".py")]
        
        if not python_files:
            return PluginResult(
                plugin_name=self.name,
                status="skipped",
                summary="No Python files to analyze"
            )
        
        self.logger.info(f"Running Bandit on {len(python_files)} Python files")
        
        # Create temporary file with list of files to analyze
        with tempfile.NamedTemporaryFile(mode="w", suffix=".txt") as temp:
            for file in python_files:
                temp.write(f"{file}\n")
            temp.flush()
            
            # Build command
            cmd = ["bandit", "-f", "json", "--recursive", "-l", temp.name]
            
            # Add configuration if provided
            if "config_file" in self.config:
                cmd.extend(["-c", self.config["config_file"]])
                
            # Add severity filter if provided
            if "severity" in self.config:
                cmd.extend(["-l", self.config["severity"]])
                
            # Add confidence filter if provided
            if "confidence" in self.config:
                cmd.extend(["-i", self.config["confidence"]])
                
            # Add excluded tests if provided
            if "skips" in self.config:
                cmd.extend(["-s", ",".join(self.config["skips"])])
                
            # Run command
            try:
                self.logger.debug(f"Running command: {' '.join(cmd)}")
                result = subprocess.run(
                    cmd, 
                    capture_output=True, 
                    text=True,
                    check=False
                )
                
                # Parse output
                if result.stdout:
                    try:
                        output = json.loads(result.stdout)
                        
                        # Extract issues
                        issues = []
                        for result in output.get("results", []):
                            issues.append({
                                "file": result.get("filename", ""),
                                "line": result.get("line_number", 0),
                                "message": result.get("issue_text", ""),
                                "severity": result.get("issue_severity", "MEDIUM"),
                                "confidence": result.get("issue_confidence", "MEDIUM"),
                                "test_id": result.get("test_id", ""),
                                "test_name": result.get("test_name", "")
                            })
                        
                        # Calculate metrics
                        metrics = {
                            "high_severity": len([i for i in issues if i["severity"] == "HIGH"]),
                            "medium_severity": len([i for i in issues if i["severity"] == "MEDIUM"]),
                            "low_severity": len([i for i in issues if i["severity"] == "LOW"]),
                            "file_count": output.get("metrics", {}).get("_totals", {}).get("loc", 0),
                            "skipped_tests": output.get("metrics", {}).get("_totals", {}).get("skipped_tests", 0)
                        }
                        
                        # Determine status
                        status = "success"
                        if metrics["high_severity"] > 0:
                            status = "error"
                        elif metrics["medium_severity"] > 0:
                            status = "warning"
                        
                        # Create summary
                        summary = (
                            f"Found {metrics['high_severity']} high, "
                            f"{metrics['medium_severity']} medium, and "
                            f"{metrics['low_severity']} low severity issues "
                            f"in {len(python_files)} files."
                        )
                        
                        self.logger.info(summary)
                        
                        return PluginResult(
                            plugin_name=self.name,
                            status=status,
                            issues=issues,
                            metrics=metrics,
                            summary=summary
                        )
                    except json.JSONDecodeError as e:
                        self.logger.error(f"Failed to parse Bandit output: {e}")
                        return PluginResult(
                            plugin_name=self.name,
                            status="error",
                            summary=f"Failed to parse Bandit output: {result.stdout}"
                        )
                
                self.logger.error(f"Bandit failed with no output. Error: {result.stderr}")
                return PluginResult(
                    plugin_name=self.name,
                    status="error",
                    summary=f"Bandit failed with no output. Error: {result.stderr}"
                )
                
            except Exception as e:
                self.logger.error(f"Bandit failed with error: {e}")
                return PluginResult(
                    plugin_name=self.name,
                    status="error",
                    summary=f"Bandit failed with error: {str(e)}"
                )

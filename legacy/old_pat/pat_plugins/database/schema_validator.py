"""
File: schema_validator.py
Purpose: Implements a PAT plugin for database schema validation.

This module provides the DatabaseSchemaValidator class, which validates
database schemas against expected structures defined in models.
"""

import importlib
import inspect
import logging
import os
import sys
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from pat_core.plugin import Plugin, PluginResult


class DatabaseSchemaValidator(Plugin):
    """
    Plugin for validating database schemas against expected structures.
    
    This plugin compares the actual database schema with the expected schema
    defined in model classes to identify discrepancies.
    """
    
    @property
    def name(self) -> str:
        return "db_schema_validator"
    
    @property
    def description(self) -> str:
        return "Validates database schemas against expected structures defined in models"
    
    async def analyze(self, 
                     files: List[str], 
                     context: Dict[str, Any] = None) -> PluginResult:
        """
        Validate database schemas against expected structures.
        
        Args:
            files: List of file paths to analyze (model files)
            context: Additional context information
            
        Returns:
            PluginResult containing the validation results
        """
        context = context or {}
        
        # Filter Python files that might contain models
        model_files = [f for f in files if f.endswith(".py") and ("model" in f.lower() or "schema" in f.lower())]
        
        if not model_files:
            return PluginResult(
                plugin_name=self.name,
                status="skipped",
                summary="No model files found to analyze"
            )
        
        self.logger.info(f"Analyzing {len(model_files)} potential model files")
        
        # Get database configuration
        db_config = self.config.get("db_config", {})
        if not db_config:
            self.logger.warning("No database configuration provided")
            return PluginResult(
                plugin_name=self.name,
                status="skipped",
                summary="No database configuration provided"
            )
        
        # Get expected schema from models
        try:
            expected_schema = self._get_expected_schema(model_files)
            self.logger.info(f"Extracted expected schema with {len(expected_schema.get('tables', {}))} tables")
        except Exception as e:
            self.logger.error(f"Error extracting expected schema: {e}")
            return PluginResult(
                plugin_name=self.name,
                status="error",
                summary=f"Error extracting expected schema: {str(e)}"
            )
        
        # Get actual schema from database
        try:
            actual_schema = self._get_actual_schema(db_config)
            self.logger.info(f"Extracted actual schema with {len(actual_schema.get('tables', {}))} tables")
        except Exception as e:
            self.logger.error(f"Error extracting actual schema: {e}")
            return PluginResult(
                plugin_name=self.name,
                status="error",
                summary=f"Error extracting actual schema: {str(e)}"
            )
        
        # Compare schemas
        differences = self._compare_schemas(expected_schema, actual_schema)
        
        # Create issues from differences
        issues = []
        for diff in differences:
            issue_type = diff.get("type", "unknown")
            if issue_type == "missing_table":
                issues.append({
                    "type": "missing_table",
                    "table": diff.get("table", ""),
                    "message": f"Missing table: {diff.get('table', '')}"
                })
            elif issue_type == "extra_table":
                issues.append({
                    "type": "extra_table",
                    "table": diff.get("table", ""),
                    "message": f"Unexpected table: {diff.get('table', '')}"
                })
            elif issue_type == "missing_column":
                issues.append({
                    "type": "missing_column",
                    "table": diff.get("table", ""),
                    "column": diff.get("column", ""),
                    "message": f"Missing column: {diff.get('table', '')}.{diff.get('column', '')}"
                })
            elif issue_type == "extra_column":
                issues.append({
                    "type": "extra_column",
                    "table": diff.get("table", ""),
                    "column": diff.get("column", ""),
                    "message": f"Unexpected column: {diff.get('table', '')}.{diff.get('column', '')}"
                })
            elif issue_type == "column_type_mismatch":
                issues.append({
                    "type": "column_type_mismatch",
                    "table": diff.get("table", ""),
                    "column": diff.get("column", ""),
                    "expected_type": diff.get("expected_type", ""),
                    "actual_type": diff.get("actual_type", ""),
                    "message": (
                        f"Column type mismatch: {diff.get('table', '')}.{diff.get('column', '')} "
                        f"(expected: {diff.get('expected_type', '')}, actual: {diff.get('actual_type', '')})"
                    )
                })
        
        # Calculate metrics
        metrics = {
            "total_differences": len(differences),
            "missing_tables": len([d for d in differences if d.get("type") == "missing_table"]),
            "extra_tables": len([d for d in differences if d.get("type") == "extra_table"]),
            "missing_columns": len([d for d in differences if d.get("type") == "missing_column"]),
            "extra_columns": len([d for d in differences if d.get("type") == "extra_column"]),
            "column_type_mismatches": len([d for d in differences if d.get("type") == "column_type_mismatch"]),
            "expected_tables": len(expected_schema.get("tables", {})),
            "actual_tables": len(actual_schema.get("tables", {}))
        }
        
        # Determine status
        status = "success"
        if metrics["missing_tables"] > 0 or metrics["missing_columns"] > 0 or metrics["column_type_mismatches"] > 0:
            status = "error"
        elif metrics["extra_tables"] > 0 or metrics["extra_columns"] > 0:
            status = "warning"
        
        # Create summary
        summary = (
            f"Found {metrics['total_differences']} schema differences: "
            f"{metrics['missing_tables']} missing tables, "
            f"{metrics['extra_tables']} extra tables, "
            f"{metrics['missing_columns']} missing columns, "
            f"{metrics['extra_columns']} extra columns, "
            f"{metrics['column_type_mismatches']} column type mismatches."
        )
        
        self.logger.info(summary)
        
        return PluginResult(
            plugin_name=self.name,
            status=status,
            issues=issues,
            metrics=metrics,
            summary=summary
        )
    
    def _get_expected_schema(self, model_files: List[str]) -> Dict[str, Any]:
        """
        Extract expected schema from model files.
        
        Args:
            model_files: List of model file paths
            
        Returns:
            Dictionary containing expected schema
        """
        # This is a simplified implementation
        # In a real implementation, this would parse model files and extract schema information
        
        expected_schema = {
            "tables": {}
        }
        
        # Add model files to Python path
        for file in model_files:
            module_dir = os.path.dirname(file)
            if module_dir not in sys.path:
                sys.path.insert(0, module_dir)
        
        # Import model modules
        for file in model_files:
            try:
                # Convert file path to module name
                module_name = os.path.basename(file).replace(".py", "")
                
                # Import module
                module = importlib.import_module(module_name)
                
                # Find model classes
                for name, obj in inspect.getmembers(module):
                    if inspect.isclass(obj) and hasattr(obj, "__tablename__"):
                        table_name = getattr(obj, "__tablename__")
                        columns = {}
                        
                        # Extract column information
                        for attr_name, attr_value in vars(obj).items():
                            if hasattr(attr_value, "type"):
                                columns[attr_name] = {
                                    "type": str(attr_value.type),
                                    "nullable": getattr(attr_value, "nullable", True),
                                    "primary_key": getattr(attr_value, "primary_key", False)
                                }
                        
                        # Add table to schema
                        expected_schema["tables"][table_name] = {
                            "columns": columns
                        }
            except Exception as e:
                self.logger.warning(f"Error importing model from {file}: {e}")
        
        return expected_schema
    
    def _get_actual_schema(self, db_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract actual schema from database.
        
        Args:
            db_config: Database configuration
            
        Returns:
            Dictionary containing actual schema
        """
        # This is a simplified implementation
        # In a real implementation, this would connect to the database and extract schema information
        
        # For demonstration purposes, we'll return a mock schema
        return {
            "tables": {
                "users": {
                    "columns": {
                        "id": {
                            "type": "INTEGER",
                            "nullable": False,
                            "primary_key": True
                        },
                        "username": {
                            "type": "VARCHAR",
                            "nullable": False,
                            "primary_key": False
                        },
                        "email": {
                            "type": "VARCHAR",
                            "nullable": False,
                            "primary_key": False
                        },
                        "created_at": {
                            "type": "TIMESTAMP",
                            "nullable": True,
                            "primary_key": False
                        }
                    }
                },
                "posts": {
                    "columns": {
                        "id": {
                            "type": "INTEGER",
                            "nullable": False,
                            "primary_key": True
                        },
                        "title": {
                            "type": "VARCHAR",
                            "nullable": False,
                            "primary_key": False
                        },
                        "content": {
                            "type": "TEXT",
                            "nullable": True,
                            "primary_key": False
                        },
                        "user_id": {
                            "type": "INTEGER",
                            "nullable": False,
                            "primary_key": False
                        },
                        "created_at": {
                            "type": "TIMESTAMP",
                            "nullable": True,
                            "primary_key": False
                        }
                    }
                }
            }
        }
    
    def _compare_schemas(self, 
                        expected: Dict[str, Any], 
                        actual: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Compare expected and actual schemas.
        
        Args:
            expected: Expected schema
            actual: Actual schema
            
        Returns:
            List of differences between schemas
        """
        differences = []
        
        expected_tables = expected.get("tables", {})
        actual_tables = actual.get("tables", {})
        
        # Check for missing tables
        for table_name in expected_tables:
            if table_name not in actual_tables:
                differences.append({
                    "type": "missing_table",
                    "table": table_name
                })
                
        # Check for extra tables
        for table_name in actual_tables:
            if table_name not in expected_tables:
                differences.append({
                    "type": "extra_table",
                    "table": table_name
                })
                
        # Check columns for each table
        for table_name in expected_tables:
            if table_name in actual_tables:
                expected_columns = expected_tables[table_name].get("columns", {})
                actual_columns = actual_tables[table_name].get("columns", {})
                
                # Check for missing columns
                for column_name in expected_columns:
                    if column_name not in actual_columns:
                        differences.append({
                            "type": "missing_column",
                            "table": table_name,
                            "column": column_name
                        })
                    else:
                        # Check column type
                        expected_type = expected_columns[column_name].get("type", "")
                        actual_type = actual_columns[column_name].get("type", "")
                        if not self._types_compatible(expected_type, actual_type):
                            differences.append({
                                "type": "column_type_mismatch",
                                "table": table_name,
                                "column": column_name,
                                "expected_type": expected_type,
                                "actual_type": actual_type
                            })
                
                # Check for extra columns
                for column_name in actual_columns:
                    if column_name not in expected_columns:
                        differences.append({
                            "type": "extra_column",
                            "table": table_name,
                            "column": column_name
                        })
        
        return differences
    
    def _types_compatible(self, expected_type: str, actual_type: str) -> bool:
        """
        Check if two column types are compatible.
        
        Args:
            expected_type: Expected column type
            actual_type: Actual column type
            
        Returns:
            True if types are compatible, False otherwise
        """
        # This is a simplified implementation
        # In a real implementation, this would check for type compatibility
        # considering database-specific type mappings
        
        # Convert types to uppercase for case-insensitive comparison
        expected_type = expected_type.upper()
        actual_type = actual_type.upper()
        
        # Exact match
        if expected_type == actual_type:
            return True
        
        # Check for compatible types
        if expected_type in ["VARCHAR", "CHAR", "TEXT"] and actual_type in ["VARCHAR", "CHAR", "TEXT"]:
            return True
        
        if expected_type in ["INTEGER", "BIGINT", "SMALLINT"] and actual_type in ["INTEGER", "BIGINT", "SMALLINT"]:
            return True
        
        if expected_type in ["FLOAT", "DOUBLE", "REAL"] and actual_type in ["FLOAT", "DOUBLE", "REAL"]:
            return True
        
        if expected_type in ["TIMESTAMP", "DATETIME"] and actual_type in ["TIMESTAMP", "DATETIME"]:
            return True
        
        return False

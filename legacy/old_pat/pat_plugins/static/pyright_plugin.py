"""
File: pyright_plugin.py
Purpose: Implements a PAT plugin for Pyright static type checking.

This module provides the PyrightPlugin class, which integrates Pyright
static type checking into the PAT system.
"""

import json
import subprocess
from typing import Any, Dict, List

from pat_core.plugin import Plugin, PluginResult


class PyrightPlugin(Plugin):
    """
    Plugin for Pyright static type checking.
    
    This plugin runs Pyright on Python files to perform static type checking
    and report type errors and warnings.
    """
    
    @property
    def name(self) -> str:
        return "pyright"
    
    @property
    def description(self) -> str:
        return "Static type checking using Pyright"
    
    @property
    def dependencies(self) -> List[str]:
        return ["pyright"]
    
    async def analyze(self, 
                     files: List[str], 
                     context: Dict[str, Any] = None) -> PluginResult:
        """
        Run Pyright on the given files.
        
        Args:
            files: List of file paths to analyze
            context: Additional context information
            
        Returns:
            PluginResult containing the analysis results
        """
        context = context or {}
        
        # Filter Python files
        python_files = [f for f in files if f.endswith(".py")]
        
        if not python_files:
            return PluginResult(
                plugin_name=self.name,
                status="skipped",
                summary="No Python files to analyze"
            )
        
        self.logger.info(f"Running Pyright on {len(python_files)} Python files")
        
        # Build command
        cmd = ["pyright", "--outputjson"]
        
        # Add configuration if provided
        if "config_file" in self.config:
            cmd.extend(["--project", self.config["config_file"]])
            
        # Add files to analyze
        cmd.extend(python_files)
        
        # Run command
        try:
            self.logger.debug(f"Running command: {' '.join(cmd)}")
            result = subprocess.run(
                cmd, 
                capture_output=True, 
                text=True,
                check=False
            )
            
            # Parse output
            if result.stdout:
                try:
                    output = json.loads(result.stdout)
                    
                    # Extract issues
                    issues = []
                    for diagnostic in output.get("diagnostics", []):
                        issues.append({
                            "file": diagnostic.get("file", ""),
                            "line": diagnostic.get("line", 0),
                            "column": diagnostic.get("column", 0),
                            "message": diagnostic.get("message", ""),
                            "severity": diagnostic.get("severity", "error")
                        })
                    
                    # Calculate metrics
                    metrics = {
                        "error_count": len([i for i in issues if i["severity"] == "error"]),
                        "warning_count": len([i for i in issues if i["severity"] == "warning"]),
                        "info_count": len([i for i in issues if i["severity"] == "information"]),
                        "file_count": len(python_files)
                    }
                    
                    # Determine status
                    status = "success"
                    if metrics["error_count"] > 0:
                        status = "error"
                    elif metrics["warning_count"] > 0:
                        status = "warning"
                    
                    # Create summary
                    summary = (
                        f"Found {metrics['error_count']} errors, "
                        f"{metrics['warning_count']} warnings, and "
                        f"{metrics['info_count']} informational messages "
                        f"in {metrics['file_count']} files."
                    )
                    
                    self.logger.info(summary)
                    
                    return PluginResult(
                        plugin_name=self.name,
                        status=status,
                        issues=issues,
                        metrics=metrics,
                        summary=summary
                    )
                except json.JSONDecodeError as e:
                    self.logger.error(f"Failed to parse Pyright output: {e}")
                    return PluginResult(
                        plugin_name=self.name,
                        status="error",
                        summary=f"Failed to parse Pyright output: {e}"
                    )
            
            self.logger.error(f"Pyright failed with no output. Error: {result.stderr}")
            return PluginResult(
                plugin_name=self.name,
                status="error",
                summary=f"Pyright failed with no output. Error: {result.stderr}"
            )
            
        except Exception as e:
            self.logger.error(f"Pyright failed with error: {e}")
            return PluginResult(
                plugin_name=self.name,
                status="error",
                summary=f"Pyright failed with error: {str(e)}"
            )

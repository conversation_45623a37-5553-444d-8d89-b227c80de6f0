"""
CAW Paradigm Analyzer Plugin

This plugin analyzes code for adherence to the Contextual Adaptive Wave (CAW) 
programming paradigm patterns.
"""

import ast
import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

from pat_plugins.core import PluginInterface


class CAWPattern:
    """Represents a CAW paradigm pattern to detect in code."""
    
    def __init__(self, name: str, description: str, keywords: List[str], 
                 ast_patterns: Optional[List[str]] = None):
        """
        Initialize a CAW pattern.
        
        Args:
            name: Name of the pattern
            description: Description of the pattern
            keywords: Keywords to look for in code
            ast_patterns: AST patterns to match (if any)
        """
        self.name = name
        self.description = description
        self.keywords = keywords
        self.ast_patterns = ast_patterns or []


class CAWAnalyzerPlugin(PluginInterface):
    """
    Analyzes Python code for CAW paradigm patterns.
    
    This plugin scans Python files for evidence of CAW paradigm implementation,
    including contextual computation, duality, adaptive computational fidelity,
    and more.
    """
    
    @property
    def name(self) -> str:
        return "caw_analyzer"
    
    @property
    def version(self) -> str:
        return "0.1.0"
    
    @property
    def description(self) -> str:
        return "Analyzes code for Contextual Adaptive Wave paradigm patterns"
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the plugin with configuration.
        
        Args:
            config: Plugin configuration
        """
        self.logger = logging.getLogger("pat.plugins.caw_analyzer")
        self.patterns = self._create_patterns()
        self.config = config
        self.min_confidence = config.get("min_confidence", 0.5)
        self.ignored_dirs = config.get("ignored_dirs", ["venv", "__pycache__", ".git"])
    
    def _create_patterns(self) -> List[CAWPattern]:
        """
        Create the list of CAW patterns to detect.
        
        Returns:
            List of CAW patterns
        """
        patterns = [
            CAWPattern(
                name="contextual_computation",
                description="Code that explicitly uses context to modify behavior",
                keywords=["context", "adaptive", "contextual", "environment", 
                          "adaptation", "situational", "dynamic context"],
            ),
            CAWPattern(
                name="duality",
                description="Implementation of wave-particle duality in information",
                keywords=["duality", "wave", "particle", "potential", "actual", 
                          "dual representation", "wavefront", "multiple states"],
            ),
            CAWPattern(
                name="adaptive_computational_fidelity",
                description="Dynamic adjustment of computational resources based on context",
                keywords=["adaptive fidelity", "computational fidelity", "acf", 
                          "resource adjustment", "quality metrics", "dynamic resources"],
            ),
            CAWPattern(
                name="caw_actors",
                description="Implementation of CAW actors",
                keywords=["caw actor", "reactive entity", "actor", "choreography", 
                          "concurrent actor", "message handling"],
            ),
            CAWPattern(
                name="caw_choreographies",
                description="Coordinated interaction protocols for CAW actors",
                keywords=["choreography", "protocol", "interaction", "coordination", 
                          "message pattern", "message exchange"],
            ),
            CAWPattern(
                name="caw_capabilities",
                description="Context-aware access control via capability tokens",
                keywords=["capability", "token", "access control", "auth", 
                          "permission", "unforgeable", "capability context"],
            ),
            CAWPattern(
                name="caw_effects",
                description="Explicit management of operational consequences",
                keywords=["effect", "side effect", "effect system", "operational", 
                          "consequence", "composable effect"],
            ),
        ]
        
        return patterns
    
    def analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Analyze a single file for CAW paradigm patterns.
        
        Args:
            file_path: Path to the file to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        if not file_path.exists() or not file_path.is_file():
            self.logger.warning(f"File not found or not a file: {file_path}")
            return {"error": "File not found or not a file"}
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # Create base result structure
            result = {
                "file_path": str(file_path),
                "patterns_found": [],
                "overall_caw_score": 0.0,
                "pattern_details": {},
                "suggests_improvements": False,
                "improvement_suggestions": []
            }
            
            # Perform keyword analysis
            keyword_results = self._analyze_keywords(content)
            
            # Perform AST analysis if possible
            ast_results = {}
            try:
                tree = ast.parse(content)
                ast_results = self._analyze_ast(tree)
            except SyntaxError:
                self.logger.warning(f"Syntax error in file: {file_path}")
                result["syntax_error"] = True
            
            # Combine results
            for pattern in self.patterns:
                confidence = keyword_results.get(pattern.name, 0.0)
                ast_confidence = ast_results.get(pattern.name, 0.0)
                
                # Weight AST findings more heavily
                combined_confidence = (confidence + ast_confidence * 2) / 3 if ast_confidence > 0 else confidence
                
                if combined_confidence > self.min_confidence:
                    result["patterns_found"].append(pattern.name)
                    result["pattern_details"][pattern.name] = {
                        "confidence": combined_confidence,
                        "description": pattern.description,
                        "matches": keyword_results.get(f"{pattern.name}_matches", [])
                    }
            
            # Calculate overall CAW score
            if result["patterns_found"]:
                result["overall_caw_score"] = sum(
                    result["pattern_details"][p]["confidence"] for p in result["patterns_found"]
                ) / len(self.patterns)
            
            # Generate improvement suggestions
            result.update(self._generate_suggestions(result))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error analyzing file {file_path}: {str(e)}")
            return {"error": str(e)}
    
    def _analyze_keywords(self, content: str) -> Dict[str, Any]:
        """
        Analyze file content for CAW pattern keywords.
        
        Args:
            content: The file content to analyze
            
        Returns:
            Dictionary of pattern names and confidence scores
        """
        results = {}
        content_lower = content.lower()
        
        for pattern in self.patterns:
            matches = []
            match_count = 0
            
            for keyword in pattern.keywords:
                keyword_lower = keyword.lower()
                count = content_lower.count(keyword_lower)
                
                if count > 0:
                    matches.append((keyword, count))
                    match_count += count
            
            # Calculate confidence based on matches
            if matches:
                # More keywords matched = higher confidence
                unique_keyword_ratio = len(matches) / len(pattern.keywords)
                
                # More matches overall = higher confidence, but with diminishing returns
                import math
                match_score = min(1.0, math.log(match_count + 1) / 4)
                
                confidence = (unique_keyword_ratio * 0.7) + (match_score * 0.3)
                
                results[pattern.name] = confidence
                results[f"{pattern.name}_matches"] = matches
            else:
                results[pattern.name] = 0.0
                results[f"{pattern.name}_matches"] = []
        
        return results
    
    def _analyze_ast(self, tree: ast.AST) -> Dict[str, float]:
        """
        Analyze AST for CAW pattern implementation.
        
        Args:
            tree: AST of the file
            
        Returns:
            Dictionary of pattern names and confidence scores
        """
        results = {}
        
        # Create an AST visitor to find relevant patterns
        class CAWVisitor(ast.NodeVisitor):
            def __init__(self):
                self.context_params = 0
                self.context_attrs = 0
                self.effect_functions = 0
                self.capability_usage = 0
                self.duality_instances = 0
                self.choreography_patterns = 0
                self.adaptive_patterns = 0
                
                # Track classes and functions to analyze structure
                self.classes = []
                self.functions = []
            
            def visit_ClassDef(self, node):
                self.classes.append(node)
                
                # Check for Actor pattern
                if "Actor" in node.name or any("actor" in base.id.lower() for base in node.bases if hasattr(base, 'id')):
                    self.choreography_patterns += 1
                
                # Check for capability pattern
                if "Capability" in node.name or "Token" in node.name:
                    self.capability_usage += 1
                
                self.generic_visit(node)
            
            def visit_FunctionDef(self, node):
                self.functions.append(node)
                
                # Check for context parameters
                for arg in node.args.args:
                    if hasattr(arg, 'arg') and 'context' in arg.arg.lower():
                        self.context_params += 1
                
                # Check for effect pattern
                if 'effect' in node.name.lower() or any('effect' in d.id.lower() for d in node.decorator_list if hasattr(d, 'id')):
                    self.effect_functions += 1
                
                self.generic_visit(node)
            
            def visit_Attribute(self, node):
                # Check for context attributes
                if hasattr(node, 'attr') and 'context' in node.attr.lower():
                    self.context_attrs += 1
                
                # Check for capability context
                if hasattr(node, 'attr') and ('capability' in node.attr.lower() or 'token' in node.attr.lower()):
                    self.capability_usage += 1
                
                self.generic_visit(node)
                
            def visit_Call(self, node):
                # Check for adaptive fidelity calls
                if hasattr(node, 'func') and hasattr(node.func, 'attr'):
                    if 'adapt' in node.func.attr.lower() or 'fidelity' in node.func.attr.lower():
                        self.adaptive_patterns += 1
                
                self.generic_visit(node)
        
        visitor = CAWVisitor()
        visitor.visit(tree)
        
        # Convert visitor findings to pattern confidences
        
        # Contextual computation confidence
        context_evidence = visitor.context_params + visitor.context_attrs
        if context_evidence > 0:
            results["contextual_computation"] = min(1.0, context_evidence / 5)
        
        # Effect system confidence
        if visitor.effect_functions > 0:
            results["caw_effects"] = min(1.0, visitor.effect_functions / 3)
        
        # Capability confidence
        if visitor.capability_usage > 0:
            results["caw_capabilities"] = min(1.0, visitor.capability_usage / 3)
        
        # Choreography/Actor confidence
        if visitor.choreography_patterns > 0:
            results["caw_actors"] = min(1.0, visitor.choreography_patterns / 2)
            results["caw_choreographies"] = min(1.0, visitor.choreography_patterns / 3)
        
        # Adaptive fidelity confidence
        if visitor.adaptive_patterns > 0:
            results["adaptive_computational_fidelity"] = min(1.0, visitor.adaptive_patterns / 2)
        
        return results
    
    def _generate_suggestions(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate improvement suggestions based on analysis.
        
        Args:
            result: Analysis result
            
        Returns:
            Dictionary with improvement suggestions
        """
        suggestions = []
        missing_patterns = [p.name for p in self.patterns if p.name not in result["patterns_found"]]
        low_confidence_patterns = [
            p for p in result["patterns_found"] 
            if result["pattern_details"][p]["confidence"] < 0.7
        ]
        
        # Make suggestions for missing patterns
        if "contextual_computation" in missing_patterns:
            suggestions.append({
                "pattern": "contextual_computation",
                "suggestion": "Consider adding explicit context objects that affect behavior based on environmental or situational factors."
            })
        
        if "duality" in missing_patterns:
            suggestions.append({
                "pattern": "duality",
                "suggestion": "Implement information entities with both wave (potential) and particle (actual) aspects for more flexible computation."
            })
        
        if "adaptive_computational_fidelity" in missing_patterns:
            suggestions.append({
                "pattern": "adaptive_computational_fidelity",
                "suggestion": "Add mechanisms to dynamically adjust computational resources based on context importance."
            })
        
        if "caw_capabilities" in missing_patterns:
            suggestions.append({
                "pattern": "caw_capabilities",
                "suggestion": "Use capability tokens for access control instead of global permission checks."
            })
        
        if "caw_effects" in missing_patterns:
            suggestions.append({
                "pattern": "caw_effects",
                "suggestion": "Make operational effects explicit in your code to improve composability and reasoning."
            })
        
        # Make suggestions for weak pattern implementations
        for pattern in low_confidence_patterns:
            pattern_obj = next((p for p in self.patterns if p.name == pattern), None)
            if pattern_obj:
                suggestions.append({
                    "pattern": pattern,
                    "suggestion": f"Strengthen your implementation of {pattern_obj.name} - {pattern_obj.description}"
                })
        
        return {
            "suggests_improvements": len(suggestions) > 0,
            "improvement_suggestions": suggestions
        }
    
    def analyze_project(self, project_path: Path) -> Dict[str, Any]:
        """
        Analyze an entire project for CAW paradigm patterns.
        
        Args:
            project_path: Path to the project to analyze
            
        Returns:
            Dictionary containing analysis results
        """
        if not project_path.exists() or not project_path.is_dir():
            self.logger.error(f"Project directory not found: {project_path}")
            return {"error": "Project directory not found"}
        
        results = {
            "project_path": str(project_path),
            "files_analyzed": 0,
            "files_with_caw": 0,
            "overall_caw_score": 0.0,
            "pattern_summary": {p.name: 0 for p in self.patterns},
            "top_caw_files": [],
            "needs_improvement": [],
            "file_results": {}
        }
        
        total_score = 0.0
        python_files = []
        
        # Find all Python files in the project
        for root, dirs, files in os.walk(project_path):
            # Skip ignored directories
            dirs[:] = [d for d in dirs if d not in self.ignored_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(os.path.join(root, file)))
        
        # Analyze each file
        for file_path in python_files:
            file_result = self.analyze_file(file_path)
            
            if "error" not in file_result:
                results["files_analyzed"] += 1
                
                if file_result["patterns_found"]:
                    results["files_with_caw"] += 1
                    
                    # Update pattern summary
                    for pattern in file_result["patterns_found"]:
                        results["pattern_summary"][pattern] += 1
                    
                    # Track total score for average calculation
                    total_score += file_result["overall_caw_score"]
                    
                    # Track top CAW files
                    results["top_caw_files"].append({
                        "file_path": file_result["file_path"],
                        "score": file_result["overall_caw_score"],
                        "patterns": file_result["patterns_found"]
                    })
                
                if file_result["suggests_improvements"]:
                    results["needs_improvement"].append({
                        "file_path": file_result["file_path"],
                        "suggestions": file_result["improvement_suggestions"]
                    })
                
                # Store individual file results
                rel_path = os.path.relpath(file_result["file_path"], str(project_path))
                results["file_results"][rel_path] = file_result
        
        # Calculate overall score
        if results["files_with_caw"] > 0:
            results["overall_caw_score"] = total_score / results["files_with_caw"]
        
        # Sort top CAW files by score
        results["top_caw_files"] = sorted(
            results["top_caw_files"], 
            key=lambda x: x["score"], 
            reverse=True
        )[:10]  # Top 10
        
        return results 
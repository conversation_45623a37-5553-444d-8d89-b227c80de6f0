"""
Codebase Graph Analyzer Plugin

This plugin builds a comprehensive dependency graph of the codebase, mapping all
functions, classes, modules, and their relationships (calls, imports, inheritance,
composition). The resulting graph enables advanced architectural analysis, cross-
plugin checks, and interactive visualizations.

Related Files:
    - All analyzer plugins (for integration)
    - core/ and shared/ modules (for architectural boundaries)

Dependencies:
    - pat_plugins.core.PluginInterface
    - Python standard library (ast, inspect, os, etc.)
    - Optionally: networkx, graphviz, or other graph libraries

"""

import ast
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from pat_plugins.core import PluginInterface

# Optionally import networkx for graph representation
try:
    import networkx as nx
except ImportError:
    nx = None  # Fallback to custom graph structure if needed

class CodebaseGraphAnalyzerPlugin(PluginInterface):
    """
    Builds a dependency graph of the codebase for advanced analysis.

    This plugin parses all Python files, extracts functions, classes, modules,
    and their relationships (calls, imports, inheritance, composition), and
    builds an in-memory graph. The graph can be exported for visualization or
    queried by other analyzers.
    """

    @property
    def name(self) -> str:
        """Returns the plugin name."""
        return "codebase_graph_analyzer"

    @property
    def version(self) -> str:
        """Returns the plugin version."""
        return "0.1.0"

    @property
    def description(self) -> str:
        """Returns the plugin description."""
        return "Builds a dependency graph of the codebase for advanced analysis."

    def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the plugin with configuration.

        Args:
            config: Plugin configuration
        """
        self.config = config
        self.ignored_dirs = config.get("ignored_dirs", ["venv", "__pycache__", ".git"])
        self.graph = nx.DiGraph() if nx else None  # Directed graph for dependencies
        # TODO: Add more configuration options as needed

    def analyze_project(self, project_path: Path, context=None) -> Dict[str, Any]:
        """
        Analyze the entire project and build the dependency graph.

        Args:
            project_path: Path to the project to analyze
            context: SharedAnalysisContext for cross-plugin data sharing (optional)

        Returns:
            Dictionary containing analysis results and the graph structure
        """
        errors = []
        node_count = 0
        edge_count = 0
        if not project_path.exists() or not project_path.is_dir():
            errors.append(f"Project directory not found: {project_path}")
            return {"error": "Project directory not found", "errors": errors}

        # Collect all Python files
        python_files = []
        for root, dirs, files in os.walk(project_path):
            dirs[:] = [d for d in dirs if d not in self.ignored_dirs]
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(os.path.join(root, file)))

        # Initialize graph if not already
        if self.graph is None and nx:
            self.graph = nx.DiGraph()
        elif self.graph is None:
            self.graph = {}  # fallback: dict of nodes

        # Helper: add node to graph
        def add_node(node_id, node_type, **attrs):
            nonlocal node_count
            if nx and isinstance(self.graph, nx.DiGraph):
                self.graph.add_node(node_id, type=node_type, **attrs)
            else:
                self.graph[node_id] = {"type": node_type, **attrs}
            node_count += 1

        # Helper: add edge to graph
        def add_edge(src, dst, edge_type):
            nonlocal edge_count
            if nx and isinstance(self.graph, nx.DiGraph):
                self.graph.add_edge(src, dst, type=edge_type)
            else:
                self.graph.setdefault(src, {}).setdefault("edges", []).append({"to": dst, "type": edge_type})
            edge_count += 1

        # Parse each file and extract nodes/edges
        for py_file in python_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                try:
                    tree = ast.parse(content, filename=str(py_file))
                except SyntaxError as e:
                    errors.append(f"Syntax error in {py_file}: {e}")
                    continue
                module_id = str(py_file.relative_to(project_path)).replace(os.sep, '.')[:-3]  # remove .py
                add_node(module_id, "module", file=str(py_file))
                # --- Build import/alias table for this module ---
                import_table = {}  # alias or name -> fully qualified module or object
                star_imports = []
                for node in ast.iter_child_nodes(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            imported_mod = alias.name
                            import_alias = alias.asname or imported_mod
                            import_table[import_alias] = imported_mod
                            add_edge(module_id, imported_mod, "imports")
                            # Optionally, store alias
                            if nx and isinstance(self.graph, nx.DiGraph):
                                self.graph[module_id][imported_mod]["alias"] = alias.asname
                            else:
                                for edge in self.graph[module_id]["edges"]:
                                    if edge["to"] == imported_mod and edge["type"] == "imports":
                                        edge["alias"] = alias.asname
                    elif isinstance(node, ast.ImportFrom):
                        imported_mod = node.module if node.module else ""
                        for alias in node.names:
                            import_name = alias.name
                            import_alias = alias.asname or import_name
                            if import_name == "*":
                                star_imports.append(imported_mod)
                                errors.append(f"Star import detected in {py_file}: from {imported_mod} import * (ambiguous)")
                                continue
                            import_id = f"{imported_mod}.{import_name}" if imported_mod else import_name
                            import_table[import_alias] = import_id
                            add_edge(module_id, import_id, "imports")
                            if nx and isinstance(self.graph, nx.DiGraph):
                                self.graph[module_id][import_id]["alias"] = alias.asname
                            else:
                                for edge in self.graph[module_id]["edges"]:
                                    if edge["to"] == import_id and edge["type"] == "imports":
                                        edge["alias"] = alias.asname
                # --- End import/alias table ---
                class_defs = {}
                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef):
                        class_id = f"{module_id}.{node.name}"
                        base_names = [
                            base.id if isinstance(base, ast.Name) else getattr(base, 'attr', None)
                            for base in node.bases
                        ]
                        add_node(
                            class_id,
                            "class",
                            file=str(py_file),
                            lineno=node.lineno,
                            docstring=ast.get_docstring(node),
                            bases=base_names,
                        )
                        class_defs[node.name] = class_id
                        # Inheritance edges
                        for base in node.bases:
                            if isinstance(base, ast.Name):
                                base_id = f"{module_id}.{base.id}"
                                add_edge(class_id, base_id, "inherits")
                            elif isinstance(base, ast.Attribute):
                                base_id = f"{module_id}.{base.attr}"
                                add_edge(class_id, base_id, "inherits")
                        # Detect attribute-based composition (class attributes that are instances of other classes)
                        for stmt in node.body:
                            if isinstance(stmt, ast.Assign):
                                for target in stmt.targets:
                                    if isinstance(target, ast.Name):
                                        # Try to detect instantiation: attr = SomeClass(...)
                                        if isinstance(stmt.value, ast.Call) and isinstance(stmt.value.func, ast.Name):
                                            composed_class = stmt.value.func.id
                                            composed_id = f"{module_id}.{composed_class}"
                                            add_edge(class_id, composed_id, "composes")
                    elif isinstance(node, ast.FunctionDef):
                        func_id = f"{module_id}.{node.name}"
                        arg_names = [arg.arg for arg in node.args.args]
                        decorators = [
                            d.id if isinstance(d, ast.Name) else getattr(d, 'attr', None)
                            for d in node.decorator_list
                        ]
                        is_method = False
                        parent_class = None
                        for parent in ast.iter_child_nodes(tree):
                            if isinstance(parent, ast.ClassDef):
                                for item in parent.body:
                                    if item is node:
                                        is_method = True
                                        parent_class = parent.name
                        add_node(
                            func_id,
                            "method" if is_method else "function",
                            file=str(py_file),
                            lineno=node.lineno,
                            docstring=ast.get_docstring(node),
                            args=arg_names,
                            decorators=decorators,
                            parent_class=parent_class,
                        )
                        # Function/method calls (improved: handle method/attribute calls and cross-module resolution)
                        for child in ast.walk(node):
                            if isinstance(child, ast.Call):
                                callee_id = None
                                call_lineno = getattr(child, 'lineno', None)
                                # --- Dynamic import detection ---
                                if isinstance(child.func, ast.Name) and child.func.id in {"__import__", "eval", "exec"}:
                                    errors.append(f"Dynamic import or code execution detected in {py_file} at line {call_lineno}: {child.func.id}")
                                    add_edge(func_id, child.func.id, "dynamic_import")
                                    continue
                                if isinstance(child.func, ast.Attribute):
                                    if hasattr(child.func, 'value') and isinstance(child.func.value, ast.Name):
                                        if child.func.value.id == "importlib" and child.func.attr == "import_module":
                                            errors.append(f"Dynamic importlib.import_module detected in {py_file} at line {call_lineno}")
                                            add_edge(func_id, "importlib.import_module", "dynamic_import")
                                            continue
                                # --- End dynamic import detection ---
                                # --- Cross-module call resolution ---
                                if isinstance(child.func, ast.Name):
                                    callee = child.func.id
                                    # Try to resolve via import table
                                    if callee in import_table:
                                        callee_id = f"{import_table[callee]}"
                                    else:
                                        callee_id = f"{module_id}.{callee}"
                                elif isinstance(child.func, ast.Attribute):
                                    attr = child.func.attr
                                    value = child.func.value
                                    if isinstance(value, ast.Name):
                                        # If value is an import alias, resolve
                                        if value.id in import_table:
                                            callee_id = f"{import_table[value.id]}.{attr}"
                                        # If value is 'self', try to resolve to parent class
                                        elif value.id == 'self' and parent_class:
                                            callee_id = f"{module_id}.{parent_class}.{attr}"
                                        else:
                                            callee_id = f"{module_id}.{value.id}.{attr}"
                                    else:
                                        callee_id = f"{module_id}.{attr}"
                                if callee_id:
                                    add_edge(func_id, callee_id, "calls")
                                    # Optionally, store call site line number
                                    if nx and isinstance(self.graph, nx.DiGraph):
                                        self.graph[func_id][callee_id]["call_lineno"] = call_lineno
                                    else:
                                        for edge in self.graph[func_id]["edges"]:
                                            if edge["to"] == callee_id and edge["type"] == "calls":
                                                edge["call_lineno"] = call_lineno
                        # TODO: Further improve type inference for obj.method() calls
            except Exception as e:
                errors.append(f"Error processing {py_file}: {e}")
                continue

        # Store the graph in the shared context if provided
        if context is not None:
            context.set_graph(self.graph)

        # Prepare a serializable graph summary (basic: node/edge counts, errors, node list)
        summary = {
            "nodes": node_count,
            "edges": edge_count,
            "errors": errors,
            "graph": None
        }
        if nx and isinstance(self.graph, nx.DiGraph):
            # Only include node/edge lists for now (full export via export_graph)
            summary["graph"] = {
                "nodes": list(self.graph.nodes(data=True)),
                "edges": list(self.graph.edges(data=True))
            }
        else:
            summary["graph"] = self.graph
        return summary

    def export_graph(self, format: str = "json") -> Any:
        """
        Export the dependency graph in the specified format.

        Args:
            format: Output format ("json", "graphml", "dot", etc.)

        Returns:
            Serialized graph in the requested format
        """
        if self.graph is None:
            raise ValueError("Graph has not been built yet.")
        if format == "json":
            if nx and isinstance(self.graph, nx.DiGraph):
                return {
                    "nodes": list(self.graph.nodes(data=True)),
                    "edges": list(self.graph.edges(data=True))
                }
            else:
                return self.graph
        elif format == "graphml":
            if nx and isinstance(self.graph, nx.DiGraph):
                import io
                output = io.StringIO()
                nx.write_graphml(self.graph, output)
                return output.getvalue()
            else:
                raise NotImplementedError("GraphML export requires networkx.")
        elif format == "dot":
            if nx and isinstance(self.graph, nx.DiGraph):
                import io
                output = io.StringIO()
                nx.drawing.nx_pydot.write_dot(self.graph, output)
                return output.getvalue()
            else:
                raise NotImplementedError("DOT export requires networkx.")
        else:
            raise ValueError(f"Unsupported export format: {format}")

    def get_node(self, node_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve information about a specific node (function, class, module).

        Args:
            node_id: Unique identifier for the node

        Returns:
            Node information dictionary, or None if not found
        """
        if self.graph is None:
            return None
        if nx and isinstance(self.graph, nx.DiGraph):
            if node_id in self.graph:
                return self.graph.nodes[node_id]
            return None
        else:
            return self.graph.get(node_id)

    def query_graph(self, query: Dict[str, Any]) -> Any:
        """
        Query the graph for specific relationships or patterns.

        Args:
            query: Query parameters (e.g., {"type": "callers", "target": "module.func"})

        Returns:
            Query result (list, subgraph, etc.)
        """
        if self.graph is None:
            return None
        qtype = query.get("type")
        target = query.get("target")
        if not qtype or not target:
            raise ValueError("Query must specify 'type' and 'target'.")
        results = []
        if nx and isinstance(self.graph, nx.DiGraph):
            if qtype == "callers":
                # Find all nodes with an outgoing 'calls' edge to target
                for src, dst, data in self.graph.edges(data=True):
                    if dst == target and data.get("type") == "calls":
                        results.append(src)
            elif qtype == "imports":
                # Find all nodes with an outgoing 'imports' edge to target
                for src, dst, data in self.graph.edges(data=True):
                    if dst == target and data.get("type") == "imports":
                        results.append(src)
            elif qtype == "subclasses":
                # Find all nodes with an outgoing 'inherits' edge to target
                for src, dst, data in self.graph.edges(data=True):
                    if dst == target and data.get("type") == "inherits":
                        results.append(src)
            else:
                raise NotImplementedError(f"Query type '{qtype}' not supported.")
        else:
            # Fallback for dict-based graph
            for src, node in self.graph.items():
                for edge in node.get("edges", []):
                    if edge["to"] == target and edge["type"] == qtype.rstrip('s'):
                        results.append(src)
        return results

    # Additional helper methods for parsing, node/edge creation, etc.
    # TODO: Implement AST visitors and graph construction utilities 
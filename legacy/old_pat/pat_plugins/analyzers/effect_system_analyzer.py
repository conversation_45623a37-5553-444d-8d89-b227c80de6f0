"""
Effect System Analyzer Plugin

This plugin analyzes code for explicit effect system usage, effect annotations,
and effectful function patterns, as required by the Person Suit architecture.
It helps ensure that side effects are managed, tracked, and composed
according to the project's effect system standards.

Related Files:
    - folded_mind_analyzer.py: For pathway/effect integration checks
    - memory_analyzer.py: For effectful memory operations
    - core/ and shared/ modules: For effect system definitions

Dependencies:
    - pat_plugins.core.PluginInterface
    - Python standard library (ast, re, etc.)

"""

import ast
import logging
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional

from pat_plugins.core import PluginInterface


class EffectPattern:
    """Represents a pattern in the effect system architecture."""
    
    def __init__(self, name: str, description: str, keywords: List[str]):
        """
        Initialize an effect pattern.
        
        Args:
            name: Name of the pattern
            description: Description of the pattern
            keywords: Keywords to look for in code
        """
        self.name = name
        self.description = description
        self.keywords = keywords

class EffectSystemAnalyzerPlugin(PluginInterface):
    """
    Analyzes Python code for effect system patterns.
    
    This plugin scans Python files for evidence of explicit effect management,
    effect annotations, effect handlers, and architectural adherence to the
    Person Suit effect system paradigm.
    """
    
    @property
    def name(self) -> str:
        """Returns the plugin name."""
        return "effect_system_analyzer"
    
    @property
    def version(self) -> str:
        """Returns the plugin version."""
        return "0.1.0"
    
    @property
    def description(self) -> str:
        """Returns the plugin description."""
        return "Analyzes code for effect system patterns and issues"
    
    def initialize(self, config: Dict[str, Any]) -> None:
        """
        Initialize the plugin with configuration.
        
        Args:
            config: Plugin configuration
        """
        self.logger = logging.getLogger("pat.plugins.effect_system_analyzer")
        self.patterns = self._create_patterns()
        self.config = config
        self.min_confidence = config.get("min_confidence", 0.5)
        self.ignored_dirs = config.get("ignored_dirs", ["venv", "__pycache__", ".git"])
        # TODO: Add more configuration options as needed

    def _create_patterns(self) -> List[EffectPattern]:
        """
        Create the list of effect system patterns to detect.
        
        Returns:
            List of effect patterns
        """
        # TODO: Expand with more effect system patterns as needed
        patterns = [
            EffectPattern(
                name="effect_annotation",
                description="Explicit effect annotation or type",
                keywords=["@effect", "Effect", "effectful", "effects:"]
            ),
            EffectPattern(
                name="effect_handler",
                description="Effect handler or interpreter",
                keywords=["handle_effect", "effect_handler", "run_effects", "EffectHandler"]
            ),
            EffectPattern(
                name="side_effect_tracking",
                description="Explicit side effect tracking or declaration",
                keywords=["side_effect", "side effects", "track_effects", "effect_log"]
            ),
            EffectPattern(
                name="effect_composition",
                description="Composition of effects or effectful functions",
                keywords=["compose_effects", "effect_composer", "compose", "chain_effects"]
            ),
        ]
        return patterns

    def analyze_file(self, file_path: Path) -> Dict[str, Any]:
        """
        Analyze a single file for effect system patterns.

        Args:
            file_path: Path to the file to analyze

        Returns:
            Dictionary containing analysis results
        """
        if not file_path.exists() or not file_path.is_file():
            self.logger.warning(f"File not found or not a file: {file_path}")
            return {"error": "File not found or not a file"}

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            result = {
                "file_path": str(file_path),
                "patterns_found": [],
                "overall_score": 0.0,
                "pattern_details": {},
                "syntax_error": False,
                "suggests_improvements": False,
                "improvement_suggestions": []
            }

            # Keyword-based detection
            keyword_results = {}
            content_lower = content.lower()
            for pattern in self.patterns:
                matches = []
                match_count = 0
                for keyword in pattern.keywords:
                    keyword_lower = keyword.lower()
                    count = content_lower.count(keyword_lower)
                    if count > 0:
                        matches.append((keyword, count))
                        match_count += count
                if matches:
                    unique_keyword_ratio = len(matches) / len(pattern.keywords)
                    import math
                    match_score = min(1.0, math.log(match_count + 1) / 4)
                    confidence = (unique_keyword_ratio * 0.7) + (match_score * 0.3)
                    keyword_results[pattern.name] = confidence
                    keyword_results[f"{pattern.name}_matches"] = matches
                else:
                    keyword_results[pattern.name] = 0.0
                    keyword_results[f"{pattern.name}_matches"] = []

            # AST-based detection
            ast_results = {}
            try:
                tree = ast.parse(content)
                ast_results = self._analyze_ast(tree)
            except SyntaxError:
                self.logger.warning(f"Syntax error in file: {file_path}")
                result["syntax_error"] = True
                tree = None

            # Combine results
            for pattern in self.patterns:
                confidence = keyword_results.get(pattern.name, 0.0)
                ast_confidence = ast_results.get(pattern.name, 0.0)
                combined_confidence = (confidence + ast_confidence) / 2
                if combined_confidence > self.min_confidence:
                    result["patterns_found"].append(pattern.name)
                    result["pattern_details"][pattern.name] = {
                        "confidence": combined_confidence,
                        "description": pattern.description,
                        "matches": keyword_results.get(f"{pattern.name}_matches", [])
                    }

            if result["patterns_found"]:
                result["overall_score"] = sum(
                    result["pattern_details"][p]["confidence"] for p in result["patterns_found"]
                ) / len(self.patterns)

            # Add improvement suggestions
            suggestions = self._generate_suggestions(content, tree, result)
            result["suggests_improvements"] = bool(suggestions)
            result["improvement_suggestions"] = suggestions

            return result
        except Exception as e:
            self.logger.error(f"Error analyzing file {file_path}: {str(e)}")
            return {"error": str(e)}

    def _analyze_ast(self, tree: ast.AST) -> Dict[str, float]:
        """
        Analyze AST for effect system patterns.

        Args:
            tree: AST of the file

        Returns:
            Dictionary of pattern names and confidence scores
        """
        results = {}
        # Visitor for effect system constructs
        class EffectVisitor(ast.NodeVisitor):
            def __init__(self):
                self.effect_annotation = 0
                self.effect_handler = 0
                self.side_effect_tracking = 0
                self.effect_composition = 0

            def visit_FunctionDef(self, node):
                # Detect decorators like @effect
                for decorator in node.decorator_list:
                    if isinstance(decorator, ast.Name) and decorator.id.lower() == "effect":
                        self.effect_annotation += 1
                    elif isinstance(decorator, ast.Attribute) and "effect" in decorator.attr.lower():
                        self.effect_annotation += 1
                # Detect type hints mentioning Effect
                if node.returns and hasattr(node.returns, 'id') and 'effect' in str(node.returns.id).lower():
                    self.effect_annotation += 1
                # Detect effect handler functions
                if "handle" in node.name.lower() and "effect" in node.name.lower():
                    self.effect_handler += 1
                if "compose" in node.name.lower() and "effect" in node.name.lower():
                    self.effect_composition += 1
                self.generic_visit(node)

            def visit_ClassDef(self, node):
                # Detect handler/interpreter classes
                if "effect" in node.name.lower() and ("handler" in node.name.lower() or "interpreter" in node.name.lower()):
                    self.effect_handler += 1
                self.generic_visit(node)

            def visit_Call(self, node):
                # Detect calls to effect composition or tracking
                if isinstance(node.func, ast.Name):
                    if "compose" in node.func.id.lower() and "effect" in node.func.id.lower():
                        self.effect_composition += 1
                    if "track" in node.func.id.lower() and "effect" in node.func.id.lower():
                        self.side_effect_tracking += 1
                elif isinstance(node.func, ast.Attribute):
                    if "compose" in node.func.attr.lower() and "effect" in node.func.attr.lower():
                        self.effect_composition += 1
                    if "track" in node.func.attr.lower() and "effect" in node.func.attr.lower():
                        self.side_effect_tracking += 1
                self.generic_visit(node)

        visitor = EffectVisitor()
        visitor.visit(tree)
        # Normalize to [0, 1] confidence
        if visitor.effect_annotation > 0:
            results["effect_annotation"] = min(1.0, visitor.effect_annotation / 3)
        if visitor.effect_handler > 0:
            results["effect_handler"] = min(1.0, visitor.effect_handler / 2)
        if visitor.side_effect_tracking > 0:
            results["side_effect_tracking"] = min(1.0, visitor.side_effect_tracking / 2)
        if visitor.effect_composition > 0:
            results["effect_composition"] = min(1.0, visitor.effect_composition / 2)
        return results

    def _generate_suggestions(self, content: str, tree: Optional[ast.AST], result: Dict[str, Any]) -> list:
        """
        Generate improvement suggestions based on analysis results.

        Args:
            content: File content
            tree: AST of the file (or None if syntax error)
            result: Analysis result so far

        Returns:
            List of suggestion dicts
        """
        suggestions = []
        patterns_found = set(result.get("patterns_found", []))
        # 1. Flag files with function definitions but no effect annotations or tracking
        has_functions = False
        if tree is not None:
            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    has_functions = True
                    break
        if has_functions and not ("effect_annotation" in patterns_found or "side_effect_tracking" in patterns_found):
            suggestions.append({
                "issue": "missing_effect_annotation_or_tracking",
                "suggestion": "This file defines functions but lacks explicit effect annotations or side effect tracking. Consider using @effect decorators or effect tracking mechanisms."
            })
        # 2. Suggest using effect handlers if effectful functions are present but handlers are missing
        if ("effect_annotation" in patterns_found or "side_effect_tracking" in patterns_found) and "effect_handler" not in patterns_found:
            suggestions.append({
                "issue": "missing_effect_handler",
                "suggestion": "Effectful functions detected but no effect handler found. Consider implementing or using an effect handler/interpreter."
            })
        # 3. Warn about possible implicit side effects (I/O, mutation) if no effect system usage is detected
        io_keywords = ["open(", "write(", "read(", "print(", "send(", "recv(", "socket.", "requests.", "subprocess.", "os.system", "shutil.", "delete(", "remove(", "update(", "insert(", "commit(", "rollback("]
        mutation_keywords = ["=", "+=", "-=", "*=", "/=", "%=", "append(", "extend(", "pop(", "del "]
        effect_system_used = any(p in patterns_found for p in ["effect_annotation", "side_effect_tracking", "effect_handler", "effect_composition"])
        if not effect_system_used:
            found_io = any(kw in content for kw in io_keywords)
            found_mut = any(kw in content for kw in mutation_keywords)
            if found_io or found_mut:
                suggestions.append({
                    "issue": "implicit_side_effects",
                    "suggestion": "Possible I/O or state mutation detected without explicit effect system usage. Consider making side effects explicit and managed via the effect system."
                })
        return suggestions

    def analyze_project(self, project_path: Path, context=None) -> Dict[str, Any]:
        """
        Analyze an entire project for effect system patterns.

        Args:
            project_path: Path to the project to analyze
            context: SharedAnalysisContext for cross-plugin data sharing (optional)

        Returns:
            Dictionary containing analysis results
        """
        if not project_path.exists() or not project_path.is_dir():
            self.logger.error(f"Project directory not found: {project_path}")
            return {"error": "Project directory not found"}

        results = {
            "project_path": str(project_path),
            "files_analyzed": 0,
            "pattern_summary": {p.name: 0 for p in self.patterns},
            "top_files": [],
            "needs_improvement": [],
            "file_results": {}
        }
        python_files = []
        for root, dirs, files in os.walk(project_path):
            dirs[:] = [d for d in dirs if d not in self.ignored_dirs]
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(os.path.join(root, file)))

        # --- Cross-plugin integration: access the codebase graph if available ---
        graph = None
        if context is not None:
            graph = context.get_graph()
            # TODO: Use the graph for cross-cutting checks (e.g., trace effect propagation, boundary checks)

        for file_path in python_files:
            file_result = self.analyze_file(file_path)
            if "error" not in file_result:
                results["files_analyzed"] += 1
                for pattern in file_result["patterns_found"]:
                    results["pattern_summary"][pattern] += 1
                if file_result["patterns_found"]:
                    results["top_files"].append({
                        "file_path": file_result["file_path"],
                        "score": file_result["overall_score"],
                        "patterns": file_result["patterns_found"]
                    })
                if file_result["suggests_improvements"]:
                    results["needs_improvement"].append({
                        "file_path": file_result["file_path"],
                        "suggestions": file_result["improvement_suggestions"]
                    })
                rel_path = os.path.relpath(file_result["file_path"], str(project_path))
                results["file_results"][rel_path] = file_result
        results["top_files"] = sorted(
            results["top_files"],
            key=lambda x: x["score"],
            reverse=True
        )[:10]
        return results 
"""
Parser functions for various tool outputs.

This module contains functions to parse the output from various analysis tools
such as Ruff, Bandit, Radon, Pylint, Vulture, and Pyright.
"""

import json
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional


def parse_ruff_output(stdout: str, project_root: Optional[Path] = None) -> List[Dict[str, Any]]:
    """Parse Ruff linter output (JSON format)."""
    issues = []
    try:
        # Try to parse JSON from Ruff output
        ruff_issues = json.loads(stdout)
        for issue in ruff_issues:
            abs_path = issue.get("filename")
            if abs_path and project_root:
                rel_path = os.path.relpath(abs_path, str(project_root))
                issues.append({
                    'tool': 'Ruff',
                    'code': issue.get('code'),
                    'message': issue.get('message'),
                    'line': issue.get('location', {}).get('row'),
                    'severity': map_severity(issue.get('code', ''), 'warning')
                })
    except json.JSONDecodeError:
        # Fallback for non-JSON format (line-by-line text output)
        # Format: /path/to/file.py:line:col: code message
        pattern = re.compile(r"^(.*?):(\d+):(\d+): ([A-Z]\d+) (.*)$", re.MULTILINE)
        for match in pattern.finditer(stdout):
            abs_path, line, col, code, message = match.groups()
            if abs_path and project_root:
                rel_path = os.path.relpath(abs_path, str(project_root))
                issues.append({
                    'tool': 'Ruff',
                    'code': code,
                    'message': message.strip(),
                    'line': int(line) if line else None,
                    'severity': map_severity(code, 'warning')
                })
    return issues
    
def parse_bandit_output(stdout: str, project_root: Optional[Path] = None) -> List[Dict[str, Any]]:
    """Parse Bandit security tool output (JSON format)."""
    issues = []
    try:
        # Try to parse JSON from Bandit output
        bandit_data = json.loads(stdout)
        for result in bandit_data.get('results', []):
            abs_path = result.get('filename')
            if abs_path and project_root:
                rel_path = os.path.relpath(abs_path, str(project_root))
                issues.append({
                    'tool': 'Bandit',
                    'code': f"B{result.get('test_id', '000')}",
                    'message': result.get('issue_text', 'Security issue'),
                    'line': result.get('line_number'),
                    'severity': result.get('issue_severity', 'medium')
                })
    except json.JSONDecodeError:
        # Fallback for non-JSON format (line-by-line text output)
        # Format: /path/to/file.py:line: [severity][B123] message
        pattern = re.compile(r"^(.*?):(\d+): \[(low|medium|high)\]\[([^\]]+)\] (.*)$", re.MULTILINE)
        for match in pattern.finditer(stdout):
            abs_path, line, severity, code, message = match.groups()
            if abs_path and project_root:
                rel_path = os.path.relpath(abs_path, str(project_root))
                issues.append({
                    'tool': 'Bandit',
                    'code': code,
                    'message': message.strip(),
                    'line': int(line) if line else None,
                    'severity': severity
                })
    return issues
    
def parse_radon_output(stdout: str, project_root: Optional[Path] = None) -> Dict[str, List[Dict[str, Any]]]:
    """Parse Radon complexity tool output (JSON format)."""
    complexity_data = {}
    try:
        # Try to parse JSON from Radon output
        radon_data = json.loads(stdout)
        for abs_path, complexities in radon_data.items():
            if abs_path and project_root:
                rel_path = os.path.relpath(abs_path, str(project_root))
                complexity_data[rel_path] = [
                    {
                        'name': item.get('name', 'unknown'),
                        'type': item.get('type', 'unknown'),
                        'complexity': item.get('complexity', 0),
                        'line': item.get('lineno', 0)
                    }
                    for item in complexities
                ]
    except json.JSONDecodeError:
        # Fallback for non-JSON format (line-by-line text output)
        # Format: /path/to/file.py
        #   funcname - X
        #   classname - Y
        current_file = None
        complexity_list = []
        
        for line in stdout.splitlines():
            line = line.strip()
            if not line:
                continue
                
            # Check if line is a file path
            if '/' in line or '\\' in line and not line.startswith(' '):
                if current_file and complexity_list:
                    # Store previous file's data before moving to new file
                    if project_root:
                        rel_path = os.path.relpath(current_file, str(project_root))
                        complexity_data[rel_path] = complexity_list
                
                current_file = line
                complexity_list = []
            elif current_file and ' - ' in line:
                # This looks like "name - complexity" line
                parts = line.strip().split(' - ')
                if len(parts) == 2:
                    name = parts[0].strip()
                    try:
                        complexity_value = int(parts[1].strip())
                        complexity_list.append({
                            'name': name,
                            'type': 'function' if name[0].islower() else 'class',
                            'complexity': complexity_value,
                            'line': 0  # Can't determine without more information
                        })
                    except ValueError:
                        pass
        
        # Don't forget to add the last file processed
        if current_file and complexity_list:
            if project_root:
                rel_path = os.path.relpath(current_file, str(project_root))
                complexity_data[rel_path] = complexity_list
            
    return complexity_data

def map_severity(code: str, default_severity: str = 'warning') -> str:
    """Map issue code to severity level."""
    # Ruff / Flake8 codes
    if code.startswith('E'):
        return 'error'
    elif code.startswith('W'):
        return 'warning'
    elif code.startswith('F'):
        return 'error'  # Flake8 fatal errors
    elif code.startswith('C'):
        return 'convention'
    elif code.startswith('I'):
        return 'info'
    elif code.startswith('S'):
        return 'security'  # Security issues are high severity
    
    # Bandit codes
    if code.startswith('B'):
        # Map Bandit issue levels based on ID
        high_severity_bandit = ['B102', 'B103', 'B301', 'B324', 'B602', 'B605']
        medium_severity_bandit = ['B101', 'B104', 'B201', 'B501', 'B506']
        
        if code in high_severity_bandit:
            return 'high'
        elif code in medium_severity_bandit:
            return 'medium'
        else:
            return 'low'
    
    return default_severity

def parse_pylint_output(stdout: str, project_root: Optional[Path] = None) -> List[Dict[str, Any]]:
    """Parse Pylint output (JSON format or text)."""
    issues = []
    try:
        # Try to parse JSON format
        pylint_data = json.loads(stdout)
        for item in pylint_data:
            abs_path = item.get('path')
            if abs_path and project_root:
                rel_path = os.path.relpath(abs_path, str(project_root))
                issues.append({
                    'tool': 'Pylint',
                    'code': item.get('symbol', item.get('message-id', 'unknown')),
                    'message': item.get('message', 'Unknown issue'),
                    'line': item.get('line', 0),
                    'severity': map_pylint_severity(item.get('type', 'warning')),
                    'rel_path': rel_path
                })
    except json.JSONDecodeError:
        # Fallback for text format
        # Format: path/to/file.py:line: [C0103(message-id), func] Message
        pattern = re.compile(r"^(.*?):(\d+)(?::\d+)?: \[([A-Z]\d+)\(([^)]+)\)(?:, [^]]+)?\] (.*)$", re.MULTILINE)
        for match in pattern.finditer(stdout):
            abs_path, line, code, msg_id, message = match.groups()
            if abs_path and project_root:
                rel_path = os.path.relpath(abs_path, str(project_root))
                
                # Determine severity from code
                severity = map_pylint_severity(code[0])  # First char is type (C/W/E/F)
                
                issues.append({
                    'tool': 'Pylint',
                    'code': f"{code}({msg_id})",
                    'message': message.strip(),
                    'line': int(line) if line else 0,
                    'severity': severity,
                    'rel_path': rel_path
                })
    return issues
    
def map_pylint_severity(type_code: str) -> str:
    """Map Pylint message type to severity level."""
    severity_map = {
        'C': 'convention',  # Convention (coding standard)
        'R': 'refactor',    # Refactor (improvement)
        'W': 'warning',     # Warning
        'E': 'error',       # Error
        'F': 'fatal'        # Fatal error
    }
    return severity_map.get(type_code, 'warning')

def parse_vulture_output(stdout: str, project_root: Optional[Path] = None) -> List[Dict[str, Any]]:
    """Parse Vulture dead code detector output (text format)."""
    issues = []
    
    # Format: path/to/file.py:line: unused function/variable/etc 'name' (confidence: X%)
    pattern = re.compile(r"^(.*?):(\d+): (unused \w+) '([^']+)' \((\d+)% confidence\)$", re.MULTILINE)
    
    for match in pattern.finditer(stdout):
        abs_path, line, issue_type, name, confidence = match.groups()
        if abs_path and project_root:
            rel_path = os.path.relpath(abs_path, str(project_root))
            
            # Confidence as integer
            try:
                confidence_int = int(confidence)
            except ValueError:
                confidence_int = 50  # Default to medium confidence
            
            # Map confidence to severity
            if confidence_int >= 90:
                severity = 'high'
            elif confidence_int >= 60:
                severity = 'medium'
            else:
                severity = 'low'
            
            issues.append({
                'tool': 'Vulture',
                'code': 'dead-code',
                'message': f"{issue_type} '{name}'",
                'line': int(line) if line else 0,
                'severity': severity,
                'confidence': confidence_int,
                'rel_path': rel_path
            })
    
    return issues

def parse_pyright_output(stdout: str, project_root: Optional[Path] = None) -> List[Dict[str, Any]]:
    """Parse Pyright type checking output (JSON format)."""
    issues = []
    
    try:
        # Try to parse JSON format
        pyright_data = json.loads(stdout)
        for diagnostic in pyright_data.get('diagnostics', []):
            file_path = diagnostic.get('file')
            if file_path and project_root:
                try:
                    rel_path = os.path.relpath(file_path, str(project_root))
                    
                    # Map severity (1 = error, 2 = warning, 3 = information, 4 = hint)
                    severity_map = {1: 'error', 2: 'warning', 3: 'info', 4: 'hint'}
                    severity = severity_map.get(diagnostic.get('severity', 2), 'warning')
                    
                    issues.append({
                        'tool': 'Pyright',
                        'code': diagnostic.get('rule', 'type-error'),
                        'message': diagnostic.get('message', 'Unknown issue'),
                        'line': diagnostic.get('range', {}).get('start', {}).get('line', 0) + 1,  # Convert to 1-based
                        'severity': severity,
                        'rel_path': rel_path
                    })
                except Exception as e:
                    print(f"Error processing Pyright diagnostic: {e}")
    except json.JSONDecodeError:
        # Pyright can also output text format for diagnostics
        # Format: /path/to/file.py:line:col - error: message
        pattern = re.compile(r"^(.*?):(\d+)(?::(\d+))? - (error|warning|information|hint): (.*)$", re.MULTILINE)
        
        for match in pattern.finditer(stdout):
            abs_path, line, col, severity, message = match.groups()
            if abs_path and project_root:
                rel_path = os.path.relpath(abs_path, str(project_root))
                
                issues.append({
                    'tool': 'Pyright',
                    'code': 'type-error',  # Default code for text format
                    'message': message.strip(),
                    'line': int(line) if line else 0,
                    'severity': severity,
                    'rel_path': rel_path
                })
                
    return issues

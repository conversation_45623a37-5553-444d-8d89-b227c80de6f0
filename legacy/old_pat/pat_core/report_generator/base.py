"""
Base module for the PatReportGenerator class.

This module contains the main PatReportGenerator class that orchestrates
the report generation process.
"""

import json
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

from .parsers import (
    parse_ruff_output,
    parse_bandit_output,
    parse_radon_output,
    parse_pylint_output,
    parse_vulture_output,
    parse_pyright_output,
    map_severity,
    map_pylint_severity
)
from .metrics import calculate_technical_debt_hours
from .formatters import (
    format_summary_report,
    format_detailed_report,
    format_issue_report,
    format_meta_system_report
)
from .utils import get_file_length, get_import_count


class PatReportGenerator:
    """Generates enhanced reports from PAT analysis data."""

    def __init__(self, analysis_output_dir: str, project_path: str):
        """
        Initializes the report generator.

        Args:
            analysis_output_dir: Path to the PAT output directory (e.g., 'PAT_output/latest').
            project_path: Absolute path to the root of the analyzed project.
        """
        self.output_dir = Path(analysis_output_dir)
        self.project_root = Path(project_path) # Store the project path
        self.raw_data: Dict[str, Any] = {}
        self.processed_data: Dict[str, Any] = {} # Structure to hold processed info per file

        # Load raw data
        self._load_raw_data()

    def _load_raw_data(self):
        """Loads the primary JSON report file."""
        json_report_path = self.output_dir / "pat_report.json"

        if json_report_path.exists():
            try:
                with open(json_report_path, 'r') as f:
                    self.raw_data = json.load(f)
                print(f"Successfully loaded raw data from {json_report_path}")
                # Also confirm project root
                if not self.project_root or not self.project_root.is_dir():
                     print(f"Warning: Project root path '{self.project_root}' is not a valid directory.")
                     self.project_root = None # Invalidate if path is bad

            except json.JSONDecodeError:
                print(f"Error: Could not decode JSON from {json_report_path}")
            except Exception as e:
                print(f"Error loading {json_report_path}: {e}")
        else:
            print(f"Warning: Main report file not found at {json_report_path}")

    def process_data(self):
        """
        Process the raw data into a structured format for report generation.
        This is the main entry point for data processing.
        """
        # Process tool outputs
        self._process_tool_outputs()
        
        # Calculate metrics
        self._calculate_metrics()
        
        # Identify problematic files
        self._identify_problematic_files()
        
        # Process meta-system data if available
        self._process_meta_system_data()
        
        return self.processed_data

    def _process_tool_outputs(self):
        """Process outputs from various analysis tools."""
        # Implementation will be added in future updates
        pass

    def _calculate_metrics(self):
        """Calculate various metrics from the processed data."""
        # Implementation will be added in future updates
        pass

    def _identify_problematic_files(self):
        """Identify files with issues that need attention."""
        # Implementation will be added in future updates
        pass

    def _process_meta_system_data(self):
        """Process meta-system separation data if available."""
        # Implementation will be added in future updates
        pass

    def generate_reports(self):
        """
        Generate all reports based on the processed data.
        This is the main entry point for report generation.
        """
        # Ensure data is processed
        if not self.processed_data:
            self.process_data()
            
        # Generate summary report
        summary_report = format_summary_report(self.processed_data)
        
        # Generate detailed report
        detailed_report = format_detailed_report(self.processed_data)
        
        # Generate issue report
        issue_report = format_issue_report(self.processed_data)
        
        # Generate meta-system report if data is available
        meta_system_report = None
        if 'meta_system' in self.processed_data:
            meta_system_report = format_meta_system_report(self.processed_data['meta_system'])
            
        return {
            'summary': summary_report,
            'detailed': detailed_report,
            'issues': issue_report,
            'meta_system': meta_system_report
        }

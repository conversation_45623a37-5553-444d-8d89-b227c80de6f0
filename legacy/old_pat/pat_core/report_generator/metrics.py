"""
Metrics calculation functions for the report generator.

This module contains functions to calculate various metrics from the processed data,
including technical debt, code quality, and complexity metrics.
"""

from collections import Counter, defaultdict
from typing import Any, Dict, List, Optional, Tuple

# Constants for scoring or thresholds (can be adjusted)
COMPLEXITY_WEIGHT = 0.3
ISSUES_WEIGHT = 0.5
LENGTH_WEIGHT = 0.1
IMPORTS_WEIGHT = 0.1

HIGH_COMPLEXITY_THRESHOLD = 15
MANY_ISSUES_THRESHOLD = 10
LONG_FILE_THRESHOLD = 700  # Lines
MANY_IMPORTS_THRESHOLD = 20


def calculate_technical_debt_hours(processed_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate technical debt with a more sophisticated model taking into account:
    - Issue severity and type
    - Code complexity
    - File size and structure
    - Code duplication (estimated from issue patterns)
    
    Args:
        processed_data: Dictionary containing processed file data
        
    Returns:
        A dictionary with technical debt metrics
    """
    # Configure time estimates based on issue types (in hours)
    issue_time_estimates = {
        # By severity
        'fatal': 4.0,      # Fatal errors - highest priority, most time-consuming
        'error': 2.0,      # Errors - high priority
        'security': 3.0,    # Security issues - high priority
        'warning': 1.0,     # Warnings - medium priority
        'convention': 0.5,  # Convention issues - low priority
        'refactor': 1.5,    # Refactoring issues - medium priority
        'style': 0.25,      # Style issues - lowest priority
        'type-error': 1.5,  # Type errors - medium-high priority
        'info': 0.25,       # Informational - low priority
        
        # By specific code patterns
        'unused-import': 0.1,   # Quick fix
        'unused-variable': 0.2,  # Easy to fix
        'dead-code': 0.5,        # Need to verify and remove
        'duplicate-code': 1.0    # Requires refactoring
    }
    
    # Default time per issue if not matched above
    default_time = 0.5  # hours
    
    # Initialize total debt and counters
    total_debt_hours = 0
    issue_count_by_type = defaultdict(int)
    issue_debt_by_type = defaultdict(float)
    
    # Process each file
    for file_path, data in processed_data.items():
        file_issues = data.get('issues', [])
        file_complexity = data.get('complexity', 0) or 0
        file_length = data.get('length', 0)
        
        # Base debt from issues
        for issue in file_issues:
            severity = issue.get('severity', 'warning')
            code = issue.get('code', 'unknown')
            
            # Determine time estimate based on severity and code
            if code.lower() in issue_time_estimates:
                time_estimate = issue_time_estimates[code.lower()]
            elif severity.lower() in issue_time_estimates:
                time_estimate = issue_time_estimates[severity.lower()]
            else:
                time_estimate = default_time
            
            # Add to total and counters
            total_debt_hours += time_estimate
            issue_type = f"{issue.get('tool', 'Unknown')}:{severity}"
            issue_count_by_type[issue_type] += 1
            issue_debt_by_type[issue_type] += time_estimate
        
        # Add debt for complex files
        if file_complexity > HIGH_COMPLEXITY_THRESHOLD:
            # Add debt based on how much the file exceeds the threshold
            # More complex files take exponentially more time to understand and fix
            complexity_factor = (file_complexity - HIGH_COMPLEXITY_THRESHOLD) / 10
            complexity_debt = 0.5 * (1 + complexity_factor) ** 2  # Exponential growth
            total_debt_hours += complexity_debt
            issue_debt_by_type['complexity'] += complexity_debt
            issue_count_by_type['complexity'] += 1
        
        # Add debt for large files
        if file_length > LONG_FILE_THRESHOLD:
            # Add debt based on how much the file exceeds the threshold
            size_factor = (file_length - LONG_FILE_THRESHOLD) / 500
            size_debt = 0.5 * (1 + size_factor)  # Linear growth
            total_debt_hours += size_debt
            issue_debt_by_type['file_size'] += size_debt
            issue_count_by_type['file_size'] += 1
    
    # Calculate debt by category
    debt_by_category = {
        'issues': sum(issue_debt_by_type[k] for k in issue_debt_by_type if k not in ['complexity', 'file_size']),
        'complexity': issue_debt_by_type['complexity'],
        'file_size': issue_debt_by_type['file_size']
    }
    
    # Calculate debt by severity
    debt_by_severity = defaultdict(float)
    for issue_type, debt in issue_debt_by_type.items():
        if ':' in issue_type:
            severity = issue_type.split(':')[1]
            debt_by_severity[severity] += debt
    
    # Calculate estimated time to fix
    # Assume team size and productivity
    team_size = 2  # developers
    hours_per_day = 6  # productive hours per day
    days_to_fix = total_debt_hours / (team_size * hours_per_day)
    
    return {
        'total_debt_hours': total_debt_hours,
        'issue_count_by_type': dict(issue_count_by_type),
        'issue_debt_by_type': {k: round(v, 2) for k, v in issue_debt_by_type.items()},
        'debt_by_category': {k: round(v, 2) for k, v in debt_by_category.items()},
        'debt_by_severity': {k: round(v, 2) for k, v in debt_by_severity.items()},
        'days_to_fix': round(days_to_fix, 1),
        'team_size': team_size
    }


def calculate_code_quality_score(processed_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Calculate a code quality score based on various metrics.
    
    Args:
        processed_data: Dictionary containing processed file data
        
    Returns:
        A dictionary with code quality metrics
    """
    # Initialize counters
    total_files = len(processed_data)
    if total_files == 0:
        return {
            'score': 0,
            'grade': 'F',
            'metrics': {}
        }
    
    total_issues = 0
    total_complexity = 0
    total_lines = 0
    
    # Count issues by severity
    issues_by_severity = defaultdict(int)
    
    # Process each file
    for file_path, data in processed_data.items():
        file_issues = data.get('issues', [])
        file_complexity = data.get('complexity', 0) or 0
        file_length = data.get('length', 0)
        
        total_issues += len(file_issues)
        total_complexity += file_complexity
        total_lines += file_length
        
        # Count issues by severity
        for issue in file_issues:
            severity = issue.get('severity', 'warning')
            issues_by_severity[severity] += 1
    
    # Calculate average metrics
    avg_issues_per_file = total_issues / total_files if total_files > 0 else 0
    avg_complexity = total_complexity / total_files if total_files > 0 else 0
    avg_lines = total_lines / total_files if total_files > 0 else 0
    
    # Calculate score components
    # Lower is better for all these metrics
    complexity_score = max(0, 10 - avg_complexity)
    issues_score = max(0, 10 - avg_issues_per_file * 2)
    size_score = max(0, 10 - avg_lines / 100)
    
    # Calculate weighted score
    weighted_score = (
        complexity_score * COMPLEXITY_WEIGHT +
        issues_score * ISSUES_WEIGHT +
        size_score * LENGTH_WEIGHT
    ) / (COMPLEXITY_WEIGHT + ISSUES_WEIGHT + LENGTH_WEIGHT)
    
    # Determine grade
    grade = 'A'
    if weighted_score < 3:
        grade = 'F'
    elif weighted_score < 5:
        grade = 'D'
    elif weighted_score < 7:
        grade = 'C'
    elif weighted_score < 9:
        grade = 'B'
    
    return {
        'score': round(weighted_score, 1),
        'grade': grade,
        'metrics': {
            'total_files': total_files,
            'total_issues': total_issues,
            'total_complexity': total_complexity,
            'total_lines': total_lines,
            'avg_issues_per_file': round(avg_issues_per_file, 2),
            'avg_complexity': round(avg_complexity, 2),
            'avg_lines': round(avg_lines, 2),
            'issues_by_severity': dict(issues_by_severity)
        }
    }


def identify_problematic_files(processed_data: Dict[str, Any]) -> List[Dict[str, Any]]:
    """
    Identify the most problematic files based on various metrics.
    
    Args:
        processed_data: Dictionary containing processed file data
        
    Returns:
        A list of problematic files with their metrics
    """
    problematic_files = []
    
    for file_path, data in processed_data.items():
        file_issues = data.get('issues', [])
        file_complexity = data.get('complexity', 0) or 0
        file_length = data.get('length', 0)
        
        # Calculate a problem score
        # Higher score means more problematic
        issue_score = len(file_issues) * ISSUES_WEIGHT
        complexity_score = (file_complexity / 5) * COMPLEXITY_WEIGHT
        size_score = (file_length / 200) * LENGTH_WEIGHT
        
        problem_score = issue_score + complexity_score + size_score
        
        if problem_score > 0:
            problematic_files.append({
                'file_path': file_path,
                'issues': len(file_issues),
                'complexity': file_complexity,
                'lines': file_length,
                'problem_score': round(problem_score, 2)
            })
    
    # Sort by problem score (descending)
    problematic_files.sort(key=lambda x: x['problem_score'], reverse=True)
    
    return problematic_files

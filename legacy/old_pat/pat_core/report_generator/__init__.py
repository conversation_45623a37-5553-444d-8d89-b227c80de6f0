"""
Module responsible for generating enhanced analysis reports from PAT results.

This module takes the raw outputs from various analysis tools run by PAT,
processes them, calculates aggregate metrics, identifies problematic areas,
and generates user-friendly summary and detailed reports.
"""

# Import the main class for backward compatibility
from .base import PatReportGenerator

# Import other modules for easier access
from . import parsers
from . import metrics
from . import formatters
from . import utils

__all__ = ['PatReportGenerator', 'parsers', 'metrics', 'formatters', 'utils']

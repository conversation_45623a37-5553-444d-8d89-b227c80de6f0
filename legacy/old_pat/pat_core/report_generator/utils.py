"""
Utility functions for the report generator.

This module contains utility functions used by the report generator,
such as file operations and data processing helpers.
"""

import ast
from pathlib import Path
from typing import Optional


def get_file_length(file_path: str, project_root: Optional[Path] = None) -> int:
    """
    Counts lines in a file. Requires access to the analyzed project source.
    
    Args:
        file_path: Relative path to the file
        project_root: Root directory of the project
        
    Returns:
        Number of lines in the file, or -1 if the file cannot be read
    """
    if not project_root:
        return -1  # Indicate error: project root unknown

    # Construct the full path relative to the project root
    # Assume file_path is already relative to project root
    full_path = project_root / file_path

    if not full_path.exists() or not full_path.is_file():
        # print(f"Warning: File not found for length check: {full_path}")
        return -1  # Indicate error: file not found

    try:
        with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
            return sum(1 for _ in f)
    except Exception as e:
        print(f"Warning: Could not read file {full_path} for length: {e}")
        return -1  # Indicate error: cannot read file


def get_import_count(file_path: str, project_root: Optional[Path] = None) -> int:
    """
    Counts imports in a Python file using AST. Requires project source.
    
    Args:
        file_path: Relative path to the file
        project_root: Root directory of the project
        
    Returns:
        Number of imports in the file, or -1 if the file cannot be parsed
    """
    if not project_root:
         return -1  # Indicate error: project root unknown

    if not file_path.lower().endswith('.py'):
        return 0  # Not a Python file

    full_path = project_root / file_path

    if not full_path.exists() or not full_path.is_file():
        # print(f"Warning: File not found for import count: {full_path}")
        return -1  # Indicate error: file not found

    try:
        with open(full_path, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            # Handle potential encoding issues or empty files
            if not content:
                return 0
            tree = ast.parse(content, filename=str(full_path))
            count = 0
            for node in ast.walk(tree):
                if isinstance(node, (ast.Import, ast.ImportFrom)):
                    count += 1
            return count
    except SyntaxError:
        # print(f"Warning: Syntax error parsing {full_path} for imports.")
        return -1  # Indicate parsing error
    except Exception as e:
        print(f"Warning: Could not parse {full_path} for imports: {e}")
        return -1  # Indicate other error


def format_file_path(file_path: str, max_length: int = 60) -> str:
    """
    Format a file path for display, truncating if necessary.
    
    Args:
        file_path: Path to format
        max_length: Maximum length of the formatted path
        
    Returns:
        Formatted file path
    """
    if len(file_path) <= max_length:
        return file_path
    
    # Truncate the middle of the path
    parts = file_path.split('/')
    if len(parts) <= 2:
        # If there are only one or two parts, just truncate the end
        return file_path[:max_length-3] + '...'
    
    # Keep the first and last parts, and truncate the middle
    first = parts[0]
    last = '/'.join(parts[-2:])
    middle_length = max_length - len(first) - len(last) - 5  # 5 for '/.../
    
    if middle_length <= 0:
        # If the first and last parts are too long, just truncate the end
        return file_path[:max_length-3] + '...'
    
    return f"{first}/.../{last}"


def severity_to_emoji(severity: str) -> str:
    """
    Convert a severity level to an emoji for visual representation.
    
    Args:
        severity: Severity level (e.g., 'error', 'warning', 'info')
        
    Returns:
        Emoji representing the severity
    """
    severity_map = {
        'fatal': '💀',
        'error': '🔴',
        'high': '🔴',
        'security': '🔒',
        'warning': '🟠',
        'medium': '🟠',
        'convention': '🟡',
        'refactor': '🔧',
        'style': '💅',
        'info': '🔵',
        'hint': '💡',
        'low': '🟢'
    }
    return severity_map.get(severity.lower(), '❓')

"""
File: engine.py
Purpose: Implements the core analysis engine for the enhanced PAT system.

This module provides the AnalysisEngine class, which is responsible for
running analysis plugins in parallel and managing the overall analysis process.
"""

import asyncio
import logging
import os
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Any, Dict, List, Optional, Set, Tuple, Union

from .plugin import Plugin, PluginResult


class AnalysisEngine:
    """
    Core engine for running analysis plugins in parallel.
    
    This class orchestrates the execution of multiple analysis plugins,
    handling parallelization, error recovery, and result aggregation.
    
    Attributes:
        plugins: List of analysis plugins to run
        max_workers: Maximum number of worker threads for CPU-bound plugins
        config: Configuration dictionary for the engine
        logger: Logger instance for the engine
        executor: ThreadPoolExecutor for running CPU-bound plugins
    """
    
    def __init__(self, 
                 plugins: List[Plugin] = None,
                 max_workers: int = None,
                 config: Dict[str, Any] = None):
        self.plugins = plugins or []
        self.max_workers = max_workers or min(32, (os.cpu_count() or 1) + 4)
        self.config = config or {}
        self.logger = logging.getLogger("pat.engine")
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
    async def run_analysis(self, 
                          files: List[str], 
                          plugins: List[str] = None,
                          context: Dict[str, Any] = None) -> Dict[str, PluginResult]:
        """
        Run analysis on the given files using the specified plugins.
        
        This method orchestrates the parallel execution of multiple plugins,
        handling errors and aggregating results.
        
        Args:
            files: List of file paths to analyze
            plugins: List of plugin names to run (None for all)
            context: Additional context information
            
        Returns:
            Dictionary mapping plugin names to results
        """
        context = context or {}
        start_time = time.time()
        self.logger.info(f"Starting analysis of {len(files)} files with {len(self.plugins)} plugins")
        
        # Filter plugins if specific ones are requested
        plugins_to_run = self.plugins
        if plugins:
            plugins_to_run = [p for p in self.plugins if p.name in plugins]
            self.logger.info(f"Filtered to {len(plugins_to_run)} plugins: {', '.join(p.name for p in plugins_to_run)}")
            
        # Create tasks for each plugin
        tasks = []
        for plugin in plugins_to_run:
            plugin_context = {**context, "plugin_name": plugin.name}
            tasks.append(self._run_plugin(plugin, files, plugin_context))
            
        # Run all tasks concurrently
        self.logger.info(f"Running {len(tasks)} plugin tasks concurrently")
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        result_dict = {}
        for plugin, result in zip(plugins_to_run, results):
            if isinstance(result, Exception):
                # Handle plugin failure
                self.logger.error(f"Plugin {plugin.name} failed: {result}")
                result_dict[plugin.name] = PluginResult(
                    plugin_name=plugin.name,
                    status="error",
                    summary=f"Plugin failed with error: {str(result)}"
                )
            else:
                result_dict[plugin.name] = result
                self.logger.info(f"Plugin {plugin.name} completed with status: {result.status}")
                
        elapsed = time.time() - start_time
        self.logger.info(f"Analysis completed in {elapsed:.2f} seconds")
        
        return result_dict
    
    async def _run_plugin(self, 
                         plugin: Plugin, 
                         files: List[str],
                         context: Dict[str, Any]) -> PluginResult:
        """
        Run a single plugin on the given files.
        
        This method handles the execution of a single plugin, including
        error handling and timing.
        
        Args:
            plugin: Plugin to run
            files: List of file paths to analyze
            context: Additional context information
            
        Returns:
            PluginResult object containing analysis results
            
        Raises:
            Exception: If the plugin fails
        """
        self.logger.info(f"Running plugin: {plugin.name}")
        start_time = time.time()
        
        try:
            # Run CPU-bound plugins in the thread pool
            if getattr(plugin, "is_cpu_bound", False):
                self.logger.debug(f"Running CPU-bound plugin {plugin.name} in thread pool")
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    self.executor,
                    lambda: plugin.analyze(files, context)
                )
            else:
                # Run I/O-bound plugins directly
                self.logger.debug(f"Running I/O-bound plugin {plugin.name} directly")
                result = await plugin.analyze(files, context)
                
            elapsed = time.time() - start_time
            self.logger.info(f"Plugin {plugin.name} completed in {elapsed:.2f} seconds")
            return result
            
        except Exception as e:
            elapsed = time.time() - start_time
            self.logger.error(f"Plugin {plugin.name} failed after {elapsed:.2f} seconds: {e}")
            raise

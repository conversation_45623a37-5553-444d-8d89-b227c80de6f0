"""
File: file_manager.py
Purpose: Implements file management and caching for the enhanced PAT system.

This module provides the FileManager class, which is responsible for
file operations, filtering, and caching to support incremental analysis.
"""

import hashlib
import logging
import os
import pickle
import time
from pathlib import Path
from typing import Dict, List, Set, Tuple


class FileManager:
    """
    Manages file operations, filtering, and caching for PAT.
    
    This class handles file discovery, filtering, and caching to support
    incremental analysis, focusing only on files that have changed since
    the last analysis.
    
    Attributes:
        config: Configuration dictionary for the file manager
        cache_dir: Directory for storing cache files
        file_cache: Dictionary mapping file paths to cache information
        logger: Logger instance for the file manager
    """
    
    def __init__(self, config: Dict[str, any] = None):
        self.config = config or {}
        self.cache_dir = Path(self.config.get("cache_dir", ".pat_cache"))
        self.cache_dir.mkdir(exist_ok=True)
        self.file_cache = self._load_file_cache()
        self.logger = logging.getLogger("pat.file_manager")
        
    def get_files(self, 
                 paths: List[str], 
                 file_types: List[str] = None,
                 exclude_patterns: List[str] = None) -> List[str]:
        """
        Get all files matching criteria from the given paths.
        
        This method recursively searches directories and filters files
        based on file types and exclude patterns.
        
        Args:
            paths: List of file or directory paths
            file_types: List of file extensions to include (None for all)
            exclude_patterns: List of glob patterns to exclude
            
        Returns:
            List of file paths
        """
        result_files = []
        exclude_patterns = exclude_patterns or []
        
        # Add patterns from .patignore if it exists
        if os.path.exists(".patignore"):
            with open(".patignore", "r") as f:
                exclude_patterns.extend([line.strip() for line in f if line.strip()])
        
        self.logger.info(f"Searching for files in {len(paths)} paths with {len(exclude_patterns)} exclude patterns")
        
        # Process each path
        for path in paths:
            path = Path(path)
            
            if path.is_file():
                if self._should_include_file(path, file_types, exclude_patterns):
                    result_files.append(str(path))
            elif path.is_dir():
                for root, _, files in os.walk(path):
                    root_path = Path(root)
                    for file in files:
                        file_path = root_path / file
                        if self._should_include_file(file_path, file_types, exclude_patterns):
                            result_files.append(str(file_path))
        
        self.logger.info(f"Found {len(result_files)} files matching criteria")
        return result_files
    
    def get_changed_files(self, 
                         files: List[str], 
                         since_timestamp: float = None) -> List[str]:
        """
        Get files that have changed since the last analysis.
        
        This method uses file modification times and content hashes to
        determine which files have changed.
        
        Args:
            files: List of file paths to check
            since_timestamp: Timestamp to compare against (None for cache-based)
            
        Returns:
            List of changed file paths
        """
        changed_files = []
        
        self.logger.info(f"Checking {len(files)} files for changes")
        
        for file in files:
            file_path = Path(file)
            
            if not file_path.exists():
                self.logger.debug(f"File does not exist: {file}")
                continue
                
            # Get file modification time
            mtime = file_path.stat().st_mtime
            
            # Check if file has changed
            if since_timestamp and mtime > since_timestamp:
                self.logger.debug(f"File modified since timestamp: {file}")
                changed_files.append(file)
            elif file in self.file_cache:
                # Check against cached hash
                current_hash = self._hash_file(file_path)
                if current_hash != self.file_cache[file]["hash"]:
                    self.logger.debug(f"File hash changed: {file}")
                    changed_files.append(file)
                    # Update cache
                    self.file_cache[file] = {
                        "hash": current_hash,
                        "mtime": mtime,
                        "size": file_path.stat().st_size
                    }
            else:
                # File not in cache, consider it changed
                self.logger.debug(f"File not in cache: {file}")
                changed_files.append(file)
                # Add to cache
                self.file_cache[file] = {
                    "hash": self._hash_file(file_path),
                    "mtime": mtime,
                    "size": file_path.stat().st_size
                }
        
        # Save updated cache
        self._save_file_cache()
        
        self.logger.info(f"Found {len(changed_files)} changed files")
        return changed_files
    
    def _should_include_file(self, 
                            file_path: Path, 
                            file_types: List[str] = None,
                            exclude_patterns: List[str] = None) -> bool:
        """
        Check if a file should be included in analysis.
        
        Args:
            file_path: Path to the file
            file_types: List of file extensions to include
            exclude_patterns: List of glob patterns to exclude
            
        Returns:
            True if the file should be included, False otherwise
        """
        # Check if file exists
        if not file_path.exists():
            return False
            
        # Check file extension
        if file_types and file_path.suffix.lower()[1:] not in file_types:
            return False
            
        # Check exclude patterns
        if exclude_patterns:
            file_str = str(file_path)
            for pattern in exclude_patterns:
                if pattern in file_str or file_path.match(pattern):
                    return False
        
        return True
    
    def _hash_file(self, file_path: Path) -> str:
        """
        Calculate hash of a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            SHA-256 hash of the file
        """
        hasher = hashlib.sha256()
        
        try:
            with open(file_path, "rb") as f:
                # Read in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
                    
            return hasher.hexdigest()
        except Exception as e:
            self.logger.warning(f"Error hashing file {file_path}: {e}")
            # If we can't read the file, return a timestamp-based hash
            return hashlib.sha256(str(time.time()).encode()).hexdigest()
    
    def _load_file_cache(self) -> Dict[str, Dict[str, any]]:
        """
        Load file cache from disk.
        
        Returns:
            Dictionary mapping file paths to cache information
        """
        cache_file = self.cache_dir / "file_cache.pkl"
        
        if cache_file.exists():
            try:
                with open(cache_file, "rb") as f:
                    cache = pickle.load(f)
                    self.logger.info(f"Loaded cache for {len(cache)} files")
                    return cache
            except Exception as e:
                self.logger.error(f"Error loading file cache: {e}")
                return {}
        
        self.logger.info("No existing file cache found")
        return {}
    
    def _save_file_cache(self) -> None:
        """Save file cache to disk."""
        cache_file = self.cache_dir / "file_cache.pkl"
        
        try:
            with open(cache_file, "wb") as f:
                pickle.dump(self.file_cache, f)
                self.logger.info(f"Saved cache for {len(self.file_cache)} files")
        except Exception as e:
            self.logger.error(f"Error saving file cache: {e}")

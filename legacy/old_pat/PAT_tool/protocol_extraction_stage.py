"""
protocol_extraction_stage.py

Pipeline stage for extracting protocol/choreography information from code using AST and NLP.

- Purpose: Analyze Python source code to extract function/class definitions, message-passing patterns, actor/choreography structures, and protocol hints from docstrings/comments.
- Related: structure_analyzer.py, content_extractor.py, tool_stage.py, models.py
- Dependencies: ast (stdlib), optionally spaCy/NLTK for NLP, Python 3.8+
"""

import ast
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import (is_excluded_path, load_exclusion_patterns,
                                logger)
except ImportError:
    # When running as a script
    from utils import is_excluded_path, load_exclusion_patterns, logger
try:
    # When running as a package
    from PAT_tool.visualization import export_protocol_to_mermaid_html
except ImportError:
    # When running as a script
    from visualization import export_protocol_to_mermaid_html

class ProtocolExtractionStage(ToolStage):
    """Pipeline stage for extracting protocol/choreography information using AST and NLP.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for protocol extraction.
    """
    tool_name = "protocol_extraction"

    def run_tool(self) -> Any:
        """Analyze Python files to extract protocol/choreography information."""
        protocol_info = {
            "roles": set(),
            "messages": [],
            "protocol_graph": {},
            "summaries": [],
            "visualization": None,
        }
        for file_path in self._find_python_files(self.project_root):
            try:
                with open(file_path, "r", encoding="utf-8") as f:
                    source = f.read()
                tree = ast.parse(source, filename=str(file_path))
                # Extract function/class definitions, message-passing, actors
                for node in ast.walk(tree):
                    if isinstance(node, ast.ClassDef):
                        protocol_info["roles"].add(node.name)
                        # Optionally extract docstrings/comments for protocol hints
                        doc = ast.get_docstring(node)
                        if doc:
                            protocol_info["summaries"].append(f"Class {node.name}: {doc}")
                    elif isinstance(node, ast.FunctionDef):
                        protocol_info["messages"].append(node.name)
                        doc = ast.get_docstring(node)
                        if doc:
                            protocol_info["summaries"].append(f"Function {node.name}: {doc}")
                    # Detect message-passing (e.g., send/receive, actor patterns)
                    elif isinstance(node, ast.Call):
                        if hasattr(node.func, "id") and node.func.id in {"send", "receive", "tell", "ask"}:
                            protocol_info["messages"].append(ast.unparse(node))
                # Optionally: NLP analysis of comments (not implemented in scaffold)
            except Exception as e:
                logger.error(f"[ProtocolExtractionStage] Failed to analyze {file_path}: {e}")
        protocol_info["roles"] = list(protocol_info["roles"])
        # Generate Mermaid sequence diagram
        output_path = str(self.project_root / "protocol_overlay_sequence.html")
        html_path = export_protocol_to_mermaid_html(protocol_info, output_path, title="Protocol Overlay Sequence Diagram")
        protocol_info["visualization"] = html_path
        return protocol_info

    def parse_output(self, output: Any) -> None:
        """Parse protocol extraction output and update metrics with protocol/conversation graphs and summaries.

        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['protocol_extraction'] includes: roles (list), messages (list), protocol_graph (dict), summaries (list)
        Args:
            output: Dict with protocol extraction results
        """
        if not isinstance(output, dict):
            return
        self.metrics.tool_results["protocol_extraction"] = {
            "roles": output.get("roles", []),
            "messages": output.get("messages", []),
            "protocol_graph": output.get("protocol_graph", {}),
            "summaries": output.get("summaries", []),
        }

    def _find_python_files(self, root: Path):
        """Yield all Python files under the given root directory, respecting .patignore and standard exclusions."""
        excluded_patterns = load_exclusion_patterns(root)
        for path in root.rglob("*.py"):
            if not is_excluded_path(path, excluded_patterns, root):
                yield path 
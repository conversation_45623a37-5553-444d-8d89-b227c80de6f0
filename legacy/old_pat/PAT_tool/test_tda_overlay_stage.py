import os
from pathlib import Path

import networkx as nx
import pytest

try:
    # When running as a package
    from PAT_tool.tda_overlay_stage import TDAOverlayStage
except ImportError:
    # When running as a script
    from tda_overlay_stage import TDAOverlayStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics

@pytest.mark.skipif('gudhi' not in globals() or 'matplotlib' not in globals(), reason="GUDHI or matplotlib not installed")
def test_tda_overlay_stage_basic(tmp_path):
    """Test TDAOverlayStage on a simple triangle graph (call graph)."""
    # Create a simple triangle graph
    G = nx.DiGraph()
    G.add_edges_from([("A", "B"), ("B", "C"), ("C", "A")])
    # Mock ProjectMetrics with call graph
    metrics = ProjectMetrics()
    metrics.tool_results["call_graph"] = {"edges": [("A", "B"), ("B", "C"), ("C", "A")]}  # minimal
    # Run TDAOverlayStage
    stage = TDAOverlayStage(project_root=tmp_path, metrics=metrics, progress=None, config={"output_dir": str(tmp_path)})
    results = stage.run_tool()
    # Check that a diagram was generated
    assert "diagrams" in results
    assert any(os.path.exists(d["path"]) for d in results["diagrams"]), "No diagram file generated."
    # Check that summaries and features are present
    assert "summaries" in results and results["summaries"], "No summary generated."
    assert "features" in results and results["features"], "No features generated."
    # Check that metrics are updated via parse_output
    stage.parse_output(results)
    assert "tda_overlay" in metrics.tool_results

@pytest.mark.skipif('gudhi' not in globals() or 'matplotlib' not in globals(), reason="GUDHI or matplotlib not installed")
def test_tda_overlay_stage_empty_graph(tmp_path):
    """Test TDAOverlayStage on an empty graph (should handle gracefully)."""
    metrics = ProjectMetrics()
    metrics.tool_results["call_graph"] = {"edges": []}
    stage = TDAOverlayStage(project_root=tmp_path, metrics=metrics, progress=None, config={"output_dir": str(tmp_path)})
    results = stage.run_tool()
    assert "diagrams" in results
    assert results["diagrams"] == [] or all(os.path.exists(d["path"]) for d in results["diagrams"])
    stage.parse_output(results)
    assert "tda_overlay" in metrics.tool_results 
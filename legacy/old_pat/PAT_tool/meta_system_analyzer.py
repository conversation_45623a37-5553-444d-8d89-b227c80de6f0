"""
Meta-System Separation Analyzer Module

Analyzes imports between meta-systems in the person_suit project to ensure proper separation.
Meta-systems should not import from each other directly, but can use common underlying
components from person_suit.

This analyzer identifies violations of meta-system separation principles and provides
detailed reports and visualizations.
"""

import ast
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
    from PAT_tool.utils import (SafetySettings, is_excluded_path,
                               load_exclusion_patterns, logger,
                               read_file_content, safe_execution)
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
    from utils import (SafetySettings, is_excluded_path,
                      load_exclusion_patterns, logger, read_file_content,
                      safe_execution)

class MetaSystemAnalyzer:
    """Analyzes meta-system separation in the person_suit project.
    
    Identifies violations where one meta-system imports from another.
    Meta-systems should be independent and only use common underlying components.
    """

    # Define meta-systems and their module prefixes
    META_SYSTEMS = {
        "PC": ["persona_core"],
        "AN": ["analyst"],
        "PR": ["prediction", "predictor"],
        "SIO": ["shared/io", "io_layer", "sio"],
        "UI": ["ui", "user_interface"],
        "CORE": ["core"]
    }

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress: Any):
        """Initialize the meta-system analyzer.

        Args:
            project_root: Path to the project root
            metrics: ProjectMetrics instance to store results
            progress: ProgressTracker instance for tracking progress
        """
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        self.project_name = project_root.name
        self._ast_recursion_count = 0
        self.excluded_patterns = load_exclusion_patterns(project_root)
        
        # Store meta-system separation violations
        self.violations = []
        
        # Store meta-system import graph
        self.meta_system_graph = {
            meta: {"imports": set(), "imported_by": set()} 
            for meta in self.META_SYSTEMS.keys()
        }
        
        # Store module to meta-system mapping
        self.module_to_meta = {}

    def analyze(self):
        """Analyze meta-system separation in the project."""
        # Only process files not excluded by .patignore
        valid_files = {k: v for k, v in self.metrics.files.items()
                      if not is_excluded_path(self.project_root / v.path, self.excluded_patterns, self.project_root)}
        
        self.progress.start_phase("Analyzing meta-system separation", len(valid_files))
        
        # First pass: map modules to meta-systems
        for module_name, metrics in valid_files.items():
            self._map_module_to_meta_system(module_name, metrics)
            
        # Second pass: analyze imports for violations
        for module_name, metrics in valid_files.items():
            self._analyze_module_imports(module_name, metrics)
            self.progress.increment()
        
        # Store results in metrics
        self._store_results()

    def _map_module_to_meta_system(self, module_name: str, metrics: FileMetrics):
        """Map a module to its meta-system.
        
        Args:
            module_name: Name of the module
            metrics: FileMetrics instance for the module
        """
        path = metrics.path.lower()
        
        # Check if module is in a meta-system
        for meta, prefixes in self.META_SYSTEMS.items():
            for prefix in prefixes:
                if f"meta_systems/{prefix}" in path or f"meta_systems\\{prefix}" in path:
                    self.module_to_meta[module_name] = meta
                    return
                
        # If not in a meta-system, check if it's in core
        if "/core/" in path or "\\core\\" in path:
            self.module_to_meta[module_name] = "CORE"
            return
            
        # If not in a meta-system or core, mark as OTHER
        self.module_to_meta[module_name] = "OTHER"

    @safe_execution
    def _analyze_module_imports(self, module_name: str, metrics: FileMetrics):
        """Analyze imports for a specific module to check for meta-system violations.
        
        Args:
            module_name: Name of the module
            metrics: FileMetrics instance for the module
        """
        # Skip if module has no meta-system
        if module_name not in self.module_to_meta:
            return
            
        source_meta = self.module_to_meta[module_name]
        
        # Skip if module is not in a meta-system
        if source_meta == "OTHER":
            return
            
        # Check each import
        for imp in metrics.internal_deps:
            # Skip if import is not in a meta-system
            if imp not in self.module_to_meta:
                continue
                
            target_meta = self.module_to_meta[imp]
            
            # Skip if import is not in a meta-system
            if target_meta == "OTHER":
                continue
                
            # Skip if import is in the same meta-system
            if source_meta == target_meta:
                continue
                
            # Skip if import is from CORE (allowed)
            if target_meta == "CORE":
                continue
                
            # If we get here, we have a violation
            violation = {
                "source_module": module_name,
                "source_meta": source_meta,
                "target_module": imp,
                "target_meta": target_meta,
                "file_path": metrics.path
            }
            
            self.violations.append(violation)
            
            # Update meta-system graph
            self.meta_system_graph[source_meta]["imports"].add(target_meta)
            self.meta_system_graph[target_meta]["imported_by"].add(source_meta)

    def _store_results(self):
        """Store analysis results in metrics."""
        # Store violations in project metrics
        self.metrics.tool_results["meta_system_separation"] = {
            "violations": self.violations,
            "meta_system_graph": {
                meta: {
                    "imports": list(data["imports"]),
                    "imported_by": list(data["imported_by"])
                }
                for meta, data in self.meta_system_graph.items()
            },
            "module_to_meta": self.module_to_meta,
            "summary": f"Found {len(self.violations)} meta-system separation violations"
        }
        
        # Store violations in file metrics
        for violation in self.violations:
            source_module = violation["source_module"]
            if source_module in self.metrics.files:
                if "tool_results" not in self.metrics.files[source_module]:
                    self.metrics.files[source_module].tool_results = {}
                
                if "meta_system_separation" not in self.metrics.files[source_module].tool_results:
                    self.metrics.files[source_module].tool_results["meta_system_separation"] = {
                        "violations": []
                    }
                
                self.metrics.files[source_module].tool_results["meta_system_separation"]["violations"].append(violation)

"""
pyre_stage.py

Pipeline stage for integrating Pyre static type checker and taint analysis into the PAT analysis pipeline.

- Purpose: Run Pyre on the codebase, parse its output, and populate diagnostics in the metrics model.
- Related: mypy_stage.py, pyright_stage.py, tool_stage.py, models.py
- Dependencies: pyre (Facebook, install via pip), Python 3.8+, subprocess
"""

import json
import os
import platform
import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

class PyreStage(ToolStage):
    """Pipeline stage for running Pyre static analysis and collecting diagnostics.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for Pyre.
    """
    tool_name: str = "pyre"

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress, config: Optional[Dict[str, Any]] = None):
        super().__init__(project_root, metrics, progress, config=config)
        self.extra_args = self.config.get("extra_args", [])

    def _get_tool_path(self):
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "pyre"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_executable} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Any:
        """Run Pyre via subprocess and return parsed JSON output or raw text on failure."""
        if skip_controller and skip_controller.should_skip():
            print("[PyreStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}
        
        pyre_executable = self._get_tool_path()
        cmd = [
            pyre_executable,
            "--noninteractive",
            "check",
            "--output=json"
        ] + self.extra_args
        
        logger.info(f"[PyreStage] Running: {' '.join(cmd)}")
        self.progress.start_phase("Pyre Check", total_steps=1)

        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                cwd=str(self.project_root),
                check=False
            )
            if result.returncode > 1:
                error_msg = result.stderr.strip() or f"Pyre execution failed with code {result.returncode}"
                logger.error(f"[PyreStage] Pyre failed: {error_msg}")
                self.progress.complete_phase("Pyre Check", status="Error")
                return {"error": f"Pyre execution failed: {error_msg}", "raw_output": result.stdout, "stderr": result.stderr}
            
            try:
                output = json.loads(result.stdout)
                self.parse_output(output)
                self.progress.complete_phase("Pyre Check", status="Complete!")
                return output
            except json.JSONDecodeError:
                error_msg = "Failed to parse Pyre JSON output"
                logger.error(f"[PyreStage] {error_msg}. stdout:\n{result.stdout}")
                self.progress.complete_phase("Pyre Check", status="Parse Error")
                return {"error": error_msg, "raw_output": result.stdout, "stderr": result.stderr}

        except FileNotFoundError:
            error_msg = f"Pyre executable not found at '{pyre_executable}' or PATH. Ensure Pyre ('pyre-check') is installed."
            logger.error(f"[PyreStage] {error_msg}")
            self.progress.complete_phase("Pyre Check", status="Not Found")
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error running pyre: {e}"
            logger.error(f"[PyreStage] {error_msg}")
            self.progress.complete_phase("Pyre Check", status="Error")
            return {"error": error_msg}
        finally:
            self.progress.increment()

    def parse_output(self, output: Any) -> None:
        """Parse Pyre output and update metrics for each file and the project."""
        if not output or (isinstance(output, dict) and output.get("error")):
            self.metrics.tool_results["pyre"] = output
            return
        # Pyre JSON output structure: { "errors": [...], ... }
        errors = output.get("errors", [])
        file_diagnostics: Dict[str, Dict[str, Any]] = {}
        for diag in errors:
            file_path = diag.get("path", "<unknown>")
            if file_path not in file_diagnostics:
                file_diagnostics[file_path] = {"errors": []}
            file_diagnostics[file_path]["errors"].append(diag)
        for file_path, diag in file_diagnostics.items():
            rel_path = str(Path(file_path).relative_to(self.project_root)) if file_path != "<unknown>" else file_path
            file_metrics = self.metrics.files.get(rel_path)
            if not file_metrics:
                file_metrics = FileMetrics(path=rel_path)
                self.metrics.files[rel_path] = file_metrics
            file_metrics.tool_results["pyre"] = diag
        self.metrics.tool_results["pyre"] = {
            "summary": {"error_count": len(errors)},
            "errors": errors,
            "raw_output": output
        }

# TODO: Add unit tests for output parsing and error handling in tests/ or PAT_tool/tests/ 
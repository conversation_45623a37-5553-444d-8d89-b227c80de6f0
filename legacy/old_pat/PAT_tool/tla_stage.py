"""
tla_stage.py

Pipeline stage for TLA+ model checking integration.

- Purpose: Run TLA+ to check protocol/concurrency/contextual models for correctness (deadlock freedom, invariants, etc.), collect results, and update ProjectMetrics.
- Related: protocol_extraction_stage.py, tool_stage.py, models.py
- Dependencies: TLA+ tools (TLC, Apalache, etc.), Java, Python 3.8+, subprocess
"""

import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class TLAStage(ToolStage):
    """Pipeline stage for TLA+ model checking integration.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for TLA+ stage.
    """
    tool_name = "tla"

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress, config: Optional[Dict[str, Any]] = None):
        """Initialize the TLA+ stage."""
        super().__init__(project_root, metrics, progress, config)
        # Optionally parse config for spec file location, tool options, etc.

    def _get_tool_path(self):
        # Construct path relative to this script's location
        # TLC is usually a Java app, might be a wrapper script in bin
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "tlc" # The TLA+ model checker command
        # Check for common wrapper script names
        possible_executables = ["tlc", "tlc-script"] # Add others if known
        if platform.system() == "Windows":
            # Windows might use .bat or .cmd wrappers
            possible_executables = [f"{name}.bat" for name in possible_executables] + [f"{name}.cmd" for name in possible_executables] + possible_executables
            
        found_path = None
        for name in possible_executables:
            venv_path = pat_venv_bin / name
            if venv_path.is_file():
                found_path = str(venv_path)
                break
        
        if found_path:
            return found_path
        else:
            # Fallback to assuming 'tlc' is in PATH (requires separate TLA+ installation)
            logger.warning(f"[{self.__class__.__name__}] TLC executable/wrapper not found in {pat_venv_bin}. Assuming 'tlc' is in PATH.")
            return "tlc"

    def run_tool(self, skip_controller=None) -> Dict[str, Any]:
        """Run TLA+ model checker on the provided spec file and collect results.

        Returns:
            Dict with TLA+ results (invariants, counterexamples, summary, raw_output).
        """
        # Check for skip request before starting subprocess
        if skip_controller and skip_controller.should_skip():
            print("[TLAStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}
            
        spec_file = self._find_spec_file()
        if not spec_file:
            logger.warning("No TLA+ spec file found.")
            return {"summary": "No TLA+ spec file found.", "invariants": [], "counterexamples": [], "raw_output": ""}
            
        results = self._run_tla_plus(spec_file)
        
        if skip_controller and skip_controller.should_skip():
            results["skipped"] = True
            results["summary"] = "Phase skipped by user during execution."

        return results

    def parse_output(self, output: Any) -> None:
        """Parse TLA+ output and update metrics with results and summaries.

        Args:
            output: Dict or str with TLA+ results
        """
        if not isinstance(output, dict):
            return
        self.metrics.tool_results["tla"] = output

    def _find_spec_file(self) -> Optional[Path]:
        """Find the TLA+ spec file to use for model checking.

        Returns:
            Path to the spec file, or None if not found.
        """
        # Check config for spec file
        spec_path = self.config.get("spec") if self.config else None
        if spec_path:
            candidate = Path(self.project_root) / spec_path
            if candidate.exists():
                return candidate
        # Otherwise, look for any .tla file in project root
        for f in Path(self.project_root).glob("*.tla"):
            return f
        return None

    def _run_tla_plus(self, spec_file: Path) -> Dict[str, Any]:
        """Run the TLA+ model checker (TLC) on the given spec file.

        Args:
            spec_file: Path to the TLA+ spec file
        Returns:
            Dict with results (invariants, counterexamples, summary, raw_output)
        """
        tlc_executable = self._get_tool_path()
        options = self.config.get("options", "").split() 
        # Basic command assumes spec file is the main argument
        cmd = [tlc_executable] + options + [str(spec_file.relative_to(self.project_root))]
        
        logger.info(f"[TLAStage] Running command: {' '.join(cmd)}")
        self.progress.start_phase("TLA+ Check", total_steps=1)
        final_status = "Error" # Default status
        output = ""
        invariants = []
        counterexamples = []
        summary = "Failed to run TLC."

        try:
            # logger.debug(f"[TLAStage] Running command: {' '.join(cmd)}")
            # Run from project root so TLC can find modules/configs relative to spec
            result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
            output = result.stdout + "\n" + result.stderr
            
            # Basic parsing of TLC output
            if "Model checking completed. No error has been found." in output:
                 final_status = "Passed"
                 summary = "No errors found."
            elif "Error: Invariant" in output or "Error: Temporal property" in output:
                 final_status = "Violation Found"
                 summary = "Violations found."
            elif result.returncode != 0:
                 final_status = f"Execution Error (code {result.returncode})"
                 summary = f"TLC execution failed (code {result.returncode})"
                 logger.error(f"[TLAStage] TLC execution failed. Output:\n{output}")
            else:
                 # Completed but didn't find the specific success message?
                 final_status = "Unknown Outcome"
                 summary = "Model checking finished, outcome unclear from output."
            
            # Extract invariants and counterexamples
            for line in output.splitlines():
                line_strip = line.strip()
                if line_strip.startswith("Invariant") and "is violated" in line_strip:
                    counterexamples.append(line_strip)
                elif line_strip.startswith("Error: Temporal property"):
                     counterexamples.append(line_strip)
                elif line_strip.startswith("Invariant") and not line_strip.endswith("is initial state:"):
                    invariants.append(line_strip)
                    
            if not summary.startswith("TLC execution failed"):
                summary = f"{len(invariants)} invariants checked, {len(counterexamples)} violations found." # Overwrite if parsed
            
        except FileNotFoundError:
            error_msg = f"TLC executable ('{tlc_executable}') not found. Ensure TLA+ toolkit is installed and in PATH, or configure wrapper path."
            logger.error(f"[TLAStage] {error_msg}")
            final_status = "Not Found"
            counterexamples = [error_msg]
            output = error_msg
            summary = error_msg
        except Exception as e:
            error_msg = f"Unexpected error running TLC: {e}"
            logger.error(f"[TLAStage] {error_msg}")
            final_status = "Error"
            counterexamples = [error_msg]
            output = error_msg
            summary = error_msg
        finally:
            # Complete progress regardless of outcome
            self.progress.complete_phase("TLA+ Check", status=final_status)
            self.progress.increment() # Increment once for the single project-wide run
            
        return {"invariants": invariants, "counterexamples": counterexamples, "summary": summary, "raw_output": output} 
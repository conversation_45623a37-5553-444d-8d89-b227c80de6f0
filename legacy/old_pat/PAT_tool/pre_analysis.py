"""
pre_analysis.py

Pre-analysis utility for PAT: batch syntax check for Python files, with optional .patignore update.

- Purpose: Identify files with syntax errors before running main analysis tools.
- Usage: Can be run standalone or imported as a function.
"""
import argparse
import fnmatch
import os
import sys
from pathlib import Path
from typing import List, Optional, Tuple


def is_excluded(path: Path, exclude_patterns: List[str], root: Path) -> bool:
    """Check if a path should be excluded based on patterns."""
    rel_path = str(path.relative_to(root))
    for pattern in exclude_patterns:
        # Simple substring match
        if pattern in rel_path:
            return True
        # Support for directory patterns (e.g., 'venv/')
        if pattern.endswith('/') and rel_path.startswith(pattern.rstrip('/')):
            return True
        # Support for wildcard patterns (e.g., '*.pyc')
        if '*' in pattern:
            if fnmatch.fnmatch(rel_path, pattern) or fnmatch.fnmatch(path.name, pattern):
                return True
    return False

def find_python_files(root: Path, exclude_patterns: Optional[List[str]] = None) -> List[Path]:
    """Recursively find all .py files under root, respecting exclusion patterns."""
    if exclude_patterns is None:
        exclude_patterns = [
            '__pycache__', '.git', '.pytest_cache', '.mypy_cache',
            '.venv', 'venv', 'PAT_venv', 'build', 'dist', 'site-packages',
            'lib/python', 'analysis_venv', 'node_modules', 'packages', 'vendor', 'libs', 'dependencies',
            'docs_backup_', 'backup_', '.bak', '.backup',
            'nltk_data', 'PAT_output', 'PAT_tool/__pycache__',
        ]

    result = []
    for dirpath, _, files in os.walk(root):
        dir_path = Path(dirpath)
        # Skip excluded directories
        if is_excluded(dir_path, exclude_patterns, root):
            continue

        for fname in files:
            if fname.endswith('.py'):
                file_path = dir_path / fname
                if not is_excluded(file_path, exclude_patterns, root):
                    result.append(file_path)

    return result

def check_syntax(files: List[Path]) -> Tuple[List[Path], List[Tuple[Path, str]]]:
    """Check syntax of each file. Returns (ok_files, error_files)."""
    ok, errors = [], []
    for f in files:
        try:
            compile(f.read_text(encoding='utf-8'), str(f), 'exec')
            ok.append(f)
        except Exception as e:
            errors.append((f, str(e)))
    return ok, errors

def update_patignore(project_root: Path, error_files: List[Path]) -> None:
    """Append error files to .patignore if not already present."""
    patignore = project_root / '.patignore'
    existing = set()
    if patignore.exists():
        existing = set(line.strip() for line in patignore.read_text().splitlines())
    new_entries = [str(f.relative_to(project_root)) for f in error_files if str(f.relative_to(project_root)) not in existing]
    if new_entries:
        with patignore.open('a', encoding='utf-8') as f:
            for entry in new_entries:
                f.write(entry + '\n')

def run_pre_analysis(project_root: Path, auto_patignore: bool = False, log_file: str = None, exclude_patterns: Optional[List[str]] = None) -> int:
    """Run pre-analysis. Returns number of files with syntax errors.

    Args:
        project_root: Path to the project root directory.
        auto_patignore: If True, add files with syntax errors to .patignore.
        log_file: Optional path to a log file to write errors to.
        exclude_patterns: Optional list of patterns to exclude from analysis.
    """
    py_files = find_python_files(project_root, exclude_patterns)
    _, errors = check_syntax(py_files)
    if errors:
        print(f"Found {len(errors)} files with syntax errors:")
        for f, err in errors:
            print(f"  {f}: {err}")
        if auto_patignore:
            update_patignore(project_root, [f for f, _ in errors])
            print(f"Added {len(errors)} files to .patignore.")
        if log_file:
            log_path = Path(log_file)
            with log_path.open('w', encoding='utf-8') as lf:
                lf.write(f"Found {len(errors)} files with syntax errors:\n")
                for f, err in errors:
                    lf.write(f"  {f}: {err}\n")
            print(f"Syntax errors written to {log_file}.")
    else:
        print("No syntax errors found.")
    return len(errors)

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Batch syntax check for Python files, with optional .patignore update.")
    parser.add_argument("project_root", type=Path, help="Project root directory")
    parser.add_argument("--auto-patignore", action="store_true", help="Add files with syntax errors to .patignore")
    parser.add_argument("--log-file", type=str, default=None, help="Write syntax errors to this log file")
    args = parser.parse_args()
    run_pre_analysis(args.project_root, auto_patignore=args.auto_patignore, log_file=args.log_file)
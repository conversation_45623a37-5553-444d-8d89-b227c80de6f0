"""
isort_stage.py

Pipeline stage for integrating isort (import sorting checker) into the PAT analysis pipeline.

- Purpose: Run isort in check mode, parse its output, and populate diagnostics in the metrics model.
- Related: black_stage.py, flake8_stage.py, tool_stage.py, models.py
- Dependencies: isort (install via pip), Python 3.8+, subprocess
"""

import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class IsortStage(ToolStage):
    """Pipeline stage for running isort and collecting import sorting diagnostics.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for isort.
    """
    tool_name = "isort"

    def _get_tool_path(self):
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "isort"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_executable} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Any:
        """Run isort in check mode via subprocess and return output."""
        if skip_controller and skip_controller.should_skip():
            print("[IsortStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}
            
        options_str = self.config.get("options", "")
        options = options_str.split()
        if "--check-only" not in options and "--check" not in options:
            options.append("--check-only")
        if "--diff" not in options:
            options.append("--diff")
            
        isort_executable = self._get_tool_path()
        cmd = [
            isort_executable,
            str(self.project_root),
        ] + options
        
        logger.info(f"[IsortStage] Running command: {' '.join(cmd)}")
        self.progress.start_phase("Isort Check", total_steps=1)

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
            
            if result.returncode not in (0, 1):
                error_msg = result.stderr.strip() or f"isort execution failed with code {result.returncode}"
                logger.error(f"[IsortStage] isort failed: {error_msg}")
                self.progress.complete_phase("Isort Check", status="Error")
                return {"error": f"isort execution failed: {error_msg}", "raw_output": result.stdout, "stderr": result.stderr}

            self.progress.complete_phase("Isort Check", status="Complete!")
            return result.stdout
        except FileNotFoundError:
            error_msg = f"isort executable not found at '{isort_executable}'. Ensure PAT setup completed correctly."
            logger.error(f"[IsortStage] {error_msg}")
            self.progress.complete_phase("Isort Check", status="Not Found")
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error running isort: {e}"
            logger.error(f"[IsortStage] {error_msg}")
            self.progress.complete_phase("Isort Check", status="Error")
            return {"error": error_msg}
        finally:
            self.progress.increment()

    def parse_output(self, output: Any) -> None:
        """Parse isort output and update metrics with import sorting issues and summary.

        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['isort'] includes: errors (list), summary (str), raw_output (str)
            - FileMetrics.tool_results['isort'] (for all files): errors (list), summary (str)
        Args:
            output: Raw output from isort (str) or error dict
        """
        if isinstance(output, dict) and (output.get("skipped") or output.get("error")):
            self.metrics.tool_results["isort"] = output
            return
        
        if not isinstance(output, str):
            logger.warning(f"[IsortStage] parse_output received unexpected data type: {type(output)}")
            self.metrics.tool_results["isort"] = {"error": "Invalid data received by parse_output"}
            return

        errors = []
        file_error_map: Dict[str, list] = {}
        current_file_rel = None
        current_file_abs = None
        diff_block = []

        for line in output.splitlines():
            if line.startswith("ERROR: "):
                errors.append(line)
                if current_file_abs:
                    file_error_map.setdefault(current_file_abs, []).append(line)
                continue
                 
            if line.startswith("--- ") or line.startswith("+++ "): 
                if current_file_abs and diff_block:
                    file_error_map.setdefault(current_file_abs, []).extend(diff_block)
                    diff_block = []
                
                parts = line.split(maxsplit=1)
                if len(parts) == 2:
                    path_part = parts[1].split('\t')[0]
                    current_file_rel = path_part.strip()
                    try:
                        current_file_abs = str((self.project_root / current_file_rel).resolve())
                    except Exception:
                        logger.warning(f"[IsortStage] Could not resolve path '{current_file_rel}' relative to {self.project_root}. Using raw path.")
                        current_file_abs = current_file_rel
                    file_error_map.setdefault(current_file_abs, []).append(line)
                    errors.append(line)
                else:
                    current_file_rel = None
                    current_file_abs = None
                continue

            if current_file_abs and (line.startswith('+') or line.startswith('-')):
                diff_block.append(line)
                errors.append(line)

        if current_file_abs and diff_block:
            file_error_map.setdefault(current_file_abs, []).extend(diff_block)

        summary = f"{len(file_error_map)} files with import order issues"
        self.metrics.tool_results["isort"] = {
            "errors": errors,
            "summary": summary,
            "raw_output": output,
        }
        
        for file_metrics in self.metrics.files.values():
            file_metrics.tool_results["isort"] = {"errors": [], "summary": "0 issues"}
            
        for file_path_abs, file_errors in file_error_map.items():
            found = False
            for key, file_metrics in self.metrics.files.items():
                abs_key = str(Path(self.project_root) / file_metrics.path)
                if file_path_abs == abs_key:
                    file_metrics.tool_results["isort"] = {
                        "errors": file_errors,
                        "summary": f"{len(file_errors)} import order issues"
                    }
                    found = True
                    break
            
            if not found:
                for key, file_metrics in self.metrics.files.items():
                    if file_path_abs == file_metrics.path:
                        file_metrics.tool_results["isort"] = {
                            "errors": file_errors,
                            "summary": f"{len(file_errors)} import order issues"
                        } 
                        break

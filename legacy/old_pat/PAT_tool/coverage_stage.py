"""
CoverageStage for PAT
====================

Integrates coverage.py (with pytest) as a modular pipeline stage. Runs coverage analysis on the codebase,
parses coverage summary or JSON, and updates ProjectMetrics/FileMetrics for downstream use.

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)
"""

import json
import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class CoverageStage(ToolStage):
    tool_name = "coverage"

    def _get_tool_path(self, tool_name: str):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = tool_name
        if platform.system() == "Windows":
            tool_executable += ".exe"
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_name} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run coverage as a subprocess on the project root using pytest. Interruptible via skip_controller.
        
        Args:
            skip_controller: Optional controller to check for skip requests
            
        Returns:
            Raw output from coverage (str) or skip result dict
        """
        options = self.config.get("options", "").split()
        coverage_executable = self._get_tool_path("coverage")
        pytest_executable = self._get_tool_path("pytest")

        # Command to run tests under coverage
        cmd_run = [coverage_executable, "run", f"--rcfile={self.project_root / '.coveragerc'}" if (self.project_root / '.coveragerc').exists() else "", "-m", pytest_executable] + options
        cmd_run = [arg for arg in cmd_run if arg] # Remove empty strings like the rcfile option if file doesn't exist
        
        # Command to generate JSON report after running tests
        cmd_json = [coverage_executable, "json", "-o", "coverage.json"]
        
        logger.info(f"[CoverageStage] Running command: {' '.join(cmd_run)}")
        self.progress.start_phase("Coverage (run tests)", total_steps=1)
        interrupted = False
        run_failed = False
        
        # Check for skip request before starting subprocess
        if skip_controller and skip_controller.should_skip():
            print("[CoverageStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}
            
        try:
            result_run = subprocess.run(cmd_run, capture_output=True, text=True, check=False, cwd=self.project_root)
            self.progress.increment()
            self.progress.complete_phase("Coverage (run tests)", status=f"Exit code: {result_run.returncode}")
            
            # Check for skip request after test run
            if skip_controller and skip_controller.should_skip():
                print("[CoverageStage] Skip requested by user after test run.")
                return {"skipped": True, "summary": "Phase skipped by user after test run."}
                
            if result_run.returncode != 0 and result_run.returncode != 5: # 5 = no tests ran
                logger.error(f"[CoverageStage] Test execution failed (exit code {result_run.returncode}).\nStderr:\n{result_run.stderr}")
                run_failed = True
                # Return failure information
                return {"error": f"Test execution failed (code {result_run.returncode})", "stderr": result_run.stderr, "stdout": result_run.stdout}

            # Generate JSON report
            logger.info(f"[CoverageStage] Running command: {' '.join(cmd_json)}")
            self.progress.start_phase("Coverage (generate report)", total_steps=1)
            result_json = subprocess.run(cmd_json, capture_output=True, text=True, check=False, cwd=self.project_root)
            self.progress.increment()
            self.progress.complete_phase("Coverage (generate report)", status=f"Exit code: {result_json.returncode}")
            
            if result_json.returncode != 0:
                 logger.error(f"[CoverageStage] Coverage JSON report generation failed (exit code {result_json.returncode}).\nStderr:\n{result_json.stderr}")
                 return {"error": f"Coverage report generation failed (code {result_json.returncode})", "stderr": result_json.stderr, "stdout": result_json.stdout, "test_run_stdout": result_run.stdout}

            # Read the generated JSON file
            coverage_json_path = self.project_root / "coverage.json"
            if coverage_json_path.exists():
                with open(coverage_json_path, 'r') as f:
                    coverage_data = json.load(f)
                # Optionally remove the file after reading
                # coverage_json_path.unlink()
                return coverage_data # Return parsed data directly
            else:
                logger.error("[CoverageStage] coverage.json file not found after successful command execution.")
                return {"error": "coverage.json not found", "test_run_stdout": result_run.stdout, "report_gen_stdout": result_json.stdout}

        except FileNotFoundError as e:
            tool_not_found = "coverage" if coverage_executable in str(e) else "pytest" if pytest_executable in str(e) else "unknown"
            executable_path = coverage_executable if tool_not_found == "coverage" else pytest_executable
            error_msg = f"{tool_not_found.capitalize()} executable not found at '{executable_path}'. Ensure PAT setup completed correctly."
            logger.error(f"[CoverageStage] {error_msg}")
            self.progress.complete_phase("Coverage (run tests)", status="Tool Not Found")
            return {"error": error_msg}
        except Exception as e:
            logger.error(f"[CoverageStage] Unexpected error: {e}")
            # Determine which phase failed if possible
            current_phase_name = self.progress.current_phase or "Coverage"
            self.progress.complete_phase(current_phase_name, status="Error")
            return {"error": f"Unexpected error in {current_phase_name}: {e}"}

    def parse_output(self, output: Any) -> None:
        """
        Parse coverage JSON output, update metrics with coverage data.
        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['coverage'] includes: coverage (float), missing (list), summary (str), raw_output (str or dict)
            - FileMetrics.tool_results['coverage'] (for all files): coverage (float), missing (list), summary (str)
        Args:
            output: Raw output from coverage (str) or skip result dict
        """
        # Handle skip or error case passed from run_tool
        if isinstance(output, dict) and (output.get("skipped") or output.get("error")):
            self.metrics.tool_results["coverage"] = output
            return
            
        # If output is not a dict here, something went wrong upstream
        if not isinstance(output, dict):
             logger.warning(f"[CoverageStage] parse_output received unexpected data type: {type(output)}")
             self.metrics.tool_results["coverage"] = {"error": "Invalid data received by parse_output"}
             return
        
        data = output # Output is already parsed JSON data
        total_covered = 0
        total_statements = 0
        missing_files = []
        file_coverage_map: Dict[str, dict] = {}
        if data and "files" in data:
            for file_path, file_data in data["files"].items():
                # Normalize path for comparison
                norm_file_path = str(Path(self.project_root) / file_path) 
                
                summary_data = file_data.get("summary", {})
                statements = summary_data.get("num_statements", 0)
                covered = summary_data.get("covered_lines", 0)
                missing = file_data.get("missing_lines", [])
                
                total_covered += covered
                total_statements += statements
                
                if missing:
                    missing_files.append(norm_file_path)
                    
                file_coverage = 100.0 * covered / statements if statements > 0 else 0.0
                file_coverage_map[norm_file_path] = {
                    "coverage": file_coverage,
                    "missing": missing,
                    "summary": f"{file_coverage:.1f}% covered, {len(missing)} missing lines"
                }
                
        coverage_percent = 100.0 * total_covered / total_statements if total_statements > 0 else 0.0
        summary = f"{coverage_percent:.1f}% covered, {len(missing_files)} files with missing lines"
        # Store in ProjectMetrics tool_results
        self.metrics.tool_results["coverage"] = {
            "coverage": coverage_percent,
            "missing": missing_files,
            "summary": summary,
            "raw_output": output, # Store the parsed JSON
        }
        # Assign per-file coverage and missing lines to FileMetrics.tool_results['coverage'] for all files
        for key, file_metrics in self.metrics.files.items():
            abs_key = str(Path(self.project_root) / file_metrics.path) # Use relative path from metrics key
            # Find coverage data using the normalized path
            file_cov = file_coverage_map.get(abs_key)
            
            if file_cov is not None:
                file_metrics.tool_results["coverage"] = file_cov
            else:
                # If file wasn't in coverage report, assume 0%
                file_metrics.tool_results["coverage"] = {
                    "coverage": 0.0,
                    "missing": [],
                    "summary": "0.0% covered (not in report)"
                }

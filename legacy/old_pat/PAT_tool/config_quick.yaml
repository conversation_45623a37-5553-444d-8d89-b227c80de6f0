# Quick PAT Configuration
# This configuration enables only essential tools for a quick analysis

# Progress bar configuration
progress_bar:
  type: simple  # Use simple progress bar to avoid rich.live issues
  simple_tracker_details: true  # Show details in simple tracker
  enabled: true  # Enable progress bars

# Pre-analysis configuration
pre_analysis:
  enabled: true  # Enable pre-analysis to check for syntax errors
  auto_patignore: true  # Automatically create .patignore file for files with syntax errors

# Dependency audit configuration
dependency_audit:
  enabled: false  # Disable dependency audit for speed

# Profiling configuration
profiling:
  timing: true  # Enable timing to see how long each phase takes
  cprofile: false  # Disable cProfile to avoid overhead

# Parallel processing configuration
parallel:
  enabled: false  # Disable parallel processing in quick mode
  max_workers: 2  # Use fewer workers for parallel processing

# Tool configurations
tools:
  # Type checking tools
  mypy:
    enabled: false  # Disable mypy for speed
    options: "--ignore-missing-imports --follow-imports=skip"
    skip_on_error: true
    skip_phase: true
  
  pyright:
    enabled: true   # Enable pyright for type checking
    options: "--level warning"  # Use warning level instead of error
    skip_on_error: true
    skip_phase: false
  
  # Code quality tools
  ruff:
    enabled: true  # Enable ruff for linting
    options: "--select=E,F,W --ignore=E501,E722,F401,F841"  # Ignore common issues
    skip_on_error: true
    skip_phase: false

  bandit:
    enabled: false  # Disable bandit for speed
    options: "-r --format json"
    skip_on_error: true
    skip_phase: true

  coverage:
    enabled: false  # Disable coverage analysis for speed
    options: ""
    skip_on_error: true
    skip_phase: true

  flake8:
    enabled: false  # Disable flake8 as ruff is the primary linter
    options: ""
    skip_on_error: true
    skip_phase: true

  pylint:
    enabled: false  # Disable pylint for speed
    options: "--output-format=json --disable=C0114,C0115,C0116 --score=n --reports=n --ignore-patterns=.*\\.json$"
    skip_on_error: true
    skip_phase: true

  black:
    enabled: false  # Disable black code formatter for speed
    options: "--check --fast"
    skip_on_error: true
    skip_phase: true

  isort:
    enabled: false  # Disable isort import sorting checker for speed
    options: "--check-only"
    skip_on_error: true
    skip_phase: true

  pydocstyle:
    enabled: false  # Disable pydocstyle docstring quality checker for speed
    options: ""
    skip_on_error: true
    skip_phase: true

  dependency:
    enabled: false  # Disable dependency analysis for speed
    options: ""
    skip_on_error: true
    skip_phase: true

  call_graph:
    enabled: false  # Disable call graph extraction for speed
    options: ""
    skip_on_error: true
    skip_phase: true

  # Advanced analysis tools - all disabled for speed
  protocol_extraction:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

  effect_overlay:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

  graph_overlay:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

  tda_overlay:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

  pycontract:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

  tla:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

  hypothesis:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

  pyre:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

  doc_analysis:
    enabled: false
    options: ""
    skip_on_error: true
    skip_phase: true

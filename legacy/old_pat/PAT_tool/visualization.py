"""
Visualization Module

Generates visual representations of project metrics, including dependency graphs
and complexity heatmaps.
"""

import logging
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

import matplotlib.pyplot as plt
import networkx as nx
from tqdm import tqdm

# Try to import graphviz for dependency visualization
try:
    from graphviz import Digraph
except ImportError:
    # Will handle this in _check_graphviz_available method
    pass

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.utils import ensure_dir, logger, safe_execution
except ImportError:
    # When running as a script
    from models import ProjectMetrics
    from utils import ensure_dir, logger, safe_execution

class VisualizationGenerator:
    """Generates visualizations of project metrics."""

    def __init__(self, project_name: str, metrics: ProjectMetrics,
                 progress, output_dir: str = "analysis",
                 config: Dict[str, Any] = None):
        """Initialize the visualization generator.

        Args:
            project_name: Name of the project
            metrics: ProjectMetrics instance with analysis results
            progress: progress tracker instance for tracking progress
            output_dir: Directory where visualization files will be saved
            config: Configuration dictionary with visualization options
        """
        self.project_name = project_name
        self.metrics = metrics
        self.progress = progress
        self.output_dir = output_dir
        self.config = config or {}
        # Extract progress bar configuration
        self.always_show_progress = self.config.get("progress_bar", {}).get("always", True)

    def generate(self):
        """Generate all visualizations."""
        self.progress.start_phase("Generating visualizations", 2)
        ensure_dir(self.output_dir)
        self._generate_dependency_graph()
        self.progress.increment()
        self._generate_complexity_heatmap()
        self.progress.increment()

    @safe_execution
    def _generate_dependency_graph(self):
        """Generate dependency graph visualization using Graphviz."""
        try:
            if not self._check_graphviz_available():
                logger.warning("Graphviz not available, falling back to NetworkX visualization")
                self._generate_networkx_dependency_graph()
                return
            if not self.metrics.dependency_graph.nodes():
                logger.warning("No dependencies to visualize")
                return
            vis_dir = os.path.join(self.output_dir, "visualizations")
            ensure_dir(vis_dir)
            graph = Digraph(comment=f"{self.project_name} Dependencies")
            graph.attr(rankdir='LR', size='11.7,8.3', dpi='300')
            graph.attr('node', shape='box', style='filled', fontname='Arial', fontsize='10')
            nodes = list(self.metrics.dependency_graph.nodes())
            edges = list(self.metrics.dependency_graph.edges())
            # Use progress tracker for reporting instead of tqdm
            if hasattr(self.progress, 'set_status'):
                self.progress.set_status("[cyan]Adding nodes to dependency graph")
            for node in nodes:
                fillcolor = "lightblue"
                if ".test" in node or "test_" in node:
                    fillcolor = "lightgreen"
                elif ".util" in node or ".common" in node:
                    fillcolor = "lightyellow"
                graph.node(node, fillcolor=fillcolor)
            if hasattr(self.progress, 'set_status'):
                self.progress.set_status("[cyan]Adding edges to dependency graph")
            for edge in edges:
                from_node, to_node = edge
                graph.edge(from_node, to_node)
            output_path = f"{vis_dir}/PAT_{self.project_name}_dependency.pdf"
            if graph is not None:
                graph.render(output_path, view=False, cleanup=True)
                logger.info(f"Dependency graph saved to {output_path}.pdf")
            else:
                logger.error("[VisualizationGenerator] Graphviz graph object is None; skipping render().")
        except Exception as e:
            logger.error(f"Error generating dependency graph: {e}")

    @safe_execution
    def _generate_networkx_dependency_graph(self):
        """Generate dependency graph visualization using NetworkX."""
        try:
            vis_dir = os.path.join(self.output_dir, "visualizations")
            ensure_dir(vis_dir)
            G = nx.DiGraph()
            for node in self.metrics.dependency_graph.nodes():
                G.add_node(node)
            for edge in self.metrics.dependency_graph.edges():
                from_node, to_node = edge
                G.add_edge(from_node, to_node)
            plt.figure(figsize=(20, 15))
            pos = nx.spring_layout(G, seed=42)
            node_colors = []
            for node in G.nodes():
                if ".test" in node or "test_" in node:
                    node_colors.append("lightgreen")
                elif ".util" in node or ".common" in node:
                    node_colors.append("lightyellow")
                else:
                    node_colors.append("lightblue")
            nx.draw(
                G, pos,
                with_labels=True,
                node_color=node_colors,
                node_size=500,
                font_size=8,
                font_weight="bold",
                arrows=True
            )
            output_path = f"{vis_dir}/PAT_{self.project_name}_nx_dependency.png"
            plt.savefig(output_path)
            plt.close()
            logger.info(f"NetworkX dependency graph saved to {output_path}")
        except Exception as e:
            logger.error(f"Error generating NetworkX dependency graph: {e}")

    @safe_execution
    def _generate_complexity_heatmap(self):
        """Generate complexity heatmap for modules."""
        # Skip if no complexity metrics
        if not self.metrics.modules:
            logger.warning("No modules to visualize for complexity heatmap")
            return

        try:
            # Create output directory if it doesn't exist
            vis_dir = os.path.join(self.output_dir, "visualizations")
            ensure_dir(vis_dir)

            # Get modules and sort by complexity
            modules = sorted(
                self.metrics.modules.items(),
                key=lambda x: x[1].complexity or 0,
                reverse=True
            )

            # Limit to top 30 modules
            modules = modules[:30]

            # Generate bar chart
            fig, ax = plt.figure(figsize=(15, 10)), plt.gca()

            # Prepare data
            module_names = []
            complexities = []
            colors = []

            for module_name, metrics in modules:
                # Skip modules with no complexity data
                if metrics.complexity is None:
                    continue

                # Truncate long module names
                short_name = module_name
                if len(short_name) > 40:
                    short_name = "..." + short_name[-37:]

                module_names.append(short_name)
                complexities.append(metrics.complexity)

                # Color based on complexity
                if metrics.complexity > 20:
                    colors.append('#FF4136')  # High complexity - red
                elif metrics.complexity > 10:
                    colors.append('#FF851B')  # Medium complexity - orange
                else:
                    colors.append('#2ECC40')  # Low complexity - green

            # Create horizontal bar chart
            y_pos = range(len(module_names))
            ax.barh(y_pos, complexities, color=colors)
            ax.set_yticks(y_pos)
            ax.set_yticklabels(module_names)
            ax.invert_yaxis()  # Labels read top-to-bottom
            ax.set_xlabel('Cyclomatic Complexity')
            ax.set_title(f'Module Complexity - {self.project_name}')

            # Add complexity values as text
            for i, v in enumerate(complexities):
                ax.text(v + 0.5, i, str(v), color='black', va='center')

            # Save the figure
            output_path = f"{vis_dir}/PAT_{self.project_name}_complexity.png"
            plt.tight_layout()
            plt.savefig(output_path)
            plt.close()

            logger.info(f"Complexity heatmap saved to {output_path}")

        except Exception as e:
            logger.error(f"Error generating complexity heatmap: {e}")

    def _check_graphviz_available(self) -> bool:
        """Check if graphviz is available for dependency visualization.

        Returns:
            True if graphviz is available, False otherwise
        """
        try:
            import graphviz

            # Also check if the dot command is available
            try:
                import subprocess
                result = subprocess.run(
                    ["dot", "-V"],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                if result.returncode == 0:
                    version = result.stderr.strip() or result.stdout.strip()
                    logger.info(f"Graphviz dot command is available: {version}")
                    return True
                else:
                    logger.warning("Graphviz Python package is installed, but the dot command is not available in PATH.")
                    logger.warning("For better visualizations, please install Graphviz using the install_graphviz.py script.")
                    return False
            except (FileNotFoundError, subprocess.SubprocessError):
                logger.warning("Graphviz Python package is installed, but the dot command is not available in PATH.")
                logger.warning("For better visualizations, please install Graphviz using the install_graphviz.py script.")
                return False
        except ImportError:
            logger.warning("Graphviz not available, falling back to networkx for dependency graph")
            logger.warning("For better visualizations, please install Graphviz using the install_graphviz.py script.")
            return False

    def _shorten_module_name(self, module_name: str, max_length: int = 40) -> str:
        """Shorten a module name for display purposes.

        Args:
            module_name: Original module name
            max_length: Maximum length of shortened name

        Returns:
            Shortened module name
        """
        if len(module_name) <= max_length:
            return module_name

        # Split by dots and calculate how many segments to keep
        parts = module_name.split(".")

        if len(parts) <= 2:
            return module_name

        # Keep first and last parts, and add ellipsis in the middle
        return f"{parts[0]}...{'.'.join(parts[-2:])}"

    @safe_execution
    def export_html_dependency_graph(self):
        """Export dependency graph as interactive HTML using pyvis if available."""
        try:
            from pyvis.network import Network

            # Skip if no dependencies
            if not self.metrics.dependency_graph.nodes():
                logger.warning("No dependencies to visualize")
                return

            vis_dir = os.path.join(self.output_dir, "visualizations")
            ensure_dir(vis_dir)

            net = Network(height="750px", width="100%", directed=True)

            # Add nodes with attributes
            for node in self.metrics.dependency_graph.nodes():
                color = "#D2E5FF"  # Default light blue
                if ".test" in node or "test_" in node:
                    color = "#C5E8C5"  # Light green for tests
                elif ".util" in node or ".common" in node:
                    color = "#FFFFC5"  # Light yellow for utilities

                net.add_node(node, label=node, title=node, color=color)

            # Add edges
            for edge in self.metrics.dependency_graph.edges():
                from_node, to_node = edge
                net.add_edge(from_node, to_node)

            # Set physics
            net.set_options("""
            {
              "physics": {
                "barnesHut": {
                  "gravitationalConstant": -5000,
                  "centralGravity": 0.3,
                  "springLength": 95
                },
                "minVelocity": 0.75
              }
            }
            """)

            # Save the visualization
            output_path = f"{vis_dir}/PAT_{self.project_name}_interactive.html"

            # Add null check before calling .show()
            if net is not None:
                net.show(output_path)
                logger.info(f"Interactive dependency graph saved to {output_path}")
            else:
                logger.error("[VisualizationGenerator] Pyvis network object is None; skipping show().")

        except ImportError:
            logger.warning("pyvis library not found. Interactive HTML graph not generated.")
        except Exception as e:
            logger.error(f"Error generating interactive HTML graph: {e}")

def export_networkx_to_pyvis_html(G: nx.Graph, output_path: str, title: str = "Graph Overlay") -> str:
    """
    Export a NetworkX graph to an interactive HTML file using pyvis.
    If node metadata includes 'file' and 'lineno', add a clickable link in the node tooltip to the code file/line (file:// URL or custom handler).
    Args:
        G: NetworkX graph (DiGraph or Graph)
        output_path: Path to save the HTML file
        title: Title for the visualization
    Returns:
        Path to the generated HTML file
    """
    try:
        from pyvis.network import Network
    except ImportError:
        logger.warning("pyvis not installed, cannot generate interactive HTML graph.")
        return ""
    net = Network(height="800px", width="100%", directed=G.is_directed(), notebook=False)
    # Add nodes with hyperlinks if metadata is present
    for n, data in G.nodes(data=True):
        label = str(n)
        file = data.get("file")
        lineno = data.get("lineno")
        if file and lineno:
            file_url = f"file://{file}#L{lineno}"
            title_html = f"<b>{label}</b><br>File: {file}<br>Line: {lineno}<br><a href='{file_url}' target='_blank'>View Source</a>"
        elif file:
            file_url = f"file://{file}"
            title_html = f"<b>{label}</b><br>File: {file}<br><a href='{file_url}' target='_blank'>View Source</a>"
        else:
            title_html = f"<b>{label}</b>"
        net.add_node(n, label=label, title=title_html)
    # Add edges
    for u, v, data in G.edges(data=True):
        net.add_edge(u, v)
    net.show_buttons(filter_=['physics'])
    net.title = title

    # Add null check before calling .show()
    if net is not None:
        net.show(output_path)
        return output_path
    else:
        logger.error("Pyvis Network object is None; skipping show().")
        return ""

def export_effects_to_sankey_html(
    effects_data: dict, output_path: str, title: str = "Effects Flow"
) -> str:
    """
    Create an interactive Sankey diagram from effects analysis data.
    The Sankey diagram visualizes effects flow from source to target with weighted links.

    Args:
        effects_data: Dictionary containing effects data (sources, targets, values)
        output_path: Path to save the HTML file
        title: Title for the Sankey diagram

    Returns:
        Path to the generated HTML file
    """
    try:
        import plotly.graph_objects as go
    except ImportError:
        logger.warning("plotly not installed, cannot generate Sankey diagram.")
        return ""

    # Extract data for Sankey diagram
    labels = []
    sources = []
    targets = []
    values = []
    label_to_idx = {}

    # Process effects data
    for source_name, targets_dict in effects_data.items():
        if source_name not in label_to_idx:
            label_to_idx[source_name] = len(labels)
            labels.append(source_name)

        for target_name, value in targets_dict.items():
            if target_name not in label_to_idx:
                label_to_idx[target_name] = len(labels)
                labels.append(target_name)

            sources.append(label_to_idx[source_name])
            targets.append(label_to_idx[target_name])
            values.append(value)

    # Create Sankey diagram
    if not sources or not targets or not values:
        logger.warning("Insufficient data for Sankey diagram.")
        return ""

    fig = go.Figure(
        data=[
            go.Sankey(
                node=dict(
                    pad=15,
                    thickness=20,
                    line=dict(color="black", width=0.5),
                    label=labels,
                ),
                link=dict(
                    source=sources,
                    target=targets,
                    value=values,
                ),
            )
        ]
    )

    fig.update_layout(
        title_text=title,
        font_size=12,
        autosize=True,
        height=800,
    )

    # Add null check before writing to file
    if fig is not None:
        try:
            fig.write_html(output_path)
            return output_path
        except Exception as e:
            logger.error(f"Error exporting Sankey diagram: {str(e)}")
            return ""
    else:
        logger.error("Plotly Figure object is None; skipping write_html().")
        return ""

def generate_visualization_index(output_dir: str) -> str:
    """
    Generate an index HTML file that links to all generated visualizations.

    Args:
        output_dir: Directory where visualizations are stored

    Returns:
        Path to the generated index file
    """
    output_path = os.path.join(output_dir, "visualizations.html")

    # Collect all HTML files
    html_files = []
    for root, _, files in os.walk(output_dir):
        for file in files:
            if file.endswith(".html") and file != "visualizations.html":
                rel_path = os.path.relpath(os.path.join(root, file), output_dir)
                name = os.path.splitext(os.path.basename(file))[0].replace("_", " ").title()
                html_files.append((name, rel_path))

    # Sort files by name
    html_files.sort()

    # Generate HTML content
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <title>Project Analysis Visualizations</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }}
        h1 {{ color: #2c3e50; }}
        ul {{ list-style-type: none; padding: 0; }}
        li {{ margin: 10px 0; background-color: #f8f9fa; padding: 10px; border-radius: 5px; }}
        a {{ color: #3498db; text-decoration: none; }}
        a:hover {{ text-decoration: underline; }}
        .timestamp {{ color: #7f8c8d; font-size: 0.8em; }}
    </style>
</head>
<body>
    <h1>Project Analysis Visualizations</h1>
    <p class="timestamp">Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    <ul>
"""

    for name, path in html_files:
        html_content += f'        <li><a href="{path}" target="_blank">{name}</a></li>\n'

    html_content += """    </ul>
</body>
</html>
"""

    # Check before writing file
    try:
        with open(output_path, "w") as f:
            f.write(html_content)
        return output_path
    except Exception as e:
        logger.error(f"Error generating visualization index: {str(e)}")
        return ""

def export_protocol_to_mermaid_html(protocol_info: dict, output_path: str, title: str = "Protocol Overlay Sequence Diagram") -> str:
    """
    Export protocol overlay as a Mermaid sequence diagram in HTML.
    Args:
        protocol_info: Dict with roles, messages, protocol_graph, summaries.
        output_path: Path to save the HTML file.
        title: Title for the visualization.
    Returns:
        Path to the generated HTML file.
    """
    # Build a simple Mermaid sequence diagram from roles and messages
    roles = protocol_info.get("roles", [])
    messages = protocol_info.get("messages", [])
    # Start sequence diagram
    mermaid_lines = ["sequenceDiagram"]
    # For each message, create a generic interaction (could be improved with more structure)
    for msg in messages:
        if len(roles) >= 2:
            mermaid_lines.append(f"    {roles[0]}->>{roles[1]}: {msg}")
        else:
            mermaid_lines.append(f"    participant {msg}")
    mermaid_code = "\n".join(mermaid_lines)
    html = f"""
    <!DOCTYPE html>
    <html lang='en'>
    <head>
        <meta charset='UTF-8'>
        <title>{title}</title>
        <script type="module">
          import mermaid from 'https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.esm.min.mjs';
          mermaid.initialize({{ startOnLoad: true }});
        </script>
    </head>
    <body>
        <h2>{title}</h2>
        <pre class="mermaid">
{mermaid_code}
        </pre>
    </body>
    </html>
    """
    with open(output_path, "w", encoding="utf-8") as f:
        f.write(html)
    return output_path

def export_tda_persistence_diagram(diagram_data: dict, output_path: str, title: str = "TDA Persistence Diagram") -> str:
    """
    Export TDA overlay as a persistence diagram/barcode using matplotlib.
    Args:
        diagram_data: Dict with keys 'points' (list of (birth, death)), 'barcode' (optional).
        output_path: Path to save the PNG or HTML file.
        title: Title for the visualization.
    Returns:
        Path to the generated file.
    """
    try:
        import matplotlib.pyplot as plt
    except ImportError:
        logger.warning("matplotlib not installed, cannot generate persistence diagram.")
        return ""
    points = diagram_data.get("points", [])
    barcode = diagram_data.get("barcode", [])
    fig, ax = plt.subplots(figsize=(8, 6))
    if points:
        births, deaths = zip(*points)
        ax.scatter(births, deaths, c="b", label="Persistence Points")
        ax.plot([min(births+deaths), max(births+deaths)], [min(births+deaths), max(births+deaths)], "k--", alpha=0.5)
    if barcode:
        for i, (birth, death) in enumerate(barcode):
            ax.hlines(i, birth, death, colors="r", lw=2)
        ax.set_ylim(-1, len(barcode))
    ax.set_xlabel("Birth")
    ax.set_ylabel("Death")
    ax.set_title(title)
    ax.legend()
    plt.tight_layout()
    if fig is not None:
        if output_path.endswith(".png"):
            fig.savefig(output_path)
        elif output_path.endswith(".html"):
            try:
                import mpld3
                if mpld3 is not None:
                    mpld3.save_html(fig, output_path)
                else:
                    logger.error("mpld3 object is None; saving PNG instead.")
                    fig.savefig(output_path.replace(".html", ".png"))
            except ImportError:
                logger.warning("mpld3 not installed, cannot export HTML, saving PNG instead.")
                fig.savefig(output_path.replace(".html", ".png"))
        plt.close(fig)
        return output_path
    else:
        logger.error("Matplotlib Figure object is None; skipping export.")
        return ""

def update_visualization_dashboard(output_dir: str = "analysis") -> str:
    """
    Generate or update the visualization index/dashboard at the end of an analysis run.
    Args:
        output_dir: Directory where visualizations are stored.
    Returns:
        Path to the generated index file.
    """
    index_path = generate_visualization_index(output_dir=output_dir)
    logger.info(f"Visualization dashboard updated: {index_path}")
    return index_path
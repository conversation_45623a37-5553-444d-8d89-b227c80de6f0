"""
BanditStage for PAT
==================

Integrates bandit (Python security analyzer) as a modular pipeline stage. Runs bandit on the codebase,
parses security issues, and updates ProjectMetrics/FileMetrics for downstream use.

Features:
    - Parallel processing for improved performance
    - Incremental scanning (only checks modified files)
    - Configurable number of processes

Related Files:
    - tool_stage.py (base class)
    - main.py (pipeline integration)
    - config.yaml (tool options)
"""

import concurrent.futures
import hashlib
import json
import subprocess
import time
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import multiprocessing
import os
import platform


class BanditStage(ToolStage):
    tool_name = "bandit"

    def _get_tool_path(self):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "bandit"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        return str(pat_venv_bin / tool_executable)

    def _get_file_hash(self, file_path: str) -> str:
        """Generate a hash of file contents and modification time for change detection."""
        try:
            file_stat = os.stat(file_path)
            mod_time = file_stat.st_mtime
            file_size = file_stat.st_size
            # Use file size and modification time as a quick hash
            return hashlib.md5(f"{file_path}:{mod_time}:{file_size}".encode()).hexdigest()
        except Exception as e:
            logger.warning(f"[BanditStage] Error getting file hash for {file_path}: {e}")
            # If we can't get the hash, return a unique value to force scanning
            return f"error-{time.time()}"

    def _load_cache(self) -> Dict[str, Dict[str, Any]]:
        """Load the cache of previously scanned files."""
        cache_path = Path(self.project_root) / ".pat_bandit_cache.json"
        if not cache_path.exists():
            return {}
        try:
            with open(cache_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"[BanditStage] Error loading cache: {e}")
            return {}

    def _save_cache(self, cache: Dict[str, Dict[str, Any]]):
        """Save the cache of scanned files."""
        cache_path = Path(self.project_root) / ".pat_bandit_cache.json"
        try:
            with open(cache_path, 'w') as f:
                json.dump(cache, f, indent=2)
        except Exception as e:
            logger.warning(f"[BanditStage] Error saving cache: {e}")

    @staticmethod
    def _process_file_static(file_data: Tuple[str, str, List[str], str]) -> Tuple[str, str, List[Dict[str, str]]]:
        """Process a single file with bandit (static method for multiprocessing).

        Args:
            file_data: Tuple of (file_path, bandit_executable, options, project_root)

        Returns:
            Tuple of (file_path, stdout, errors)
        """
        fpath, bandit_executable, options, project_root = file_data
        errors = []

        # Construct command with full path to executable
        # Ensure format is set to something parseable if not specified
        format_options = [o for o in options if o.startswith('--format=') or o == '-f']
        cmd = [bandit_executable, fpath]
        if not format_options:
            cmd.extend(['--format', 'txt'])  # Default to text for easier parsing
        cmd.extend(options)

        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=project_root)
            if result.returncode not in (0, 1):  # 0: success, 1: issues found
                error_msg = result.stderr.strip()
                logger.error(f"[BanditStage] Bandit failed for {fpath} (return code {result.returncode}): {error_msg}")
                errors.append({"file": fpath, "error": f"Bandit execution failed: {error_msg}"})
                return fpath, "", errors
            # Return stdout even if issues are found (return code 1)
            return fpath, result.stdout, errors
        except Exception as e:
            error_msg = f"Unexpected error running bandit for {fpath}: {e}"
            logger.error(f"[BanditStage] {error_msg}")
            errors.append({"file": fpath, "error": error_msg})
            return fpath, "", errors

    def _process_file(self, file_data: Tuple[str, str, List[str]]) -> Tuple[str, str, List[Dict[str, str]]]:
        """Process a single file with bandit (instance method wrapper).

        Args:
            file_data: Tuple of (file_path, bandit_executable, options)

        Returns:
            Tuple of (file_path, stdout, errors)
        """
        # Add project_root to the arguments
        fpath, bandit_executable, options = file_data
        return self._process_file_static((fpath, bandit_executable, options, self.project_root))

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run bandit in parallel on Python files, with incremental scanning and robust error handling.
        Interruptible via skip_controller.

        Returns:
            Dict with per-file results and errors.
        """
        # Load configuration
        force_full_scan = self.config.get("force_full_scan", False)
        parallel = self.config.get("parallel", True)
        max_workers = self.config.get("max_workers", None)  # None = use CPU count
        if max_workers is None:
            max_workers = max(1, multiprocessing.cpu_count() - 1)  # Leave one CPU free

        # Load cache for incremental scanning
        cache = {} if force_full_scan else self._load_cache()

        # Discover all Python files
        file_list = []
        for root, _, files in os.walk(self.project_root):
            # Respect .patignore
            ignore_path = Path(root) / ".patignore"
            ignored_files = set()
            if ignore_path.exists():
                with open(ignore_path, 'r') as f:
                    ignored_files = {Path(root) / line.strip() for line in f}

            for fname in files:
                if fname.endswith(".py"):
                    full_path = Path(root) / fname
                    if full_path not in ignored_files:
                        file_list.append(str(full_path))

        num_files = len(file_list)
        if num_files == 0:
            logger.warning("[BanditStage] No Python files found for analysis.")
            return {"error": "No Python files found for analysis."}

        # Filter files that haven't changed (incremental scanning)
        files_to_scan = []
        cached_results = {}

        for fpath in file_list:
            file_hash = self._get_file_hash(fpath)
            if not force_full_scan and fpath in cache and cache[fpath].get("hash") == file_hash:
                # File hasn't changed, use cached result
                cached_results[fpath] = cache[fpath].get("stdout", "")
            else:
                # File is new or has changed, scan it
                files_to_scan.append(fpath)

        # Update progress information
        scan_count = len(files_to_scan)
        cache_count = len(cached_results)
        total_count = scan_count + cache_count

        if force_full_scan:
            self.progress.start_phase("Bandit", total_steps=num_files)
            logger.info(f"[BanditStage] Running full scan on {num_files} files")
        else:
            self.progress.start_phase("Bandit", total_steps=total_count)
            logger.info(f"[BanditStage] Incremental scan: {scan_count} files to scan, {cache_count} files from cache")

            # Increment progress for cached files
            for _ in range(cache_count):
                self.progress.increment()

        # If no files to scan, return cached results
        if not files_to_scan:
            self.progress.complete_phase("Bandit", status="Complete! (All from cache)")
            return {"results": cached_results, "errors": [], "skipped": False, "cached": True}

        # Prepare for scanning
        results = {}
        errors = []
        interrupted = False
        options = self.config.get("options", "").split()
        bandit_executable = self._get_tool_path()

        # Add cached results to results
        results.update(cached_results)

        # Check if bandit executable exists
        if not os.path.exists(bandit_executable):
            error_msg = f"Bandit executable not found at '{bandit_executable}'. Ensure PAT setup completed correctly."
            logger.error(f"[BanditStage] {error_msg}")
            self.progress.complete_phase("Bandit", status="Failed: Executable not found")
            return {"error": error_msg, "skipped": True}

        # Process files in parallel or sequentially
        if parallel and scan_count > 1:
            # Prepare arguments for parallel processing
            process_args = [(fpath, bandit_executable, options, self.project_root) for fpath in files_to_scan]

            # Process files in parallel
            with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
                futures = []
                for args in process_args:
                    if skip_controller and skip_controller.should_skip():
                        print("[BanditStage] Skip requested by user. Exiting phase early.")
                        interrupted = True
                        break
                    futures.append(executor.submit(self._process_file_static, args))

                # Process results as they complete
                for future in concurrent.futures.as_completed(futures):
                    if skip_controller and skip_controller.should_skip():
                        print("[BanditStage] Skip requested by user. Exiting phase early.")
                        interrupted = True
                        break

                    try:
                        fpath, stdout, file_errors = future.result()
                        if stdout:  # Only add if we got output
                            results[fpath] = stdout
                            # Update cache
                            cache[fpath] = {
                                "hash": self._get_file_hash(fpath),
                                "stdout": stdout,
                                "timestamp": time.time()
                            }
                        errors.extend(file_errors)
                        self.progress.increment()
                    except Exception as e:
                        logger.error(f"[BanditStage] Error processing future: {e}")
                        errors.append({"file": "unknown", "error": f"Error processing future: {e}"})
                        self.progress.increment()
        else:
            # Process files sequentially
            for fpath in files_to_scan:
                if skip_controller and skip_controller.should_skip():
                    print("[BanditStage] Skip requested by user. Exiting phase early.")
                    interrupted = True
                    break

                fpath, stdout, file_errors = self._process_file((fpath, bandit_executable, options))
                if stdout:  # Only add if we got output
                    results[fpath] = stdout
                    # Update cache
                    cache[fpath] = {
                        "hash": self._get_file_hash(fpath),
                        "stdout": stdout,
                        "timestamp": time.time()
                    }
                errors.extend(file_errors)
                self.progress.increment()

        # Save cache
        if not interrupted and not force_full_scan:
            self._save_cache(cache)

        # Complete progress
        status = "Complete!" if not interrupted else "Skipped or Failed Early"
        if not interrupted and cache_count > 0:
            status += f" ({cache_count} from cache, {scan_count} scanned)"
        self.progress.complete_phase("Bandit", status=status)

        # Log summary
        processed_count = len(results)
        print(f"\n[BanditStage] {processed_count} files processed ({cache_count} from cache, {processed_count - cache_count} scanned). {len(errors)} files failed. See log for details.")
        return {"results": results, "errors": errors, "skipped": interrupted}

    def parse_output(self, output: Any) -> None:
        """
        Parse Bandit output and update metrics with security issues and summary.
        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['bandit'] includes: issues (list), summary (str), raw_output (str or dict)
            - FileMetrics.tool_results['bandit'] (for all files): issues (list), summary (str)
        Args:
            output: Dict with per-file results and errors
        """
        if isinstance(output, dict) and output.get("skipped"):
            self.metrics.tool_results["bandit"] = output
            return
        if not isinstance(output, dict):
            return
        issues = []
        file_issue_map: Dict[str, list] = {}
        for fpath, out in output.get("results", {}).items():
            if not isinstance(out, str):
                continue
            for line in out.splitlines():
                if line.strip() and ("[" in line or "severity" in line.lower()):
                    issues.append(line)
                    file_issue_map.setdefault(fpath, []).append(line)
        summary = f"{len(issues)} issues"
        self.metrics.tool_results["bandit"] = {
            "issues": issues,
            "summary": summary,
            "raw_output": output,
        }
        for key, file_metrics in self.metrics.files.items():
            abs_key = str(Path(file_metrics.path).resolve())
            file_issues = file_issue_map.get(abs_key, [])
            file_metrics.tool_results["bandit"] = {
                "issues": file_issues,
                "summary": f"{len(file_issues)} issues"
            }

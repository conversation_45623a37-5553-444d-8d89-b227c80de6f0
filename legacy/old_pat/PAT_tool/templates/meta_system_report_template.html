<!DOCTYPE html>
<html>
<head>
    <title>Meta-System Separation Analysis - {project_name}</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
        }
        .container {
            display: flex;
            height: 100vh;
        }
        .graph {
            flex: 2;
            border-right: 1px solid #ccc;
        }
        .details {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }
        .node {
            cursor: pointer;
        }
        .link {
            stroke-width: 2px;
        }
        .violation {
            stroke: red !important;
        }
        .violation-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
        }
        .violation-item h4 {
            margin-top: 0;
        }
        .meta-system {
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            display: inline-block;
            margin-right: 5px;
        }
        .PC { background-color: #1f77b4; }
        .AN { background-color: #2ca02c; }
        .PR { background-color: #d62728; }
        .SIO { background-color: #9467bd; }
        .UI { background-color: #ff7f0e; }
        .CORE { background-color: #7f7f7f; }
        .OTHER { background-color: #c7c7c7; }
        .summary {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .legend {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 15px;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin-right: 15px;
            margin-bottom: 5px;
        }
        .legend-color {
            width: 15px;
            height: 15px;
            margin-right: 5px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="graph" id="graph"></div>
        <div class="details">
            <h2>Meta-System Separation Analysis</h2>
            <div class="summary">
                <h3>Summary</h3>
                <p><strong>{violation_count}</strong> meta-system separation violations found.</p>
            </div>

            <div class="legend">
                <h3>Meta-Systems</h3>
                <div style="display: flex; flex-wrap: wrap;">
                    <div class="legend-item">
                        <div class="legend-color PC"></div>
                        <div>PC (Persona Core)</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color AN"></div>
                        <div>AN (Analyst)</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color PR"></div>
                        <div>PR (Predictor)</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color SIO"></div>
                        <div>SIO (Shared I/O)</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color UI"></div>
                        <div>UI (User Interface)</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color CORE"></div>
                        <div>CORE</div>
                    </div>
                </div>
            </div>

            <h3>Violations</h3>
            <div id="violations-list">
                {violations_html}
            </div>
        </div>
    </div>

    <script>
    // Graph data
    const nodes = {nodes_json};
    const links = {links_json};

    // Set up the SVG
    const width = document.getElementById('graph').clientWidth;
    const height = document.getElementById('graph').clientHeight;

    const svg = d3.select('#graph')
        .append('svg')
        .attr('width', width)
        .attr('height', height);

    // Define arrow markers
    svg.append('defs').append('marker')
        .attr('id', 'arrowhead')
        .attr('viewBox', '-0 -5 10 10')
        .attr('refX', 20)
        .attr('refY', 0)
        .attr('orient', 'auto')
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .attr('xoverflow', 'visible')
        .append('svg:path')
        .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
        .attr('fill', '#999')
        .style('stroke', 'none');

    svg.append('defs').append('marker')
        .attr('id', 'arrowhead-violation')
        .attr('viewBox', '-0 -5 10 10')
        .attr('refX', 20)
        .attr('refY', 0)
        .attr('orient', 'auto')
        .attr('markerWidth', 6)
        .attr('markerHeight', 6)
        .attr('xoverflow', 'visible')
        .append('svg:path')
        .attr('d', 'M 0,-5 L 10 ,0 L 0,5')
        .attr('fill', 'red')
        .style('stroke', 'none');

    // Create a force simulation
    const simulation = d3.forceSimulation(nodes)
        .force('link', d3.forceLink(links).id(function(d) { return d.id; }).distance(150))
        .force('charge', d3.forceManyBody().strength(-500))
        .force('center', d3.forceCenter(width / 2, height / 2))
        .force('collision', d3.forceCollide().radius(60));

    // Create the links
    const link = svg.append('g')
        .selectAll('line')
        .data(links)
        .enter().append('path')
        .attr('class', function(d) { return d.is_violation ? 'link violation' : 'link'; })
        .attr('stroke', function(d) { return d.is_violation ? 'red' : '#999'; })
        .attr('marker-end', function(d) { return d.is_violation ? 'url(#arrowhead-violation)' : 'url(#arrowhead)'; });

    // Create the nodes
    const node = svg.append('g')
        .selectAll('g')
        .data(nodes)
        .enter().append('g')
        .attr('class', 'node')
        .call(d3.drag()
            .on('start', dragstarted)
            .on('drag', dragged)
            .on('end', dragended));

    // Add circles to the nodes
    node.append('circle')
        .attr('r', function(d) { return 30 + (d.imports + d.imported_by) * 3; })
        .attr('fill', function(d) {
            var colors = {
                'PC': '#1f77b4',
                'AN': '#2ca02c',
                'PR': '#d62728',
                'SIO': '#9467bd',
                'UI': '#ff7f0e',
                'CORE': '#7f7f7f',
                'OTHER': '#c7c7c7'
            };
            return colors[d.id] || '#c7c7c7';
        })
        .attr('stroke', '#fff')
        .attr('stroke-width', 2);

    // Add labels to the nodes
    node.append('text')
        .attr('text-anchor', 'middle')
        .attr('dy', '.35em')
        .attr('fill', 'white')
        .attr('font-weight', 'bold')
        .text(function(d) { return d.id; });

    // Add title for tooltip
    node.append('title')
        .text(function(d) { return d.id + '\nImports: ' + d.imports + '\nImported by: ' + d.imported_by; });

    // Update positions on each tick
    simulation.on('tick', function() {
        link.attr('d', function(d) {
            var dx = d.target.x - d.source.x;
            var dy = d.target.y - d.source.y;
            var dr = Math.sqrt(dx * dx + dy * dy) * 1.5; // Curve factor
            return 'M' + d.source.x + ',' + d.source.y + 'A' + dr + ',' + dr + ' 0 0,1 ' + d.target.x + ',' + d.target.y;
        });
        
        node.attr('transform', function(d) {
            // Keep nodes within bounds
            d.x = Math.max(50, Math.min(width - 50, d.x));
            d.y = Math.max(50, Math.min(height - 50, d.y));
            return 'translate(' + d.x + ',' + d.y + ')';
        });
    });
    
    // Drag functions
    function dragstarted(event, d) {
        if (!event.active) simulation.alphaTarget(0.3).restart();
        d.fx = d.x;
        d.fy = d.y;
    }
    
    function dragged(event, d) {
        d.fx = event.x;
        d.fy = event.y;
    }
    
    function dragended(event, d) {
        if (!event.active) simulation.alphaTarget(0);
        d.fx = null;
        d.fy = null;
    }
    
    // Highlight connected nodes on hover
    node.on('mouseover', function(event, d) {
        // Highlight links connected to this node
        link.style('stroke-opacity', function(l) {
            if (l.source.id === d.id || l.target.id === d.id) {
                return 1;
            } else {
                return 0.2;
            }
        });
        
        // Highlight connected nodes
        node.style('opacity', function(n) {
            var connected = links.some(function(l) {
                return (l.source.id === d.id && l.target.id === n.id) || 
                       (l.target.id === d.id && l.source.id === n.id);
            });
            return n.id === d.id || connected ? 1 : 0.2;
        });
        
        // Highlight related violations
        d3.selectAll('.violation-item').style('opacity', 0.3);
        d3.selectAll('.violation-' + d.id).style('opacity', 1);
    });
    
    node.on('mouseout', function() {
        // Reset highlights
        link.style('stroke-opacity', 1);
        node.style('opacity', 1);
        d3.selectAll('.violation-item').style('opacity', 1);
    });
    </script>
</body>
</html>

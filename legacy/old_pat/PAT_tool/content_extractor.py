"""
Content Extraction Stage for PAT
===============================

Extracts and summarizes docstrings, top-level definitions, and architectural roles
from Python files. Designed as a modular pipeline stage for use in the PAT analysis pipeline.

Related Files:
    - main.py (pipeline integration)
    - models.py (FileMetrics)

External Dependencies:
    - Python 3.8+
    - ast
"""

import ast
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
    from PAT_tool.pipeline import AnalysisStage
    from PAT_tool.utils import (is_excluded_path, load_exclusion_patterns,
                                logger, safe_execution)
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
    from pipeline import AnalysisStage
    from utils import (is_excluded_path, load_exclusion_patterns, logger,
                       safe_execution)

class ContentExtractor:
    """
    Pipeline stage that extracts docstrings, definitions, and architectural roles from Python files.
    Updates FileMetrics with extracted content for downstream use (summarization, LLM prompts, etc.).

    Supports exclusion patterns from a .patignore file at the project root (standard .gitignore-like syntax).
    """
    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress: Any):
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        self.excluded_patterns = load_exclusion_patterns(project_root)

    def run(self, project_root: Path, metrics: ProjectMetrics, progress: Any, **kwargs) -> None:
        """
        Execute content extraction for all Python files in the project.

        Args:
            project_root: Path to the project root directory.
            metrics: Shared ProjectMetrics object for storing results.
            progress: progress tracker instance for reporting progress.
        """
        py_files = [f for f in metrics.files.values() if f.path.endswith('.py') and not is_excluded_path(project_root / f.path, self.excluded_patterns, project_root)]
        progress.start_phase("Extracting content and docstrings", len(py_files))
        for file_metric in py_files:
            self._extract_content(file_metric)
            progress.increment()
        progress.complete_phase("Content extraction")

    @safe_execution
    def _extract_content(self, file_metric: FileMetrics) -> None:
        """
        Extract docstrings, definitions, and roles from a single Python file.

        Args:
            file_metric: FileMetrics instance to update
        """
        file_path = self.project_root / file_metric.path
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                source = f.read()
        except Exception as e:
            logger.warning(f"Could not read {file_path}: {e}")
            return
        try:
            tree = ast.parse(source)
        except Exception as e:
            logger.warning(f"Could not parse {file_path}: {e}")
            return
        # Extract module docstring
        module_doc = ast.get_docstring(tree)
        file_metric.docstrings = {'module': module_doc}
        # Extract top-level classes and functions
        classes = []
        functions = []
        class_docs = {}
        func_docs = {}
        for node in tree.body:
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
                class_docs[node.name] = ast.get_docstring(node)
            elif isinstance(node, ast.FunctionDef):
                functions.append(node.name)
                func_docs[node.name] = ast.get_docstring(node)
        file_metric.classes = classes
        file_metric.functions = functions
        file_metric.docstrings['classes'] = class_docs
        file_metric.docstrings['functions'] = func_docs
        # Tag architectural role (simple heuristics)
        file_metric.roles = self._tag_roles(file_metric)

    def _tag_roles(self, file_metric: FileMetrics) -> str:
        """
        Heuristically tag the architectural role of a file (PC, AN, PR, SIO, utility, test, etc.).
        Args:
            file_metric: FileMetrics instance
        Returns:
            Role string
        """
        path = file_metric.path.lower()
        if 'test' in path or file_metric.is_test:
            return 'test'
        if 'utility' in path or 'utils' in path:
            return 'utility'
        if 'persona_core' in path or 'pc' in path:
            return 'PC'
        if 'analyst' in path or 'an' in path:
            return 'AN'
        if 'predictor' in path or 'pr' in path:
            return 'PR'
        if 'io_layer' in path or 'sio' in path:
            return 'SIO'
        return 'other'

    def generate(self, project_root: Path = None, metrics: ProjectMetrics = None, progress: Any = None, **kwargs) -> None:
        """
        Alias for run, to support pipeline expectations.
        """
        # Use instance attributes if arguments are not provided
        project_root = project_root or self.project_root
        metrics = metrics or self.metrics
        progress = progress or self.progress
        self.run(project_root, metrics, progress, **kwargs)
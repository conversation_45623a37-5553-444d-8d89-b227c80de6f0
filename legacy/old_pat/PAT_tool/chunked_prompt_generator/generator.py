"""
Main ChunkedPromptGenerator class implementation.

This module contains the main ChunkedPromptGenerator class that orchestrates
the prompt generation process.
"""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional

try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
    from PAT_tool.pipeline import AnalysisStage
    from PAT_tool.utils import ensure_dir, logger
    from .content_collector import collect_content, file_needs_attention
    from .chunk_builder import format_block, has_meaningful_content
    from .manifest import build_chunk_manifest
    from .verification import insert_verification_prompts, generate_project_diagnostics_prompt
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
    from pipeline import AnalysisStage
    from utils import ensure_dir, logger
    from content_collector import collect_content, file_needs_attention
    from chunk_builder import format_block, has_meaningful_content
    from manifest import build_chunk_manifest
    from verification import insert_verification_prompts, generate_project_diagnostics_prompt


class ChunkedPromptGenerator:
    """
    Pipeline stage that generates LLM-ready prompt chunks from codebase analysis results.
    Inserts a verification/recheck prompt every 3 chunks.
    """
    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress: Any,
                 chunk_size: int = 12000, overlap: int = 1000, output_dir: Optional[str] = None,
                 verify_interval: int = 3):
        """
        Args:
            project_root: Path to the project root directory.
            metrics: Shared ProjectMetrics object for storing results.
            progress: progress tracker instance for reporting progress.
            chunk_size: Target chunk size in tokens/characters (default: 12,000 chars ~4k tokens).
            overlap: Overlap size between chunks (default: 1,000 chars).
            output_dir: Directory to write prompt chunks and manifest (default: PAT_output/prompts)
            verify_interval: Interval between verification prompts (default: 3 chunks)
        """
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        self.chunk_size = chunk_size
        self.overlap = overlap
        self.output_dir = output_dir or str(project_root / "PAT_output" / "prompts")
        self.verify_interval = verify_interval
        ensure_dir(self.output_dir)

    def run(self, *args, **kwargs) -> None:
        """Generate LLM prompt chunks and manifest from analyzed codebase.

        Args:
            *args: Ignored. Present for pipeline compatibility.
            **kwargs: Ignored. Present for pipeline compatibility.
        """
        # Ensure output directory exists with proper permissions
        try:
            os.makedirs(self.output_dir, exist_ok=True)
            # Test write permissions by creating a test file
            test_file = os.path.join(self.output_dir, "test_write_permission.txt")
            with open(test_file, "w") as f:
                f.write("Test write permission")
            os.remove(test_file)  # Clean up test file
            logger.info(f"Output directory {self.output_dir} exists and is writable")
        except Exception as e:
            logger.error(f"Error with output directory {self.output_dir}: {e}")
            # Try to create an alternative output directory
            alt_output_dir = os.path.join(os.path.dirname(self.output_dir), "prompts_alt")
            try:
                os.makedirs(alt_output_dir, exist_ok=True)
                self.output_dir = alt_output_dir
                logger.info(f"Using alternative output directory: {self.output_dir}")
            except Exception as e2:
                logger.error(f"Error creating alternative output directory: {e2}")
                # Continue anyway, but log the error

        files = list(self.metrics.files.values())
        files.sort(key=lambda f: (f.roles, f.path))  # Group by role, then path
        all_content = collect_content(files)
        logger.info(f"Collected content from {len(all_content)} files that need attention")

        # --- Project-level grouped diagnostics prompt ---
        project_diag_chunk, project_diag_meta = generate_project_diagnostics_prompt(files)
        chunks, manifest = self._chunk_content(all_content)
        logger.info(f"Generated {len(chunks)} content chunks")

        # Insert verification prompts every N chunks
        chunks_with_verification, manifest_with_verification = insert_verification_prompts(
            chunks, manifest, self.verify_interval
        )
        # Insert project diagnostics as the first chunk and manifest entry
        chunks_with_verification = [project_diag_chunk] + chunks_with_verification
        manifest_with_verification = [project_diag_meta] + manifest_with_verification

        # Write chunks and manifest
        self._write_chunks(chunks_with_verification)
        self._write_manifest(manifest_with_verification)
        logger.info(f"Generated {len(chunks_with_verification)} LLM prompt chunks (including verification prompts and project diagnostics) in {self.output_dir}")

    def _chunk_content(self, content_blocks: List[Dict[str, Any]]) -> (List[str], List[Dict[str, Any]]):
        """
        Chunks the content blocks into LLM-sized prompt windows with one file per chunk.
        Returns a list of chunk strings and a manifest of chunk metadata.
        Enhanced: Adds per-chunk issue area summaries, CAW guidance, and review metadata.
        Filters out chunks that don't have any meaningful content.
        """
        chunks = []
        manifest = []

        # If no content blocks, create a default chunk
        if not content_blocks:
            logger.warning("No files met the criteria for prompt generation. Creating a default chunk.")
            default_text = "# No Files Met Prompt Generation Criteria\n\n"
            default_text += "No files in the codebase met the criteria for prompt generation. "
            default_text += "This could be because:\n\n"
            default_text += "1. The codebase is very clean with no issues\n"
            default_text += "2. The thresholds for prompt generation are too high\n"
            default_text += "3. The analysis tools didn't find any issues\n\n"
            default_text += "Consider lowering the thresholds in the `_file_needs_attention` method "
            default_text += "or running more analysis tools."

            chunks.append(default_text)
            manifest.append({
                "files": [],
                "issue_areas": [],
                "issue_counts": {},
                "caw_guidance": {},
                "caw_priority": 0,
                "review_required": False,
                "chunk_index": 0,
                "chunk_file": "chunk_001.md",
                "entities": [],
                "diagnostics": [],
                "top_issues": {},
            })
            return chunks, manifest

        # Process one file per chunk to ensure clean boundaries
        for i, block in enumerate(content_blocks):
            block_text = format_block(block)

            # Log the block path and whether it has meaningful content
            logger.debug(f"Processing block {i}: {block.get('path', 'Unknown')} - "
                        f"Has text: {bool(block_text)}, "
                        f"Has meaningful content: {has_meaningful_content(block_text) if block_text else False}")

            # Skip empty blocks or blocks with no issues
            if not block_text:
                logger.debug(f"Skipping block {i}: Empty block text")
                continue

            if not has_meaningful_content(block_text):
                logger.debug(f"Skipping block {i}: No meaningful content")
                continue

            # Create a chunk for this file
            current_meta = {"files": [block["path"]], "start": i, "end": i}
            chunk_idx = len(chunks)
            chunk_manifest = build_chunk_manifest(block_text, current_meta["files"], chunk_idx)

            # Always add the chunk if it has meaningful content (relaxed criteria)
            chunks.append(block_text)
            manifest.append(chunk_manifest)
            logger.debug(f"Added chunk {chunk_idx} for {block.get('path', 'Unknown')}")

        # If still no chunks after processing, create a default chunk
        if not chunks:
            logger.warning("No chunks were generated after processing all blocks. Creating a default chunk.")
            default_text = "# No Meaningful Content Found\n\n"
            default_text += "No files in the codebase had meaningful content for prompt generation. "
            default_text += "This could be because:\n\n"
            default_text += "1. The `_has_meaningful_content` method is too strict\n"
            default_text += "2. The `_format_block` method isn't generating proper content\n"
            default_text += "3. There are issues with the analysis results\n\n"

            chunks.append(default_text)
            manifest.append({
                "files": [],
                "issue_areas": [],
                "issue_counts": {},
                "caw_guidance": {},
                "caw_priority": 0,
                "review_required": False,
                "chunk_index": 0,
                "chunk_file": "chunk_001.md",
                "entities": [],
                "diagnostics": [],
                "top_issues": {},
            })

        return chunks, manifest

    def _write_chunks(self, chunks: List[str]) -> None:
        """Write chunks to files in the output directory."""
        for i, chunk in enumerate(chunks):
            chunk_file = os.path.join(self.output_dir, f"chunk_{i+1:03d}.md")
            try:
                with open(chunk_file, "w", encoding="utf-8") as f:
                    f.write(chunk)
            except Exception as e:
                logger.error(f"Error writing chunk {i+1} to {chunk_file}: {e}")

    def _write_manifest(self, manifest: List[Dict[str, Any]]) -> None:
        """Write manifest to a JSON file in the output directory."""
        import json
        manifest_file = os.path.join(self.output_dir, "manifest.json")
        try:
            with open(manifest_file, "w", encoding="utf-8") as f:
                # Handle sets in the manifest by converting to lists
                json.dump(manifest, f, indent=2, default=lambda obj: list(obj) if isinstance(obj, set) else obj)
        except Exception as e:
            logger.error(f"Error writing manifest to {manifest_file}: {e}")

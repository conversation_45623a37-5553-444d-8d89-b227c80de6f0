"""
Manifest generation functions for the chunked prompt generator.

This module contains functions to build and manage chunk manifests.
"""

from collections import Counter
from typing import Any, Dict, List, Set


def build_chunk_manifest(chunk_text: str, file_paths: List[str], chunk_idx: int) -> Dict[str, Any]:
    """
    Build a manifest entry for a chunk, including issue areas, counts, CAW guidance, 
    priority, review flag, chunk file, entities, diagnostics, and top issues.
    
    Args:
        chunk_text: The text content of the chunk
        file_paths: List of file paths included in the chunk
        chunk_idx: Index of the chunk
        
    Returns:
        Dictionary containing manifest metadata
    """
    # Aggregate diagnostics for all files in this chunk
    issue_areas = []
    issue_counts = {}
    caw_guidance = {}
    caw_priority = 0
    review_required = False
    diagnostics = []
    entities = set()
    top_issues = {}
    
    # Extract entities from the chunk text
    entities.update(extract_entities_from_chunk(chunk_text))
    
    # Extract diagnostics from the chunk text
    diagnostics = extract_diagnostics_from_chunk(chunk_text)
    
    # Determine issue areas and counts
    issue_areas, issue_counts = extract_issue_areas(chunk_text)
    
    # Determine CAW guidance
    caw_guidance = extract_caw_guidance(chunk_text)
    
    # Determine CAW priority
    caw_priority = calculate_caw_priority(issue_areas, issue_counts)
    
    # Determine if review is required
    review_required = determine_review_required(issue_areas, issue_counts)
    
    # Determine top issues
    top_issues = extract_top_issues(chunk_text)
    
    return {
        "files": file_paths,
        "issue_areas": issue_areas,
        "issue_counts": issue_counts,
        "caw_guidance": caw_guidance,
        "caw_priority": caw_priority,
        "review_required": review_required,
        "chunk_index": chunk_idx,
        "chunk_file": f"chunk_{chunk_idx+1:03d}.md",
        "entities": list(entities),
        "diagnostics": diagnostics,
        "top_issues": top_issues,
    }


def extract_entities_from_chunk(chunk_text: str) -> Set[str]:
    """
    Extract entities (classes, functions) from the chunk text.
    
    Args:
        chunk_text: The text content of the chunk
        
    Returns:
        Set of entity names
    """
    entities = set()
    
    # Extract classes
    if "## Classes" in chunk_text:
        classes_section = chunk_text.split("## Classes")[1].split("##")[0]
        for line in classes_section.split("\n"):
            if line.strip().startswith("- **") and "**" in line[3:]:
                entity = line.split("**")[1]
                entities.add(entity)
    
    # Extract functions
    if "## Functions" in chunk_text:
        functions_section = chunk_text.split("## Functions")[1].split("##")[0]
        for line in functions_section.split("\n"):
            if line.strip().startswith("- **") and "**" in line[3:]:
                entity = line.split("**")[1]
                entities.add(entity)
    
    return entities


def extract_diagnostics_from_chunk(chunk_text: str) -> List[Dict[str, Any]]:
    """
    Extract diagnostics from the chunk text.
    
    Args:
        chunk_text: The text content of the chunk
        
    Returns:
        List of diagnostic dictionaries
    """
    diagnostics = []
    
    # Extract file path
    file_path = ""
    if "# File: " in chunk_text:
        file_path = chunk_text.split("# File: ")[1].split("\n")[0].strip()
    
    # Extract diagnostics
    if "## Diagnostics" in chunk_text:
        diagnostics_section = chunk_text.split("## Diagnostics")[1].split("##")[0]
        for line in diagnostics_section.split("\n"):
            if line.strip().startswith("- **") and "**" in line[3:]:
                issue_type = line.split("**")[1]
                value = line.split("**: ")[1].strip() if "**: " in line else ""
                
                # Map issue type to severity
                severity = "info"
                if "Error" in issue_type or "Security" in issue_type:
                    severity = "error"
                elif "Issue" in issue_type or "Coverage" in issue_type:
                    severity = "warning"
                
                diagnostics.append({
                    "file": file_path,
                    "issue_area": issue_type,
                    "severity": severity,
                    "summary": f"{issue_type}: {value}"
                })
    
    return diagnostics


def extract_issue_areas(chunk_text: str) -> (List[str], Dict[str, int]):
    """
    Extract issue areas and counts from the chunk text.
    
    Args:
        chunk_text: The text content of the chunk
        
    Returns:
        Tuple of (list of issue areas, dictionary of issue counts)
    """
    issue_areas = []
    issue_counts = {}
    
    # Extract diagnostics
    if "## Diagnostics" in chunk_text:
        diagnostics_section = chunk_text.split("## Diagnostics")[1].split("##")[0]
        for line in diagnostics_section.split("\n"):
            if line.strip().startswith("- **") and "**" in line[3:]:
                issue_type = line.split("**")[1]
                value_str = line.split("**: ")[1].strip() if "**: " in line else ""
                
                # Convert value to int if possible
                try:
                    value = int(value_str)
                except (ValueError, TypeError):
                    if value_str.lower() == "yes":
                        value = 1
                    elif value_str.endswith("%"):
                        try:
                            value = float(value_str.rstrip("%"))
                        except (ValueError, TypeError):
                            value = 1
                    else:
                        value = 1
                
                issue_areas.append(issue_type)
                issue_counts[issue_type] = value
    
    return issue_areas, issue_counts


def extract_caw_guidance(chunk_text: str) -> Dict[str, str]:
    """
    Extract CAW guidance from the chunk text.
    
    Args:
        chunk_text: The text content of the chunk
        
    Returns:
        Dictionary of CAW guidance
    """
    caw_guidance = {}
    
    # Extract CAW Reflection
    if "**CAW Reflection:**" in chunk_text:
        reflection_section = chunk_text.split("**CAW Reflection:**")[1].split("**")[0]
        caw_guidance["reflection"] = reflection_section.strip()
    
    # Extract CAW-Aligned Next Steps
    if "**CAW-Aligned Next Steps:**" in chunk_text:
        next_steps_section = chunk_text.split("**CAW-Aligned Next Steps:**")[1].split("**")[0]
        caw_guidance["next_steps"] = next_steps_section.strip()
    
    return caw_guidance


def calculate_caw_priority(issue_areas: List[str], issue_counts: Dict[str, int]) -> int:
    """
    Calculate CAW priority based on issue areas and counts.
    
    Args:
        issue_areas: List of issue areas
        issue_counts: Dictionary of issue counts
        
    Returns:
        CAW priority (0-5)
    """
    priority = 0
    
    # High priority issues
    high_priority_issues = ["Security Issues", "Type Errors", "Circular Import"]
    for issue in high_priority_issues:
        if issue in issue_areas:
            priority += 1
    
    # Medium priority issues with high counts
    medium_priority_issues = ["Lint Issues", "Pylint Issues"]
    for issue in medium_priority_issues:
        if issue in issue_areas and issue_counts.get(issue, 0) > 5:
            priority += 1
    
    # Cap priority at 5
    return min(priority, 5)


def determine_review_required(issue_areas: List[str], issue_counts: Dict[str, int]) -> bool:
    """
    Determine if review is required based on issue areas and counts.
    
    Args:
        issue_areas: List of issue areas
        issue_counts: Dictionary of issue counts
        
    Returns:
        True if review is required, False otherwise
    """
    # Review is required for security issues
    if "Security Issues" in issue_areas:
        return True
    
    # Review is required for high complexity
    if "High Complexity" in issue_areas and issue_counts.get("High Complexity", 0) > 15:
        return True
    
    # Review is required for many type errors
    if "Type Errors" in issue_areas and issue_counts.get("Type Errors", 0) > 10:
        return True
    
    # Review is required for circular imports
    if "Circular Import" in issue_areas:
        return True
    
    return False


def extract_top_issues(chunk_text: str) -> Dict[str, Any]:
    """
    Extract top issues from the chunk text.
    
    Args:
        chunk_text: The text content of the chunk
        
    Returns:
        Dictionary of top issues
    """
    # Extract diagnostics
    if "## Diagnostics" in chunk_text:
        diagnostics_section = chunk_text.split("## Diagnostics")[1].split("##")[0]
        
        # Count issue types
        issue_counter = Counter()
        for line in diagnostics_section.split("\n"):
            if line.strip().startswith("- **") and "**" in line[3:]:
                issue_type = line.split("**")[1]
                issue_counter[issue_type] += 1
        
        # Get top 3 issues
        top_issues = {}
        for issue_type, count in issue_counter.most_common(3):
            top_issues[issue_type] = count
        
        return top_issues
    
    return {}

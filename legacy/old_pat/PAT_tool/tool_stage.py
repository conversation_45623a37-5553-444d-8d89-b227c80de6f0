"""
ToolStage Base Class for PAT
===========================

Provides a standard interface for integrating external analysis tools (mypy, ruff, bandit, etc.)
as modular pipeline stages. Supports enable/disable, configurable options, and structured output parsing.

Usage:
    - Subclass ToolStage for each tool (e.g., MypyStage, RuffStage).
    - Implement the run_tool and parse_output methods.
    - Use config to control tool options and enable/disable.

Related Files:
    - pipeline.py (AnalysisStage protocol)
    - main.py (pipeline integration)
    - config.yaml (optional, for tool options)
"""

from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
    from PAT_tool.pipeline import AnalysisStage
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from models import ProjectMetrics
    from pipeline import AnalysisStage
    from utils import logger

class ToolStage(AnalysisStage):
    """
    Base class for external tool integration as a pipeline stage.
    Supports interruptible execution via skip_controller.
    """
    tool_name: str = "external_tool"
    enabled: bool = True
    config: Dict[str, Any] = {}

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress: Any, config: Optional[Dict[str, Any]] = None):
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        if config is not None:
            self.config = config
        self.enabled = self.config.get("enabled", True)

    def run(self, project_root: Path, metrics: ProjectMetrics, progress: Any, **kwargs) -> None:
        """
        Run the tool if enabled, parse output, and update metrics. Logs subprocess start/end and command.
        Supports skipping via skip_controller if provided in kwargs.

        Args:
            project_root: Path to the project root directory
            metrics: Project metrics to update
            progress: Progress tracker for reporting
            **kwargs: Additional keyword arguments, including skip_controller if available
        """
        skip_controller = kwargs.get('skip_controller', None)

        # Check for skip request before starting
        if skip_controller and skip_controller.should_skip():
            print(f"[{self.tool_name}] Skip requested by user. Skipping phase.")
            skipped_result = {"skipped": True, "summary": "Phase skipped by user during execution."}
            metrics.tool_results[self.tool_name] = skipped_result
            progress.start_phase(f"{self.tool_name}", total_steps=1)
            progress.complete_phase(f"{self.tool_name}", status="Skipped by user")
            return

        if not self.enabled:
            progress.start_phase(f"Skipping {self.tool_name} (disabled)")
            progress.complete_phase(f"{self.tool_name} (skipped)")
            return

        progress.start_phase(f"Running {self.tool_name}")
        logger.info(f"[ToolStage] Starting {self.tool_name} subprocess...")
        try:
            # Pass skip_controller to run_tool if the subclass supports it
            if hasattr(self, 'run_tool') and 'skip_controller' in self.run_tool.__code__.co_varnames:
                output = self.run_tool(skip_controller=skip_controller)
            else:
                output = self.run_tool()

            # Check for skip after run_tool if the subclass doesn't handle it
            if skip_controller and skip_controller.should_skip() and not (isinstance(output, dict) and output.get('skipped')):
                print(f"[{self.tool_name}] Skip requested by user after execution.")
                output = {"skipped": True, "summary": "Phase skipped by user after execution."}
        finally:
            logger.info(f"[ToolStage] Finished {self.tool_name} subprocess.")

        self.parse_output(output)
        progress.complete_phase(f"{self.tool_name}")

    def run_tool(self, skip_controller=None) -> Any:
        """
        Run the external tool (to be implemented by subclasses).

        Args:
            skip_controller: Optional controller to check for skip requests

        Returns:
            Raw output from the tool (str, dict, etc.) or a dict with 'skipped' flag if interrupted
        """
        raise NotImplementedError("Subclasses must implement run_tool()")

    def parse_output(self, output: Any) -> None:
        """
        Parse the tool output and update metrics (to be implemented by subclasses).
        Should handle skip cases (dict with 'skipped' key) appropriately.

        Args:
            output: Raw output from the tool or skip info dict
        """
        raise NotImplementedError("Subclasses must implement parse_output()")
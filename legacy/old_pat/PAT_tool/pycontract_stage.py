"""
pycontract_stage.py

Pipeline stage for integrating PyContract (runtime contract verification) into the PAT analysis pipeline.

- Purpose: Run PyContract to check runtime contracts (pre/postconditions, invariants) for context/capability adherence, collect contract violations and summaries, and update ProjectMetrics.
- Related: effect_overlay_stage.py, tool_stage.py, models.py
- Dependencies: pycontract (install via pip), Python 3.8+, subprocess
"""

import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class PyContractStage(ToolStage):
    """Pipeline stage for running PyContract and collecting contract verification diagnostics.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress (ProgressTracker): Progress tracker for reporting.
        config (Optional[Dict[str, Any]]): Optional config for PyContract.
    """
    tool_name = "pycontract"

    def _get_tool_path(self):
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "pycontract"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_executable} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Any:
        """Run PyContract via subprocess and return output."""
        if skip_controller and skip_controller.should_skip():
            print("[PyContractStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}

        options = self.config.get("options", "").split()
        pycontract_executable = self._get_tool_path()
        
        cmd = [pycontract_executable] + options + [str(self.project_root)]
        
        logger.info(f"[PyContractStage] Running command: {' '.join(cmd)}")
        self.progress.start_phase("PyContract Check", total_steps=1)
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
            
            if result.returncode != 0:
                error_msg = result.stderr.strip() or f"PyContract execution failed with code {result.returncode}"
                logger.error(f"[PyContractStage] PyContract failed: {error_msg}")
                self.progress.complete_phase("PyContract Check", status="Error")
                return {"error": f"PyContract execution failed: {error_msg}", "raw_output": result.stdout, "stderr": result.stderr}
            
            self.progress.complete_phase("PyContract Check", status="Complete!")
            return result.stdout
        except FileNotFoundError:
            error_msg = f"PyContract executable ('{pycontract_executable}') not found. Ensure PyContracts is installed and provides a CLI tool, or adjust stage implementation."
            logger.error(f"[PyContractStage] {error_msg}")
            self.progress.complete_phase("PyContract Check", status="Not Found")
            return {"error": error_msg}
        except Exception as e:
            error_msg = f"Unexpected error running PyContract: {e}"
            logger.error(f"[PyContractStage] {error_msg}")
            self.progress.complete_phase("PyContract Check", status="Error")
            return {"error": error_msg}
        finally:
            self.progress.increment()

    def parse_output(self, output: Any) -> None:
        """Parse PyContract output and update metrics with contract violations and summaries.

        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['pycontract'] includes: violations (list), summary (str), raw_output (str)
        Args:
            output: Raw output from PyContract (str)
        """
        if isinstance(output, dict) and (output.get("skipped") or output.get("error")):
            self.metrics.tool_results["pycontract"] = output
            return

        if not isinstance(output, str):
            logger.warning(f"[PyContractStage] parse_output received unexpected data type: {type(output)}")
            self.metrics.tool_results["pycontract"] = {"error": "Invalid data received by parse_output"}
            return

        violations = []
        for line in output.splitlines():
            line_lower = line.lower()
            if "contract violation" in line_lower or "failed" in line_lower or "error" in line_lower:
                if "0 errors" not in line_lower:
                    violations.append(line.strip())
                    
        summary = f"{len(violations)} contract violations found"
        self.metrics.tool_results["pycontract"] = {
            "violations": violations,
            "summary": summary,
            "raw_output": output,
        } 
"""
Structure Analyzer Module

Analyzes the structure of Python projects, including directory organization,
file size distribution, and module descriptions.
"""

import ast
import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Set, Tuple

try:
    # When running as a package
    from PAT_tool.models import DirectoryMetrics, FileMetrics, ProjectMetrics
    from PAT_tool.utils import (is_excluded_path, load_exclusion_patterns,
                                logger, read_file_content, safe_execution)
except ImportError:
    # When running as a script
    from models import DirectoryMetrics, FileMetrics, ProjectMetrics
    from utils import (is_excluded_path, load_exclusion_patterns, logger,
                       read_file_content, safe_execution)

# --- Add AST Node Visitor for Name Extraction ---
class NameExtractor(ast.NodeVisitor):
    """Visits AST nodes to extract function and class names."""
    def __init__(self):
        self.functions: List[str] = []
        self.classes: List[str] = []

    def visit_FunctionDef(self, node: ast.FunctionDef):
        self.functions.append(node.name)
        # Do not visit nested functions/classes within this function
        # self.generic_visit(node) # Remove or comment if we only want top-level

    def visit_AsyncFunctionDef(self, node: ast.AsyncFunctionDef):
        self.functions.append(node.name)
        # Do not visit nested functions/classes
        # self.generic_visit(node) # Remove or comment if we only want top-level

    def visit_ClassDef(self, node: ast.ClassDef):
        self.classes.append(node.name)
        # Do not visit nested functions/classes within this class
        # self.generic_visit(node) # Remove or comment if we only want top-level

# --- End of AST Node Visitor ---

class StructureAnalyzer:
    """Analyzes the structure of a Python project.

    Supports exclusion patterns from a .patignore file at the project root (standard .gitignore-like syntax).
    """

    def __init__(self, project_root: Path, metrics: ProjectMetrics,
                 progress: Any, excluded_patterns: List[str] = None,
                 file_extensions: List[str] = None):
        """Initialize the structure analyzer.

        Args:
            project_root: Path to the project root
            metrics: ProjectMetrics instance to store results
            progress: progress tracker instance for tracking progress
            excluded_patterns: Patterns for directories to exclude from analysis
            file_extensions: File extensions to include in analysis (e.g., ['.py', '.md'])
        """
        self.project_root = project_root
        self.metrics = metrics
        self.progress = progress
        self.excluded_patterns = excluded_patterns or load_exclusion_patterns(project_root)
        self.file_extensions = file_extensions or ['.py']

    def analyze(self):
        """Run the structure analysis."""
        # Count files for progress tracking
        total_files = len([
            f for f in self.project_root.rglob("*")
            if self._is_project_file(f)
        ])

        self.progress.start_phase("Analyzing project structure", total_files)

        # First analyze directories to build the structure
        for dir_path in sorted([d for d in self.project_root.rglob("*") if d.is_dir()]):
            if self._is_project_dir(dir_path):
                self._analyze_directory(dir_path)

        # Then analyze individual files
        for file_path in sorted([f for f in self.project_root.rglob("*") if self._is_project_file(f)]):
            self._analyze_file(file_path)
            self.progress.increment()

        # Check if we found any files
        if not self.metrics.files:
            logger.warning("No project files found to analyze!")

    def _is_project_file(self, path: Path) -> bool:
        """Check if a file is part of the project and should be analyzed."""
        if is_excluded_path(path, self.excluded_patterns, self.project_root):
            return False
        if path.suffix not in self.file_extensions:
            return False
        try:
            path.relative_to(self.project_root)
            return True
        except ValueError:
            return False

    def _is_project_dir(self, path: Path) -> bool:
        """Check if a directory is part of the project and should be analyzed."""
        if is_excluded_path(path, self.excluded_patterns, self.project_root):
            return False
        try:
            path.relative_to(self.project_root)
            return True
        except ValueError:
            return False

    @safe_execution
    def _analyze_file(self, file_path: Path) -> FileMetrics:
        """Analyze a single file.

        Args:
            file_path: Path to the file

        Returns:
            FileMetrics instance with file information
        """
        rel_path = str(file_path.relative_to(self.project_root))
        file_size = file_path.stat().st_size

        # Get file content and line count
        content, line_count = read_file_content(file_path)

        # Skip very large files or binary files
        if file_size > 10 * 1024 * 1024:  # Skip files larger than 10MB
            logger.warning(f"Skipping large file {file_path} ({file_size} bytes)")
            return None

        # Create default file metrics
        metrics = FileMetrics(
            name=file_path.name,
            path=rel_path,
            size=file_size,
            lines=line_count,
            imports=[],
            classes=[],
            functions=[],
            complexity=0,
            docstring_coverage=0.0,
            is_package=False,
            is_test=False,
            is_documentation=False,
            documentation_quality=0.0
        )

        # Determine file type based on extension
        ext = file_path.suffix.lower()

        # Documentation files (markdown, rst, txt, etc.)
        if ext in ['.md', '.rst', '.txt', '.org']:
            metrics.is_documentation = True
            self._analyze_markdown_file(file_path, metrics, content)

        # Python files
        elif ext == '.py':
            self._analyze_python_file(file_path, metrics, content)

        # Configuration files
        elif ext in ['.json', '.yml', '.yaml', '.ini', '.conf', '.cfg', '.config']:
            self._analyze_config_file(file_path, metrics, content)

        # Web files (HTML, CSS, JS, etc.)
        elif ext in ['.html', '.css', '.js', '.ts']:
            self._analyze_web_file(file_path, metrics, content)

        # Shell scripts
        elif ext in ['.sh', '.bash']:
            self._analyze_shell_script(file_path, metrics, content)

        # Other source code files (C, C++, Java, etc.)
        elif ext in ['.c', '.cpp', '.h', '.hpp', '.java', '.go', '.rs']:
            self._analyze_source_file(file_path, metrics, content)

        # Add to parent directory's file list
        dir_path = str(file_path.relative_to(self.project_root).parent)
        if dir_path in self.metrics.directories:
            self.metrics.directories[dir_path].files.append(str(file_path))

            # Update directory metrics
            dir_metrics = self.metrics.directories[dir_path]
            dir_metrics.total_lines += metrics.lines
            if metrics.lines > dir_metrics.max_file_lines:
                dir_metrics.max_file_lines = metrics.lines
                dir_metrics.max_file = str(file_path)

            # Update average
            if dir_metrics.files:
                dir_metrics.avg_lines = dir_metrics.total_lines / len(dir_metrics.files)

        # Store the metrics
        file_id = str(file_path.relative_to(self.project_root))
        self.metrics.files[file_id] = metrics

        # If it's a documentation file, also add to documentation_files
        if metrics.is_documentation:
            self.metrics.documentation_files[file_id] = metrics
            self.metrics.documentation_quality_scores[file_id] = metrics.documentation_quality

            # Add to size distribution
            size_category = self._get_size_category(file_size)
            self.metrics.documentation_size_distribution[size_category] = \
                self.metrics.documentation_size_distribution.get(size_category, 0) + 1

        return metrics

    @safe_execution
    def _analyze_directory(self, dir_path: Path):
        """Analyze a directory.

        Args:
            dir_path: Path to the directory
        """
        try:
            relative_path = dir_path.relative_to(self.project_root)
        except ValueError:
            return  # Skip if not within project directory

        # Skip if already analyzed
        dir_str = str(relative_path)
        if dir_str in self.metrics.directories:
            return

        metrics = DirectoryMetrics(path=dir_str)

        # Get directory description
        metrics.description = self._get_dir_description(dir_path)

        # Store the metrics
        self.metrics.directories[dir_str] = metrics

    def _get_file_description(self, file_path: Path, content: Optional[str] = None) -> str:
        """Extract a description from file docstring.

        Args:
            file_path: Path to the file
            content: File content, if already read

        Returns:
            File description from docstring or filename
        """
        if content is None:
            content, _ = read_file_content(file_path)
            if not content:
                return file_path.name

        try:
            tree = ast.parse(content)
            for node in ast.walk(tree):
                if isinstance(node, ast.Module) and ast.get_docstring(node):
                    docstring = ast.get_docstring(node)
                    # Return first line of docstring
                    return docstring.strip().split('\n')[0]
        except SyntaxError:
            pass
        except Exception as e:
            logger.warning(f"Error parsing docstring from {file_path}: {e}")

        return file_path.name

    def _get_dir_description(self, dir_path: Path) -> str:
        """Get a description for a directory.

        Args:
            dir_path: Path to the directory

        Returns:
            Directory description from __init__.py docstring or directory name
        """
        # Look for __init__.py
        init_path = dir_path / "__init__.py"
        if init_path.exists():
            return self._get_file_description(init_path)

        return "No description available"

    def _analyze_python_file(self, file_path: Path, metrics: FileMetrics, content: str):
        """Analyze a Python file for additional metrics.

        Args:
            file_path: Path to the file
            metrics: FileMetrics instance to update
            content: File content as string
        """
        if not content:
            return

        # Get file description from docstring
        metrics.description = self._get_file_description(file_path, content)

        # Add to parent directory's file list
        dir_path = str(file_path.relative_to(self.project_root).parent)
        if dir_path in self.metrics.directories:
            self.metrics.directories[dir_path].files.append(str(file_path))

            # Update directory metrics
            dir_metrics = self.metrics.directories[dir_path]
            dir_metrics.total_lines += metrics.lines
            if metrics.lines > dir_metrics.max_file_lines:
                dir_metrics.max_file_lines = metrics.lines
                dir_metrics.max_file = str(file_path)

            # Update average
            if dir_metrics.files:
                dir_metrics.avg_lines = dir_metrics.total_lines / len(dir_metrics.files)

        # Store the metrics
        self.metrics.files[str(file_path.relative_to(self.project_root).with_suffix(''))] = metrics
        # --- Modify _analyze_python_file to use the visitor ---
        # Parse the code using AST
        try:
            tree = ast.parse(content, filename=str(file_path))
            visitor = NameExtractor()
            visitor.visit(tree)
            metrics.functions = sorted(list(set(visitor.functions))) # Store unique names
            metrics.classes = sorted(list(set(visitor.classes)))     # Store unique names

            # Also, extract imports using AST (more robust than regex)
            imports = set()
            for node in ast.walk(tree):
                if isinstance(node, ast.Import):
                    for alias in node.names:
                        imports.add(alias.name.split('.')[0]) # Add top-level package
                elif isinstance(node, ast.ImportFrom):
                    # Handle relative imports appropriately if needed
                    # For now, just add the module name
                    if node.module:
                        imports.add(node.module.split('.')[0]) # Add top-level package

            metrics.imports = sorted(list(imports))

        except SyntaxError as e:
            logger.warning(f"Could not parse Python file {file_path} due to SyntaxError: {e}")
        except Exception as e:
            logger.error(f"Error analyzing AST for {file_path}: {e}")
        # --- End of modifications in _analyze_python_file ---

    def _analyze_markdown_file(self, file_path: Path, metrics: FileMetrics, content: str):
        """Analyze a Markdown file for documentation quality.

        Args:
            file_path: Path to the file
            metrics: FileMetrics instance to update
            content: File content as string
        """
        if not content:
            return

        # Calculate basic metrics
        metrics.is_documentation = True

        # Split content into sections based on headings
        sections = re.split(r'^#{1,6}\s+', content, flags=re.MULTILINE)
        metrics.sections = len(sections) - 1  # First split is before first heading

        # Count code blocks
        code_blocks = len(re.findall(r'```[\w]*\n[\s\S]*?```', content))
        metrics.code_examples = code_blocks

        # Analyze headings structure
        heading_pattern = re.compile(r'^(#{1,6})\s+(.*?)$', re.MULTILINE)
        headings = heading_pattern.findall(content)
        heading_levels = [len(h[0]) for h in headings]

        # Check if headings follow a hierarchical structure
        has_hierarchical_structure = True
        current_level = 0
        for level in heading_levels:
            if level > current_level + 1:
                has_hierarchical_structure = False
                break
            current_level = max(current_level, level)

        # Calculate a simple documentation quality score (0.0 to 1.0)
        quality_score = 0.0
        factors = []

        # Factor 1: Has headings
        if headings:
            factors.append(min(1.0, len(headings) / 5))  # Having up to 5 headings is good

        # Factor 2: Hierarchical structure
        if has_hierarchical_structure and len(headings) > 1:
            factors.append(1.0)
        elif len(headings) > 1:
            factors.append(0.5)

        # Factor 3: Code examples
        if code_blocks:
            factors.append(min(1.0, code_blocks / 3))  # Having up to 3 code blocks is good

        # Factor 4: Length (neither too short nor too long)
        optimal_line_count = 200
        line_count_factor = min(1.0, metrics.lines / optimal_line_count) if metrics.lines < optimal_line_count else min(1.0, (2 * optimal_line_count) / metrics.lines)
        factors.append(line_count_factor)

        # Calculate overall quality if we have factors
        if factors:
            quality_score = sum(factors) / len(factors)

        metrics.documentation_quality = quality_score

    def _get_size_category(self, size_bytes: int) -> str:
        """Get a category name for a file size.

        Args:
            size_bytes: File size in bytes

        Returns:
            Category name as string
        """
        if size_bytes < 1024:
            return "< 1 KB"
        elif size_bytes < 5 * 1024:
            return "1-5 KB"
        elif size_bytes < 10 * 1024:
            return "5-10 KB"
        elif size_bytes < 20 * 1024:
            return "10-20 KB"
        elif size_bytes < 50 * 1024:
            return "20-50 KB"
        elif size_bytes < 100 * 1024:
            return "50-100 KB"
        else:
            return "> 100 KB"

    def _analyze_config_file(self, file_path: Path, metrics: FileMetrics, content: str):
        """Analyze a configuration file.

        Args:
            file_path: Path to the file
            metrics: FileMetrics instance to update
            content: File content as string
        """
        if not content:
            return

        # Set description based on filename
        metrics.description = f"Configuration file: {file_path.name}"

        # Simple analysis based on file type
        ext = file_path.suffix.lower()

        if ext in ['.json']:
            # Count number of top-level keys
            try:
                import json
                data = json.loads(content)
                if isinstance(data, dict):
                    metrics.description = f"JSON configuration with {len(data)} top-level keys"
            except:
                pass

        elif ext in ['.yml', '.yaml']:
            # Count number of document sections (separated by ---)
            sections = content.split('---')
            if len(sections) > 1:
                metrics.description = f"YAML configuration with {len(sections)} documents"

        elif ext in ['.ini', '.conf', '.cfg']:
            # Count number of sections ([section])
            import re
            sections = re.findall(r'^\[([^\]]+)\]', content, re.MULTILINE)
            if sections:
                metrics.description = f"INI configuration with {len(sections)} sections"

    def _analyze_web_file(self, file_path: Path, metrics: FileMetrics, content: str):
        """Analyze a web file (HTML, CSS, JS, etc.).

        Args:
            file_path: Path to the file
            metrics: FileMetrics instance to update
            content: File content as string
        """
        if not content:
            return

        ext = file_path.suffix.lower()

        if ext == '.html':
            # Count number of elements
            import re
            tags = re.findall(r'<([a-zA-Z][a-zA-Z0-9]*)[^>]*>', content)
            metrics.description = f"HTML file with approximately {len(tags)} elements"

        elif ext == '.css':
            # Count number of selectors
            import re
            selectors = re.findall(r'^([^{]+){', content, re.MULTILINE)
            metrics.description = f"CSS file with {len(selectors)} selectors"

        elif ext in ['.js', '.ts']:
            # Count number of functions
            import re
            functions = re.findall(r'function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(', content)
            functions += re.findall(r'([a-zA-Z_$][a-zA-Z0-9_$]*)\s*:\s*function\s*\(', content)
            functions += re.findall(r'(const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(.*\)\s*=>', content)

            metrics.description = f"JavaScript/TypeScript file with approximately {len(functions)} functions"
            metrics.functions = functions

    def _analyze_shell_script(self, file_path: Path, metrics: FileMetrics, content: str):
        """Analyze a shell script.

        Args:
            file_path: Path to the file
            metrics: FileMetrics instance to update
            content: File content as string
        """
        if not content:
            return

        # Count number of functions
        import re
        functions = re.findall(r'^([a-zA-Z_][a-zA-Z0-9_]*)\s*\(\s*\)\s*{', content, re.MULTILINE)

        metrics.description = f"Shell script with {len(functions)} functions"
        metrics.functions = functions

    def _analyze_source_file(self, file_path: Path, metrics: FileMetrics, content: str):
        """Analyze a source code file.

        Args:
            file_path: Path to the file
            metrics: FileMetrics instance to update
            content: File content as string
        """
        if not content:
            return

        ext = file_path.suffix.lower()

        # Simple description based on file type
        if ext in ['.c', '.h']:
            metrics.description = f"C source file: {file_path.name}"
        elif ext in ['.cpp', '.hpp']:
            metrics.description = f"C++ source file: {file_path.name}"
        elif ext == '.java':
            metrics.description = f"Java source file: {file_path.name}"
        elif ext == '.go':
            metrics.description = f"Go source file: {file_path.name}"
        elif ext == '.rs':
            metrics.description = f"Rust source file: {file_path.name}"
        else:
            metrics.description = f"Source file: {file_path.name}"

#!/usr/bin/env python3
"""
Project Analysis Tool - Main Module

This is the main entry point for the Project Analysis Tool, which provides
comprehensive analysis of Python project structure, dependencies, and complexity.

Usage:
    python -m PAT_project_analysis.PAT_tool.main <project_root>

Example:
    python -m PAT_project_analysis.PAT_tool.main .
"""

import argparse
import cProfile
import datetime
import logging
import os
import pstats
import select
import sys
import threading
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml

# Use absolute imports instead of relative imports
try:
    # When running as a package
    from PAT_project_analysis.PAT_tool.bandit_stage import BanditStage
    from PAT_project_analysis.PAT_tool.black_stage import BlackStage
    from PAT_project_analysis.PAT_tool.call_graph_stage import CallGraphStage
    from PAT_project_analysis.PAT_tool.call_graph_visualizer import \
        CallGraphVisualizerStage
    from PAT_project_analysis.PAT_tool.chunked_prompt_generator import \
        ChunkedPromptGenerator
    from PAT_project_analysis.PAT_tool.complexity_analyzer import \
        ComplexityAnalyzer
    from PAT_project_analysis.PAT_tool.content_extractor import \
        ContentExtractor
    from PAT_project_analysis.PAT_tool.coverage_stage import CoverageStage
    from PAT_project_analysis.PAT_tool.dependency_analyzer import \
        DependencyAnalyzer
    from PAT_project_analysis.PAT_tool.dependency_stage import DependencyStage
    from PAT_project_analysis.PAT_tool.effect_overlay_stage import \
        EffectOverlayStage
    from PAT_project_analysis.PAT_tool.flake8_stage import Flake8Stage
    from PAT_project_analysis.PAT_tool.graph_overlay_stage import \
        GraphOverlayStage
    from PAT_project_analysis.PAT_tool.hypothesis_stage import HypothesisStage
    from PAT_project_analysis.PAT_tool.isort_stage import IsortStage
    from PAT_project_analysis.PAT_tool.meta_system_stage import \
        MetaSystemStage
    from PAT_project_analysis.PAT_tool.meta_system_visualizer import \
        MetaSystemVisualizer
    from PAT_project_analysis.PAT_tool.models import (ProjectMetrics,
                                                      SimpleProgressTracker,
                                                      get_progress_tracker)
    from PAT_project_analysis.PAT_tool.mypy_stage import MypyStage
    from PAT_project_analysis.PAT_tool.pipeline import (AnalysisPipeline,
                                                        AnalysisStage)
    from PAT_project_analysis.PAT_tool.pre_analysis import run_pre_analysis
    from PAT_project_analysis.PAT_tool.protocol_extraction_stage import \
        ProtocolExtractionStage
    from PAT_project_analysis.PAT_tool.pycontract_stage import PyContractStage
    from PAT_project_analysis.PAT_tool.pydocstyle_stage import PydocstyleStage
    from PAT_project_analysis.PAT_tool.pylint_stage import PylintStage
    from PAT_project_analysis.PAT_tool.pyre_stage import PyreStage
    from PAT_project_analysis.PAT_tool.pyright_stage import PyrightStage
    from PAT_project_analysis.PAT_tool.reporter import PatReporter as Reporter
    from PAT_project_analysis.PAT_tool.ruff_stage import RuffStage
    from PAT_project_analysis.PAT_tool.structure_analyzer import \
        StructureAnalyzer
    from PAT_project_analysis.PAT_tool.tda_overlay_stage import TDAOverlayStage
    from PAT_project_analysis.PAT_tool.tla_stage import TLAStage
    from PAT_project_analysis.PAT_tool.utils import ensure_dir, logger
    from PAT_project_analysis.PAT_tool.visualization import (
        VisualizationGenerator, update_visualization_dashboard)
except ImportError:
    # When running as a script
    from bandit_stage import BanditStage
    from black_stage import BlackStage
    from call_graph_stage import CallGraphStage
    from call_graph_visualizer import CallGraphVisualizerStage
    from chunked_prompt_generator import ChunkedPromptGenerator
    from complexity_analyzer import ComplexityAnalyzer
    from content_extractor import ContentExtractor
    from coverage_stage import CoverageStage
    from dependency_analyzer import DependencyAnalyzer
    from dependency_stage import DependencyStage
    from effect_overlay_stage import EffectOverlayStage
    from flake8_stage import Flake8Stage
    from graph_overlay_stage import GraphOverlayStage
    from hypothesis_stage import HypothesisStage
    from isort_stage import IsortStage
    from meta_system_stage import MetaSystemStage
    from meta_system_visualizer import MetaSystemVisualizer
    from models import (ProjectMetrics, SimpleProgressTracker,
                        get_progress_tracker)
    from mypy_stage import MypyStage
    from pipeline import AnalysisPipeline, AnalysisStage
    from pre_analysis import run_pre_analysis
    from protocol_extraction_stage import ProtocolExtractionStage
    from pycontract_stage import PyContractStage
    from pydocstyle_stage import PydocstyleStage
    from pylint_stage import PylintStage
    from pyre_stage import PyreStage
    from pyright_stage import PyrightStage
    from reporter import PatReporter as Reporter
    from ruff_stage import RuffStage
    from structure_analyzer import StructureAnalyzer
    from tda_overlay_stage import TDAOverlayStage
    from tla_stage import TLAStage
    from utils import ensure_dir, logger
    from visualization import (VisualizationGenerator,
                               update_visualization_dashboard)

sys.setrecursionlimit(5000)

class SkipController:
    """Listens for 's' keypress to signal skipping the current phase."""
    def __init__(self):
        self.skip_event = threading.Event()
        self._listener_thread = threading.Thread(target=self._listen, daemon=True)
        self._active = False

    def start(self):
        self._active = True
        self._listener_thread.start()

    def stop(self):
        self._active = False
        self.skip_event.set()

    def _listen(self):
        print("[PAT] Press 's' then Enter at any time to skip the current phase.")
        while self._active:
            try:
                if sys.stdin in select.select([sys.stdin], [], [], 0.1)[0]:
                    line = sys.stdin.readline().strip().lower()
                    if line == 's':
                        print("[PAT] Skip requested by user.")
                        self.skip_event.set()
            except Exception:
                pass

    def should_skip(self) -> bool:
        return self.skip_event.is_set()

    def clear(self):
        self.skip_event.clear()

def parse_args():
    """Parse command line arguments.

    Returns:
        Parsed arguments
    """
    parser = argparse.ArgumentParser(
        description="Project Analysis Tool - Comprehensive analysis of Python projects"
    )
    parser.add_argument(
        "project_root",
        help="Path to the project root directory"
    )
    parser.add_argument(
        "--output-dir",
        default=str(Path(__file__).parent.parent / "PAT_output"),
        help="Directory to save analysis results (default: PAT_project_analysis/PAT_output)"
    )
    parser.add_argument(
        "--project-name",
        help="Project name (default: derived from project root)"
    )
    parser.add_argument(
        "--skip-visualizations",
        action="store_true",
        help="Skip generating visualizations (useful if graphviz is not installed)"
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
        default="INFO",
        help="Logging level (default: INFO)"
    )
    parser.add_argument(
        "--file-extensions",
        nargs="+",
        default=[".py"],
        help="File extensions to analyze (default: .py), e.g. --file-extensions .py .md"
    )
    parser.add_argument(
        "--analyze-docs",
        action="store_true",
        help="Include documentation files (.md) in analysis"
    )
    parser.add_argument(
        "--profile",
        action="store_true",
        help="Enable cProfile profiling for this run (overrides config)"
    )
    parser.add_argument(
        "--timing",
        action="store_true",
        help="Enable timing of each major pipeline stage (overrides config)"
    )
    return parser.parse_args()

def make_stage_from_analyzer(analyzer_cls, extra_kwargs=None):
    """Wrap an analyzer class as an AnalysisStage-compatible object."""
    class Stage:
        def __init__(self, *args, **kwargs):
            if extra_kwargs:
                kwargs.update(extra_kwargs)
            self.analyzer = analyzer_cls(*args, **kwargs)
        def run(self, project_root, metrics, progress, **kwargs):
            if hasattr(self.analyzer, 'analyze'):
                self.analyzer.analyze()
            elif hasattr(self.analyzer, 'generate'):
                self.analyzer.generate()
            elif hasattr(self.analyzer, 'generate_reports'):
                self.analyzer.generate_reports()
            else:
                raise NotImplementedError(
                    f"{self.analyzer.__class__.__name__} does not implement analyze, generate, or generate_reports."
                )
    return Stage

StructureStage = make_stage_from_analyzer(StructureAnalyzer)
# Legacy dependency analyzer (for reference, not used in pipeline)
LegacyDependencyStage = make_stage_from_analyzer(DependencyAnalyzer)
ComplexityStage = make_stage_from_analyzer(ComplexityAnalyzer)
ContentExtractorStage = make_stage_from_analyzer(ContentExtractor)
ChunkedPromptGeneratorStage = make_stage_from_analyzer(ChunkedPromptGenerator)

# Visualization and Report stages need project_name and output_dir
class VisualizationStage:
    def __init__(self, project_name, metrics, progress, output_dir):
        try:
            from PAT_project_analysis.PAT_tool.visualization import \
                VisualizationGenerator
        except ImportError:
            from visualization import VisualizationGenerator
        self.generator = VisualizationGenerator(project_name, metrics, progress, output_dir)
    def run(self, project_root, metrics, progress, **kwargs):
        self.generator.generate()

class ReportStage:
    def __init__(self, project_name, metrics, progress, output_dir):
        try:
            from PAT_project_analysis.PAT_tool.reporter import PatReporter as Reporter
        except ImportError:
            from reporter import Reporter
        self.reporter = Reporter(project_name, metrics, progress, output_dir)
    def run(self, project_root, metrics, progress, **kwargs):
        self.reporter.generate_reports()

def load_config(config_path: Path) -> dict:
    """Load YAML config file for tool integration."""
    if not config_path.exists():
        return {}
    with open(config_path, "r", encoding="utf-8") as f:
        return yaml.safe_load(f)

def check_graphviz_availability():
    """Check if Graphviz is available and provide installation instructions if not."""
    try:
        import graphviz
        try:
            import subprocess
            result = subprocess.run(
                ["dot", "-V"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                check=False
            )
            if result.returncode == 0:
                version = result.stderr.strip() or result.stdout.strip()
                print(f"✅ Graphviz is available: {version}")
                return True
            else:
                print("⚠️  Graphviz Python package is installed, but the dot command is not available in PATH.")
                print("   For better visualizations, please run the install_graphviz.py script.")
                return False
        except (FileNotFoundError, subprocess.SubprocessError):
            print("⚠️  Graphviz Python package is installed, but the dot command is not available in PATH.")
            print("   For better visualizations, please run the install_graphviz.py script.")
            return False
    except ImportError:
        print("⚠️  Graphviz is not available. Some visualizations will use NetworkX instead.")
        print("   For better visualizations, please run the install_graphviz.py script.")
        return False

def main(timing_override: bool = None, cprofile_override: bool = None):
    """Main entry point for the Project Analysis Tool."""
    progress = None  # Ensure progress is always defined
    # Parse command line arguments
    verbose_mode = os.environ.get("PAT_VERBOSE", "0") == "1"
    if verbose_mode:
        print("\n[DEBUG] Starting PAT analysis...")
        print(f"[DEBUG] Command line arguments: {sys.argv}")
    args = parse_args()
    if verbose_mode:
        print(f"[DEBUG] Parsed arguments: {args}")
        print(f"[DEBUG] Project root: {args.project_root}")
        print(f"[DEBUG] Output directory: {args.output_dir}")

    # Check for Graphviz availability
    if not args.skip_visualizations:
        check_graphviz_availability()

    # Load tool config
    # Check if PAT_CONFIG_PATH environment variable is set
    config_path = os.environ.get("PAT_CONFIG_PATH")
    if config_path:
        config_path = Path(config_path)
    else:
        config_path = Path(__file__).parent / "config.yaml"
    config = load_config(config_path)
    logger.info(f"Using config file: {config_path}")
    profiling_cfg = config.get("profiling", {})
    # Determine profiling settings (CLI overrides config)
    timing_enabled = args.timing if hasattr(args, 'timing') and args.timing else profiling_cfg.get("timing", False)
    cprofile_enabled = args.profile if hasattr(args, 'profile') and args.profile else profiling_cfg.get("cprofile", False)
    cprofile_output = profiling_cfg.get("cprofile_output", "pat_profile.prof")

    if cprofile_override is not None:
        cprofile_enabled = cprofile_override
    if timing_override is not None:
        timing_enabled = timing_override

    # Create timestamped output directory
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    base_output_dir = Path(args.output_dir).resolve()
    run_output_dir = base_output_dir / timestamp
    os.makedirs(run_output_dir, exist_ok=True)

    # Optionally update 'latest' symlink
    latest_symlink = base_output_dir / "latest"
    try:
        if latest_symlink.is_symlink() or latest_symlink.exists():
            latest_symlink.unlink()
        latest_symlink.symlink_to(run_output_dir, target_is_directory=True)
    except Exception as e:
        logger.warning(f"Could not update 'latest' symlink: {e}")

    # Configure logging (console + file, detailed)
    log_file_path = run_output_dir / "PAT_analysis.log"
    logging.basicConfig(
        level=getattr(logging, args.log_level),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler(str(log_file_path), mode="w", encoding="utf-8")
        ]
    )
    logger.info(f"PAT run started. Output directory: {run_output_dir}")

    # Ensure output directory exists
    ensure_dir(run_output_dir)

    # Determine project name
    project_root = Path(args.project_root).resolve()
    project_name = args.project_name or project_root.name

    # Print welcome message
    print(f"Starting comprehensive project analysis for {project_name}...")
    print("=" * 80)

    # Determine file extensions to analyze
    file_extensions = args.file_extensions
    # Ensure extensions start with a dot
    file_extensions = [ext if ext.startswith('.') else '.' + ext for ext in file_extensions]
    if args.analyze_docs and ".md" not in file_extensions:
        file_extensions.append(".md")

    # Initialize metrics and progress tracker
    metrics = ProjectMetrics()
    # Force SimpleProgressTracker to avoid rich.live issues, but respect config for details
    show_simple_details = config.get('progress_bar', {}).get('simple_tracker_details', False)
    progress = SimpleProgressTracker(show_details=show_simple_details)
    if show_simple_details:
        logger.info("Forcing SimpleProgressTracker with details enabled due to potential rich.live conflicts.")
    else:
        logger.info("Forcing SimpleProgressTracker with details disabled due to potential rich.live conflicts.")
    # try:
    #     progress = get_progress_tracker(config)
    # except Exception as e:
    #     print(f"[PAT] Failed to initialize progress tracker: {e}\\nUsing SimpleProgressTracker as fallback.")
    #     progress = SimpleProgressTracker()
    # if progress is None:
    #     progress = SimpleProgressTracker()

    tool_configs = config.get("tools", {})
    pre_analysis_cfg = config.get("pre_analysis", {})
    timings = {}

    def run_pipeline():
        nonlocal progress
        if progress is None:
            progress = SimpleProgressTracker()

        # Initialize start_time for non-timing mode
        start_time = time.time()

        # Set up error tracking
        error_summary = {
            "critical_errors": [],
            "warnings": [],
            "tool_errors": {}
        }
        # Pre-analysis timing
        if pre_analysis_cfg.get("enabled", False):
            if timing_enabled:
                t0 = time.perf_counter()
            print("\n[Pre-Analysis] Checking for syntax errors in Python files...")
            syntax_log = run_output_dir / "syntax_errors.log"
            # Get exclude patterns from config
            exclude_patterns = config.get("exclude_patterns", [])
            run_pre_analysis(project_root, auto_patignore=pre_analysis_cfg.get("auto_patignore", False), log_file=str(syntax_log), exclude_patterns=exclude_patterns)
            if timing_enabled:
                timings['pre_analysis'] = time.perf_counter() - t0

        # Dependency audit phase (insert at start if enabled)
        dependency_audit_cfg = config.get("dependency_audit", {})
        if dependency_audit_cfg.get("enabled", False):
            if timing_enabled:
                t0 = time.perf_counter()
            try:
                from PAT_project_analysis.PAT_tool.dependency_audit import \
                    audit_missing_dependencies
            except ImportError:
                from dependency_audit import audit_missing_dependencies
            log_file = run_output_dir / dependency_audit_cfg.get("log_file", "dependency_audit.log")
            print("\n[Dependency Audit] Checking for missing dependencies...")
            missing = audit_missing_dependencies(project_root, log_file=log_file)
            metrics.tool_results["dependency_audit"] = {
                "missing": missing,
                "summary": f"{len(missing)} missing dependencies",
                "log_file": str(log_file),
            }
            if timing_enabled:
                timings['dependency_audit'] = time.perf_counter() - t0

        # Get exclude patterns from config
        exclude_patterns = config.get("exclude_patterns", [])

        # Build modular analysis pipeline
        pipeline_stages = [
            StructureStage(
                project_root, metrics, progress, file_extensions=file_extensions,
                excluded_patterns=exclude_patterns
            ),
            ProtocolExtractionStage(project_root, metrics, progress, config=tool_configs.get("protocol_extraction", {})) if tool_configs.get("protocol_extraction", {}).get("enabled", False) else None,
            EffectOverlayStage(project_root, metrics, progress, config=tool_configs.get("effect_overlay", {}), output_dir=run_output_dir) if tool_configs.get("effect_overlay", {}).get("enabled", False) else None,
            PyContractStage(project_root, metrics, progress, config=tool_configs.get("pycontract", {})) if tool_configs.get("pycontract", {}).get("enabled", False) else None,
            TLAStage(project_root, metrics, progress, config=tool_configs.get("tla", {})) if tool_configs.get("tla", {}).get("enabled", False) else None,
            GraphOverlayStage(project_root, metrics, progress, config={**tool_configs.get("graph_overlay", {}), "output_dir": str(run_output_dir)}) if tool_configs.get("graph_overlay", {}).get("enabled", False) else None,
            TDAOverlayStage(project_root, metrics, progress, config=tool_configs.get("tda_overlay", {})) if tool_configs.get("tda_overlay", {}).get("enabled", False) else None,
            ContentExtractorStage(project_root, metrics, progress),
            LegacyDependencyStage(project_root, metrics, progress),
            ComplexityStage(project_root, metrics, progress),
            MetaSystemStage(project_root, metrics, progress, config=tool_configs.get("meta_system_separation", {})) if tool_configs.get("meta_system_separation", {}).get("enabled", True) else None,
        ]
        # Dynamically add tool stages if enabled in config
        if tool_configs.get("mypy", {}).get("enabled", False):
            pipeline_stages.append(
                MypyStage(project_root, metrics, progress, config=tool_configs.get("mypy", {}))
            )
        if tool_configs.get("pyright", {}).get("enabled", False):
            pipeline_stages.append(
                PyrightStage(project_root, metrics, progress, config=tool_configs.get("pyright", {}))
            )
        if tool_configs.get("pyre", {}).get("enabled", False):
            pipeline_stages.append(
                PyreStage(project_root, metrics, progress, config=tool_configs.get("pyre", {}))
            )
        if tool_configs.get("ruff", {}).get("enabled", False):
            pipeline_stages.append(
                RuffStage(project_root, metrics, progress, config=tool_configs.get("ruff", {}))
            )
        if tool_configs.get("bandit", {}).get("enabled", False):
            pipeline_stages.append(
                BanditStage(project_root, metrics, progress, config=tool_configs.get("bandit", {}))
            )
        if tool_configs.get("coverage", {}).get("enabled", False):
            pipeline_stages.append(
                CoverageStage(project_root, metrics, progress, config=tool_configs.get("coverage", {}))
            )
        if tool_configs.get("flake8", {}).get("enabled", False):
            pipeline_stages.append(
                Flake8Stage(project_root, metrics, progress, config=tool_configs.get("flake8", {}))
            )
        if tool_configs.get("pylint", {}).get("enabled", False):
            pipeline_stages.append(
                PylintStage(project_root, metrics, progress, config=tool_configs.get("pylint", {}))
            )
        if tool_configs.get("black", {}).get("enabled", False):
            pipeline_stages.append(
                BlackStage(project_root, metrics, progress, config=tool_configs.get("black", {}))
            )
        if tool_configs.get("isort", {}).get("enabled", False):
            pipeline_stages.append(
                IsortStage(project_root, metrics, progress, config=tool_configs.get("isort", {}))
            )
        if tool_configs.get("pydocstyle", {}).get("enabled", False):
            pipeline_stages.append(
                PydocstyleStage(project_root, metrics, progress, config=tool_configs.get("pydocstyle", {}))
            )
        if tool_configs.get("dependency", {}).get("enabled", False):
            pipeline_stages.append(
                DependencyStage(project_root, metrics, progress, config=tool_configs.get("dependency", {}))
            )
        if tool_configs.get("call_graph", {}).get("enabled", False):
            pipeline_stages.append(
                CallGraphStage(project_root, metrics, progress, config=tool_configs.get("call_graph", {}))
            )
            pipeline_stages.append(
                CallGraphVisualizerStage(project_name, metrics, progress, str(run_output_dir))
            )
        if tool_configs.get("hypothesis", {}).get("enabled", False):
            pipeline_stages.append(
                HypothesisStage(project_root, metrics, progress, config=tool_configs.get("hypothesis", {}))
            )
        pipeline_stages += [
            ChunkedPromptGeneratorStage(project_root, metrics, progress, output_dir=str(run_output_dir / "prompts")),
            # Add meta-system visualizer if meta-system analysis was enabled
            (lambda: MetaSystemVisualizer(project_name, metrics, str(run_output_dir)).generate())
            if tool_configs.get("meta_system_separation", {}).get("enabled", True) and not args.skip_visualizations else None,
            VisualizationStage(project_name, metrics, progress, str(run_output_dir)) if not args.skip_visualizations else None,
            ReportStage(project_name, metrics, progress, str(run_output_dir)),
        ]
        pipeline_stages = [stage for stage in pipeline_stages if stage is not None]
        pipeline = AnalysisPipeline(pipeline_stages)

        # Start global progress bar for the pipeline
        progress.start_global("PAT Analysis", total_steps=len(pipeline_stages))

        skip_controller = SkipController()
        skip_controller.start()
        all_phase_errors = []  # Collect all errors across phases
        try:
            for stage in pipeline_stages:
                if stage is None:
                    continue
                tool_name = getattr(stage, 'tool_name', None)
                if not tool_name and hasattr(stage, 'analyzer'):
                    tool_name = getattr(stage.analyzer, 'tool_name', None)
                if not tool_name:
                    tool_name = stage.__class__.__name__.replace('Stage', '').lower()
                tool_cfg = tool_configs.get(tool_name, {})
                if tool_cfg.get("skip_phase", False):
                    print(f"[PAT] Skipping phase: {tool_name}")
                    metrics.tool_results[tool_name] = {"skipped": True, "summary": "Phase skipped by user config."}
                    continue
                if skip_controller.should_skip():
                    print(f"[PAT] Skipping phase: {tool_name} (user requested)")
                    metrics.tool_results[tool_name] = {"skipped": True, "summary": "Phase skipped by user during execution."}
                    skip_controller.clear()
                    continue
                try:
                    if timing_enabled:
                        t0 = time.perf_counter()
                    if 'skip_controller' in stage.run.__code__.co_varnames:
                        result = stage.run(project_root, metrics, progress, config=tool_cfg, skip_controller=skip_controller)
                    else:
                        result = stage.run(project_root, metrics, progress, config=tool_cfg)
                    if timing_enabled:
                        timings[tool_name] = time.perf_counter() - t0
                    # Check for per-file errors in result
                    if isinstance(result, dict) and 'errors' in result and result['errors']:
                        error_count = len(result['errors'])
                        print(f"[PAT] Phase '{tool_name}' encountered {error_count} file errors.")
                        # Track errors in the error summary
                        error_summary['tool_errors'][tool_name] = {
                            "count": error_count,
                            "errors": []
                        }
                        for err in result['errors']:
                            file = err.get('file', '<unknown>')
                            error_msg = err.get('error', str(err))
                            print(f"  [Error] {file}: {error_msg}")
                            # Add to the error summary
                            error_summary['tool_errors'][tool_name]['errors'].append({
                                "file": file,
                                "error": error_msg
                            })
                        all_phase_errors.extend([{**err, 'phase': tool_name} for err in result['errors']])
                except RuntimeError as e:
                        error_msg = f"Phase {tool_name} failed: {e}"
                        print(f"[PAT] {error_msg}")
                        # Add to critical errors
                        error_summary['critical_errors'].append({
                            "phase": tool_name,
                            "error": str(e),
                            "type": "RuntimeError"
                        })
                        metrics.tool_results[tool_name] = {"skipped": True, "error": str(e), "summary": "Phase failed and was skipped."}
                        if tool_cfg.get("skip_on_error", True):
                            print(f"[PAT] Skipping to next phase after error in {tool_name}.")
                            continue
                        else:
                            print(f"[PAT] Halting pipeline due to error in {tool_name}.")
                            break
                except FileNotFoundError as e:
                    error_msg = f"Phase {tool_name} failed: Required executable not found - {e}"
                    print(f"[PAT] {error_msg}")
                    # Add to critical errors
                    error_summary['critical_errors'].append({
                        "phase": tool_name,
                        "error": str(e),
                        "type": "FileNotFoundError",
                        "hint": "Make sure all required tools are installed. Run PAT_install.py to set up dependencies."
                    })
                    metrics.tool_results[tool_name] = {"skipped": True, "error": str(e), "summary": "Phase failed due to missing executable."}
                    if tool_cfg.get("skip_on_error", True):
                        print(f"[PAT] Skipping to next phase after error in {tool_name}.")
                        continue
                    else:
                        print(f"[PAT] Halting pipeline due to error in {tool_name}.")
                        break
                except Exception as e:
                    error_msg = f"Phase {tool_name} failed: {e}"
                    print(f"[PAT] {error_msg}")
                    # Add to critical errors
                    error_summary['critical_errors'].append({
                        "phase": tool_name,
                        "error": str(e),
                        "type": type(e).__name__
                    })
                    metrics.tool_results[tool_name] = {"skipped": True, "error": str(e), "summary": "Phase failed and was skipped."}
                    if tool_cfg.get("skip_on_error", True):
                        print(f"[PAT] Skipping to next phase after error in {tool_name}.")
                        continue
                    else:
                        print(f"[PAT] Halting pipeline due to error in {tool_name}.")
                        break
            if not metrics.files:
                print("\nNo project files found to analyze! Make sure you're running this from the correct directory.")
                sys.exit(1)
            elapsed_time = sum(timings.values()) if timing_enabled else time.time() - start_time
            print("\n" + "=" * 80)
            print(f"Analysis complete in {elapsed_time:.1f} seconds!")
            print(f"Project files analyzed: {len(metrics.files)}")
            print(f"Project directories analyzed: {len(metrics.directories)}")
            doc_files = sum(1 for _, file_metric in metrics.files.items() if file_metric.is_documentation)
            if doc_files > 0:
                print(f"Documentation files analyzed: {doc_files}")
            print(f"\nReports generated in {run_output_dir}/:")
            print(f"- PAT_{project_name}_analysis.json (Complete analysis data)")
            print(f"- PAT_{project_name}_report.md (Summary report)")
            print(f"- PAT_{project_name}_structure.md (Project structure analysis)")
            if ".py" in file_extensions:
                print(f"- PAT_{project_name}_dependency_graph.png (Visual dependency map)")
                print(f"- PAT_{project_name}_complexity_heatmap.png (Complexity visualization)")
                print(f"- call_graph.html (Interactive call graph visualization)")
                if tool_configs.get("meta_system_separation", {}).get("enabled", True):
                    print(f"- PAT_{project_name}_meta_system_graph.png (Meta-system separation visualization)")
                    print(f"- PAT_{project_name}_meta_system_report.html (Interactive meta-system report)")
            if ".md" in file_extensions:
                print(f"- PAT_{project_name}_documentation_report.md (Documentation analysis)")
            update_visualization_dashboard(output_dir="analysis")
            # Print summary of all errors encountered
            if all_phase_errors:
                print("\nSummary of all per-file errors encountered across all phases:")
                for err in all_phase_errors:
                    phase = err.get('phase', '<unknown phase>')
                    file = err.get('file', '<unknown file>')
                    error_msg = err.get('error', str(err))
                    print(f"  [Phase: {phase}] {file}: {error_msg}")
                print(f"\nTotal per-file errors: {len(all_phase_errors)}")

            # Print critical errors summary
            if error_summary['critical_errors']:
                print("\n" + "=" * 80)
                print("CRITICAL ERRORS SUMMARY:")
                print("=" * 80)
                for err in error_summary['critical_errors']:
                    phase = err.get('phase', '<unknown>')
                    error_type = err.get('type', 'Error')
                    error_msg = err.get('error', '<no message>')
                    hint = err.get('hint', '')
                    print(f"  [Phase: {phase}] {error_type}: {error_msg}")
                    if hint:
                        print(f"    Hint: {hint}")
                print("\nPlease fix these errors before continuing with the analysis.")

            # Save error summary to a file (even if there are no errors)
            error_summary_path = run_output_dir / "error_summary.json"
            try:
                import json
                print(f"\nAttempting to save error summary to {error_summary_path}")
                with open(error_summary_path, 'w') as f:
                    json.dump(error_summary, f, indent=2)
                if error_summary['critical_errors'] or error_summary['warnings'] or error_summary['tool_errors']:
                    print(f"Detailed error summary saved to {error_summary_path}")
                else:
                    print(f"No errors found! Clean error summary saved to {error_summary_path}")
            except Exception as e:
                print(f"\nFailed to save error summary: {e}")
                import traceback
                traceback.print_exc()
        except Exception as e:
            logger.error(f"PAT analysis failed: {e}")
            sys.exit(1)
        finally:
            skip_controller.stop()

    # Run with cProfile if enabled
    if cprofile_enabled:
        print("[PAT] Running with cProfile enabled...")
        profiler = cProfile.Profile()
        profiler.enable()
        run_pipeline()
        profiler.disable()
        stats = pstats.Stats(profiler).sort_stats('cumtime')
        stats.dump_stats(str(run_output_dir / cprofile_output))
        print(f"[PAT] cProfile stats saved to {run_output_dir / cprofile_output}")
    else:
        run_pipeline()

    # Print timing summary if enabled
    if timing_enabled and timings:
        print("\n[Timing Summary]")
        for stage, duration in timings.items():
            print(f"- {stage}: {duration:.2f} seconds")
        print(f"Total pipeline time: {sum(timings.values()):.2f} seconds")

if __name__ == "__main__":
    main()

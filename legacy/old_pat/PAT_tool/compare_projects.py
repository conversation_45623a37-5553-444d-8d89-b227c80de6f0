#!/usr/bin/env python3
"""
Comparative Project Analysis
--------------------------
Analyzes and compares multiple Python projects.

Features:
- Multi-project analysis
- Structural comparison
- Complexity comparison
- Dependency comparison
- Quality metric comparison
- Visualization of differences
"""

import json
import os
import sys
from collections import defaultdict
from datetime import datetime
from pathlib import Path

import matplotlib.pyplot as plt
import networkx as nx


class ComparativeAnalyzer:
    """Analyzes and compares multiple projects."""
    
    def __init__(self, project_paths):
        self.project_paths = project_paths
        self.project_names = [Path(p).name for p in project_paths]
        self.project_data = {}
        self.comparative_metrics = {}
        
    def load_project_data(self):
        """Load analysis data for all projects."""
        for i, path in enumerate(self.project_paths):
            name = self.project_names[i]
            data_file = Path(f"analysis/{name}_analysis.json")
            if data_file.exists():
                with open(data_file, "r") as f:
                    self.project_data[name] = json.load(f)
                print(f"Loaded analysis data for {name}")
            else:
                print(f"Analysis data for {name} not found.")
                print(f"Please run analyze_project.py on {path} first.")
                sys.exit(1)
    
    def compare(self):
        """Run all comparisons."""
        print("Loading project data...")
        self.load_project_data()
        
        print("Comparing project structures...")
        self._compare_structure()
        
        print("Comparing project complexity...")
        self._compare_complexity()
        
        print("Comparing project dependencies...")
        self._compare_dependencies()
        
        print("Generating comparative report...")
        self._generate_comparative_report()
        
        print("Generating comparative visualizations...")
        self._generate_comparative_visualizations()
        
        print("Comparative analysis complete!")
    
    def _compare_structure(self):
        """Compare project structures."""
        structure_metrics = {}
        
        for name in self.project_names:
            data = self.project_data[name]
            
            # File counts
            file_count = len(data['files'])
            
            # Directory counts
            dir_count = len(data['directories'])
            
            # Line counts
            line_count = sum(file_data['lines'] for file_data in data['files'].values())
            
            # Average lines per file
            avg_lines = line_count / file_count if file_count > 0 else 0
            
            # Directory structure
            dir_structure = {dir_name: dir_data['files'] for dir_name, dir_data in data['directories'].items()}
            
            structure_metrics[name] = {
                'file_count': file_count,
                'dir_count': dir_count,
                'line_count': line_count,
                'avg_lines': avg_lines,
                'dir_structure': dir_structure
            }
        
        self.comparative_metrics['structure'] = structure_metrics
    
    def _compare_complexity(self):
        """Compare project complexity metrics."""
        complexity_metrics = {}
        
        for name in self.project_names:
            data = self.project_data[name]
            
            # Average complexity
            complexity_scores = data.get('complexity_scores', {})
            avg_complexity = (sum(complexity_scores.values()) / len(complexity_scores)) if complexity_scores else 0
            
            # Most complex modules
            top_complexity = sorted(
                complexity_scores.items(),
                key=lambda x: x[1],
                reverse=True
            )[:10]
            
            complexity_metrics[name] = {
                'avg_complexity': avg_complexity,
                'top_complexity': top_complexity
            }
        
        self.comparative_metrics['complexity'] = complexity_metrics
    
    def _compare_dependencies(self):
        """Compare project dependencies."""
        dependency_metrics = {}
        
        for name in self.project_names:
            data = self.project_data[name]
            
            # External dependencies
            external_deps = set()
            for file_data in data['files'].values():
                external_deps.update(file_data.get('external_deps', []))
            
            # Internal dependencies
            internal_deps = {}
            for module, file_data in data['files'].items():
                internal_deps[module] = file_data.get('internal_deps', [])
            
            dependency_metrics[name] = {
                'external_deps': list(external_deps),
                'internal_deps': internal_deps
            }
        
        self.comparative_metrics['dependencies'] = dependency_metrics
    
    def _generate_comparative_report(self):
        """Generate comprehensive comparison report."""
        os.makedirs('analysis', exist_ok=True)
        
        with open('analysis/comparative_report.md', 'w') as f:
            f.write("# Project Comparative Analysis\n\n")
            f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"Projects compared: {', '.join(self.project_names)}\n\n")
            
            # Structure comparison
            f.write("## Structure Comparison\n\n")
            self._write_structure_comparison(f)
            
            # Complexity comparison
            f.write("## Complexity Comparison\n\n")
            self._write_complexity_comparison(f)
            
            # Dependency comparison
            f.write("## Dependency Comparison\n\n")
            self._write_dependency_comparison(f)
            
            # Migration analysis (specific to these projects)
            if 'ai_twitch_streamer' in self.project_names and 'person_suit' in self.project_names:
                f.write("## AI Twitch Streamer to Person Suit Migration Analysis\n\n")
                self._write_migration_analysis(f)
            
            # Visualizations
            f.write("## Comparative Visualizations\n\n")
            f.write("- [Complexity Comparison](comparative_complexity.png)\n")
            f.write("- [Dependency Comparison](comparative_dependencies.png)\n")
    
    def _write_structure_comparison(self, f):
        """Write structure comparison to the report."""
        structure = self.comparative_metrics['structure']
        
        # Create comparison table
        f.write("### Project Size Comparison\n\n")
        f.write("| Metric | " + " | ".join(self.project_names) + " |\n")
        f.write("| ------ | " + " | ".join(['------' for _ in self.project_names]) + " |\n")
        
        # File counts
        f.write("| Files | " + " | ".join([f"{structure[name]['file_count']}" for name in self.project_names]) + " |\n")
        
        # Directory counts
        f.write("| Directories | " + " | ".join([f"{structure[name]['dir_count']}" for name in self.project_names]) + " |\n")
        
        # Line counts
        f.write("| Lines of Code | " + " | ".join([f"{structure[name]['line_count']:,}" for name in self.project_names]) + " |\n")
        
        # Avg lines per file
        f.write("| Avg Lines/File | " + " | ".join([f"{structure[name]['avg_lines']:.1f}" for name in self.project_names]) + " |\n\n")
        
        # Directory structure comparison
        f.write("### Directory Structure Comparison\n\n")
        for name in self.project_names:
            f.write(f"#### {name} Top-Level Directories\n\n")
            dirs = structure[name]['dir_structure']
            top_level_dirs = [d for d in dirs.keys() if '/' not in d]
            for dir_name in sorted(top_level_dirs):
                file_count = len(dirs[dir_name])
                f.write(f"- **{dir_name}**: {file_count} files\n")
            f.write("\n")
    
    def _write_complexity_comparison(self, f):
        """Write complexity comparison to the report."""
        complexity = self.comparative_metrics['complexity']
        
        # Average complexity comparison
        f.write("### Average Complexity\n\n")
        f.write("| Project | Average Complexity |\n")
        f.write("| ------- | ------------------ |\n")
        for name in self.project_names:
            f.write(f"| {name} | {complexity[name]['avg_complexity']:.2f} |\n")
        f.write("\n")
        
        # Most complex modules
        f.write("### Most Complex Modules\n\n")
        for name in self.project_names:
            f.write(f"#### {name} Top 5 Most Complex Modules\n\n")
            for module, score in complexity[name]['top_complexity'][:5]:
                f.write(f"- **{module}**: {score}\n")
            f.write("\n")
    
    def _write_dependency_comparison(self, f):
        """Write dependency comparison to the report."""
        dependencies = self.comparative_metrics['dependencies']
        
        # External dependency comparison
        f.write("### External Dependencies\n\n")
        all_deps = set()
        for name in self.project_names:
            all_deps.update(dependencies[name]['external_deps'])
        
        f.write("| Dependency | " + " | ".join(self.project_names) + " |\n")
        f.write("| ---------- | " + " | ".join(['------' for _ in self.project_names]) + " |\n")
        
        for dep in sorted(all_deps):
            row = []
            for name in self.project_names:
                if dep in dependencies[name]['external_deps']:
                    row.append("✓")
                else:
                    row.append(" ")
            f.write(f"| {dep} | " + " | ".join(row) + " |\n")
        f.write("\n")
    
    def _write_migration_analysis(self, f):
        """Write analysis of migration from AI Twitch Streamer to Person Suit."""
        if 'ai_twitch_streamer' not in self.project_names or 'person_suit' not in self.project_names:
            return
            
        f.write("### Migration Overview\n\n")
        
        # Size changes
        ats_files = self.comparative_metrics['structure']['ai_twitch_streamer']['file_count']
        ps_files = self.comparative_metrics['structure']['person_suit']['file_count']
        file_change = ps_files - ats_files
        file_change_pct = (file_change / ats_files) * 100 if ats_files > 0 else 0
        
        ats_lines = self.comparative_metrics['structure']['ai_twitch_streamer']['line_count']
        ps_lines = self.comparative_metrics['structure']['person_suit']['line_count']
        line_change = ps_lines - ats_lines
        line_change_pct = (line_change / ats_lines) * 100 if ats_lines > 0 else 0
        
        f.write(f"- **File Count Change**: {file_change:+d} files ({file_change_pct:+.1f}%)\n")
        f.write(f"- **Line Count Change**: {line_change:+,d} lines ({line_change_pct:+.1f}%)\n\n")
        
        # Complexity changes
        ats_complexity = self.comparative_metrics['complexity']['ai_twitch_streamer']['avg_complexity']
        ps_complexity = self.comparative_metrics['complexity']['person_suit']['avg_complexity']
        complexity_change = ps_complexity - ats_complexity
        complexity_change_pct = (complexity_change / ats_complexity) * 100 if ats_complexity > 0 else 0
        
        f.write(f"- **Average Complexity Change**: {complexity_change:+.2f} ({complexity_change_pct:+.1f}%)\n\n")
        
        # Dependency changes
        ats_ext_deps = len(self.comparative_metrics['dependencies']['ai_twitch_streamer']['external_deps'])
        ps_ext_deps = len(self.comparative_metrics['dependencies']['person_suit']['external_deps'])
        deps_change = ps_ext_deps - ats_ext_deps
        
        f.write(f"- **External Dependency Change**: {deps_change:+d} dependencies\n\n")
        
        # Key architectural changes
        f.write("### Key Architectural Changes\n\n")
        
        # Compare top-level directories
        ats_dirs = set(d for d in self.comparative_metrics['structure']['ai_twitch_streamer']['dir_structure'].keys() if '/' not in d)
        ps_dirs = set(d for d in self.comparative_metrics['structure']['person_suit']['dir_structure'].keys() if '/' not in d)
        
        new_dirs = ps_dirs - ats_dirs
        removed_dirs = ats_dirs - ps_dirs
        common_dirs = ats_dirs.intersection(ps_dirs)
        
        f.write("#### Directory Structure Changes\n\n")
        
        if new_dirs:
            f.write("**New Directories in Person Suit:**\n\n")
            for dir_name in sorted(new_dirs):
                f.write(f"- `{dir_name}/`\n")
            f.write("\n")
        
        if removed_dirs:
            f.write("**Directories Removed from AI Twitch Streamer:**\n\n")
            for dir_name in sorted(removed_dirs):
                f.write(f"- `{dir_name}/`\n")
            f.write("\n")
        
        if common_dirs:
            f.write("**Common Directories (Potentially Refactored):**\n\n")
            for dir_name in sorted(common_dirs):
                ats_files = len(self.comparative_metrics['structure']['ai_twitch_streamer']['dir_structure'][dir_name])
                ps_files = len(self.comparative_metrics['structure']['person_suit']['dir_structure'][dir_name])
                file_diff = ps_files - ats_files
                f.write(f"- `{dir_name}/`: {file_diff:+d} files\n")
            f.write("\n")
    
    def _generate_comparative_visualizations(self):
        """Generate visualizations comparing the projects."""
        # Ensure the analysis directory exists
        os.makedirs('analysis', exist_ok=True)
        
        # Generate complexity comparison chart
        self._generate_complexity_chart()
        
        # Generate dependency comparison chart
        self._generate_dependency_chart()
    
    def _generate_complexity_chart(self):
        """Generate a chart comparing complexity between projects."""
        complexity = self.comparative_metrics['complexity']
        
        # Create data for the chart
        projects = []
        avg_complexity = []
        max_complexity = []
        
        for name in self.project_names:
            projects.append(name)
            avg_complexity.append(complexity[name]['avg_complexity'])
            max_val = max([score for _, score in complexity[name]['top_complexity']]) if complexity[name]['top_complexity'] else 0
            max_complexity.append(max_val)
        
        # Create the chart
        fig, ax = plt.subplots(figsize=(10, 6))
        
        x = range(len(projects))
        width = 0.35
        
        ax.bar([i - width/2 for i in x], avg_complexity, width, label='Average Complexity')
        ax.bar([i + width/2 for i in x], max_complexity, width, label='Max Complexity')
        
        ax.set_ylabel('Complexity Score')
        ax.set_title('Complexity Comparison')
        ax.set_xticks(x)
        ax.set_xticklabels(projects)
        ax.legend()
        
        plt.tight_layout()
        plt.savefig('analysis/comparative_complexity.png')
        plt.close()
    
    def _generate_dependency_chart(self):
        """Generate a chart comparing dependencies between projects."""
        dependencies = self.comparative_metrics['dependencies']
        
        # Count dependencies by project
        dep_counts = {}
        for name in self.project_names:
            ext_deps = len(dependencies[name]['external_deps'])
            int_deps_count = sum(len(deps) for deps in dependencies[name]['internal_deps'].values())
            dep_counts[name] = {
                'external': ext_deps,
                'internal': int_deps_count
            }
        
        # Create the chart
        fig, ax = plt.subplots(figsize=(10, 6))
        
        x = range(len(self.project_names))
        width = 0.35
        
        external_counts = [dep_counts[name]['external'] for name in self.project_names]
        internal_counts = [dep_counts[name]['internal'] for name in self.project_names]
        
        ax.bar([i - width/2 for i in x], external_counts, width, label='External Dependencies')
        ax.bar([i + width/2 for i in x], internal_counts, width, label='Internal Dependencies')
        
        ax.set_ylabel('Dependency Count')
        ax.set_title('Dependency Comparison')
        ax.set_xticks(x)
        ax.set_xticklabels(self.project_names)
        ax.legend()
        
        plt.tight_layout()
        plt.savefig('analysis/comparative_dependencies.png')
        plt.close()


def main():
    """Main entry point."""
    if len(sys.argv) < 3:
        print("Usage: compare_projects.py <project1_path> <project2_path> ...")
        sys.exit(1)
    
    analyzer = ComparativeAnalyzer(sys.argv[1:])
    analyzer.compare()

if __name__ == "__main__":
    main() 
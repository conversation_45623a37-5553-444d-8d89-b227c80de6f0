"""
dependency_audit.py

Standalone script and PAT phase for auditing missing Python dependencies.
- Scans all Python files for import statements.
- Compares to installed packages in the current environment.
- Reports missing dependencies in the console and to a log file.

Usage:
    python dependency_audit.py <project_root> [--log-file <logfile>]

Can also be imported and used as a function in the PAT pipeline.
"""
import ast
import os
import sys
from pathlib import Path
from typing import List, Optional, Set

import pkg_resources


def find_imports_in_file(file_path: Path) -> Set[str]:
    """Parse a Python file and extract all top-level imported module names."""
    imports = set()
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            node = ast.parse(f.read(), filename=str(file_path))
        for n in ast.walk(node):
            if isinstance(n, ast.Import):
                for alias in n.names:
                    imports.add(alias.name.split(".")[0])
            elif isinstance(n, ast.ImportFrom):
                if n.module:
                    imports.add(n.module.split(".")[0])
    except Exception as e:
        print(f"[dependency_audit] Failed to parse {file_path}: {e}")
    return imports


def find_all_imports(project_root: Path) -> Set[str]:
    """Recursively find all imports in Python files under the project root."""
    all_imports = set()
    for root, _, files in os.walk(project_root):
        for fname in files:
            if fname.endswith(".py"):
                fpath = Path(root) / fname
                all_imports.update(find_imports_in_file(fpath))
    return all_imports


def get_installed_packages() -> Set[str]:
    """Get the set of all installed top-level packages in the current environment."""
    installed = set()
    for dist in pkg_resources.working_set:
        installed.add(dist.project_name.lower())
    return installed


def audit_missing_dependencies(project_root: Path, log_file: Optional[Path] = None) -> List[str]:
    """Audit missing dependencies by comparing imports to installed packages.

    Args:
        project_root: Path to the project root directory.
        log_file: Optional path to write the audit report.
    Returns:
        List of missing dependencies (by import name).
    """
    print(f"[dependency_audit] Scanning {project_root} for imports...")
    imports = find_all_imports(project_root)
    installed = get_installed_packages()
    # Map import names to package names (best effort)
    missing = []
    for imp in sorted(imports):
        if imp in sys.builtin_module_names:
            continue
        if imp.lower() not in installed:
            missing.append(imp)
    report_lines = [
        f"Dependency Audit Report for {project_root}",
        f"Total unique imports found: {len(imports)}",
        f"Missing dependencies: {len(missing)}",
    ]
    if missing:
        report_lines.append("Missing packages:")
        for m in missing:
            report_lines.append(f"  - {m}")
    else:
        report_lines.append("All imports satisfied by installed packages.")
    report = "\n".join(report_lines)
    print(report)
    if log_file:
        try:
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(report)
        except Exception as e:
            print(f"[dependency_audit] Failed to write log file: {e}")
    return missing

if __name__ == "__main__":
    import argparse
    parser = argparse.ArgumentParser(description="Audit missing Python dependencies in a project.")
    parser.add_argument("project_root", help="Path to the project root directory")
    parser.add_argument("--log-file", help="Optional path to write the audit report")
    args = parser.parse_args()
    audit_missing_dependencies(Path(args.project_root), Path(args.log_file) if args.log_file else None) 
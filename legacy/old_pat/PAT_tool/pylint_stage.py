"""
pylint_stage.py

Pipeline stage for integrating pylint (advanced static analysis and code quality checker) into the PAT analysis pipeline.

- Purpose: Run pylint, parse its output, and populate diagnostics in the metrics model.
- Related: flake8_stage.py, ruff_stage.py, tool_stage.py, models.py
- Dependencies: pylint (install via pip), Python 3.8+, subprocess
"""

import fnmatch
import json
import os
import platform
import subprocess
import time
from pathlib import Path
from typing import Any, Dict, List, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import (is_excluded_by_patignore,
                                load_patignore_patterns, logger)
except ImportError:
    # When running as a script
    from utils import is_excluded_by_patignore, load_patignore_patterns, logger

class PylintStage(ToolStage):
    """Pipeline stage for running pylint and collecting static analysis diagnostics.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for pylint.
    """
    tool_name = "pylint"

    def _get_tool_path(self):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = "pylint"
        if platform.system() == "Windows":
            tool_executable += ".exe"
        return str(pat_venv_bin / tool_executable)

    def run_tool(self, skip_controller=None) -> Any:
        """Run pylint on each Python file individually, showing a progress bar and elapsed time. Interruptible via skip_controller."""
        # Discover all Python files, respecting .patignore and file extensions
        file_list: List[Path] = []
        patignore_patterns = load_patignore_patterns(self.project_root)
        for root, _, files in os.walk(self.project_root):
            for fname in files:
                if not fname.endswith(".py"):
                    continue
                fpath = Path(root) / fname
                rel_path = fpath.relative_to(self.project_root)
                if is_excluded_by_patignore(rel_path, patignore_patterns):
                    continue
                file_list.append(fpath)
        num_files = len(file_list)
        if num_files == 0:
            logger.warning("[PylintStage] No Python files found for analysis.")
            return {"error": "No Python files found for analysis."}
        self.progress.start_phase("Pylint", total_steps=num_files)
        all_issues = []
        errors = []
        raw_outputs = []  # Collect raw outputs for triage
        interrupted = False
        pylint_executable = self._get_tool_path()

        for idx, fpath in enumerate(file_list, 1):
            if skip_controller and skip_controller.should_skip():
                print("[PylintStage] Skip requested by user. Exiting phase early.")
                interrupted = True
                break

            options_str = self.config.get("options", "--output-format=json")
            options = options_str.split()
            # Ensure output format is json if not specified, as parsing depends on it
            if '--output-format=json' not in options_str and '-f json' not in options_str:
                options.extend(['--output-format=json'])

            # Construct command with full path to executable
            cmd = [pylint_executable, str(fpath)] + options

            try:
                # logger.debug(f"[PylintStage] Running command: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root)
                # Always log raw output for triage
                raw_outputs.append({
                    "file": str(fpath),
                    "stdout": result.stdout,
                    "stderr": result.stderr,
                    "returncode": result.returncode,
                })

                # Check return code (Pylint uses bit flags, 0=OK, 1=Fatal, 2=Error, 4=Warning, 8=Refactor, 16=Convention, 32=Usage)
                # We generally consider fatal errors (1) or execution errors as problems here.
                # We parse issues regardless of other flags (2, 4, 8, 16, 32).
                if result.returncode == 1 or (result.returncode < 0) or (result.returncode > 32):
                    error_msg = result.stderr.strip() or f"Pylint execution failed with unexpected code {result.returncode}"
                    logger.error(f"[PylintStage] Pylint failed for {fpath}: {error_msg}")
                    errors.append({"file": str(fpath), "error": f"Pylint execution failed: {error_msg}"})
                    continue # Skip parsing if pylint itself failed badly

                # Attempt to parse JSON output
                try:
                    # Handle empty stdout which is valid JSON ('null') but not a list
                    if result.stdout.strip():
                        output = json.loads(result.stdout)
                        if isinstance(output, list):
                            all_issues.extend(output)
                        else:
                            logger.warning(f"[PylintStage] Pylint output for {fpath} was valid JSON but not a list: {output}")
                    # else: Pylint found no issues, stdout is empty
                except json.JSONDecodeError:
                    # Only log error if stdout was not empty
                    if result.stdout.strip():
                        logger.error(f"[PylintStage] Failed to parse JSON output for {fpath}. stdout:\n{result.stdout}")
                        errors.append({"file": str(fpath), "error": "Failed to parse Pylint JSON output"})
                    raw_outputs[-1]["json_parse_error"] = True
                    # else: Empty stdout, no issues, no parse error.

            except FileNotFoundError:
                error_msg = f"Pylint executable not found at '{pylint_executable}'. Ensure PAT setup completed correctly."
                logger.error(f"[PylintStage] {error_msg}")
                errors.append({"file": str(fpath), "error": error_msg})
                interrupted = True
                break # Assume all will fail
            except Exception as e:
                error_msg = f"Unexpected error running pylint for {fpath}: {e}"
                logger.error(f"[PylintStage] {error_msg}")
                errors.append({"file": str(fpath), "error": error_msg})

            self.progress.increment()

        self.progress.complete_phase("Pylint", status="Complete!" if not interrupted else "Skipped or Failed Early")
        # Write raw outputs and errors to a log file in the output directory
        output_dir = getattr(self, "output_dir", None)
        if not output_dir and hasattr(self.progress, "output_dir"):
            output_dir = getattr(self.progress, "output_dir", None)
        if not output_dir:
            output_dir = os.environ.get("PAT_OUTPUT_DIR", "./PAT_output/latest")
        try:
            os.makedirs(output_dir, exist_ok=True)
            with open(os.path.join(output_dir, "pylint_errors.log"), "w", encoding="utf-8") as logf:
                for entry in errors:
                    logf.write(f"{entry['file']}: {entry['error']}\n")
                logf.write("\n--- RAW OUTPUTS FOR TRIAGE ---\n")
                for raw in raw_outputs:
                    logf.write(f"File: {raw['file']}\nReturn code: {raw['returncode']}\n")
                    if raw.get("json_parse_error"):
                        logf.write("[JSON PARSE ERROR]\n")
                    logf.write(f"STDOUT:\n{raw['stdout']}\nSTDERR:\n{raw['stderr']}\n\n")
        except Exception as e:
            logger.error(f"[PylintStage] Failed to write pylint_errors.log: {e}")
        # Print summary
        print(f"\n[PylintStage] {idx if interrupted else num_files} files processed. {len(errors)} files failed or had parse errors. See pylint_errors.log for details.")
        # Aggregate and parse results as before
        self.parse_output(all_issues)
        result = {
            "issues": all_issues,
            "summary": f"{len(all_issues)} pylint issues",
            "errors": errors,
        }
        if interrupted:
            result["skipped"] = True
            result["summary"] = "Phase skipped or failed early."
        # Store raw outputs in metrics for easier debugging if needed
        result["raw_outputs_log"] = "pylint_errors.log"
        self.metrics.tool_results["pylint"] = result
        return result

    def parse_output(self, output: Any) -> None:
        """Parse pylint output and update metrics for each file and the project.

        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['pylint'] includes: issues (list), summary (str), raw_output (str or dict)
            - FileMetrics.tool_results['pylint']: issues (list), summary (str)
        Args:
            output: Parsed JSON output from pylint (list of dicts)
        """
        if not output or (isinstance(output, dict) and output.get("error")):
            self.metrics.tool_results["pylint"] = output
            return
        issues = output if isinstance(output, list) else []
        file_issue_map: Dict[str, list] = {}
        for issue in issues:
            file_path = issue.get("path") or issue.get("abspath") or issue.get("module", "<unknown>")
            if file_path not in file_issue_map:
                file_issue_map[file_path] = []
            file_issue_map[file_path].append(issue)
        for file_path, file_issues in file_issue_map.items():
            rel_path = str(Path(file_path).relative_to(self.project_root)) if file_path != "<unknown>" else file_path
            file_metrics = self.metrics.files.get(rel_path)
            if not file_metrics:
                file_metrics = FileMetrics(path=rel_path)
                self.metrics.files[rel_path] = file_metrics
            file_metrics.tool_results["pylint"] = {
                "issues": file_issues,
                "summary": f"{len(file_issues)} pylint issues"
            }
        self.metrics.tool_results["pylint"] = {
            "issues": issues,
            "summary": f"{len(issues)} pylint issues",
            "raw_output": output
        }

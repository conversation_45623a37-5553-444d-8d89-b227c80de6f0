"""
graph_overlay_stage.py

Pipeline stage for graph-based and topological analysis/visualization using NetworkX and TDA libraries.

- Purpose: Build and analyze graphs from project metrics (call graphs, protocol graphs, effect/context overlays), and optionally apply TDA (e.g., persistent homology, clustering) for emergent structure analysis.
- Related: call_graph_stage.py, protocol_extraction_stage.py, effect_overlay_stage.py, tool_stage.py, models.py
- Dependencies: networkx (install via pip), optionally GUDHI/giotto-tda, Python 3.8+
"""

from pathlib import Path
from typing import Any, Dict, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import networkx as nx

try:
    # When running as a package
    from PAT_tool.visualization import export_networkx_to_pyvis_html
except ImportError:
    # When running as a script
    from visualization import export_networkx_to_pyvis_html

import os


class GraphOverlayStage(ToolStage):
    """Pipeline stage for graph-based and topological analysis/visualization (NetworkX/TDA).

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for graph overlays.
    """
    tool_name = "graph_overlay"

    def run_tool(self) -> Any:
        """Build and analyze graphs from project metrics, optionally apply TDA."""
        graph_info = {
            "graphs": {},
            "topological_summaries": [],
            "visualizations": [],
        }
        # Example: Build a call graph from metrics (if available)
        call_graph_data = self.metrics.tool_results.get("call_graph", {})
        G = nx.DiGraph()
        if "edges" in call_graph_data:
            for edge in call_graph_data["edges"]:
                G.add_edge(edge[0], edge[1])
        graph_info["graphs"]["call_graph"] = nx.node_link_data(G)
        # Example: Add summary
        graph_info["topological_summaries"].append(f"Call graph: {G.number_of_nodes()} nodes, {G.number_of_edges()} edges")
        # --- New: Generate interactive HTML visualization ---
        output_dir = self.config.get("output_dir")
        if not output_dir:
            # Fallback to environment variable or default PAT_output
            output_dir = os.environ.get("PAT_OUTPUT_DIR", "PAT_output")
        Path(output_dir).mkdir(parents=True, exist_ok=True)
        html_path = os.path.join(output_dir, "graph_overlay.html")
        vis_path = export_networkx_to_pyvis_html(G, html_path, title="Call Graph Overlay")
        if vis_path:
            graph_info["visualization"] = vis_path
            graph_info["visualizations"].append(vis_path)
        else:
            logger.error("[GraphOverlayStage] Failed to generate graph overlay visualization (no HTML output).")
        return graph_info

    def parse_output(self, output: Any) -> None:
        """Parse graph overlay output and update metrics with graph/topological overlays and summaries.

        Conforms to the extended tool_results schema:
            - ProjectMetrics.tool_results['graph_overlay'] includes: graphs (dict), topological_summaries (list), visualizations (list)
        Args:
            output: Dict with graph overlay results
        """
        if not isinstance(output, dict):
            return
        self.metrics.tool_results["graph_overlay"] = {
            "graphs": output.get("graphs", {}),
            "topological_summaries": output.get("topological_summaries", []),
            "visualization": output.get("visualization", ""),
            "visualizations": output.get("visualizations", []),
        } 
"""
HTML formatter for PAT reports.

This module contains the HtmlFormatter class for generating reports in HTML format.
"""

from typing import Any, Dict, List, Optional

try:
    # When running as a package
    from PAT_tool.models import ProjectMetrics
except ImportError:
    # When running as a script
    from models import ProjectMetrics


class HtmlFormatter:
    """Formatter for generating reports in HTML format."""
    
    def __init__(self):
        """Initialize the HTML formatter."""
        self.extension = "html"
    
    def format_summary_report(self, metrics: ProjectMetrics) -> str:
        """
        Format a summary report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted summary report as a string
        """
        html = self._html_header("PAT Analysis Summary Report")
        
        # Add project overview
        html += "<h2>Project Overview</h2>"
        html += "<ul>"
        html += f"<li><strong>Project:</strong> {metrics.project_name}</li>"
        html += f"<li><strong>Files Analyzed:</strong> {len(metrics.files)}</li>"
        html += f"<li><strong>Total Lines:</strong> {sum(f.lines for f in metrics.files.values() if hasattr(f, 'lines'))}</li>"
        html += f"<li><strong>Average Complexity:</strong> {self._calculate_avg_complexity(metrics):.2f}</li>"
        html += "</ul>"
        
        # Add issue summary
        html += "<h2>Issue Summary</h2>"
        html += self._generate_issue_summary_html(metrics)
        
        # Add top problematic files
        html += "<h2>Top Problematic Files</h2>"
        problematic_files = self._identify_problematic_files(metrics)
        html += "<ol>"
        for file in problematic_files[:5]:  # Show top 5
            html += f"<li><strong>{file['path']}</strong>"
            html += "<ul>"
            html += f"<li>Issues: {file.get('issues', 0)}</li>"
            html += f"<li>Complexity: {file.get('complexity', 0)}</li>"
            html += f"<li>Lines: {file.get('lines', 0)}</li>"
            html += "</ul></li>"
        html += "</ol>"
        
        # Add recommendations
        html += "<h2>Recommendations</h2>"
        html += self._generate_recommendations_html(metrics)
        
        html += self._html_footer()
        return html
    
    def format_detailed_report(self, metrics: ProjectMetrics) -> str:
        """
        Format a detailed report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted detailed report as a string
        """
        html = self._html_header("PAT Detailed Analysis Report")
        
        # Add table of contents
        html += "<h2>Table of Contents</h2>"
        html += "<ol>"
        html += "<li><a href='#project-overview'>Project Overview</a></li>"
        html += "<li><a href='#code-quality-metrics'>Code Quality Metrics</a></li>"
        html += "<li><a href='#issue-analysis'>Issue Analysis</a></li>"
        html += "<li><a href='#file-by-file-analysis'>File-by-File Analysis</a></li>"
        html += "<li><a href='#recommendations'>Recommendations</a></li>"
        html += "</ol>"
        
        # Add project overview
        html += "<h2 id='project-overview'>Project Overview</h2>"
        html += "<ul>"
        html += f"<li><strong>Project:</strong> {metrics.project_name}</li>"
        html += f"<li><strong>Files Analyzed:</strong> {len(metrics.files)}</li>"
        html += f"<li><strong>Total Lines:</strong> {sum(f.lines for f in metrics.files.values() if hasattr(f, 'lines'))}</li>"
        html += f"<li><strong>Average Complexity:</strong> {self._calculate_avg_complexity(metrics):.2f}</li>"
        html += "</ul>"
        
        # Add code quality metrics
        html += "<h2 id='code-quality-metrics'>Code Quality Metrics</h2>"
        html += self._generate_quality_metrics_html(metrics)
        
        # Add issue analysis
        html += "<h2 id='issue-analysis'>Issue Analysis</h2>"
        html += self._generate_issue_analysis_html(metrics)
        
        # Add file-by-file analysis
        html += "<h2 id='file-by-file-analysis'>File-by-File Analysis</h2>"
        html += self._generate_file_analysis_html(metrics)
        
        # Add recommendations
        html += "<h2 id='recommendations'>Recommendations</h2>"
        html += self._generate_recommendations_html(metrics)
        
        html += self._html_footer()
        return html
    
    def format_issue_report(self, metrics: ProjectMetrics) -> str:
        """
        Format an issue report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted issue report as a string
        """
        html = self._html_header("PAT Issue Report")
        
        # Add issue summary
        html += "<h2>Issue Summary</h2>"
        html += self._generate_issue_summary_html(metrics)
        
        # Add issues by severity
        html += "<h2>Issues by Severity</h2>"
        html += self._generate_severity_issues_html(metrics)
        
        # Add issues by type
        html += "<h2>Issues by Type</h2>"
        html += self._generate_type_issues_html(metrics)
        
        # Add issues by file
        html += "<h2>Issues by File</h2>"
        html += self._generate_file_issues_html(metrics)
        
        html += self._html_footer()
        return html
    
    def format_meta_system_report(self, metrics: ProjectMetrics) -> str:
        """
        Format a meta-system report from the project metrics.
        
        Args:
            metrics: ProjectMetrics object containing analysis results
            
        Returns:
            Formatted meta-system report as a string
        """
        html = self._html_header("PAT Meta-System Separation Report")
        
        # Add meta-system overview
        html += "<h2>Meta-System Overview</h2>"
        if hasattr(metrics, 'meta_systems') and metrics.meta_systems:
            html += f"<p><strong>Meta-Systems Identified:</strong> {len(metrics.meta_systems)}</p>"
            
            for name, meta_system in metrics.meta_systems.items():
                html += f"<h3>{name}</h3>"
                html += "<ul>"
                html += f"<li><strong>Files:</strong> {len(meta_system.get('files', []))}</li>"
                html += f"<li><strong>Dependencies:</strong> {len(meta_system.get('dependencies', []))}</li>"
                html += "</ul>"
        else:
            html += "<p>No meta-systems identified in the project.</p>"
        
        # Add violations
        html += "<h2>Meta-System Violations</h2>"
        if hasattr(metrics, 'meta_system_violations') and metrics.meta_system_violations:
            html += "<ul>"
            for violation in metrics.meta_system_violations:
                html += f"<li><strong>{violation['type']}:</strong> {violation['description']}"
                html += "<ul>"
                html += f"<li><strong>Source:</strong> {violation['source']}</li>"
                html += f"<li><strong>Target:</strong> {violation['target']}</li>"
                html += "</ul></li>"
            html += "</ul>"
        else:
            html += "<p>No meta-system violations identified.</p>"
        
        # Add recommendations
        html += "<h2>Recommendations</h2>"
        html += "<ol>"
        html += "<li>Ensure meta-systems are properly separated</li>"
        html += "<li>Use interfaces for communication between meta-systems</li>"
        html += "<li>Avoid direct dependencies between meta-systems</li>"
        html += "</ol>"
        
        html += self._html_footer()
        return html
    
    def _html_header(self, title: str) -> str:
        """Generate HTML header."""
        return f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        h1 {{
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        h2 {{
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-top: 30px;
        }}
        ul, ol {{
            margin-bottom: 20px;
        }}
        table {{
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }}
        th {{
            background-color: #f2f2f2;
        }}
        tr:nth-child(even) {{
            background-color: #f9f9f9;
        }}
        .error {{
            color: #e74c3c;
        }}
        .warning {{
            color: #f39c12;
        }}
        .info {{
            color: #3498db;
        }}
    </style>
</head>
<body>
    <h1>{title}</h1>
"""
    
    def _html_footer(self) -> str:
        """Generate HTML footer."""
        return """
    <footer>
        <p><em>Generated by PAT (Project Analysis Tool)</em></p>
    </footer>
</body>
</html>
"""
    
    def _calculate_avg_complexity(self, metrics: ProjectMetrics) -> float:
        """Calculate average complexity from project metrics."""
        complexities = [f.complexity for f in metrics.files.values() if hasattr(f, 'complexity')]
        return sum(complexities) / len(complexities) if complexities else 0
    
    def _generate_issue_summary_html(self, metrics: ProjectMetrics) -> str:
        """Generate issue summary HTML from project metrics."""
        html = "<ul>"
        
        # Count issues by tool
        tool_issues = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool not in tool_issues:
                    tool_issues[tool] = 0
                
                if tool == 'mypy' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    tool_issues[tool] += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    tool_issues[tool] += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    tool_issues[tool] += len(results['errors'])
        
        # Format issue summary
        for tool, count in tool_issues.items():
            html += f"<li><strong>{tool.capitalize()}:</strong> {count} issues</li>"
        
        html += "</ul>"
        return html
    
    def _identify_problematic_files(self, metrics: ProjectMetrics) -> List[Dict[str, Any]]:
        """Identify problematic files from project metrics."""
        problematic_files = []
        
        for path, file_metric in metrics.files.items():
            issues = 0
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issues += len(results['errors'])
            
            if issues > 0 or getattr(file_metric, 'complexity', 0) > 5:
                problematic_files.append({
                    'path': path,
                    'issues': issues,
                    'complexity': getattr(file_metric, 'complexity', 0),
                    'lines': getattr(file_metric, 'lines', 0)
                })
        
        # Sort by issues (descending)
        problematic_files.sort(key=lambda x: x['issues'], reverse=True)
        
        return problematic_files
    
    def _generate_recommendations_html(self, metrics: ProjectMetrics) -> str:
        """Generate recommendations HTML from project metrics."""
        html = "<ol>"
        
        # Count issues by type
        issue_types = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issue_types['type_errors'] = issue_types.get('type_errors', 0) + len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issue_types['lint_issues'] = issue_types.get('lint_issues', 0) + len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issue_types['security_issues'] = issue_types.get('security_issues', 0) + len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issue_types['docstring_issues'] = issue_types.get('docstring_issues', 0) + len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issue_types['pylint_issues'] = issue_types.get('pylint_issues', 0) + len(results['errors'])
        
        # Generate recommendations based on issue types
        recommendations_added = False
        
        if issue_types.get('security_issues', 0) > 0:
            html += "<li><strong>Address Security Issues:</strong> Fix security vulnerabilities identified by Bandit</li>"
            recommendations_added = True
        
        if issue_types.get('type_errors', 0) > 0:
            html += "<li><strong>Fix Type Errors:</strong> Resolve type inconsistencies identified by mypy</li>"
            recommendations_added = True
        
        if issue_types.get('lint_issues', 0) > 0:
            html += "<li><strong>Address Lint Issues:</strong> Fix code quality issues identified by ruff</li>"
            recommendations_added = True
        
        if issue_types.get('docstring_issues', 0) > 0:
            html += "<li><strong>Improve Documentation:</strong> Add or fix docstrings as identified by pydocstyle</li>"
            recommendations_added = True
        
        # Add general recommendations
        if not recommendations_added:
            html += "<li><strong>Maintain Code Quality:</strong> Continue to monitor and maintain code quality</li>"
            html += "<li><strong>Add Tests:</strong> Increase test coverage for critical components</li>"
            html += "<li><strong>Refactor Complex Code:</strong> Simplify complex functions and classes</li>"
        
        html += "</ol>"
        return html
    
    def _generate_quality_metrics_html(self, metrics: ProjectMetrics) -> str:
        """Generate quality metrics HTML from project metrics."""
        html = ""
        
        # Calculate complexity metrics
        complexities = [f.complexity for f in metrics.files.values() if hasattr(f, 'complexity')]
        avg_complexity = sum(complexities) / len(complexities) if complexities else 0
        max_complexity = max(complexities) if complexities else 0
        
        # Calculate size metrics
        lines = [f.lines for f in metrics.files.values() if hasattr(f, 'lines')]
        avg_lines = sum(lines) / len(lines) if lines else 0
        max_lines = max(lines) if lines else 0
        
        # Format quality metrics
        html += "<h3>Complexity Metrics</h3>"
        html += "<ul>"
        html += f"<li><strong>Average Complexity:</strong> {avg_complexity:.2f}</li>"
        html += f"<li><strong>Maximum Complexity:</strong> {max_complexity}</li>"
        html += f"<li><strong>Files with High Complexity:</strong> {sum(1 for c in complexities if c > 10)}</li>"
        html += "</ul>"
        
        html += "<h3>Size Metrics</h3>"
        html += "<ul>"
        html += f"<li><strong>Average Lines:</strong> {avg_lines:.2f}</li>"
        html += f"<li><strong>Maximum Lines:</strong> {max_lines}</li>"
        html += f"<li><strong>Files with High Line Count:</strong> {sum(1 for l in lines if l > 500)}</li>"
        html += "</ul>"
        
        return html
    
    def _generate_issue_analysis_html(self, metrics: ProjectMetrics) -> str:
        """Generate issue analysis HTML from project metrics."""
        html = ""
        
        # Count issues by tool and type
        tool_issues = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool not in tool_issues:
                    tool_issues[tool] = {}
                
                if tool == 'mypy' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('type', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('code', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('test_id', 'unknown')
                        tool_issues[tool][issue_type] = tool_issues[tool].get(issue_type, 0) + 1
                elif tool == 'pydocstyle' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('code', 'unknown')
                        tool_issues[tool][issue_type] = tool_issues[tool].get(issue_type, 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('symbol', 'unknown')
                        tool_issues[tool][error_type] = tool_issues[tool].get(error_type, 0) + 1
        
        # Format issue analysis
        for tool, issues in tool_issues.items():
            html += f"<h3>{tool.capitalize()} Issues</h3>"
            
            # Sort issues by count (descending)
            sorted_issues = sorted(issues.items(), key=lambda x: x[1], reverse=True)
            
            html += "<ul>"
            for issue_type, count in sorted_issues[:10]:  # Show top 10
                html += f"<li><strong>{issue_type}:</strong> {count}</li>"
            
            if len(sorted_issues) > 10:
                html += f"<li>... and {len(sorted_issues) - 10} more</li>"
            
            html += "</ul>"
        
        return html
    
    def _generate_file_analysis_html(self, metrics: ProjectMetrics) -> str:
        """Generate file-by-file analysis HTML from project metrics."""
        html = ""
        
        # Sort files by path
        sorted_files = sorted(metrics.files.items(), key=lambda x: x[0])
        
        for path, file_metric in sorted_files:
            html += f"<h3>{path}</h3>"
            
            # Add file metrics
            html += "<ul>"
            html += f"<li><strong>Lines:</strong> {getattr(file_metric, 'lines', 'Unknown')}</li>"
            html += f"<li><strong>Complexity:</strong> {getattr(file_metric, 'complexity', 'Unknown')}</li>"
            
            # Add file issues
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results and results['errors']:
                    html += f"<li><strong>Type Errors:</strong> {len(results['errors'])}</li>"
                elif tool == 'ruff' and 'errors' in results and results['errors']:
                    html += f"<li><strong>Lint Issues:</strong> {len(results['errors'])}</li>"
                elif tool == 'bandit' and 'issues' in results and results['issues']:
                    html += f"<li><strong>Security Issues:</strong> {len(results['issues'])}</li>"
                elif tool == 'pydocstyle' and 'issues' in results and results['issues']:
                    html += f"<li><strong>Docstring Issues:</strong> {len(results['issues'])}</li>"
                elif tool == 'pylint' and 'errors' in results and results['errors']:
                    html += f"<li><strong>Pylint Issues:</strong> {len(results['errors'])}</li>"
            
            html += "</ul>"
        
        return html
    
    def _generate_severity_issues_html(self, metrics: ProjectMetrics) -> str:
        """Generate issues by severity HTML from project metrics."""
        html = "<ul>"
        
        # Count issues by severity
        severity_counts = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    severity_counts['error'] = severity_counts.get('error', 0) + len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        severity = error.get('severity', 'warning')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        severity = issue.get('issue_severity', 'medium')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        severity = error.get('type', 'warning')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        # Format severity issues
        for severity, count in sorted(severity_counts.items(), key=lambda x: x[1], reverse=True):
            severity_class = "info"
            if severity.lower() in ['error', 'high']:
                severity_class = "error"
            elif severity.lower() in ['warning', 'medium']:
                severity_class = "warning"
            
            html += f"<li><strong class='{severity_class}'>{severity.capitalize()}:</strong> {count}</li>"
        
        html += "</ul>"
        return html
    
    def _generate_type_issues_html(self, metrics: ProjectMetrics) -> str:
        """Generate issues by type HTML from project metrics."""
        html = "<ul>"
        
        # Count issues by type
        type_counts = {}
        for file_metric in metrics.files.values():
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('type', 'unknown')
                        type_counts[f"mypy:{error_type}"] = type_counts.get(f"mypy:{error_type}", 0) + 1
                elif tool == 'ruff' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('code', 'unknown')
                        type_counts[f"ruff:{error_type}"] = type_counts.get(f"ruff:{error_type}", 0) + 1
                elif tool == 'bandit' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('test_id', 'unknown')
                        type_counts[f"bandit:{issue_type}"] = type_counts.get(f"bandit:{issue_type}", 0) + 1
                elif tool == 'pydocstyle' and 'issues' in results:
                    for issue in results['issues']:
                        issue_type = issue.get('code', 'unknown')
                        type_counts[f"pydocstyle:{issue_type}"] = type_counts.get(f"pydocstyle:{issue_type}", 0) + 1
                elif tool == 'pylint' and 'errors' in results:
                    for error in results['errors']:
                        error_type = error.get('symbol', 'unknown')
                        type_counts[f"pylint:{error_type}"] = type_counts.get(f"pylint:{error_type}", 0) + 1
        
        # Format type issues
        for issue_type, count in sorted(type_counts.items(), key=lambda x: x[1], reverse=True)[:20]:  # Show top 20
            html += f"<li><strong>{issue_type}:</strong> {count}</li>"
        
        if len(type_counts) > 20:
            html += f"<li>... and {len(type_counts) - 20} more</li>"
        
        html += "</ul>"
        return html
    
    def _generate_file_issues_html(self, metrics: ProjectMetrics) -> str:
        """Generate issues by file HTML from project metrics."""
        html = "<ul>"
        
        # Count issues by file
        file_counts = {}
        for path, file_metric in metrics.files.items():
            issues = 0
            tool_results = getattr(file_metric, 'tool_results', {})
            
            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    issues += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    issues += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    issues += len(results['errors'])
            
            if issues > 0:
                file_counts[path] = issues
        
        # Format file issues
        for path, count in sorted(file_counts.items(), key=lambda x: x[1], reverse=True)[:20]:  # Show top 20
            html += f"<li><strong>{path}:</strong> {count}</li>"
        
        if len(file_counts) > 20:
            html += f"<li>... and {len(file_counts) - 20} more</li>"
        
        html += "</ul>"
        return html

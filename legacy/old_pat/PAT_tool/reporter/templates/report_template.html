<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        h1 {
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #bdc3c7;
            padding-bottom: 5px;
            margin-top: 30px;
        }
        ul, ol {
            margin-bottom: 20px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .error {
            color: #e74c3c;
        }
        .warning {
            color: #f39c12;
        }
        .info {
            color: #3498db;
        }
        .chart-container {
            width: 100%;
            max-width: 800px;
            margin: 20px auto;
        }
        .progress-bar {
            background-color: #f1f1f1;
            border-radius: 5px;
            padding: 3px;
            margin-bottom: 10px;
        }
        .progress-bar-fill {
            height: 20px;
            border-radius: 3px;
            background-color: #4CAF50;
            text-align: center;
            line-height: 20px;
            color: white;
        }
        .card {
            box-shadow: 0 4px 8px 0 rgba(0,0,0,0.2);
            transition: 0.3s;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .card:hover {
            box-shadow: 0 8px 16px 0 rgba(0,0,0,0.2);
        }
        .file-card {
            border-left: 4px solid #3498db;
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 10px;
        }
        .issue-card {
            border-left: 4px solid #e74c3c;
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 10px;
        }
        .recommendation-card {
            border-left: 4px solid #2ecc71;
            background-color: #f8f9fa;
            padding: 10px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <h1>{{ title }}</h1>
    
    <!-- Content will be inserted here -->
    {{ content }}
    
    <footer>
        <p><em>Generated by PAT (Project Analysis Tool) on {{ date }}</em></p>
    </footer>
</body>
</html>

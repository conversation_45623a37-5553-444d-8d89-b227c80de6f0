"""
Reporter Module for PAT
======================

Generates comprehensive reports from PAT analysis results in various formats.
Includes summary, detailed, and issue reports with metrics, diagnostics, and recommendations.

This module has been modularized for better maintainability and extensibility.
"""

# Import the main class for backward compatibility
from .base import PatReporter

# Import formatters for easier access
from .formatters import <PERSON>Formatter, HtmlFormatter, JsonFormatter

__all__ = ['PatReporter', 'TextFormatter', 'HtmlFormatter', 'JsonFormatter']

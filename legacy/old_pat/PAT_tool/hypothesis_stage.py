"""
hypothesis_stage.py

Pipeline stage for property-based testing using Hypothesis.

- Purpose: Discover and run property-based tests (Hypothesis) in the codebase. Optionally, auto-generate simple property-based tests for functions with type hints. Collect and report results in project metrics and downstream reports/prompts.
- Related: models.py, main.py, pytest integration, chunked_prompt_generator.py
- Dependencies: Hypothesis (pip install hypothesis), pytest, Python 3.8+
"""

import re
import subprocess
from pathlib import Path
from typing import Any, Dict, List, Optional

try:
    # When running as a package
    from PAT_tool.tool_stage import ToolStage
except ImportError:
    # When running as a script
    from tool_stage import ToolStage
try:
    # When running as a package
    from PAT_tool.models import FileMetrics, ProjectMetrics
except ImportError:
    # When running as a script
    from models import FileMetrics, ProjectMetrics
try:
    # When running as a package
    from PAT_tool.utils import logger
except ImportError:
    # When running as a script
    from utils import logger

import os
import platform


class HypothesisStage(ToolStage):
    """Pipeline stage for property-based testing using Hypothesis.

    Args:
        project_root (Path): Root directory of the project to analyze.
        metrics (ProjectMetrics): Project-level metrics object to populate.
        progress: progress tracker instance for reporting progress.
        config (Optional[Dict[str, Any]]): Optional config for Hypothesis stage.
    """
    tool_name = "hypothesis"

    def __init__(self, project_root: Path, metrics: ProjectMetrics, progress, config: Optional[Dict[str, Any]] = None):
        """Initialize the Hypothesis stage."""
        super().__init__(project_root, metrics, progress, config=config)
        # Optionally parse config for test discovery/generation options

    def _get_tool_path(self, tool_name: str):
        # Construct path relative to this script's location
        pat_venv_bin = Path(__file__).parent.parent / "PAT_venv" / "bin"
        tool_executable = tool_name
        if platform.system() == "Windows":
            tool_executable += ".exe"
        venv_path = pat_venv_bin / tool_executable
        if venv_path.is_file():
            return str(venv_path)
        else:
            logger.warning(f"[{self.__class__.__name__}] {tool_executable} not found in {pat_venv_bin}. Assuming it's in PATH.")
            return tool_executable

    def run_tool(self, skip_controller=None) -> Dict[str, Any]:
        """Run property-based tests using Hypothesis and collect results.

        Returns:
            Dict with Hypothesis test results (failures, summary, raw_output).
        """
        # Check for skip request before starting subprocess
        if skip_controller and skip_controller.should_skip():
            print("[HypothesisStage] Skip requested by user. Skipping phase.")
            return {"skipped": True, "summary": "Phase skipped by user before execution."}
            
        test_files = self._discover_hypothesis_tests()
        if not test_files:
            logger.info("No Hypothesis-based tests found.")
            return {"summary": "No Hypothesis-based tests found.", "failures": [], "raw_output": ""}
            
        # Note: self.progress is not used here as _run_hypothesis_tests handles it internally
        results = self._run_hypothesis_tests(test_files)
        
        # Check if skip was requested during the run (though subprocess run blocks)
        # This check might be redundant if skip interrupts the subprocess externally
        if skip_controller and skip_controller.should_skip():
             results["skipped"] = True
             results["summary"] = "Phase potentially skipped by user during execution." # Adjust summary
             
        return results

    def parse_output(self, output: Any) -> None:
        """Parse Hypothesis test output and update metrics with results and summaries.

        Args:
            output: Dict with Hypothesis test results
        """
        if not isinstance(output, dict):
            return
        self.metrics.tool_results["hypothesis"] = output

    def _discover_hypothesis_tests(self) -> List[Path]:
        """Discover existing Hypothesis-based test files in the project.

        Returns:
            List of test file paths containing Hypothesis tests.
        """
        test_files = []
        for py_file in Path(self.project_root).rglob("test_*.py"):
            try:
                with open(py_file, "r", encoding="utf-8") as f:
                    content = f.read()
                if "import hypothesis" in content or "from hypothesis" in content or "@given" in content:
                    test_files.append(py_file)
            except Exception as e:
                logger.warning(f"[HypothesisStage] Could not read {py_file}: {e}")
        return test_files

    def _run_hypothesis_tests(self, test_files: List[Path]) -> Dict[str, Any]:
        """Run Hypothesis-based tests using pytest and collect results.

        Args:
            test_files: List of test file paths to run
        Returns:
            Dict with test results (failures, summary, raw_output)
        """
        pytest_executable = self._get_tool_path("pytest")
        options = self.config.get("options", "").split()
        # Ensure project root is added to pythonpath if tests rely on it
        env = os.environ.copy()
        env["PYTHONPATH"] = str(self.project_root) + os.pathsep + env.get("PYTHONPATH", "")
        
        cmd = [pytest_executable, "--tb=short", "--maxfail=20", "--hypothesis-show-statistics"] + options + [str(f) for f in test_files]
        
        logger.info(f"[HypothesisStage] Running command: {' '.join(cmd)}")
        self.progress.start_phase("Hypothesis Tests", total_steps=1) # Treat as one step
        final_status = "Error" # Default status
        output = "" # Initialize output
        failures = []

        try:
            # logger.debug(f"[HypothesisStage] Running command: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True, check=False, cwd=self.project_root, env=env)
            output = result.stdout + "\n" + result.stderr
            # Pytest exit codes: 0=OK, 1=Tests failed, 2=Interrupted, 3=Internal error, 4=Usage error, 5=No tests collected
            if result.returncode == 0:
                 final_status = "Passed"
            elif result.returncode == 1:
                 final_status = "Failed"
            elif result.returncode == 5:
                 final_status = "No Tests Collected"
            else:
                 final_status = f"Error (code {result.returncode})"
                 logger.error(f"[HypothesisStage] Pytest execution failed (code {result.returncode}). Output:\n{output}")
                 # Return failure info immediately
                 failures = [f"Pytest execution failed (code {result.returncode})"]
                 summary = "Pytest execution failed"
                 # Note: Don't return here, let finally clause complete progress

            # Parse output for failures regardless of exit code (except critical errors)
            if not failures: # Only parse if no critical execution error occurred
                # Enhanced failure detection regex
                failure_pattern = re.compile(r"(?:Falsifying example:|@given|Failed:|E   |==+ FAILURES ==+|_+ ERROR collecting _+)")
                in_failure_block = False
                collecting_failure = False
                for line in output.splitlines():
                    if "ERROR collecting " in line:
                        collecting_failure = True
                        failures.append(line)
                        continue
                    if collecting_failure and line.strip(): 
                         failures.append(line)
                         continue
                    elif collecting_failure and not line.strip():
                         collecting_failure = False
                    
                    is_failure_line = failure_pattern.search(line)
                    if is_failure_line or (in_failure_block and line.strip()):
                        if not (line.startswith("===") and not failures):
                            failures.append(line)
                        in_failure_block = True
                    elif line.strip() == "":
                        in_failure_block = False
                    elif line.startswith("==="):
                        in_failure_block = False 
            
        except FileNotFoundError:
            error_msg = f"Pytest executable not found at '{pytest_executable}'. Ensure PAT setup completed correctly."
            logger.error(f"[HypothesisStage] {error_msg}")
            final_status = "Not Found"
            failures = [error_msg]
            output = error_msg
        except Exception as e:
            error_msg = f"Unexpected error running pytest: {e}"
            logger.error(f"[HypothesisStage] {error_msg}")
            final_status = "Error"
            failures = [error_msg]
            output = error_msg
        finally:
            # Complete progress regardless of outcome
            self.progress.complete_phase("Hypothesis Tests", status=final_status)
            self.progress.increment() # Increment once for the single project-wide run
            
        summary = f"{len(failures)} property-based test failures" if failures and final_status == "Failed" else final_status
        return {"failures": failures, "summary": summary, "raw_output": output}

    def _generate_property_tests(self) -> List[Path]:
        """(Optional) Auto-generate simple property-based tests for functions with type hints.

        Returns:
            List of generated test file paths.
        """
        # Scaffold for future auto-generation of property-based tests
        return [] 
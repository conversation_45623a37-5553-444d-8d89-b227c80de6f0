"""
Analysis page for the PAT Web UI.
"""
import json
import os
import streamlit as st
from pathlib import Path

from pat_backend import LogLevel, run_analysis_stream

def render_analysis(inputs):
    """Render the analysis page.

    Args:
        inputs: Dictionary containing the inputs from the sidebar
    """
    st.title("Analysis")

    # Extract inputs
    project_path = inputs['project_path']
    output_dir = inputs['output_dir']
    selected_tools = inputs['selected_tools']
    log_level = inputs['log_level']
    run_button = inputs['run_button']

    # Check if we should run the analysis
    if run_button:
        if not project_path:
            st.error("Please enter a project path.")
            return

        if not selected_tools:
            st.error("Please select at least one tool to run.")
            return

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)

        # Add project to recent projects
        if 'recent_projects' not in st.session_state:
            st.session_state['recent_projects'] = []

        if project_path not in st.session_state['recent_projects']:
            st.session_state['recent_projects'].insert(0, project_path)
            # Keep only the 5 most recent projects
            st.session_state['recent_projects'] = st.session_state['recent_projects'][:5]

        # Run the analysis
        st.info(f"Running analysis on {project_path} with tools: {', '.join(selected_tools)}")

        # Create a placeholder for the log
        log_placeholder = st.empty()

        # Create a progress bar
        progress_bar = st.progress(0)

        # Initialize log
        log = []

        # Run the analysis with streaming
        for i, entry in enumerate(run_analysis_stream(
            project_path=project_path,
            tools=selected_tools,
            tests=[],  # No specific test files
            report_types=["JSON", "Console"],  # Default report types
            timeout=300,  # Increased timeout for larger projects
            config_override=st.session_state.get('pat_config', {})
        )):
            # Add entry to log
            log.append(entry)

            # Format log entries for display
            log_html = "<div style='height: 300px; overflow-y: auto;'>"
            for entry in log:
                timestamp = entry.get('timestamp', '')
                level = entry.get('level', 'INFO')
                message = entry.get('message', '')

                # Format timestamp
                if timestamp:
                    try:
                        from datetime import datetime
                        dt = datetime.fromisoformat(timestamp)
                        timestamp = dt.strftime('%H:%M:%S')
                    except (ValueError, TypeError):
                        pass

                # Color-code by level
                if level == 'ERROR':
                    log_html += f"<div style='color:red'>[{timestamp}] {level}: {message}</div>"
                elif level == 'WARNING':
                    log_html += f"<div style='color:orange'>[{timestamp}] {level}: {message}</div>"
                elif level == 'DEBUG':
                    log_html += f"<div style='color:gray'>[{timestamp}] {level}: {message}</div>"
                else:
                    log_html += f"<div>[{timestamp}] {level}: {message}</div>"

            log_html += "</div>"

            # Update log display
            log_placeholder.markdown(log_html, unsafe_allow_html=True)

            # Update progress bar (simple heuristic)
            progress = min(0.1 + 0.9 * (i / (len(selected_tools) * 10)), 1.0)
            progress_bar.progress(progress)

            # Check for completion
            if entry.get('message', '').startswith('Analysis complete'):
                # Load the report
                report_path = Path(output_dir) / 'report.json'
                if report_path.exists():
                    try:
                        with open(report_path, 'r') as f:
                            st.session_state['report_json'] = json.load(f)
                        st.success(f"Analysis complete! View the results in the tabs below.")
                    except Exception as e:
                        st.error(f"Error loading report: {e}")
                else:
                    st.warning("Analysis complete, but no report was generated.")

        # Ensure progress bar is complete
        progress_bar.progress(1.0)

    # Display instructions if no analysis has been run
    if 'report_json' not in st.session_state:
        st.info("Enter your project path and select tools in the sidebar, then click 'Run Analysis'.")

        # Display sample project path
        st.subheader("Example")
        st.code("path/to/your/project")

        # Display available tools
        st.subheader("Available Tools")
        st.markdown("""
        - **Ruff (Linter)**: Fast Python linter
        - **Bandit (Security)**: Security vulnerability scanner
        - **Pytest (Tests)**: Test runner
        - **Mypy (Type Checker)**: Static type checker
        - **Pyright (Type Checker)**: Microsoft's type checker
        - **Black (Formatter)**: Code formatter
        - **Isort (Import Sorter)**: Import sorter
        - **Pylint (Linter)**: Comprehensive linter
        - **meta_system_separation**: Analyzes separation between meta-systems
        """)

    # Return whether an analysis was run
    return run_button and project_path and selected_tools

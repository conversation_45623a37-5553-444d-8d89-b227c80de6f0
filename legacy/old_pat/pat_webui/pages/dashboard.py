"""
Dashboard page for the PAT Web UI.

This module contains the dashboard page for the PAT Web UI, which displays
project overview, metrics, and summary information.
"""

import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from typing import Any, Dict, List, Optional

from ..components.visualizations import (
    create_issue_summary_chart,
    create_complexity_histogram,
    create_file_size_histogram,
    create_issue_severity_chart,
    create_issue_type_chart
)


def render_dashboard(metrics: Dict[str, Any], project_path: str):
    """
    Render the dashboard page.

    Args:
        metrics: Dictionary containing project metrics
        project_path: Path to the project being analyzed
    """
    st.title("PAT Dashboard")

    # Project overview
    st.header("Project Overview")

    # Create columns for metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            label="Files Analyzed",
            value=len(metrics.get('files', {}))
        )

    with col2:
        total_lines = sum(f.get('lines', 0) for f in metrics.get('files', {}).values())
        st.metric(
            label="Total Lines",
            value=f"{total_lines:,}"
        )

    with col3:
        complexities = [f.get('complexity', 0) for f in metrics.get('files', {}).values() if 'complexity' in f]
        avg_complexity = sum(complexities) / len(complexities) if complexities else 0
        st.metric(
            label="Avg. Complexity",
            value=f"{avg_complexity:.2f}"
        )

    with col4:
        # Count total issues
        total_issues = 0
        for file_metric in metrics.get('files', {}).values():
            tool_results = file_metric.get('tool_results', {})

            for tool, results in tool_results.items():
                if tool == 'mypy' and 'errors' in results:
                    total_issues += len(results['errors'])
                elif tool == 'ruff' and 'errors' in results:
                    total_issues += len(results['errors'])
                elif tool == 'bandit' and 'issues' in results:
                    total_issues += len(results['issues'])
                elif tool == 'pydocstyle' and 'issues' in results:
                    total_issues += len(results['issues'])
                elif tool == 'pylint' and 'errors' in results:
                    total_issues += len(results['errors'])

        st.metric(
            label="Total Issues",
            value=f"{total_issues:,}"
        )

    # Issue summary chart
    st.subheader("Issue Summary")
    issue_chart = create_issue_summary_chart(metrics)
    st.plotly_chart(issue_chart, use_container_width=True)

    # Create two columns for complexity and file size histograms
    col1, col2 = st.columns(2)

    with col1:
        st.subheader("Complexity Distribution")
        complexity_chart = create_complexity_histogram(metrics)
        st.plotly_chart(complexity_chart, use_container_width=True)

    with col2:
        st.subheader("File Size Distribution")
        file_size_chart = create_file_size_histogram(metrics)
        st.plotly_chart(file_size_chart, use_container_width=True)

    # Issue severity chart
    st.subheader("Issue Severity")
    severity_chart = create_issue_severity_chart(metrics)
    st.plotly_chart(severity_chart, use_container_width=True)

    # Issue type chart
    st.subheader("Top Issue Types")
    type_chart = create_issue_type_chart(metrics)
    st.plotly_chart(type_chart, use_container_width=True)

    # Top problematic files
    st.header("Top Problematic Files")

    # Identify problematic files
    problematic_files = []
    for path, file_metric in metrics.get('files', {}).items():
        issues = 0
        tool_results = file_metric.get('tool_results', {})

        for tool, results in tool_results.items():
            if tool == 'mypy' and 'errors' in results:
                issues += len(results['errors'])
            elif tool == 'ruff' and 'errors' in results:
                issues += len(results['errors'])
            elif tool == 'bandit' and 'issues' in results:
                issues += len(results['issues'])
            elif tool == 'pydocstyle' and 'issues' in results:
                issues += len(results['issues'])
            elif tool == 'pylint' and 'errors' in results:
                issues += len(results['errors'])

        if issues > 0 or file_metric.get('complexity', 0) > 5:
            problematic_files.append({
                'path': path,
                'issues': issues,
                'complexity': file_metric.get('complexity', 0),
                'lines': file_metric.get('lines', 0)
            })

    # Sort by issues (descending)
    problematic_files.sort(key=lambda x: x['issues'], reverse=True)

    # Create a DataFrame for the table
    if problematic_files:
        df = pd.DataFrame(problematic_files[:10])  # Show top 10
        st.dataframe(df, use_container_width=True)
    else:
        st.info("No problematic files identified.")

    # Recommendations
    st.header("Recommendations")

    # Count issues by type
    issue_types = {}
    for file_metric in metrics.get('files', {}).values():
        tool_results = file_metric.get('tool_results', {})

        for tool, results in tool_results.items():
            if tool == 'mypy' and 'errors' in results:
                issue_types['type_errors'] = issue_types.get('type_errors', 0) + len(results['errors'])
            elif tool == 'ruff' and 'errors' in results:
                issue_types['lint_issues'] = issue_types.get('lint_issues', 0) + len(results['errors'])
            elif tool == 'bandit' and 'issues' in results:
                issue_types['security_issues'] = issue_types.get('security_issues', 0) + len(results['issues'])
            elif tool == 'pydocstyle' and 'issues' in results:
                issue_types['docstring_issues'] = issue_types.get('docstring_issues', 0) + len(results['issues'])
            elif tool == 'pylint' and 'errors' in results:
                issue_types['pylint_issues'] = issue_types.get('pylint_issues', 0) + len(results['errors'])

    # Generate recommendations based on issue types
    if issue_types.get('security_issues', 0) > 0:
        st.info("🔒 **Address Security Issues**: Fix security vulnerabilities identified by Bandit")

    if issue_types.get('type_errors', 0) > 0:
        st.info("🔍 **Fix Type Errors**: Resolve type inconsistencies identified by mypy")

    if issue_types.get('lint_issues', 0) > 0:
        st.info("🧹 **Address Lint Issues**: Fix code quality issues identified by ruff")

    if issue_types.get('docstring_issues', 0) > 0:
        st.info("📝 **Improve Documentation**: Add or fix docstrings as identified by pydocstyle")

    # Add general recommendations if no specific issues
    if not issue_types:
        st.info("✅ **Maintain Code Quality**: Continue to monitor and maintain code quality")
        st.info("🧪 **Add Tests**: Increase test coverage for critical components")
        st.info("♻️ **Refactor Complex Code**: Simplify complex functions and classes")

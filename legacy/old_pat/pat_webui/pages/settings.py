"""
Settings page for the PAT Web UI.
"""
import streamlit as st
import yaml
from pathlib import Path

from ..config import generate_config_widgets, save_config, load_config

def render_settings():
    """Render the settings page.
    """
    st.title("Settings")
    
    # Create tabs for different settings
    tabs = st.tabs(["Configuration", "Appearance", "Advanced"])
    
    # Configuration Tab
    with tabs[0]:
        st.subheader("Configuration")
        
        # Display configuration widgets
        if 'pat_config' in st.session_state:
            generate_config_widgets(st.session_state['pat_config'])
            
            # Save/load configuration
            col1, col2 = st.columns(2)
            
            with col1:
                save_path = st.text_input("Save Configuration Path", value="pat_config.yaml")
                if st.button("Save Configuration"):
                    if save_config(save_path):
                        st.success(f"Configuration saved to {save_path}")
            
            with col2:
                load_path = st.text_input("Load Configuration Path")
                if st.button("Load Configuration"):
                    if Path(load_path).exists():
                        if load_config(load_path):
                            st.success(f"Configuration loaded from {load_path}")
                            st.rerun()
                    else:
                        st.error(f"File not found: {load_path}")
            
            # Reset configuration
            if st.button("Reset to Default Configuration"):
                # Load base config
                from ..utils import load_base_config
                st.session_state['pat_config'] = load_base_config()
                st.success("Configuration reset to default")
                st.rerun()
        else:
            st.info("Configuration not loaded. Please run an analysis first.")
    
    # Appearance Tab
    with tabs[1]:
        st.subheader("Appearance")
        
        # Theme selection
        theme = st.selectbox(
            "Theme",
            options=["Light", "Dark", "System"],
            index=2,
            help="Select the UI theme"
        )
        
        # Font size
        font_size = st.slider(
            "Font Size",
            min_value=0.8,
            max_value=1.5,
            value=1.0,
            step=0.1,
            help="Adjust the font size"
        )
        
        # Apply button
        if st.button("Apply Appearance Settings"):
            st.info("Appearance settings will be applied in a future update.")
    
    # Advanced Tab
    with tabs[2]:
        st.subheader("Advanced Settings")
        
        # Debug mode
        debug_mode = st.checkbox(
            "Debug Mode",
            value=st.session_state.get('debug_mode', False),
            help="Enable debug mode for more detailed logging"
        )
        
        # Cache settings
        cache_enabled = st.checkbox(
            "Enable Cache",
            value=st.session_state.get('cache_enabled', True),
            help="Enable caching of analysis results"
        )
        
        if cache_enabled:
            cache_dir = st.text_input(
                "Cache Directory",
                value=st.session_state.get('cache_dir', '.pat_cache'),
                help="Directory to store cached results"
            )
            
            if st.button("Clear Cache"):
                import shutil
                try:
                    shutil.rmtree(cache_dir, ignore_errors=True)
                    st.success(f"Cache cleared from {cache_dir}")
                except Exception as e:
                    st.error(f"Error clearing cache: {e}")
        
        # Update session state
        st.session_state['debug_mode'] = debug_mode
        st.session_state['cache_enabled'] = cache_enabled
        if cache_enabled:
            st.session_state['cache_dir'] = cache_dir

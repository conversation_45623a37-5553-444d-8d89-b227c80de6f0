"""
Results page for the PAT Web UI.
"""
import json
import streamlit as st
from pathlib import Path

from ..components.visualizations import (
    render_summary_visualization,
    render_problematic_files,
    render_meta_system_visualization
)

def render_results():
    """Render the results page.
    
    Returns:
        True if results were rendered, False otherwise
    """
    st.title("Results")
    
    # Check if we have results to display
    if 'report_json' not in st.session_state:
        st.info("No results available. Run an analysis first.")
        return False
    
    # Get the report data
    report_data = st.session_state['report_json']
    
    # Create tabs for different views
    tabs = st.tabs([
        "Useful Summary", 
        "Problematic Files", 
        "Detailed Summary", 
        "Meta-Analysis", 
        "Test Analysis", 
        "Meta-System Separation", 
        "Trends"
    ])
    
    # Useful Summary Tab
    with tabs[0]:
        render_summary_visualization(report_data)
    
    # Problematic Files Tab
    with tabs[1]:
        render_problematic_files(report_data)
    
    # Detailed Summary Tab
    with tabs[2]:
        st.subheader("Detailed Summary")
        
        # Display the raw report data
        with st.expander("Raw Report Data", expanded=False):
            st.json(report_data)
        
        # Display enhanced reports if available
        if 'enhanced_reports' in st.session_state:
            enhanced_reports = st.session_state['enhanced_reports']
            
            if 'summary' in enhanced_reports:
                st.markdown(enhanced_reports['summary'])
            else:
                st.info("No enhanced summary available.")
        else:
            st.info("No enhanced reports available.")
    
    # Meta-Analysis Tab
    with tabs[3]:
        st.subheader("Meta-Analysis")
        
        # Display meta-analysis if available
        if 'enhanced_reports' in st.session_state and 'meta_analysis' in st.session_state['enhanced_reports']:
            st.markdown(st.session_state['enhanced_reports']['meta_analysis'])
        else:
            st.info("No meta-analysis available.")
    
    # Test Analysis Tab
    with tabs[4]:
        st.subheader("Test Analysis")
        
        # Display test analysis if available
        if 'enhanced_reports' in st.session_state and 'test_analysis' in st.session_state['enhanced_reports']:
            st.markdown(st.session_state['enhanced_reports']['test_analysis'])
            
            # Add download button for the test analysis report
            st.download_button(
                "Download Test Analysis Report",
                data=st.session_state['enhanced_reports']['test_analysis'],
                file_name="test_analysis_report.txt"
            )
        else:
            st.info("Test analysis report not available")
    
    # Meta-System Separation Tab
    with tabs[5]:
        render_meta_system_visualization(report_data)
    
    # Trends Tab
    with tabs[6]:
        st.info("Trend analysis will be available in a future update.")
    
    return True

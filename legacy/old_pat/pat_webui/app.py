"""
PAT Web UI Application
=====================

Main application module for the PAT Web UI.
"""

import streamlit as st
import os
import sys
import json
from pathlib import Path

# Add the parent directory to the path to import PAT modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from .components.sidebar import render_sidebar
from .components.visualizations import (
    create_issue_summary_chart,
    create_complexity_histogram,
    create_file_size_histogram,
    create_issue_severity_chart,
    create_issue_type_chart,
    create_meta_system_network
)

# Import pages
from .pages.dashboard import render_dashboard
from .pages.issues import render_issues
from .pages.files import render_files
from .pages.meta_systems import render_meta_systems


def run_app():
    """Run the PAT Web UI application."""
    # Set page config
    st.set_page_config(
        page_title="PAT - Project Analysis Tool",
        page_icon="🔍",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Initialize session state
    if 'metrics' not in st.session_state:
        st.session_state['metrics'] = None
    if 'project_path' not in st.session_state:
        st.session_state['project_path'] = None
    if 'current_page' not in st.session_state:
        st.session_state['current_page'] = 'dashboard'
    
    # Render sidebar
    sidebar_result = render_sidebar()
    
    # Load metrics if available
    if sidebar_result and 'metrics_file' in sidebar_result and sidebar_result['metrics_file']:
        metrics_file = sidebar_result['metrics_file']
        project_path = sidebar_result.get('project_path', '')
        
        try:
            with open(metrics_file, 'r') as f:
                metrics = json.load(f)
            
            st.session_state['metrics'] = metrics
            st.session_state['project_path'] = project_path
        except Exception as e:
            st.error(f"Error loading metrics file: {e}")
    
    # Render the selected page
    if st.session_state['metrics'] is not None:
        metrics = st.session_state['metrics']
        project_path = st.session_state['project_path']
        
        if st.session_state['current_page'] == 'dashboard':
            render_dashboard(metrics, project_path)
        elif st.session_state['current_page'] == 'issues':
            render_issues(metrics, project_path)
        elif st.session_state['current_page'] == 'files':
            render_files(metrics, project_path)
        elif st.session_state['current_page'] == 'meta_systems':
            render_meta_systems(metrics, project_path)
    else:
        st.title("Welcome to PAT Web UI")
        st.write("Please select a metrics file from the sidebar to get started.")
        
        # Show a demo image or instructions
        st.info("""
        ## Getting Started
        
        1. Run PAT analysis on your project
        2. Select the metrics file from the sidebar
        3. Explore the dashboard, issues, and files
        
        If you haven't run PAT analysis yet, you can do so with:
        ```
        python -m PAT_tool.main --project-path /path/to/your/project
        ```
        """)


if __name__ == "__main__":
    run_app()

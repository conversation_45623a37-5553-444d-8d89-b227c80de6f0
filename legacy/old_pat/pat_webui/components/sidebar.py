"""
Sidebar components for the PAT Web UI.
"""
import os
from pathlib import Path
import streamlit as st

from ..utils import get_available_tools

def render_sidebar():
    """Render the sidebar components.
    
    Returns:
        Dictionary containing the sidebar inputs
    """
    with st.sidebar:
        st.title("PAT - Project Analysis Tool")
        
        # Project path input
        project_path = st.text_input(
            "Project Path", 
            value=st.session_state.get('project_path', ''),
            help="Path to the project you want to analyze"
        )
        
        # Output directory input
        output_dir = st.text_input(
            "Output Directory", 
            value=st.session_state.get('output_dir', 'PAT_output'),
            help="Directory where analysis results will be saved"
        )
        
        # Tool selection
        st.subheader("Select Tools")
        
        # Get available tools
        all_tools = [
            "Ruff (Linter)", 
            "Bandit (Security)", 
            "Pytest (Tests)",
            "Mypy (Type Checker)",
            "Pyright (Type Checker)",
            "Black (Formatter)",
            "Isort (Import Sorter)",
            "<PERSON><PERSON><PERSON> (Linter)",
            "meta_system_separation"
        ]
        
        # Dynamically determine supported tools
        supported_tools = get_available_tools()
        
        # Create the options list, marking unsupported tools
        tool_options = []
        default_selection = []
        for t in all_tools:
            is_supported = t in supported_tools
            label = f"{t}{'' if is_supported else ' (unavailable)'}"
            tool_options.append(label)
            if is_supported and t in {"Ruff (Linter)", "Bandit (Security)", "Pytest (Tests)", "meta_system_separation"}:
                 default_selection.append(label)
        
        # Tool selection multi-select
        selected_tools = st.multiselect(
            "Tools to Run",
            options=tool_options,
            default=default_selection,
            help="Select the tools you want to run"
        )
        
        # Filter out unavailable tools and extract the tool names
        selected_tools = [t.split(' (unavailable)')[0] for t in selected_tools]
        
        # Log level selection
        log_level = st.selectbox(
            "Log Level",
            options=["DEBUG", "INFO", "WARNING", "ERROR"],
            index=1,  # Default to INFO
            help="Set the logging verbosity"
        )
        
        # Run button
        run_button = st.button("Run Analysis", type="primary")
        
        # Recent projects dropdown (if available)
        if 'recent_projects' in st.session_state and st.session_state['recent_projects']:
            st.subheader("Recent Projects")
            recent_project = st.selectbox(
                "Select a recent project",
                options=st.session_state['recent_projects'],
                help="Select a recently analyzed project"
            )
            
            if st.button("Load Selected Project"):
                project_path = recent_project
                st.session_state['project_path'] = project_path
                st.rerun()
        
        # About section
        with st.expander("About PAT"):
            st.markdown("""
            **Project Analysis Tool (PAT)** helps you analyze Python projects for issues, 
            code quality, and security vulnerabilities.
            
            - Run multiple analysis tools with a single click
            - Get comprehensive reports and visualizations
            - Identify high-priority issues to fix
            
            [View Documentation](https://github.com/yourusername/PAT)
            """)
    
    # Update session state
    st.session_state['project_path'] = project_path
    st.session_state['output_dir'] = output_dir
    st.session_state['selected_tools'] = selected_tools
    st.session_state['log_level'] = log_level
    
    # Return the inputs
    return {
        'project_path': project_path,
        'output_dir': output_dir,
        'selected_tools': selected_tools,
        'log_level': log_level,
        'run_button': run_button
    }

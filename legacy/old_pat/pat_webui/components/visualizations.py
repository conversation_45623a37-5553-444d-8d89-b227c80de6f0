"""
Visualization components for the PAT Web UI.
"""
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
import networkx as nx
from typing import Any, Dict, List, Optional
from pathlib import Path

import streamlit as st

# Import pandas within functions to avoid circular imports

def create_issue_summary_chart(metrics: Dict[str, Any]) -> go.Figure:
    """
    Create a bar chart showing issue counts by tool.

    Args:
        metrics: Dictionary containing project metrics

    Returns:
        Plotly figure object
    """
    # Count issues by tool
    tool_issues = {}
    for file_metric in metrics.get('files', {}).values():
        tool_results = file_metric.get('tool_results', {})

        for tool, results in tool_results.items():
            if tool not in tool_issues:
                tool_issues[tool] = 0

            if tool == 'mypy' and 'errors' in results:
                tool_issues[tool] += len(results['errors'])
            elif tool == 'ruff' and 'errors' in results:
                tool_issues[tool] += len(results['errors'])
            elif tool == 'bandit' and 'issues' in results:
                tool_issues[tool] += len(results['issues'])
            elif tool == 'pydocstyle' and 'issues' in results:
                tool_issues[tool] += len(results['issues'])
            elif tool == 'pylint' and 'errors' in results:
                tool_issues[tool] += len(results['errors'])

    # Create a DataFrame for the chart
    if tool_issues:
        df = pd.DataFrame([
            {"Tool": tool.capitalize(), "Issues": count}
            for tool, count in tool_issues.items()
        ])

        # Create the chart
        fig = px.bar(
            df,
            x="Tool",
            y="Issues",
            color="Tool",
            title="Issues by Tool",
            labels={"Tool": "Tool", "Issues": "Issue Count"},
            height=400
        )

        # Update layout
        fig.update_layout(
            xaxis_title="Tool",
            yaxis_title="Issue Count",
            showlegend=False
        )

        return fig

    # Return an empty figure if no issues
    return go.Figure()


def create_complexity_histogram(metrics: Dict[str, Any]) -> go.Figure:
    """
    Create a histogram showing complexity distribution.

    Args:
        metrics: Dictionary containing project metrics

    Returns:
        Plotly figure object
    """
    # Get complexity values
    complexities = [f.get('complexity', 0) for f in metrics.get('files', {}).values() if 'complexity' in f]

    # Create a DataFrame for the chart
    if complexities:
        df = pd.DataFrame({"Complexity": complexities})

        # Create the chart
        fig = px.histogram(
            df,
            x="Complexity",
            nbins=20,
            title="Complexity Distribution",
            labels={"Complexity": "Complexity"},
            height=400
        )

        # Update layout
        fig.update_layout(
            xaxis_title="Complexity",
            yaxis_title="File Count",
            showlegend=False
        )

        # Add a vertical line for the average
        avg_complexity = sum(complexities) / len(complexities) if complexities else 0
        fig.add_vline(
            x=avg_complexity,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Avg: {avg_complexity:.2f}",
            annotation_position="top right"
        )

        return fig

    # Return an empty figure if no complexity data
    return go.Figure()


def create_file_size_histogram(metrics: Dict[str, Any]) -> go.Figure:
    """
    Create a histogram showing file size distribution.

    Args:
        metrics: Dictionary containing project metrics

    Returns:
        Plotly figure object
    """
    # Get file sizes
    file_sizes = [f.get('lines', 0) for f in metrics.get('files', {}).values() if 'lines' in f]

    # Create a DataFrame for the chart
    if file_sizes:
        df = pd.DataFrame({"Lines": file_sizes})

        # Create the chart
        fig = px.histogram(
            df,
            x="Lines",
            nbins=20,
            title="File Size Distribution",
            labels={"Lines": "Lines of Code"},
            height=400
        )

        # Update layout
        fig.update_layout(
            xaxis_title="Lines of Code",
            yaxis_title="File Count",
            showlegend=False
        )

        # Add a vertical line for the average
        avg_lines = sum(file_sizes) / len(file_sizes) if file_sizes else 0
        fig.add_vline(
            x=avg_lines,
            line_dash="dash",
            line_color="red",
            annotation_text=f"Avg: {avg_lines:.2f}",
            annotation_position="top right"
        )

        return fig

    # Return an empty figure if no file size data
    return go.Figure()


def create_issue_severity_chart(metrics: Dict[str, Any]) -> go.Figure:
    """
    Create a pie chart showing issue counts by severity.

    Args:
        metrics: Dictionary containing project metrics

    Returns:
        Plotly figure object
    """
    # Count issues by severity
    severity_counts = {}
    for file_metric in metrics.get('files', {}).values():
        tool_results = file_metric.get('tool_results', {})

        for tool, results in tool_results.items():
            if tool == 'mypy' and 'errors' in results:
                severity_counts['error'] = severity_counts.get('error', 0) + len(results['errors'])
            elif tool == 'ruff' and 'errors' in results:
                for error in results['errors']:
                    severity = error.get('severity', 'warning')
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1
            elif tool == 'bandit' and 'issues' in results:
                for issue in results['issues']:
                    severity = issue.get('issue_severity', 'medium')
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1
            elif tool == 'pylint' and 'errors' in results:
                for error in results['errors']:
                    severity = error.get('type', 'warning')
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1

    # Create a DataFrame for the chart
    if severity_counts:
        df = pd.DataFrame([
            {"Severity": severity.capitalize(), "Count": count}
            for severity, count in severity_counts.items()
        ])

        # Create the chart
        fig = px.pie(
            df,
            values="Count",
            names="Severity",
            title="Issues by Severity",
            height=400,
            color="Severity",
            color_discrete_map={
                "Error": "#FF5252",
                "High": "#FF5252",
                "Warning": "#FFC107",
                "Medium": "#FFC107",
                "Info": "#2196F3",
                "Low": "#4CAF50",
                "Convention": "#9C27B0",
                "Refactor": "#FF9800",
                "Fatal": "#000000"
            }
        )

        # Update layout
        fig.update_layout(
            showlegend=True
        )

        return fig

    # Return an empty figure if no severity data
    return go.Figure()


def create_issue_type_chart(metrics: Dict[str, Any]) -> go.Figure:
    """
    Create a bar chart showing issue counts by type.

    Args:
        metrics: Dictionary containing project metrics

    Returns:
        Plotly figure object
    """
    # Count issues by type
    type_counts = {}
    for file_metric in metrics.get('files', {}).values():
        tool_results = file_metric.get('tool_results', {})

        for tool, results in tool_results.items():
            if tool == 'mypy' and 'errors' in results:
                for error in results['errors']:
                    error_type = error.get('type', 'unknown')
                    type_counts[f"mypy:{error_type}"] = type_counts.get(f"mypy:{error_type}", 0) + 1
            elif tool == 'ruff' and 'errors' in results:
                for error in results['errors']:
                    error_type = error.get('code', 'unknown')
                    type_counts[f"ruff:{error_type}"] = type_counts.get(f"ruff:{error_type}", 0) + 1
            elif tool == 'bandit' and 'issues' in results:
                for issue in results['issues']:
                    issue_type = issue.get('test_id', 'unknown')
                    type_counts[f"bandit:{issue_type}"] = type_counts.get(f"bandit:{issue_type}", 0) + 1
            elif tool == 'pydocstyle' and 'issues' in results:
                for issue in results['issues']:
                    issue_type = issue.get('code', 'unknown')
                    type_counts[f"pydocstyle:{issue_type}"] = type_counts.get(f"pydocstyle:{issue_type}", 0) + 1
            elif tool == 'pylint' and 'errors' in results:
                for error in results['errors']:
                    error_type = error.get('symbol', 'unknown')
                    type_counts[f"pylint:{error_type}"] = type_counts.get(f"pylint:{error_type}", 0) + 1

    # Create a DataFrame for the chart
    if type_counts:
        # Sort by count (descending) and take top 20
        sorted_types = sorted(type_counts.items(), key=lambda x: x[1], reverse=True)[:20]

        df = pd.DataFrame([
            {"Type": issue_type, "Count": count}
            for issue_type, count in sorted_types
        ])

        # Create the chart
        fig = px.bar(
            df,
            x="Count",
            y="Type",
            title="Top 20 Issue Types",
            labels={"Count": "Issue Count", "Type": "Issue Type"},
            height=600,
            orientation='h'
        )

        # Update layout
        fig.update_layout(
            xaxis_title="Issue Count",
            yaxis_title="Issue Type",
            showlegend=False,
            yaxis=dict(autorange="reversed")
        )

        return fig

    # Return an empty figure if no type data
    return go.Figure()


def create_meta_system_network(metrics: Dict[str, Any]) -> go.Figure:
    """
    Create a network graph showing meta-system dependencies.

    Args:
        metrics: Dictionary containing project metrics

    Returns:
        Plotly figure object
    """
    # Check if meta-systems are available
    if not hasattr(metrics, 'meta_systems') or not metrics.meta_systems:
        return go.Figure()

    # Create a graph
    G = nx.DiGraph()

    # Add nodes for each meta-system
    for name in metrics.meta_systems.keys():
        G.add_node(name)

    # Add edges for dependencies
    for name, meta_system in metrics.meta_systems.items():
        for dependency in meta_system.get('dependencies', []):
            if dependency in metrics.meta_systems:
                G.add_edge(name, dependency)

    # Use networkx spring layout
    pos = nx.spring_layout(G)

    # Create edge trace
    edge_x = []
    edge_y = []
    for edge in G.edges():
        x0, y0 = pos[edge[0]]
        x1, y1 = pos[edge[1]]
        edge_x.extend([x0, x1, None])
        edge_y.extend([y0, y1, None])

    edge_trace = go.Scatter(
        x=edge_x, y=edge_y,
        line=dict(width=1, color='#888'),
        hoverinfo='none',
        mode='lines')

    # Create node trace
    node_x = []
    node_y = []
    for node in G.nodes():
        x, y = pos[node]
        node_x.append(x)
        node_y.append(y)

    node_trace = go.Scatter(
        x=node_x, y=node_y,
        mode='markers+text',
        text=list(G.nodes()),
        textposition="top center",
        hoverinfo='text',
        marker=dict(
            showscale=True,
            colorscale='YlGnBu',
            size=15,
            colorbar=dict(
                thickness=15,
                title='Node Connections',
                xanchor='left',
                titleside='right'
            ),
            line_width=2))

    # Color nodes by number of connections
    node_adjacencies = []
    for node, adjacencies in enumerate(G.adjacency()):
        node_adjacencies.append(len(adjacencies[1]))

    node_trace.marker.color = node_adjacencies

    # Create the figure
    fig = go.Figure(data=[edge_trace, node_trace],
                    layout=go.Layout(
                        title='Meta-System Dependencies',
                        titlefont_size=16,
                        showlegend=False,
                        hovermode='closest',
                        margin=dict(b=20, l=5, r=5, t=40),
                        xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                        yaxis=dict(showgrid=False, zeroline=False, showticklabels=False))
                    )

    return fig

def render_summary_visualization(report_data):
    """Render the summary visualization.

    Args:
        report_data: Dictionary containing the report data
    """
    if not report_data:
        st.info("No report data available. Run an analysis first.")
        return

    # Extract metrics
    metrics = report_data.get('metrics', {})

    # Display key metrics
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric(
            "Files Analyzed",
            metrics.get('total_files', 0),
            help="Total number of files analyzed"
        )

    with col2:
        st.metric(
            "Issues Found",
            metrics.get('total_issues', 0),
            help="Total number of issues found"
        )

    with col3:
        st.metric(
            "High Priority",
            metrics.get('high_priority_issues', 0),
            help="Number of high priority issues"
        )

    with col4:
        st.metric(
            "Code Quality",
            f"{metrics.get('code_quality_score', 0):.1f}/10",
            help="Overall code quality score"
        )

    # Display charts
    st.subheader("Issue Distribution")

    # Issue distribution by type
    issue_types = report_data.get('issue_distribution', {})
    if issue_types:
        import pandas as pd
        df = pd.DataFrame({
            'Issue Type': list(issue_types.keys()),
            'Count': list(issue_types.values())
        })
        st.bar_chart(df.set_index('Issue Type'))
    else:
        st.info("No issue distribution data available.")

    # Display file complexity
    st.subheader("File Complexity")

    file_complexity = report_data.get('file_complexity', {})
    if file_complexity:
        # Import pandas locally
        import pandas as pd

        # Convert to DataFrame
        df = pd.DataFrame([
            {'File': file, 'Complexity': complexity}
            for file, complexity in file_complexity.items()
        ])

        # Sort by complexity
        df = df.sort_values('Complexity', ascending=False).head(10)

        # Display
        st.bar_chart(df.set_index('File'))
    else:
        st.info("No file complexity data available.")

def render_problematic_files(report_data):
    """Render the problematic files visualization.

    Args:
        report_data: Dictionary containing the report data
    """
    if not report_data:
        st.info("No report data available. Run an analysis first.")
        return

    # Extract problematic files
    problematic_files = report_data.get('problematic_files', [])

    if not problematic_files:
        st.success("No problematic files found!")
        return

    # Display problematic files
    st.subheader("Top Problematic Files")

    # Import pandas locally
    import pandas as pd

    # Convert to DataFrame
    df = pd.DataFrame(problematic_files)

    # Display as table
    st.dataframe(
        df,
        column_config={
            "file_path": st.column_config.TextColumn("File Path"),
            "issues": st.column_config.NumberColumn("Issues"),
            "complexity": st.column_config.NumberColumn("Complexity"),
            "score": st.column_config.ProgressColumn("Score", format="%.1f", min_value=0, max_value=10)
        },
        hide_index=True
    )

    # Display issue details for each file
    st.subheader("Issue Details")

    for file in problematic_files[:5]:  # Limit to top 5 for performance
        with st.expander(f"{file['file_path']} ({file['issues']} issues)"):
            # Get issues for this file
            file_issues = report_data.get('file_issues', {}).get(file['file_path'], [])

            if file_issues:
                # Import pandas locally
                import pandas as pd

                # Convert to DataFrame
                issues_df = pd.DataFrame(file_issues)

                # Display as table
                st.dataframe(
                    issues_df,
                    column_config={
                        "tool": st.column_config.TextColumn("Tool"),
                        "type": st.column_config.TextColumn("Type"),
                        "line": st.column_config.NumberColumn("Line"),
                        "message": st.column_config.TextColumn("Message"),
                        "severity": st.column_config.TextColumn("Severity")
                    },
                    hide_index=True
                )
            else:
                st.info("No detailed issues available for this file.")

def render_meta_system_visualization(report_data):
    """Render the meta-system separation visualization.

    Args:
        report_data: Dictionary containing the report data
    """
    # Check if meta-system separation analysis was run
    if 'meta_system_separation' not in report_data.get('tool_results', {}):
        st.info("Meta-system separation analysis was not run. Select 'meta_system_separation' in the tools list and run the analysis.")
        return

    meta_system_data = report_data['tool_results']['meta_system_separation']

    # Display summary
    violations = meta_system_data.get('violations', [])
    if not violations:
        st.success("No meta-system separation violations found. Great job!")
        st.markdown("""
        This means:

        - Meta-systems are properly separated
        - They only use common underlying components
        - The architecture follows good separation of concerns

        This separation makes your codebase more maintainable and easier to evolve.
        """)
    else:
        st.error(f"Found {len(violations)} meta-system separation violations.")
        st.markdown("Meta-systems should not import from each other directly, but can use common underlying components from the core.")

        # Count violations by source meta-system
        source_counts = {}
        for violation in violations:
            source = violation["source_meta"]
            source_counts[source] = source_counts.get(source, 0) + 1

        # Count violations by target meta-system
        target_counts = {}
        for violation in violations:
            target = violation["target_meta"]
            target_counts[target] = target_counts.get(target, 0) + 1

        # Display counts in columns
        col1, col2 = st.columns(2)
        with col1:
            st.subheader("Violations by Source Meta-System")
            for source, count in source_counts.items():
                st.markdown(f"- **{source}**: {count} violations")

        with col2:
            st.subheader("Violations by Target Meta-System")
            for target, count in target_counts.items():
                st.markdown(f"- **{target}**: {count} violations")

        # Display violations in a table
        st.subheader("All Violations")

        # Import pandas locally
        import pandas as pd

        violations_df = pd.DataFrame(violations)
        st.dataframe(violations_df)

        # Recommendations
        st.subheader("Recommendations")
        st.markdown("""
        To fix these violations:

        1. **Refactor the code** to remove direct dependencies between meta-systems
        2. **Move shared functionality** to the core or create proper interfaces
        3. **Use dependency injection** to decouple meta-systems
        4. **Create adapters** to translate between meta-systems if necessary
        5. **Run the meta-system separation analysis** regularly to catch new violations
        """)

    # Display visualization if available
    if 'meta_connections' in meta_system_data:
        st.subheader("Meta-System Connections")

        # Check if visualization files exist
        project_name = Path(st.session_state.get('project_path', '')).name
        graph_path = f"analysis/{project_name}_meta_system_graph.png"
        html_path = f"analysis/{project_name}_meta_system_report.html"

        if Path(graph_path).exists():
            st.image(graph_path, caption="Meta-System Connections Graph")

        if Path(html_path).exists():
            st.markdown(f"[Open Interactive Meta-System Report](file://{Path(html_path).absolute()})")

            # Add button to open in browser
            if st.button("Open Interactive Report in Browser"):
                import webbrowser
                webbrowser.open(f"file://{Path(html_path).absolute()}")

"""
Form components for the PAT Web UI.
"""
import streamlit as st
import yaml
from pathlib import Path

def render_advanced_config():
    """Render the advanced configuration form.
    
    Returns:
        Dictionary containing the form inputs
    """
    with st.expander("Advanced Configuration", expanded=False):
        # Config file options
        st.subheader("Configuration File")
        
        # Save config
        save_config_path = st.text_input(
            "Save Config Path",
            value=st.session_state.get('save_config_path', 'pat_config.yaml'),
            help="Path to save the current configuration"
        )
        
        save_config_button = st.button("Save Configuration")
        
        # Load config
        load_config_path = st.text_input(
            "Load Config Path",
            value=st.session_state.get('load_config_path', ''),
            help="Path to load a configuration file"
        )
        
        load_config_button = st.button("Load Configuration")
        
        # Reset config
        reset_config_button = st.button("Reset to Default Configuration")
        
        # Additional options
        st.subheader("Additional Options")
        
        # Parallel execution
        parallel = st.checkbox(
            "Parallel Execution",
            value=st.session_state.get('parallel', False),
            help="Run tools in parallel (may cause issues with some tools)"
        )
        
        # Skip existing
        skip_existing = st.checkbox(
            "Skip Existing Results",
            value=st.session_state.get('skip_existing', True),
            help="Skip tools that have already been run for this project"
        )
        
        # Update session state
        st.session_state['save_config_path'] = save_config_path
        st.session_state['load_config_path'] = load_config_path
        st.session_state['parallel'] = parallel
        st.session_state['skip_existing'] = skip_existing
        
        # Return the inputs
        return {
            'save_config_path': save_config_path,
            'save_config_button': save_config_button,
            'load_config_path': load_config_path,
            'load_config_button': load_config_button,
            'reset_config_button': reset_config_button,
            'parallel': parallel,
            'skip_existing': skip_existing
        }

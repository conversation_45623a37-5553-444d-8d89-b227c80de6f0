#!/usr/bin/env python3
"""
Simple PAT Runner Script

This script sets up the Python path correctly and runs the PAT tool.
It fixes the import issues by ensuring the PAT_tool directory is in the Python path.

Usage:
    python run_pat_simple.py <project_path>
"""

import os
import subprocess
import sys
from pathlib import Path


def main():
    """Main function to run the PAT tool with proper Python path setup."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Add the PAT_project_analysis directory to Python path
    sys.path.insert(0, str(pat_dir))
    
    # Add PAT_tool directory to Python path
    pat_tool_dir = pat_dir / "PAT_tool"
    sys.path.insert(0, str(pat_tool_dir))
    
    # Import the main module directly
    try:
        from PAT_tool import main

        # Call the main function
        main.main()
        return 0
    except Exception as e:
        print(f"Error running PAT tool: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())

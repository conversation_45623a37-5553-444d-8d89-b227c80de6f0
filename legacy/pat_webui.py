"""
PAT Web UI
==========

Web interface for the Project Analysis Tool (PAT).
Displays project metrics, issues, and recommendations.

This is a backward compatibility module that imports from the modularized
pat_webui package. New code should import directly from the package.
"""

import streamlit as st
import os
import sys
import json
from pathlib import Path

# Add the parent directory to the path to import PAT modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import from the modularized package
from pat_webui.app import run_app

if __name__ == "__main__":
    # Run the app
    run_app()

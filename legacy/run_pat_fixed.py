#!/usr/bin/env python3
"""
Fixed PAT Runner Script

This script sets up the Python path correctly and runs the PAT tool.
It fixes the import issues by ensuring the PAT_tool directory is in the Python path
and by creating a temporary wrapper script that properly handles imports.

Usage:
    python run_pat_fixed.py <project_path>
"""

import os
import subprocess
import sys
import tempfile
from pathlib import Path


def main():
    """Main function to run the PAT tool with proper Python path setup."""
    # Get the absolute path to the PAT_project_analysis directory
    pat_dir = Path(__file__).parent.absolute()
    
    # Create a temporary directory for our wrapper script
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create a temporary wrapper script
        wrapper_script = Path(temp_dir) / "pat_wrapper.py"
        
        # Write the wrapper script content
        with open(wrapper_script, "w") as f:
            f.write(f'''
#!/usr/bin/env python3
import sys
import os
from pathlib import Path

# Add PAT_project_analysis directory to Python path
sys.path.insert(0, "{str(pat_dir)}")

# Add PAT_tool directory to Python path
pat_tool_dir = Path("{str(pat_dir)}") / "PAT_tool"
sys.path.insert(0, str(pat_tool_dir))

# Create __init__.py files to make the directories proper packages
init_path = pat_tool_dir / "__init__.py"
if not init_path.exists():
    with open(init_path, "w") as f:
        f.write("# Auto-generated by run_pat_fixed.py\\n")

# Import and run the main function
from PAT_tool.main import main
if __name__ == "__main__":
    # Forward command line arguments
    sys.exit(main())
''')
        
        # Make the wrapper script executable
        os.chmod(wrapper_script, 0o755)
        
        # Run the wrapper script with the same arguments
        cmd = [sys.executable, str(wrapper_script)] + sys.argv[1:]
        try:
            return subprocess.call(cmd)
        except KeyboardInterrupt:
            print("\nAnalysis interrupted by user.")
            return 1
        except Exception as e:
            print(f"\nError running analysis tool: {e}")
            import traceback
            traceback.print_exc()
            return 1

if __name__ == "__main__":
    sys.exit(main())

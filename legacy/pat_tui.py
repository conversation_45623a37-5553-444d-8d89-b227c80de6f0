"""
PAT TUI (Textual User Interface) - Scaffold

A modern TUI for running PAT analyzers and external tools (<PERSON><PERSON>, <PERSON>it, Pytest)
with interactive selection of analyzers, path, and output format.

Run with: python pat_tui.py
"""
import asyncio
import json
import os
import re
import shlex
from pathlib import Path
from typing import Any

from textual.app import App, ComposeResult
from textual.containers import Horizontal, Vertical
from textual.reactive import reactive
from textual.screen import Screen
from textual.scroll_view import ScrollView
from textual.widgets import But<PERSON>, Checkbox, Footer, Header
from textual.widgets import Input
from textual.widgets import Input as TextInput
from textual.widgets import Label, Select, SelectionList, Static

ANALYZERS = [
    ("PAT Analyzers", True),
    ("Ruff (Linter)", True),
    ("Pylint", False),
    ("PyCodeStyle", False),
    ("Radon (Complexity)", False),
    ("Vulture (Dead Code)", False),
    ("Mc<PERSON>abe (Complexity)", False),
    ("Mypy (Type Checking)", False),
    ("Pyright (Type Checking)", False),
    ("Pyre-Check (Type Checking)", False),
    ("Bandit (Security)", True),
    ("Safety (Dependencies)", False),
    ("Black (Formatter)", False),
    ("Isort (Imports)", False),
    ("Pydocstyle (Docstrings)", False),
    ("Pipdeptree (Dependencies)", False),
    ("Pytest (Tests)", True),
    ("Pytest-JSON-Report", False),
    ("Coverage", False),
]
OUTPUT_FORMATS = [
    ("Console", "console"),
    ("JSON", "json"),
    ("HTML", "html"),
    ("Coverage", "coverage"),
    ("Security", "security"),
]

TOOL_COMMANDS = {
    "Ruff (Linter)": lambda path, tests: ["ruff", path, "--format=json"],
    "Pylint": lambda path, tests: ["pylint", path, "--output-format=json"],
    "PyCodeStyle": lambda path, tests: ["pycodestyle", path, "--format=pylint"],
    "Radon (Complexity)": lambda path, tests: ["radon", "cc", path, "-j"],
    "Vulture (Dead Code)": lambda path, tests: ["vulture", path],
    "McCabe (Complexity)": lambda path, tests: ["python", "-m", "mccabe", path],
    "Mypy (Type Checking)": lambda path, tests: ["mypy", path, "--show-error-codes", "--no-color-output"],
    "Pyright (Type Checking)": lambda path, tests: ["pyright", path, "--outputjson"],
    "Pyre-Check (Type Checking)": lambda path, tests: ["pyre", "--output=json"],
    "Bandit (Security)": lambda path, tests: ["bandit", "-r", path, "-f", "json"],
    "Safety (Dependencies)": lambda path, tests: ["safety", "check", "--full-report", "--json"],
    "Black (Formatter)": lambda path, tests: ["black", "--check", path],
    "Isort (Imports)": lambda path, tests: ["isort", "--check-only", path],
    "Pydocstyle (Docstrings)": lambda path, tests: ["pydocstyle", path, "--convention=google"],
    "Pipdeptree (Dependencies)": lambda path, tests: ["pipdeptree", "--json-tree"],
    "Pytest (Tests)": lambda path, tests: ["pytest"] + (tests if tests else []),
    "Pytest-JSON-Report": lambda path, tests: ["pytest", "--json-report"] + (tests if tests else []),
    "Coverage": lambda path, tests: ["coverage", "run", "-m", "pytest"] + (tests if tests else []) + ["&&", "coverage", "json"],
}

async def run_tool(tool_name: str, path: str, tests: list[str]) -> dict:
    cmd = TOOL_COMMANDS[tool_name](path, tests)
    # Handle '&&' for coverage as a special case
    if "&&" in cmd:
        first_cmd = cmd[:cmd.index("&&")]
        second_cmd = cmd[cmd.index("&&")+1:]
        try:
            proc1 = await asyncio.create_subprocess_exec(*first_cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)
            out1, err1 = await proc1.communicate()
            proc2 = await asyncio.create_subprocess_exec(*second_cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)
            out2, err2 = await proc2.communicate()
            return {"tool": tool_name, "stdout": out1.decode() + out2.decode(), "stderr": err1.decode() + err2.decode()}
        except FileNotFoundError:
            return {"tool": tool_name, "error": "Tool not installed"}
    try:
        proc = await asyncio.create_subprocess_exec(*cmd, stdout=asyncio.subprocess.PIPE, stderr=asyncio.subprocess.PIPE)
        out, err = await proc.communicate()
        return {"tool": tool_name, "stdout": out.decode(), "stderr": err.decode()}
    except FileNotFoundError:
        return {"tool": tool_name, "error": "Tool not installed"}

async def run_selected_tools(selected_tools: list[str], path: str, tests: list[str]) -> list[dict]:
    tasks = [run_tool(tool, path, tests) for tool in selected_tools if tool in TOOL_COMMANDS]
    return await asyncio.gather(*tasks)

def parse_tool_output(tool: str, stdout: str, stderr: str) -> tuple[str, str]:
    """
    Parse tool output and return (summary, detail) strings with color/highlighting.
    """
    try:
        if tool == "Ruff (Linter)":
            data = json.loads(stdout)
            if not data:
                return "[green]No issues[/green]", "No issues found."
            # Group by file
            issues_by_file = {}
            for issue in data:
                issues_by_file.setdefault(issue['filename'], []).append(issue)
            summary = f"[red]{len(data)} issues in {len(issues_by_file)} files[/red]"
            detail = ""
            for fname, issues in issues_by_file.items():
                detail += f"[bold]{fname}[/bold]:\n"
                for i in issues:
                    detail += f"  [red]{i['code']}[/red] L{i['location']['row']}: {i['message']}\n"
            return summary, detail[:3000]
        if tool == "Pylint":
            data = json.loads(stdout)
            if not data:
                return "[green]No issues[/green]", "No issues found."
            types = {}
            for d in data:
                types.setdefault(d.get("type", "other"), []).append(d)
            summary = ", ".join([
                f"[red]{len(types.get('error', []))} errors[/red]",
                f"[yellow]{len(types.get('warning', []))} warnings[/yellow]",
                f"[blue]{len(types.get('refactor', []))} refactor[/blue]",
                f"[magenta]{len(types.get('convention', []))} convention[/magenta]"
            ])
            detail = ""
            for t, items in types.items():
                detail += f"[bold]{t.title()}[/bold]:\n"
                for i in items[:10]:
                    detail += f"  {i.get('path', '')}:{i.get('line', '')} {i.get('symbol', '')}: {i.get('message', '')}\n"
                if len(items) > 10:
                    detail += f"  ...and {len(items)-10} more\n"
            return summary, detail[:3000]
        if tool == "Bandit (Security)":
            data = json.loads(stdout)
            issues = data.get("results", [])
            if not issues:
                return "[green]No security issues[/green]", "No issues found."
            by_severity = {}
            for i in issues:
                by_severity.setdefault(i['issue_severity'], []).append(i)
            summary = ", ".join([f"[red]{k}: {len(v)}[/red]" for k, v in by_severity.items()])
            detail = ""
            for sev, items in by_severity.items():
                detail += f"[bold]{sev}[/bold]:\n"
                for i in items[:5]:
                    detail += f"  {i['filename']}:{i['line_number']} {i['issue_text']} ([blue]{i['test_id']}[/blue])\n"
                if len(items) > 5:
                    detail += f"  ...and {len(items)-5} more\n"
            return summary, detail[:3000]
        if tool == "Safety (Dependencies)":
            data = json.loads(stdout)
            vulns = data.get("vulnerabilities", [])
            if not vulns:
                return "[green]No vulnerabilities[/green]", "No vulnerabilities found."
            summary = f"[red]{len(vulns)} vulnerable packages[/red]"
            detail = ""
            for v in vulns:
                detail += f"[bold]{v['package_name']}[/bold] {v['affected_versions']} CVE: {v.get('cve', 'N/A')}\n  {v['advisory']}\n"
            return summary, detail[:3000]
        if tool == "Pytest-JSON-Report":
            data = json.loads(stdout)
            s = data.get('summary', {})
            summary = f"[green]Passed: {s.get('passed', 0)}[/green], [red]Failed: {s.get('failed', 0)}[/red], Skipped: {s.get('skipped', 0)}"
            detail = ""
            for test in data.get('tests', []):
                if test.get('outcome') == 'failed':
                    detail += f"[red]{test['nodeid']}[/red]: {test.get('longrepr', '')}\n"
            if not detail:
                detail = "All tests passed."
            return summary, detail[:3000]
        if tool == "Coverage":
            data = json.loads(stdout)
            totals = data.get("totals", {})
            cov = totals.get("percent_covered", 0)
            summary = f"[green]{cov}% covered[/green]"
            detail = ""
            for f, d in data.get('files', {}).items():
                detail += f"[bold]{f}[/bold]: {d.get('percent_covered', 0)}%\n"
            return summary, detail[:3000]
        if tool == "Radon (Complexity)":
            data = json.loads(stdout)
            summary = f"[blue]Complexity report for {len(data)} files[/blue]"
            detail = ""
            for fname, items in data.items():
                detail += f"[bold]{fname}[/bold]:\n"
                for i in items:
                    detail += f"  {i['name']} (L{i['lineno']}): {i['complexity']} ({i['rank']})\n"
            return summary, detail[:3000]
        if tool == "Vulture (Dead Code)":
            # Plain text, one dead code per line
            lines = [l for l in stdout.splitlines() if l.strip()]
            summary = f"[red]{len(lines)} dead code locations[/red]" if lines else "[green]No dead code[/green]"
            detail = "\n".join(lines[:20])
            if len(lines) > 20:
                detail += f"\n...and {len(lines)-20} more"
            return summary, detail[:3000]
        if tool in {"Mypy (Type Checking)", "Pyright (Type Checking)", "Pyre-Check (Type Checking)"}:
            # Try JSON, else plain
            try:
                data = json.loads(stdout)
                errors = data.get("generalDiagnostics", []) if isinstance(data, dict) else data
                summary = f"[red]{len(errors)} type errors[/red]" if errors else "[green]No type errors[/green]"
                detail = json.dumps(errors, indent=2)[:3000]
                return summary, detail
            except Exception:
                # Fallback: plain text
                lines = [l for l in stdout.splitlines() if l.strip()]
                errors = [l for l in lines if 'error' in l.lower()]
                summary = f"[red]{len(errors)} type errors[/red]" if errors else "[green]No type errors[/green]"
                detail = "\n".join(errors[:20])
                return summary, detail[:3000]
        if tool in {"Black (Formatter)", "Isort (Imports)", "Pydocstyle (Docstrings)"}:
            lines = [l for l in stdout.splitlines() if l.strip()]
            issues = [l for l in lines if 'would reformat' in l or 'error' in l or 'missing' in l]
            summary = f"[red]{len(issues)} files with issues[/red]" if issues else "[green]All files OK[/green]"
            detail = "\n".join(issues[:20])
            return summary, detail[:3000]
        if tool == "Pipdeptree (Dependencies)":
            data = json.loads(stdout)
            summary = f"[blue]Dependency tree generated[/blue]"
            detail = json.dumps(data, indent=2)[:3000]
            return summary, detail
    except Exception as e:
        return f"[red]Failed to parse output: {e}[/red]", stdout[:1000]
    # Fallback: generic error/warning regex
    error_count = len(re.findall(r"error|failed|exception", stdout, re.I))
    warning_count = len(re.findall(r"warn", stdout, re.I))
    if error_count or warning_count:
        summary = f"[red]{error_count} errors[/red], [yellow]{warning_count} warnings[/yellow]"
    else:
        summary = "[green]No errors or warnings detected[/green]"
    detail = stdout[:3000]
    if stderr:
        detail += f"\n[dim]stderr:[/dim] {stderr[:1000]}"
    return summary, detail

def make_export_data(tool_summaries, tool_details):
    """Collects exportable data for all tools."""
    export = {}
    for tool, summary in tool_summaries.items():
        export[tool] = {
            "summary": summary,
            "detail": tool_details.get(tool, "")
        }
    return export

def export_short_report(export_data: dict[str, Any], path: str, tools=None):
    with open(path, "w") as f:
        for tool, data in export_data.items():
            if tools and tool not in tools:
                continue
            f.write(f"{tool}: {data['summary'].replace('[','').replace(']','') }\n")

def export_log_report(export_data: dict[str, Any], path: str, tools=None):
    with open(path, "w") as f:
        for tool, data in export_data.items():
            if tools and tool not in tools:
                continue
            f.write(f"[{tool}]\n{data['detail']}\n---\n")

def export_full_json(export_data: dict[str, Any], path: str, tools=None):
    with open(path, "w") as f:
        if tools:
            json.dump({k: v for k, v in export_data.items() if k in tools}, f, indent=2)
        else:
            json.dump(export_data, f, indent=2)

def export_prompts(export_data: dict[str, Any], path: str, tools=None):
    with open(path, "w") as f:
        for tool, data in export_data.items():
            if tools and tool not in tools:
                continue
            if tool == "Ruff (Linter)":
                # Parse issues for prompts
                try:
                    issues = []
                    for line in data['detail'].splitlines():
                        if line.startswith("  [red"):
                            parts = line.split()
                            code = parts[0].replace("[red]","").replace("[/red]","")
                            line_num = parts[1][1:] if len(parts) > 1 else "?"
                            msg = " ".join(parts[2:])
                            issues.append((code, line_num, msg))
                    for code, line_num, msg in issues:
                        f.write(f"[Ruff] Issue in line {line_num}\nMessage: {msg}\nContext: (see code)\nSuggestion: See Ruff docs for {code}\n---\n")
                except Exception:
                    f.write(f"[Ruff] Could not parse issues.\n---\n")
            elif tool == "Bandit (Security)":
                try:
                    for line in data['detail'].splitlines():
                        if line.strip().startswith("  "):
                            parts = line.strip().split()
                            if len(parts) > 2:
                                file_line = parts[0]
                                msg = " ".join(parts[1:-1])
                                test_id = parts[-1]
                                f.write(f"[Bandit] Issue in {file_line}\nMessage: {msg}\nContext: (see code)\nSuggestion: See Bandit docs for {test_id}\n---\n")
                except Exception:
                    f.write(f"[Bandit] Could not parse issues.\n---\n")
            elif tool == "Safety (Dependencies)":
                try:
                    for line in data['detail'].splitlines():
                        if line.startswith("[bold"):
                            pkg = line.split()[0].replace("[bold]","").replace("[/bold]","")
                            rest = " ".join(line.split()[1:])
                            f.write(f"[Safety] Vulnerable package: {pkg}\n{rest}\n---\n")
                        elif "CVE:" in line:
                            f.write(f"[Safety] {line}\n---\n")
                except Exception:
                    f.write(f"[Safety] Could not parse vulnerabilities.\n---\n")
            elif tool == "Coverage":
                try:
                    for line in data['detail'].splitlines():
                        if "%" in line:
                            parts = line.split(":")
                            if len(parts) == 2:
                                fname, cov = parts
                                cov_val = int(cov.strip().replace("%", "").replace("[/bold]", ""))
                                if cov_val < 80:
                                    f.write(f"[Coverage] Low coverage in {fname.strip()}: {cov.strip()}\n---\n")
                except Exception:
                    f.write(f"[Coverage] Could not parse coverage.\n---\n")
            elif tool == "Radon (Complexity)":
                try:
                    for line in data['detail'].splitlines():
                        if ")" in line and "(" in line:
                            f.write(f"[Radon] {line}\nContext: (see code)\n---\n")
                except Exception:
                    f.write(f"[Radon] Could not parse complexity.\n---\n")
            elif tool == "Vulture (Dead Code)":
                try:
                    for line in data['detail'].splitlines():
                        if line.strip():
                            f.write(f"[Vulture] Dead code: {line.strip()}\n---\n")
                except Exception:
                    f.write(f"[Vulture] Could not parse dead code.\n---\n")
            elif tool in {"Black (Formatter)", "Isort (Imports)", "Pydocstyle (Docstrings)"}:
                try:
                    for line in data['detail'].splitlines():
                        if line.strip():
                            f.write(f"[{tool}] {line.strip()}\n---\n")
                except Exception:
                    f.write(f"[{tool}] Could not parse issues.\n---\n")
            elif tool in {"Mypy (Type Checking)", "Pyright (Type Checking)", "Pyre-Check (Type Checking)"}:
                try:
                    for line in data['detail'].splitlines():
                        if "error" in line.lower():
                            f.write(f"[{tool}] {line.strip()}\n---\n")
                except Exception:
                    f.write(f"[{tool}] Could not parse type errors.\n---\n")
            elif tool == "Pipdeptree (Dependencies)":
                try:
                    for line in data['detail'].splitlines():
                        if "conflict" in line.lower() or "missing" in line.lower():
                            f.write(f"[Pipdeptree] {line.strip()}\n---\n")
                except Exception:
                    f.write(f"[Pipdeptree] Could not parse dependency issues.\n---\n")
            elif tool == "Pytest-JSON-Report":
                try:
                    for line in data['detail'].splitlines():
                        if line.startswith("[red"):
                            nodeid = line.split()[0].replace("[red]","").replace("[/red]","")
                            msg = " ".join(line.split()[1:])
                            f.write(f"[Pytest] Failed test: {nodeid}\nMessage: {msg}\nContext: (see traceback above)\n---\n")
                except Exception:
                    f.write(f"[Pytest] Could not parse failed tests.\n---\n")
            elif tool == "Pylint":
                try:
                    for line in data['detail'].splitlines():
                        if ":" in line and ("error" in line or "warning" in line):
                            f.write(f"[Pylint] {line}\nContext: (see code)\n---\n")
                except Exception:
                    f.write(f"[Pylint] Could not parse issues.\n---\n")
            else:
                # Fallback: split by lines
                lines = data['detail'].splitlines()
                prompt = ""
                for line in lines:
                    if line.strip().startswith("[") or line.strip().startswith(tool):
                        if prompt:
                            f.write(prompt + "\n---\n")
                            prompt = ""
                    prompt += f"{line}\n"
                if prompt:
                    f.write(prompt + "\n---\n")

def sanitize_id(name: str) -> str:
    return re.sub(r'[^a-zA-Z0-9_-]', '_', name)

class ExportDialogScreen(Screen):
    def __init__(self, tools, formats, on_export, **kwargs):
        super().__init__(**kwargs)
        self.tools = tools
        self.formats = formats
        self.on_export = on_export
        self.selected_tools = set(tools)
        self.selected_formats = set(formats)
        self.export_dir = "./"
        self.use_selection_list = True
        try:
            from textual.widgets import SelectionList
        except ImportError:
            self.use_selection_list = False
    def compose(self) -> ComposeResult:
        yield Static("Select export formats:")
        if self.use_selection_list:
            yield SelectionList(*[(fmt, fmt) for fmt in self.formats], id="format_list")
        else:
            for fmt in self.formats:
                yield Checkbox(fmt, value=True, id=f"fmt_{sanitize_id(fmt)}")
        yield Static("Select tools to include:")
        if self.use_selection_list:
            yield SelectionList(*[(tool, tool) for tool in self.tools], id="tool_list")
        else:
            for tool in self.tools:
                yield Checkbox(tool, value=True, id=f"tool_{sanitize_id(tool)}")
        yield Static("Export directory:")
        yield TextInput(value=self.export_dir, id="export_dir")
        yield Button("Export", id="btn_do_export", variant="success")
        yield Button("Cancel", id="btn_cancel_export", variant="error")
    def on_button_pressed(self, event):
        if event.button.id == "btn_do_export":
            if self.use_selection_list:
                self.selected_formats = set(self.query_one("#format_list").selected_values)
                self.selected_tools = set(self.query_one("#tool_list").selected_values)
            else:
                self.selected_formats = set(fmt for fmt in self.formats if self.query_one(f"#fmt_{sanitize_id(fmt)}").value)
                self.selected_tools = set(tool for tool in self.tools if self.query_one(f"#tool_{sanitize_id(tool)}").value)
            self.export_dir = self.query_one("#export_dir").value.strip() or "./"
            self.app.pop_screen()
            self.on_export(self.selected_tools, self.selected_formats, self.export_dir)
        elif event.button.id == "btn_cancel_export":
            self.app.pop_screen()

class PATMainMenu(ScrollView):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, can_focus=True, **kwargs)
        self.show_vertical_scrollbar = True
    def compose(self) -> ComposeResult:
        yield Label("PAT Analysis Tool", id="title")
        yield Static("[Tab] to move, [Space] to select, [Enter] to activate button, [↑/↓ to scroll, Ctrl+M to focus menu, q to quit]", id="instructions", markup=False)
        yield Label("Select analyzers/tools:")
        for name, checked in ANALYZERS:
            yield Checkbox(name, value=checked, id=f"chk_{sanitize_id(name.lower())}")
        yield Label("Select project path:")
        yield Input(placeholder="Enter path to analyze...", id="input_path")
        yield Button("Scan for Tests", id="btn_scan_tests", variant="primary")
        yield Static("", id="test_status")
        yield Static("", id="test_menu")
        yield Label("Select output/report types:")
        for name, value in OUTPUT_FORMATS:
            yield Checkbox(name, value=(value == "console"), id=f"chk_report_{sanitize_id(value)}")
        yield Button("Run Analysis", id="btn_run", variant="success", disabled=True)
        yield Button("Export Results", id="btn_export", variant="primary", disabled=True)
        yield Static("", id="status")
        yield Static("[↑/↓ to scroll menu]", id="scroll_hint", markup=False)
        yield Horizontal(
            Button("▲", id="btn_scroll_up", variant="primary"),
            Button("▼", id="btn_scroll_down", variant="primary"),
            id="scroll_buttons"
        )
    def on_button_pressed(self, event):
        if event.button.id == "btn_scroll_up":
            self.scroll_to(y=max(self.scroll_y - 5, 0))
        elif event.button.id == "btn_scroll_down":
            self.scroll_to(y=self.scroll_y + 5)

class PATResultsView(ScrollView):
    def update_results(self, results: str):
        for child in list(self.children):
            self.remove(child)
        self.mount(Static(results))
        # Always clear navigation callbacks when showing summary
        self._on_back = None
        self._on_next = None
        self._on_prev = None
    def show_tool_detail(self, tool: str, detail: str, on_back, on_next=None, on_prev=None):
        nav = "[blue][Back to Summary][/blue]"
        if on_prev:
            nav = "[blue][Previous][/blue] " + nav
        if on_next:
            nav = nav + " [blue][Next][/blue]"
        for child in list(self.children):
            self.remove(child)
        self.mount(Static(f"[bold]{tool} - Details[/bold]\n\n{detail}\n\n{nav}"))
        self._on_back = on_back if on_back else None
        self._on_next = on_next if on_next else None
        self._on_prev = on_prev if on_prev else None
    def on_click(self, event=None):
        # Simulate button navigation by click position (simple demo)
        if getattr(self, '_on_back', None):
            self._on_back()

class PATApp(App):
    CSS_PATH = None
    BINDINGS = [
        ("q", "quit", "Quit"),
        ("ctrl+m", "focus_menu", "Focus Menu"),
    ]
    selected_analyzers = reactive(set)
    selected_path = reactive("")
    selected_output = reactive("console")
    selected_tests = reactive(set)
    test_files = reactive([])
    tool_summaries = reactive({})
    tool_details = reactive({})
    current_tool_idx = reactive(0)
    export_data = reactive({})

    def compose(self) -> ComposeResult:
        yield Header()
        yield PATMainMenu(id="main_menu")
        yield PATResultsView(id="results_view")
        yield Footer()

    def on_mount(self):
        self.query_one("#results_view").update_results("[Results will appear here after analysis]")
        # Set initial focus to tool selection
        self.set_focus(self.query_one("#chk_pat_analyzers"))

    def on_input_submitted(self, event):
        # If Enter is pressed in the path input, trigger scan for tests and enable Run Analysis if valid
        if event.input.id == "input_path":
            self.scan_for_tests()
            menu = self.query_one("#main_menu")
            path = menu.query_one("#input_path").value.strip()
            menu.query_one("#btn_run").disabled = not (os.path.isdir(path))

    def on_input_changed(self, event):
        # Enable Run Analysis button only if path is valid
        if event.input.id == "input_path":
            menu = self.query_one("#main_menu")
            path = menu.query_one("#input_path").value.strip()
            menu.query_one("#btn_run").disabled = not (os.path.isdir(path))

    def on_button_pressed(self, event):
        if event.button.id == "btn_scan_tests":
            self.scan_for_tests()
        elif event.button.id == "btn_run":
            self.run_analysis()
        elif event.button.id == "btn_export":
            # Show export dialog as a custom Screen overlay
            tools = list(self.export_data.keys())
            formats = ["short", "log", "json", "prompts"]
            def do_export(selected_tools, selected_formats, export_dir):
                # Error handling for invalid export path
                if not os.path.isdir(export_dir) or not os.access(export_dir, os.W_OK):
                    self.query_one("#main_menu").query_one("#status").update(f"[red]Export directory '{export_dir}' does not exist or is not writable.[/red]")
                    return
                if "short" in selected_formats:
                    export_short_report(self.export_data, f"{export_dir}/short_report.txt", selected_tools)
                if "log" in selected_formats:
                    export_log_report(self.export_data, f"{export_dir}/log_report.txt", selected_tools)
                if "json" in selected_formats:
                    export_full_json(self.export_data, f"{export_dir}/full_report.json", selected_tools)
                if "prompts" in selected_formats:
                    export_prompts(self.export_data, f"{export_dir}/prompts.txt", selected_tools)
                self.query_one("#main_menu").query_one("#status").update("[green]Exported selected reports.")
            self.push_screen(ExportDialogScreen(tools, formats, do_export))

    def scan_for_tests(self):
        menu = self.query_one("#main_menu")
        path = menu.query_one("#input_path").value.strip()
        test_status = menu.query_one("#test_status")
        test_menu = menu.query_one("#test_menu")
        if not path or not os.path.isdir(path):
            test_status.update("[red]Invalid or missing path. Please enter a valid directory.[/red]")
            test_menu.update("")
            self.test_files = []
            self.selected_tests = set()
            menu.query_one("#btn_run").disabled = True
            return
        # Scan for test files
        test_files = []
        for root, dirs, files in os.walk(path):
            for file in files:
                if file.startswith("test_") and file.endswith(".py") or file.endswith("_test.py"):
                    test_files.append(os.path.join(root, file))
        self.test_files = test_files
        if not test_files:
            test_status.update("[yellow]No test files found.[/yellow]")
            test_menu.update("")
            self.selected_tests = set()
            menu.query_one("#btn_run").disabled = False  # Allow running analysis even if no tests
            return
        test_status.update(f"[green]Found {len(test_files)} test files.[/green]")
        # Show checkboxes for each test file
        test_menu_content = "Select tests to run:\n" + "\n".join(
            f"[ ] {os.path.relpath(f, path)}" for f in test_files
        )
        test_menu.update(test_menu_content)
        self.selected_tests = set(test_files)
        menu.query_one("#btn_run").disabled = False

    def run_analysis(self):
        menu = self.query_one("#main_menu")
        analyzers = [name for name, _ in ANALYZERS if menu.query_one(f"#chk_{sanitize_id(name.lower())}").value]
        path = menu.query_one("#input_path").value.strip()
        reports = [value for name, value in OUTPUT_FORMATS if menu.query_one(f"#chk_report_{sanitize_id(value)}").value]
        tests = list(self.selected_tests) if self.selected_tests else self.test_files
        self.query_one("#results_view").update_results("[yellow]Running analysis...[/yellow]")
        async def do_run():
            results = await run_selected_tools(analyzers, path, tests)
            self.tool_summaries = {}
            self.tool_details = {}
            for res in results:
                tool = res.get("tool", "Unknown")
                if "error" in res:
                    self.tool_summaries[tool] = f"[red]{tool}: {res['error']}[/red]"
                    self.tool_details[tool] = res.get("stderr", "")
                else:
                    summary, detail = parse_tool_output(tool, res.get("stdout", ""), res.get("stderr", ""))
                    self.tool_summaries[tool] = f"[bold]{tool}[/bold]: {summary} [blue][View Details][/blue]"
                    self.tool_details[tool] = detail
            self.export_data = make_export_data(self.tool_summaries, self.tool_details)
            self.show_summary()
            menu.query_one("#btn_export").disabled = False
        asyncio.create_task(do_run())

    def show_summary(self):
        # Show all tool summaries with clickable detail links
        content = "\n".join(self.tool_summaries.values())
        self.query_one("#results_view").update_results(content)
        self.current_tool_idx = 0

    def show_tool_detail(self, idx):
        tool_names = list(self.tool_summaries.keys())
        if not (0 <= idx < len(tool_names)):
            return
        tool = tool_names[idx]
        detail = self.tool_details.get(tool, "No details available.")
        self.query_one("#results_view").show_tool_detail(
            tool, detail,
            on_back=self.show_summary,
            on_next=(lambda: self.show_tool_detail(idx+1)) if idx+1 < len(tool_names) else None,
            on_prev=(lambda: self.show_tool_detail(idx-1)) if idx > 0 else None
        )
        self.current_tool_idx = idx

    def on_mouse_down(self, event):
        # Detect which tool's detail was clicked
        if not hasattr(self, 'tool_summaries') or not self.tool_summaries:
            return
        y = event.y
        idx = y - 1  # skip header
        tool_names = list(self.tool_summaries.keys())
        if 0 <= idx < len(tool_names):
            self.show_tool_detail(idx)

    def on_key(self, event):
        results_view = self.query_one("#results_view")
        on_back = getattr(results_view, "_on_back", None)
        if on_back:
            if event.key in ("b", "escape"):
                self.show_summary()
            elif event.key in ("j", "down"):
                self.show_tool_detail(self.current_tool_idx + 1)
            elif event.key in ("k", "up"):
                self.show_tool_detail(self.current_tool_idx - 1)
        if event.key == "q":
            self.exit()

    def action_focus_menu(self):
        self.set_focus(self.query_one("#main_menu"))

if __name__ == "__main__":
    PATApp().run() 
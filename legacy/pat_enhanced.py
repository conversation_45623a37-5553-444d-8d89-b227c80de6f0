#!/usr/bin/env python3
"""
PAT Enhanced - Project Analysis Tool with Plugin System

An enhanced version of PAT with a plugin system, performance optimization,
and synergy framework.

Usage:
    python pat_enhanced.py <project_path> [options]

Options:
    --mode MODE         Analysis mode: 'standard', 'full', 'quick', 'parallel' (default: standard)
    --output DIR        Custom output directory (default: PAT_output/timestamp)
    --focus FILE        Focus analysis on a specific file or directory
    --plugins DIR       Directory containing additional plugins
    --no-cache          Disable result caching
    --verbose           Show detailed output
    --help              Show this help message and exit
"""

import argparse
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from pat_plugins.analyzers import CAWAnalyzerPlugin
# Import plugin system
from pat_plugins.core import PluginInterface, PluginManager
from pat_plugins.performance import AnalysisCacheManager
# Import synergy framework
from pat_synergy import AnalysisOrchestrator, AnalysisTool, SynergyRule
# Import core PAT components
from PAT_tool.main import setup_logging


# ANSI color codes for terminal output
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'


class SynergyCAWTool(AnalysisTool):
    """Adapter that wraps the CAW Analyzer Plugin for use with the Synergy framework."""
    
    def __init__(self, plugin: CAWAnalyzerPlugin):
        """
        Initialize the CAW tool.
        
        Args:
            plugin: The CAW analyzer plugin instance
        """
        super().__init__("caw_analyzer")
        self.plugin = plugin
    
    def analyze(self, project_path, focus_path=None):
        """Run the CAW analysis on a project."""
        return self.plugin.analyze_project(Path(project_path))
    
    def get_capabilities(self):
        """Get the capabilities of this tool."""
        return ["caw_pattern_detection", "code_quality"]


class PATEnhanced:
    """
    Enhanced PAT with plugin system, performance optimization, and synergy framework.
    """
    
    def __init__(self, args):
        """
        Initialize the enhanced PAT.
        
        Args:
            args: Command-line arguments
        """
        self.args = args
        self.project_path = Path(args.project_path).absolute()
        
        # Create timestamp for output directory
        self.timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
        
        # Set output directory
        if args.output:
            self.output_dir = Path(args.output).absolute()
        else:
            self.output_dir = Path("PAT_output") / self.timestamp
        
        # Create output directory
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self.logger = setup_logging(self.output_dir / "pat_enhanced.log", args.verbose)
        
        # Initialize plugin manager
        plugin_dirs = ["pat_plugins"]
        if args.plugins:
            plugin_dirs.append(args.plugins)
        self.plugin_manager = PluginManager(plugin_dirs)
        
        # Initialize synergy orchestrator
        self.orchestrator = AnalysisOrchestrator()
        
        # Initialize performance metrics
        self.start_time = time.time()
        
        # Print banner
        self._print_banner()
    
    def _print_banner(self):
        """Print the PAT Enhanced banner."""
        print(f"\n{Colors.BOLD}{Colors.HEADER}{'=' * 80}{Colors.ENDC}")
        print(f"{Colors.BOLD}{Colors.HEADER}{'PAT Enhanced - Project Analysis Tool':^80}{Colors.ENDC}")
        print(f"{Colors.BOLD}{Colors.HEADER}{'=' * 80}{Colors.ENDC}")
        print(f"{Colors.CYAN}Project:{Colors.ENDC} {self.project_path}")
        print(f"{Colors.CYAN}Mode:{Colors.ENDC} {self.args.mode}")
        print(f"{Colors.CYAN}Output:{Colors.ENDC} {self.output_dir}")
        if self.args.focus:
            print(f"{Colors.CYAN}Focus:{Colors.ENDC} {self.args.focus}")
        print(f"{Colors.BOLD}{Colors.HEADER}{'=' * 80}{Colors.ENDC}\n")
    
    def load_plugins(self):
        """Discover and load plugins."""
        print(f"{Colors.BOLD}{Colors.HEADER}Loading Plugins{Colors.ENDC}")
        
        # Configure plugins
        plugin_config = {
            "cache_manager": {
                "cache_dir": str(self.output_dir / "cache"),
                "enabled": not self.args.no_cache
            },
            "caw_analyzer": {
                "min_confidence": 0.3,
                "ignored_dirs": ["venv", "__pycache__", ".git", "PAT_output"]
            }
        }
        
        # Load all plugins
        self.plugin_manager.load_plugins(plugin_config)
        
        # Print loaded plugins
        plugins = self.plugin_manager.get_all_plugins()
        if plugins:
            print(f"Loaded {len(plugins)} plugins:")
            for name, plugin in plugins.items():
                print(f"  - {name} v{plugin.version}: {plugin.description}")
        else:
            print(f"{Colors.YELLOW}No plugins loaded{Colors.ENDC}")
    
    def configure_synergy(self):
        """Configure the synergy framework with tools and rules."""
        print(f"{Colors.BOLD}{Colors.HEADER}Configuring Synergy Framework{Colors.ENDC}")
        
        # Register CAW analyzer as a synergy tool
        caw_plugin = self.plugin_manager.get_plugin("caw_analyzer")
        if caw_plugin:
            caw_tool = SynergyCAWTool(caw_plugin)
            self.orchestrator.register_tool(caw_tool)
            print(f"Registered CAW analyzer as a synergy tool")
        
        # TODO: Add more tools and rules
    
    def run_analysis(self):
        """Run the enhanced analysis."""
        try:
            # Step 1: Load plugins
            self.load_plugins()
            
            # Step 2: Configure synergy framework
            self.configure_synergy()
            
            # Step 3: Run PAT core analysis
            print(f"{Colors.BOLD}{Colors.HEADER}Running PAT Core Analysis{Colors.ENDC}")
            pat_cmd = [
                sys.executable, "-m", "PAT_project_analysis.pat",
                str(self.project_path),
                "--mode", self.args.mode,
                "--output", str(self.output_dir / "core_analysis")
            ]
            
            if self.args.focus:
                pat_cmd.extend(["--focus", self.args.focus])
            
            if self.args.verbose:
                pat_cmd.append("--verbose")
            
            # Run the PAT core analysis
            import subprocess
            print(f"Running: {' '.join(pat_cmd)}")
            result = subprocess.run(pat_cmd, capture_output=not self.args.verbose)
            
            if result.returncode != 0:
                print(f"{Colors.RED}PAT core analysis failed with code {result.returncode}{Colors.ENDC}")
                if not self.args.verbose and result.stderr:
                    print(result.stderr.decode('utf-8'))
                    
                # Continue with plugin analysis anyway
                print(f"{Colors.YELLOW}Continuing with plugin analysis...{Colors.ENDC}")
            
            # Step 4: Run CAW analyzer
            caw_plugin = self.plugin_manager.get_plugin("caw_analyzer")
            if caw_plugin:
                print(f"{Colors.BOLD}{Colors.HEADER}Running CAW Analysis{Colors.ENDC}")
                caw_results = caw_plugin.analyze_project(self.project_path)
                
                # Save results
                import json
                caw_output_path = self.output_dir / "caw_analysis.json"
                with open(caw_output_path, 'w') as f:
                    json.dump(caw_results, f, indent=2)
                    
                print(f"CAW analysis complete. Results saved to {caw_output_path}")
                
                # Print summary
                print(f"\nCAW Analysis Summary:")
                print(f"  Files analyzed: {caw_results.get('files_analyzed', 0)}")
                print(f"  Files with CAW: {caw_results.get('files_with_caw', 0)}")
                print(f"  Overall CAW score: {caw_results.get('overall_caw_score', 0.0):.2f}")
                
                if caw_results.get('top_caw_files'):
                    print(f"\nTop CAW Files:")
                    for file_info in caw_results.get('top_caw_files', [])[:5]:
                        print(f"  - {file_info['file_path']} (Score: {file_info['score']:.2f})")
                        
                if caw_results.get('needs_improvement'):
                    print(f"\nFiles Needing Improvement: {len(caw_results.get('needs_improvement', []))}")
            
            # Step 5: Run synergy analysis
            print(f"{Colors.BOLD}{Colors.HEADER}Running Synergy Analysis{Colors.ENDC}")
            synergy_results = self.orchestrator.execute_synergy_pipeline(
                self.project_path, 
                Path(self.args.focus) if self.args.focus else None
            )
            
            # Save synergy results
            synergy_output_path = self.output_dir / "synergy_analysis.json"
            with open(synergy_output_path, 'w') as f:
                json.dump(synergy_results, f, indent=2)
                
            print(f"Synergy analysis complete. Results saved to {synergy_output_path}")
            
            # Step 6: Print summary
            self._print_summary()
            
            return 0
            
        except Exception as e:
            print(f"\n{Colors.RED}Error: {str(e)}{Colors.ENDC}")
            if self.args.verbose:
                import traceback
                traceback.print_exc()
            return 1
    
    def _print_summary(self):
        """Print a summary of the analysis."""
        elapsed_time = time.time() - self.start_time
        
        print(f"\n{Colors.BOLD}{Colors.HEADER}{'=' * 80}{Colors.ENDC}")
        print(f"{Colors.BOLD}{Colors.HEADER}{'Analysis Complete':^80}{Colors.ENDC}")
        print(f"{Colors.BOLD}{Colors.HEADER}{'=' * 80}{Colors.ENDC}")
        print(f"{Colors.CYAN}Project:{Colors.ENDC} {self.project_path}")
        print(f"{Colors.CYAN}Output:{Colors.ENDC} {self.output_dir}")
        print(f"{Colors.CYAN}Elapsed Time:{Colors.ENDC} {elapsed_time:.2f} seconds")
        
        # Count loaded plugins
        plugins = self.plugin_manager.get_all_plugins()
        print(f"{Colors.CYAN}Plugins Used:{Colors.ENDC} {len(plugins)}")
        
        print(f"\nResults are available in the output directory:")
        print(f"  - Core Analysis: {self.output_dir / 'core_analysis'}")
        print(f"  - CAW Analysis: {self.output_dir / 'caw_analysis.json'}")
        print(f"  - Synergy Analysis: {self.output_dir / 'synergy_analysis.json'}")
        
        print(f"\n{Colors.GREEN}To view detailed results, check the output files.{Colors.ENDC}")
        print(f"{Colors.BOLD}{Colors.HEADER}{'=' * 80}{Colors.ENDC}\n")


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="PAT Enhanced - Project Analysis Tool with Plugin System"
    )
    
    parser.add_argument(
        "project_path",
        help="Path to the project to analyze"
    )
    
    parser.add_argument(
        "--mode",
        choices=["standard", "full", "quick", "parallel"],
        default="standard",
        help="Analysis mode (default: standard)"
    )
    
    parser.add_argument(
        "--output",
        help="Custom output directory"
    )
    
    parser.add_argument(
        "--focus",
        help="Focus analysis on a specific file or directory"
    )
    
    parser.add_argument(
        "--plugins",
        help="Directory containing additional plugins"
    )
    
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Disable result caching"
    )
    
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Show detailed output"
    )
    
    return parser.parse_args()


def main():
    """Main entry point."""
    args = parse_args()
    pat = PATEnhanced(args)
    return pat.run_analysis()


if __name__ == "__main__":
    sys.exit(main()) 
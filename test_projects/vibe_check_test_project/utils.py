"""
Utilities module for the test project.

This module contains utility functions used in the test project.
"""

import os
import re
from typing import List, Dict, Any, Optional


def format_string(text: str, capitalize: bool = False) -> str:
    """
    Format a string by removing extra whitespace and optionally capitalizing.
    
    Args:
        text: The string to format
        capitalize: Whether to capitalize the string
    
    Returns:
        The formatted string
    """
    # Remove extra whitespace
    formatted = re.sub(r'\s+', ' ', text).strip()
    
    # Capitalize if requested
    if capitalize:
        formatted = formatted.capitalize()
    
    return formatted


def calculate_sum(numbers: List[int]) -> int:
    """
    Calculate the sum of a list of numbers.
    
    Args:
        numbers: The list of numbers to sum
    
    Returns:
        The sum of the numbers
    """
    # This is a performance issue - inefficient implementation
    result = 0
    for num in numbers:
        result += num
    
    return result


def read_file(file_path: str) -> str:
    """
    Read the contents of a file.
    
    Args:
        file_path: Path to the file to read
    
    Returns:
        The contents of the file as a string
    
    Raises:
        FileNotFoundError: If the file does not exist
        PermissionError: If the file cannot be read
    """
    # This is a security issue - no path validation
    with open(file_path, 'r') as file:
        return file.read()


def write_file(file_path: str, content: str) -> None:
    """
    Write content to a file.
    
    Args:
        file_path: Path to the file to write
        content: Content to write to the file
    
    Raises:
        PermissionError: If the file cannot be written
    """
    # This is a security issue - no path validation
    with open(file_path, 'w') as file:
        file.write(content)


def parse_config(config_str: str) -> Dict[str, Any]:
    """
    Parse a configuration string into a dictionary.
    
    Args:
        config_str: The configuration string to parse
    
    Returns:
        A dictionary containing the parsed configuration
    """
    # This is a security issue - using eval
    config = {}
    
    for line in config_str.splitlines():
        if '=' in line:
            key, value = line.split('=', 1)
            key = key.strip()
            value = value.strip()
            
            # This is a security issue - using eval
            try:
                config[key] = eval(value)
            except:
                config[key] = value
    
    return config

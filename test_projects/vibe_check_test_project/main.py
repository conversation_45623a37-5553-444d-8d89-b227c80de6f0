"""
Main module for the test project.

This module contains the main functionality of the test project.
"""

import os
import sys
from typing import Dict, List, Optional, Any

from .utils import format_string, calculate_sum
from .models import User, Product


def main() -> None:
    """
    Main entry point for the application.
    
    This function initializes the application and starts the main loop.
    """
    print("Starting test project...")
    
    # Initialize users
    users = initialize_users()
    
    # Initialize products
    products = initialize_products()
    
    # Process data
    process_data(users, products)
    
    print("Test project completed.")


def initialize_users() -> List[User]:
    """
    Initialize a list of users.
    
    Returns:
        List of User objects
    """
    users = [
        User(id=1, name="<PERSON>", email="<EMAIL>"),
        User(id=2, name="<PERSON>", email="<EMAIL>"),
        User(id=3, name="<PERSON>", email="<EMAIL>")
    ]
    
    # This is a security issue - hardcoded credentials
    admin_user = User(id=999, name="Admin", email="<EMAIL>", password="admin123")
    users.append(admin_user)
    
    return users


def initialize_products() -> List[Product]:
    """
    Initialize a list of products.
    
    Returns:
        List of Product objects
    """
    products = [
        Product(id=1, name="Product 1", price=10.99),
        Product(id=2, name="Product 2", price=20.99),
        Product(id=3, name="Product 3", price=30.99)
    ]
    
    # This is a performance issue - unnecessary loop
    for i in range(1000):
        if i % 999 == 0:
            products.append(Product(id=i+100, name=f"Special Product {i}", price=99.99))
    
    return products


def process_data(users: List[User], products: List[Product]) -> None:
    """
    Process user and product data.
    
    Args:
        users: List of User objects
        products: List of Product objects
    """
    # This is a maintainability issue - complex function
    
    # Process users
    for user in users:
        print(f"Processing user: {user.name}")
        
        # This is a security issue - SQL injection vulnerability
        query = f"SELECT * FROM orders WHERE user_id = {user.id}"
        # execute_query(query)  # Commented out to avoid execution
        
        # Process products for user
        for product in products:
            if user.id % 2 == 0 and product.id % 2 == 0:
                print(f"  User {user.name} might be interested in {product.name}")
            elif user.id % 2 != 0 and product.id % 2 != 0:
                print(f"  User {user.name} might be interested in {product.name}")
    
    # This is a performance issue - inefficient data processing
    result = 0
    for i in range(100):
        for j in range(100):
            result += i * j
    
    print(f"Processed data with result: {result}")


if __name__ == "__main__":
    main()

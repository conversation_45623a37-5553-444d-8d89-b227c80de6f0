"""
A simple test project for Vibe Check.

This module contains a simple calculator implementation with some intentional issues.
"""

import math
import time
from typing import List, Optional, Union


class Calculator:
    """A simple calculator class with basic operations."""
    
    def __init__(self, initial_value: float = 0.0):
        self.value = initial_value
        self.history = []
        
    def add(self, x: float) -> float:
        """Add a number to the current value."""
        self.value += x
        self.history.append(f"Added {x}")
        return self.value
        
    def subtract(self, x: float) -> float:
        """Subtract a number from the current value."""
        self.value -= x
        self.history.append(f"Subtracted {x}")
        return self.value
        
    def multiply(self, x: float) -> float:
        """Multiply the current value by a number."""
        self.value *= x
        self.history.append(f"Multiplied by {x}")
        return self.value
        
    def divide(self, x: float) -> float:
        """Divide the current value by a number."""
        # Intentional issue: no check for division by zero
        self.value /= x
        self.history.append(f"Divided by {x}")
        return self.value
        
    def power(self, x: float) -> float:
        """Raise the current value to a power."""
        # Intentional issue: inefficient implementation
        result = 1.0
        for _ in range(int(x)):
            result *= self.value
        self.value = result
        self.history.append(f"Raised to power {x}")
        return self.value
        
    def square_root(self) -> float:
        """Calculate the square root of the current value."""
        # Intentional issue: no check for negative values
        self.value = math.sqrt(self.value)
        self.history.append("Calculated square root")
        return self.value
        
    def get_history(self) -> List[str]:
        """Get the history of operations."""
        return self.history
        
    def clear_history(self) -> None:
        """Clear the history of operations."""
        self.history = []
        
    def reset(self) -> None:
        """Reset the calculator to zero."""
        self.value = 0.0
        self.history.append("Reset to zero")
        
    def perform_complex_calculation(self, x: float, y: float, z: float) -> float:
        """Perform a complex calculation."""
        # Intentional issue: unnecessary sleep
        time.sleep(0.1)
        
        # Intentional issue: complex and hard-to-read code
        result = ((self.value + x) * y) / (z if z != 0 else 1) - math.sin(x) + math.cos(y) * math.tan(z if abs(z) < math.pi/2 else 0)
        
        self.value = result
        self.history.append("Performed complex calculation")
        return result


def calculate_average(numbers: List[float]) -> float:
    """Calculate the average of a list of numbers."""
    # Intentional issue: no check for empty list
    return sum(numbers) / len(numbers)


def is_prime(n: int) -> bool:
    """Check if a number is prime."""
    # Intentional issue: inefficient algorithm
    if n <= 1:
        return False
    for i in range(2, n):
        if n % i == 0:
            return False
    return True


def fibonacci(n: int) -> int:
    """Calculate the nth Fibonacci number."""
    # Intentional issue: recursive implementation without memoization
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    else:
        return fibonacci(n-1) + fibonacci(n-2)


def main():
    """Main function to demonstrate the calculator."""
    calc = Calculator(10.0)
    print(f"Initial value: {calc.value}")
    
    calc.add(5)
    print(f"After adding 5: {calc.value}")
    
    calc.multiply(2)
    print(f"After multiplying by 2: {calc.value}")
    
    calc.divide(5)
    print(f"After dividing by 5: {calc.value}")
    
    calc.power(2)
    print(f"After raising to power 2: {calc.value}")
    
    calc.square_root()
    print(f"After taking square root: {calc.value}")
    
    print("History:")
    for entry in calc.get_history():
        print(f"- {entry}")
    
    print("\nCalculating average of [1, 2, 3, 4, 5]:")
    print(calculate_average([1, 2, 3, 4, 5]))
    
    print("\nChecking if 17 is prime:")
    print(is_prime(17))
    
    print("\n10th Fibonacci number:")
    print(fibonacci(10))


if __name__ == "__main__":
    main()

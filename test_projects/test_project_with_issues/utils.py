"""
Utility functions with various issues.
"""

import time
import random
from typing import Any, Dict, List, Optional, Union


# Function with type annotation issues (issue: inconsistent return type)
def get_value(key: str, default: Any = None) -> str:
    """
    Get a value with type annotation issues.
    """
    values = {
        "name": "Test",
        "age": 30,
        "active": True
    }
    
    # Type issue: returning Any when str is promised
    return values.get(key, default)


# Function with redundant code (issue: redundant code)
def calculate_average(numbers: List[float]) -> float:
    """
    Calculate average with redundant code.
    """
    total = 0
    count = 0
    
    # Redundant: we already know the count from the list length
    for num in numbers:
        total += num
        count += 1
    
    # Redundant: could just use len(numbers)
    if count == 0:
        return 0
    
    return total / count


# Function with magic numbers (issue: magic numbers)
def calculate_price(base_price: float, quantity: int) -> float:
    """
    Calculate price with magic numbers.
    """
    # Magic numbers: 0.1, 0.05, 0.03, 10, 50
    if quantity < 10:
        discount = 0.0
    elif quantity < 50:
        discount = 0.05
    else:
        discount = 0.1
    
    tax_rate = 0.03
    
    return base_price * quantity * (1 - discount) * (1 + tax_rate)


# Function with long lines (issue: line too long)
def generate_report(data: List[Dict[str, Any]], include_details: bool = True, format_output: bool = True, add_timestamps: bool = True, calculate_totals: bool = True) -> Dict[str, Any]:
    """
    Generate a report with a very long line.
    """
    # This is a very long line that exceeds the recommended line length and should be split into multiple lines for better readability
    report = {"data": data, "generated_at": time.time(), "include_details": include_details, "format_output": format_output, "add_timestamps": add_timestamps, "calculate_totals": calculate_totals}
    
    return report


# Class with inheritance issues (issue: inheritance abuse)
class BaseHandler:
    """Base handler class."""
    
    def handle(self, data: Any) -> Any:
        """Handle data."""
        return data


class StringHandler(BaseHandler):
    """String handler."""
    
    def handle(self, data: Any) -> str:
        """Handle data as string."""
        return str(data)


class IntHandler(BaseHandler):
    """Int handler."""
    
    def handle(self, data: Any) -> int:
        """Handle data as int."""
        try:
            return int(data)
        except (ValueError, TypeError):
            return 0


class FloatHandler(BaseHandler):
    """Float handler."""
    
    def handle(self, data: Any) -> float:
        """Handle data as float."""
        try:
            return float(data)
        except (ValueError, TypeError):
            return 0.0


class BoolHandler(BaseHandler):
    """Bool handler."""
    
    def handle(self, data: Any) -> bool:
        """Handle data as bool."""
        if isinstance(data, bool):
            return data
        if isinstance(data, (int, float)):
            return data != 0
        if isinstance(data, str):
            return data.lower() in ("true", "yes", "1", "y")
        return bool(data)


class ListHandler(BaseHandler):
    """List handler."""
    
    def handle(self, data: Any) -> List[Any]:
        """Handle data as list."""
        if isinstance(data, list):
            return data
        return [data]


class DictHandler(BaseHandler):
    """Dict handler."""
    
    def handle(self, data: Any) -> Dict[str, Any]:
        """Handle data as dict."""
        if isinstance(data, dict):
            return data
        return {"value": data}


# Function with docstring issues (issue: missing docstring parameters)
def process_user_data(user_id: int, name: str, email: str, age: Optional[int] = None, 
                     active: bool = True) -> Dict[str, Any]:
    """
    Process user data.
    
    Returns:
        A dictionary with processed user data.
    """
    # Missing docstring for parameters
    
    result = {
        "id": user_id,
        "name": name,
        "email": email,
        "active": active
    }
    
    if age is not None:
        result["age"] = age
    
    return result

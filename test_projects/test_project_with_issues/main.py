#!/usr/bin/env python3
"""
Test project with intentional issues for Vibe Check to detect.
"""

import os
import sys
import time
import random
import json
from typing import List, Dict, Any, Optional


# Global variable (issue: global variable usage)
GLOBAL_CONFIG = {"debug": True, "timeout": 30, "retries": 3}


# Function with too many arguments (issue: too many arguments)
def process_data(data: List[Dict[str, Any]], 
                filter_by: str, 
                sort_by: str, 
                limit: int, 
                offset: int, 
                include_metadata: bool, 
                format_output: bool, 
                apply_transformations: bool, 
                save_results: bool, 
                output_file: str) -> List[Dict[str, Any]]:
    """
    Process data with too many parameters.
    """
    # Unused variable (issue: unused variable)
    unused_var = "This variable is never used"
    
    # Complex nested conditions (issue: cognitive complexity)
    if filter_by:
        filtered_data = []
        for item in data:
            if filter_by in item:
                if item[filter_by]:
                    if isinstance(item[filter_by], (str, int, float)):
                        if str(item[filter_by]).startswith("valid_"):
                            if len(str(item[filter_by])) > 5:
                                filtered_data.append(item)
    else:
        filtered_data = data
    
    # Duplicate code (issue: code duplication)
    if sort_by:
        sorted_data = sorted(filtered_data, key=lambda x: x.get(sort_by, 0))
    else:
        sorted_data = filtered_data
    
    # Slicing data
    result = sorted_data[offset:offset+limit]
    
    # More duplicate code (issue: code duplication)
    if format_output:
        formatted_result = []
        for item in result:
            formatted_item = {}
            for key, value in item.items():
                formatted_item[key] = str(value)
            formatted_result.append(formatted_item)
    else:
        formatted_result = result
    
    # Unnecessary condition (issue: redundant code)
    if True:
        pass
    
    # Returning without using some parameters (issue: unused parameters)
    return formatted_result


# Class with poor design (issue: poor class design)
class DataManager:
    """
    A class with design issues.
    """
    
    def __init__(self, config: Dict[str, Any]):
        # Too many instance variables (issue: too many instance variables)
        self.config = config
        self.data = []
        self.processed_data = []
        self.filters = {}
        self.sorters = {}
        self.transformers = {}
        self.validators = {}
        self.formatters = {}
        self.exporters = {}
        self.importers = {}
        self.cache = {}
        self.metadata = {}
        self.stats = {}
        self.history = []
        self.errors = []
        self.warnings = []
        self.debug_info = []
        
    # Method with high cyclomatic complexity (issue: high complexity)
    def process(self, input_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process data with high complexity.
        """
        result = []
        
        for item in input_data:
            # Complex processing with many branches
            if "id" not in item:
                self.errors.append("Item missing ID")
                continue
                
            if "type" not in item:
                if "category" in item:
                    item["type"] = item["category"]
                elif "class" in item:
                    item["type"] = item["class"]
                elif "kind" in item:
                    item["type"] = item["kind"]
                else:
                    self.errors.append(f"Item {item['id']} missing type")
                    continue
            
            if item["type"] == "user":
                if "name" not in item:
                    self.errors.append(f"User {item['id']} missing name")
                    continue
                if "email" not in item:
                    self.warnings.append(f"User {item['id']} missing email")
                    item["email"] = f"{item['name'].lower().replace(' ', '.')}@example.com"
            elif item["type"] == "product":
                if "name" not in item:
                    self.errors.append(f"Product {item['id']} missing name")
                    continue
                if "price" not in item:
                    self.warnings.append(f"Product {item['id']} missing price")
                    item["price"] = 0
            elif item["type"] == "order":
                if "user_id" not in item:
                    self.errors.append(f"Order {item['id']} missing user_id")
                    continue
                if "products" not in item:
                    self.errors.append(f"Order {item['id']} missing products")
                    continue
                if not isinstance(item["products"], list):
                    self.errors.append(f"Order {item['id']} products not a list")
                    continue
                if len(item["products"]) == 0:
                    self.warnings.append(f"Order {item['id']} has no products")
            else:
                self.warnings.append(f"Unknown item type: {item['type']}")
            
            # Add to result
            result.append(item)
        
        return result
    
    # Method that does too many things (issue: method does too many things)
    def load_process_and_save(self, input_file: str, output_file: str) -> None:
        """
        Method that does too many things.
        """
        # Load data
        with open(input_file, 'r') as f:
            data = json.load(f)
        
        # Process data
        processed_data = self.process(data)
        
        # Transform data
        transformed_data = []
        for item in processed_data:
            transformed_item = {}
            for key, value in item.items():
                transformed_key = key.lower().replace(' ', '_')
                transformed_item[transformed_key] = value
            transformed_data.append(transformed_item)
        
        # Save data
        with open(output_file, 'w') as f:
            json.dump(transformed_data, f, indent=2)


# Function with security issue (issue: security vulnerability)
def execute_command(command: str) -> str:
    """
    Execute a command with a security vulnerability.
    """
    # Security issue: os.system is vulnerable to command injection
    os.system(command)
    return "Command executed"


# Main function with error handling issues (issue: poor error handling)
def main() -> None:
    """
    Main function with error handling issues.
    """
    try:
        # Create some test data
        data = [
            {"id": 1, "name": "Item 1", "value": 100},
            {"id": 2, "name": "Item 2", "value": 200},
            {"id": 3, "name": "Item 3", "value": 300},
        ]
        
        # Process data
        result = process_data(
            data, 
            filter_by="name", 
            sort_by="value", 
            limit=10, 
            offset=0, 
            include_metadata=True, 
            format_output=True, 
            apply_transformations=True, 
            save_results=True, 
            output_file="output.json"
        )
        
        # Print result
        print(f"Processed {len(result)} items")
        
    except:  # Bare except clause (issue: bare except)
        print("An error occurred")


if __name__ == "__main__":
    main()

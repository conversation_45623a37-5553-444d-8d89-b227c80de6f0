"""
Utils Tests
========

This module contains tests for the utils module.
"""

import unittest
from src.utils import round_to_precision, format_as_currency, calculate_statistics, parse_expression


class TestRoundToPrecision(unittest.TestCase):
    """Tests for the round_to_precision function."""
    
    def test_round_to_precision(self):
        """Test rounding to different precisions."""
        self.assertEqual(round_to_precision(3.14159, 2), 3.14)
        self.assertEqual(round_to_precision(3.14159, 3), 3.142)
        self.assertEqual(round_to_precision(3.14159, 0), 3.0)
        
        # Test with negative numbers
        self.assertEqual(round_to_precision(-3.14159, 2), -3.14)


class TestFormatAsCurrency(unittest.TestCase):
    """Tests for the format_as_currency function."""
    
    def test_format_as_currency(self):
        """Test formatting as currency with different symbols."""
        self.assertEqual(format_as_currency(123.45), "$123.45")
        self.assertEqual(format_as_currency(123.45, "€"), "€123.45")
        
        # Test with negative numbers
        self.assertEqual(format_as_currency(-123.45), "$-123.45")
        
        # Test with zero
        self.assertEqual(format_as_currency(0), "$0.00")


class TestCalculateStatistics(unittest.TestCase):
    """Tests for the calculate_statistics function."""
    
    def test_calculate_statistics(self):
        """Test calculating statistics for a list of values."""
        values = [1, 2, 3, 4, 5]
        stats = calculate_statistics(values)
        
        self.assertEqual(stats["mean"], 3.0)
        self.assertEqual(stats["median"], 3.0)
        self.assertEqual(stats["min"], 1.0)
        self.assertEqual(stats["max"], 5.0)
        self.assertAlmostEqual(stats["std_dev"], 1.4142135623730951)
        
        # Test with even number of values
        values = [1, 2, 3, 4]
        stats = calculate_statistics(values)
        self.assertEqual(stats["median"], 2.5)
        
        # Test with a single value
        values = [42]
        stats = calculate_statistics(values)
        self.assertEqual(stats["mean"], 42.0)
        self.assertEqual(stats["median"], 42.0)
        self.assertEqual(stats["min"], 42.0)
        self.assertEqual(stats["max"], 42.0)
        self.assertEqual(stats["std_dev"], 0.0)
    
    def test_empty_list(self):
        """Test that an empty list raises a ValueError."""
        with self.assertRaises(ValueError):
            calculate_statistics([])


class TestParseExpression(unittest.TestCase):
    """Tests for the parse_expression function."""
    
    def test_parse_addition(self):
        """Test parsing an addition expression."""
        operation, a, b = parse_expression("5 + 3")
        self.assertEqual(operation, "add")
        self.assertEqual(a, 5.0)
        self.assertEqual(b, 3.0)
    
    def test_parse_subtraction(self):
        """Test parsing a subtraction expression."""
        operation, a, b = parse_expression("10 - 4")
        self.assertEqual(operation, "subtract")
        self.assertEqual(a, 10.0)
        self.assertEqual(b, 4.0)
    
    def test_parse_multiplication(self):
        """Test parsing a multiplication expression."""
        operation, a, b = parse_expression("4 * 3")
        self.assertEqual(operation, "multiply")
        self.assertEqual(a, 4.0)
        self.assertEqual(b, 3.0)
    
    def test_parse_division(self):
        """Test parsing a division expression."""
        operation, a, b = parse_expression("12 / 4")
        self.assertEqual(operation, "divide")
        self.assertEqual(a, 12.0)
        self.assertEqual(b, 4.0)
    
    def test_parse_power(self):
        """Test parsing a power expression."""
        operation, a, b = parse_expression("2 ^ 3")
        self.assertEqual(operation, "power")
        self.assertEqual(a, 2.0)
        self.assertEqual(b, 3.0)
    
    def test_invalid_format(self):
        """Test that an invalid format raises a ValueError."""
        with self.assertRaises(ValueError):
            parse_expression("5 + 3 + 2")  # Too many parts
        
        with self.assertRaises(ValueError):
            parse_expression("5 +")  # Too few parts
    
    def test_invalid_numbers(self):
        """Test that invalid numbers raise a ValueError."""
        with self.assertRaises(ValueError):
            parse_expression("a + 3")
        
        with self.assertRaises(ValueError):
            parse_expression("5 + b")
    
    def test_invalid_operator(self):
        """Test that an invalid operator raises a ValueError."""
        with self.assertRaises(ValueError):
            parse_expression("5 % 3")


if __name__ == "__main__":
    unittest.main()

"""
Calculator Tests
=============

This module contains tests for the calculator module.
"""

import unittest
from src.calculator import Calculator, quick_calc


class TestCalculator(unittest.TestCase):
    """Tests for the Calculator class."""
    
    def setUp(self):
        """Set up a calculator instance for each test."""
        self.calc = Calculator()
    
    def test_initial_value(self):
        """Test that the initial value is 0."""
        self.assertEqual(self.calc.get_value(), 0.0)
        
        # Test with custom initial value
        calc2 = Calculator(10.0)
        self.assertEqual(calc2.get_value(), 10.0)
    
    def test_add(self):
        """Test the add method."""
        self.calc.add(5)
        self.assertEqual(self.calc.get_value(), 5.0)
        
        self.calc.add(3)
        self.assertEqual(self.calc.get_value(), 8.0)
    
    def test_subtract(self):
        """Test the subtract method."""
        self.calc.add(10)  # Set value to 10
        self.calc.subtract(4)
        self.assertEqual(self.calc.get_value(), 6.0)
        
        self.calc.subtract(6)
        self.assertEqual(self.calc.get_value(), 0.0)
    
    def test_multiply(self):
        """Test the multiply method."""
        self.calc.add(4)  # Set value to 4
        self.calc.multiply(3)
        self.assertEqual(self.calc.get_value(), 12.0)
        
        self.calc.multiply(0)
        self.assertEqual(self.calc.get_value(), 0.0)
    
    def test_divide(self):
        """Test the divide method."""
        self.calc.add(12)  # Set value to 12
        self.calc.divide(4)
        self.assertEqual(self.calc.get_value(), 3.0)
        
        # Test division by zero
        with self.assertRaises(ZeroDivisionError):
            self.calc.divide(0)
    
    def test_power(self):
        """Test the power method."""
        self.calc.add(2)  # Set value to 2
        self.calc.power(3)
        self.assertEqual(self.calc.get_value(), 8.0)
        
        self.calc.power(0)
        self.assertEqual(self.calc.get_value(), 1.0)
    
    def test_reset(self):
        """Test the reset method."""
        self.calc.add(10)  # Set value to 10
        self.calc.reset()
        self.assertEqual(self.calc.get_value(), 0.0)
    
    def test_history(self):
        """Test that operations are recorded in history."""
        self.calc.add(5)
        self.calc.multiply(2)
        self.calc.subtract(3)
        
        history = self.calc.get_history()
        
        self.assertEqual(len(history), 3)
        self.assertEqual(history[0]["operation"], "add")
        self.assertEqual(history[0]["operand"], 5)
        self.assertEqual(history[0]["result"], 5.0)
        
        self.assertEqual(history[1]["operation"], "multiply")
        self.assertEqual(history[1]["operand"], 2)
        self.assertEqual(history[1]["result"], 10.0)
        
        self.assertEqual(history[2]["operation"], "subtract")
        self.assertEqual(history[2]["operand"], 3)
        self.assertEqual(history[2]["result"], 7.0)


class TestQuickCalc(unittest.TestCase):
    """Tests for the quick_calc function."""
    
    def test_add(self):
        """Test addition."""
        self.assertEqual(quick_calc("add", 5, 3), 8.0)
    
    def test_subtract(self):
        """Test subtraction."""
        self.assertEqual(quick_calc("subtract", 10, 4), 6.0)
    
    def test_multiply(self):
        """Test multiplication."""
        self.assertEqual(quick_calc("multiply", 4, 3), 12.0)
    
    def test_divide(self):
        """Test division."""
        self.assertEqual(quick_calc("divide", 12, 4), 3.0)
        
        # Test division by zero
        with self.assertRaises(ZeroDivisionError):
            quick_calc("divide", 5, 0)
    
    def test_power(self):
        """Test exponentiation."""
        self.assertEqual(quick_calc("power", 2, 3), 8.0)
    
    def test_invalid_operation(self):
        """Test that an invalid operation raises a ValueError."""
        with self.assertRaises(ValueError):
            quick_calc("invalid", 5, 3)


if __name__ == "__main__":
    unittest.main()

"""
Main Module
=========

This module provides the main entry point for the calculator application.
"""

import sys
from typing import List, Optional

from .calculator import Calculator, quick_calc
from .utils import parse_expression, format_as_currency


def process_command(command: str) -> str:
    """
    Process a calculator command.
    
    Args:
        command: The command to process
        
    Returns:
        The result of the command as a string
    """
    try:
        # Handle special commands
        if command.lower() == "help":
            return get_help_text()
        
        # Parse the expression
        operation, a, b = parse_expression(command)
        
        # Perform the calculation
        result = quick_calc(operation, a, b)
        
        # Format the result
        return f"Result: {result}"
    
    except ValueError as e:
        return f"Error: {str(e)}"
    except ZeroDivisionError as e:
        return f"Error: {str(e)}"
    except Exception as e:
        return f"Unexpected error: {str(e)}"


def get_help_text() -> str:
    """
    Get the help text for the calculator.
    
    Returns:
        The help text
    """
    return """
    Calculator Help
    ==============
    
    Enter expressions in the format: a operator b
    
    Supported operators:
    + : Addition
    - : Subtraction
    * : Multiplication
    / : Division
    ^ : Exponentiation
    
    Examples:
    5 + 3
    10 - 2
    4 * 5
    20 / 4
    2 ^ 3
    
    Special commands:
    help : Show this help text
    exit : Exit the calculator
    """


def interactive_mode() -> None:
    """Run the calculator in interactive mode."""
    print("Calculator Interactive Mode")
    print("Enter 'help' for instructions or 'exit' to quit")
    
    calculator = Calculator()
    
    while True:
        try:
            command = input("> ")
            
            if command.lower() == "exit":
                print("Goodbye!")
                break
            
            result = process_command(command)
            print(result)
            
        except KeyboardInterrupt:
            print("\nGoodbye!")
            break
        except Exception as e:
            print(f"Unexpected error: {str(e)}")


def batch_mode(expressions: List[str]) -> None:
    """
    Run the calculator in batch mode.
    
    Args:
        expressions: A list of expressions to process
    """
    print("Calculator Batch Mode")
    
    for expression in expressions:
        result = process_command(expression)
        print(f"{expression} => {result}")


def main() -> None:
    """Main entry point for the calculator application."""
    args = sys.argv[1:]
    
    if not args:
        # No arguments, run in interactive mode
        interactive_mode()
    else:
        # Arguments provided, run in batch mode
        batch_mode(args)


if __name__ == "__main__":
    main()

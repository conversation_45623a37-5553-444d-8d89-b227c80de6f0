"""
Calculator Module
===============

This module provides basic calculator functionality.
"""

from typing import Union, List, Optional, Dict, Any


class Calculator:
    """A simple calculator class with basic operations."""
    
    def __init__(self, initial_value: float = 0.0):
        """
        Initialize the calculator with an optional initial value.
        
        Args:
            initial_value: The initial value of the calculator
        """
        self.value = initial_value
        self.history: List[Dict[str, Any]] = []
        
    def add(self, x: float) -> float:
        """
        Add a number to the current value.
        
        Args:
            x: The number to add
            
        Returns:
            The new value after addition
        """
        self.value += x
        self._record_operation("add", x)
        return self.value
    
    def subtract(self, x: float) -> float:
        """
        Subtract a number from the current value.
        
        Args:
            x: The number to subtract
            
        Returns:
            The new value after subtraction
        """
        self.value -= x
        self._record_operation("subtract", x)
        return self.value
    
    def multiply(self, x: float) -> float:
        """
        Multiply the current value by a number.
        
        Args:
            x: The number to multiply by
            
        Returns:
            The new value after multiplication
        """
        self.value *= x
        self._record_operation("multiply", x)
        return self.value
    
    def divide(self, x: float) -> float:
        """
        Divide the current value by a number.
        
        Args:
            x: The number to divide by
            
        Returns:
            The new value after division
            
        Raises:
            ZeroDivisionError: If x is zero
        """
        if x == 0:
            raise ZeroDivisionError("Cannot divide by zero")
        self.value /= x
        self._record_operation("divide", x)
        return self.value
    
    def power(self, x: float) -> float:
        """
        Raise the current value to the power of x.
        
        Args:
            x: The exponent
            
        Returns:
            The new value after exponentiation
        """
        self.value **= x
        self._record_operation("power", x)
        return self.value
    
    def reset(self) -> None:
        """Reset the calculator to zero."""
        self.value = 0.0
        self._record_operation("reset", None)
    
    def get_value(self) -> float:
        """
        Get the current value.
        
        Returns:
            The current value
        """
        return self.value
    
    def get_history(self) -> List[Dict[str, Any]]:
        """
        Get the operation history.
        
        Returns:
            A list of operations performed
        """
        return self.history
    
    def _record_operation(self, operation: str, operand: Optional[float]) -> None:
        """
        Record an operation in the history.
        
        Args:
            operation: The operation performed
            operand: The operand used (if any)
        """
        self.history.append({
            "operation": operation,
            "operand": operand,
            "result": self.value
        })


def quick_calc(operation: str, a: float, b: float) -> float:
    """
    Perform a quick calculation without creating a Calculator instance.
    
    Args:
        operation: The operation to perform (add, subtract, multiply, divide, power)
        a: The first operand
        b: The second operand
        
    Returns:
        The result of the calculation
        
    Raises:
        ValueError: If the operation is not supported
        ZeroDivisionError: If dividing by zero
    """
    if operation == "add":
        return a + b
    elif operation == "subtract":
        return a - b
    elif operation == "multiply":
        return a * b
    elif operation == "divide":
        if b == 0:
            raise ZeroDivisionError("Cannot divide by zero")
        return a / b
    elif operation == "power":
        return a ** b
    else:
        raise ValueError(f"Unsupported operation: {operation}")

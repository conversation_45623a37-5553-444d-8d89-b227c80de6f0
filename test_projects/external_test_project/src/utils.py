"""
Utility Functions
===============

This module provides utility functions for the calculator.
"""

import math
from typing import List, Union, Optional, Dict, Any, Tuple


def round_to_precision(value: float, precision: int = 2) -> float:
    """
    Round a value to a specified precision.
    
    Args:
        value: The value to round
        precision: The number of decimal places to round to
        
    Returns:
        The rounded value
    """
    return round(value, precision)


def format_as_currency(value: float, currency: str = "$") -> str:
    """
    Format a value as currency.
    
    Args:
        value: The value to format
        currency: The currency symbol to use
        
    Returns:
        The formatted currency string
    """
    return f"{currency}{value:.2f}"


def calculate_statistics(values: List[float]) -> Dict[str, float]:
    """
    Calculate basic statistics for a list of values.
    
    Args:
        values: The list of values
        
    Returns:
        A dictionary containing the mean, median, min, max, and standard deviation
        
    Raises:
        ValueError: If the list is empty
    """
    if not values:
        raise ValueError("Cannot calculate statistics for an empty list")
    
    n = len(values)
    mean = sum(values) / n
    sorted_values = sorted(values)
    
    # Calculate median
    if n % 2 == 0:
        median = (sorted_values[n//2 - 1] + sorted_values[n//2]) / 2
    else:
        median = sorted_values[n//2]
    
    # Calculate standard deviation
    variance = sum((x - mean) ** 2 for x in values) / n
    std_dev = math.sqrt(variance)
    
    return {
        "mean": mean,
        "median": median,
        "min": min(values),
        "max": max(values),
        "std_dev": std_dev
    }


def parse_expression(expression: str) -> Tuple[str, float, float]:
    """
    Parse a simple mathematical expression.
    
    Args:
        expression: A string in the format "a operator b" (e.g., "5 + 3")
        
    Returns:
        A tuple containing the operation and the two operands
        
    Raises:
        ValueError: If the expression is invalid
    """
    # This is a simplified parser for demonstration purposes
    parts = expression.split()
    
    if len(parts) != 3:
        raise ValueError("Expression must be in the format 'a operator b'")
    
    try:
        a = float(parts[0])
        operator = parts[1]
        b = float(parts[2])
    except ValueError:
        raise ValueError("Operands must be valid numbers")
    
    # Map the operator to the operation name
    operation_map = {
        "+": "add",
        "-": "subtract",
        "*": "multiply",
        "/": "divide",
        "^": "power"
    }
    
    if operator not in operation_map:
        raise ValueError(f"Unsupported operator: {operator}")
    
    return operation_map[operator], a, b

# Project Analysis Report

**Project:** /Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis

**Date:** 2025-06-21 19:39:41

## Summary

- **Total Files:** 9
- **Total Directories:** 1
- **Total Issues:** 18
- **Max Complexity:** 39

## Files

| File | Lines | Issues | Complexity |
|------|-------|--------|------------|
| __init__.py | 24 | 1 | 0 |
| adaptive_config.py | 337 | 2 | 30 |
| file_analyzer.py | 124 | 1 | 24 |
| import_analyzer.py | 495 | 3 | 39 |
| import_visualizer.py | 569 | 7 | 38 |
| metrics_aggregator.py | 161 | 1 | 27 |
| project_analyzer.py | 103 | 1 | 16 |
| result_processor.py | 165 | 1 | 33 |
| tool_executor.py | 113 | 1 | 25 |

## Issues

### __init__.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### adaptive_config.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 0 | custom.print_statement | LOW | Print statement in production code |

### file_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### import_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 256 | B110 | LOW | Try, Except, Pass detected. |
| 0 | custom.hardcoded_path | MEDIUM | Hardcoded file path |

### import_visualizer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |

### metrics_aggregator.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### project_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### result_processor.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |

### tool_executor.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | mypy.error | LOW |  |


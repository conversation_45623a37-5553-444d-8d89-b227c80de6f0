{"project_path": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis", "files": {"__init__.py": {"path": "__init__.py", "name": "__init__.py", "size": 651, "lines": 24, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 0, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "adaptive_config.py": {"path": "adaptive_config.py", "name": "adaptive_config.py", "size": 13444, "lines": 337, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 30, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"tool": "custom_rules", "code": "custom.print_statement", "message": "Print statement in production code", "location": {"file": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis/adaptive_config.py", "line": 332, "column": 0}, "severity": "LOW", "category": "debugging", "type": "style"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "file_analyzer.py": {"path": "file_analyzer.py", "name": "file_analyzer.py", "size": 3960, "lines": 124, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 24, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "import_analyzer.py": {"path": "import_analyzer.py", "name": "import_analyzer.py", "size": 18024, "lines": 495, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 39, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"code": "B110", "name": "try_except_pass", "line": 256, "message": "Try, Except, Pass detected.", "severity": "LOW", "confidence": "HIGH", "more_info": "https://bandit.readthedocs.io/en/1.8.3/plugins/b110_try_except_pass.html"}, {"tool": "custom_rules", "code": "custom.hardcoded_path", "message": "Hardcoded file path", "location": {"file": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis/import_analyzer.py", "line": 223, "column": 0}, "severity": "MEDIUM", "category": "hardcoded_values", "type": "maintainability"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "import_visualizer.py": {"path": "import_visualizer.py", "name": "import_visualizer.py", "size": 22430, "lines": 569, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 38, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}, {"tool": "custom_rules", "code": "custom.print_statement", "message": "Print statement in production code", "location": {"file": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis/import_visualizer.py", "line": 145, "column": 0}, "severity": "LOW", "category": "debugging", "type": "style"}, {"tool": "custom_rules", "code": "custom.print_statement", "message": "Print statement in production code", "location": {"file": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis/import_visualizer.py", "line": 191, "column": 0}, "severity": "LOW", "category": "debugging", "type": "style"}, {"tool": "custom_rules", "code": "custom.print_statement", "message": "Print statement in production code", "location": {"file": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis/import_visualizer.py", "line": 246, "column": 0}, "severity": "LOW", "category": "debugging", "type": "style"}, {"tool": "custom_rules", "code": "custom.print_statement", "message": "Print statement in production code", "location": {"file": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis/import_visualizer.py", "line": 302, "column": 0}, "severity": "LOW", "category": "debugging", "type": "style"}, {"tool": "custom_rules", "code": "custom.print_statement", "message": "Print statement in production code", "location": {"file": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis/import_visualizer.py", "line": 549, "column": 0}, "severity": "LOW", "category": "debugging", "type": "style"}, {"tool": "custom_rules", "code": "custom.print_statement", "message": "Print statement in production code", "location": {"file": "/Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis/import_visualizer.py", "line": 568, "column": 0}, "severity": "LOW", "category": "debugging", "type": "style"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "metrics_aggregator.py": {"path": "metrics_aggregator.py", "name": "metrics_aggregator.py", "size": 6314, "lines": 161, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 27, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "project_analyzer.py": {"path": "project_analyzer.py", "name": "project_analyzer.py", "size": 3546, "lines": 103, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 16, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "result_processor.py": {"path": "result_processor.py", "name": "result_processor.py", "size": 6543, "lines": 165, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 33, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}, "tool_executor.py": {"path": "tool_executor.py", "name": "tool_executor.py", "size": 3926, "lines": 113, "imports": [], "imported_by": [], "functions": [], "classes": [], "complexity": 25, "docstring_coverage": 0.0, "type_coverage": 0.0, "maintainability_index": 0.0, "circular_deps": [], "internal_deps": [], "external_deps": [], "coupled_modules": [], "description": "", "issues": [{"file": "mypy", "line": 0, "column": 0, "severity": "LOW", "message": "", "code": "mypy.error"}], "is_documentation": false, "is_package": false, "is_test": false, "documentation_quality": 0.0, "sections": 0, "code_examples": 0, "docstrings": {}, "roles": "", "tool_results": {}, "security_score": 100.0, "security_issues": 0, "high_severity_issues": 0}}, "directories": {"": {"path": "", "files": ["__init__.py", "adaptive_config.py", "file_analyzer.py", "import_analyzer.py", "import_visualizer.py", "metrics_aggregator.py", "project_analyzer.py", "result_processor.py", "tool_executor.py"], "total_lines": 2091, "avg_lines": 232.33333333333334, "max_file_lines": 569, "max_file": "import_visualizer.py", "description": "No description available", "doc_files_count": 0, "total_doc_size": 0, "avg_doc_quality": 0.0, "parent_dir": "", "child_directories": [], "type_coverage": 0.0, "docstring_coverage": 0.0, "_file_metrics": [], "_avg_complexity": 25.77777777777778, "_max_complexity": 39, "_issue_count": 18}}, "issue_count": 18, "max_complexity": 39, "issues_by_severity": {"LOW": 17, "MEDIUM": 1}}
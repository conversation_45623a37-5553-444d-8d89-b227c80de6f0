
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enterprise Analysis Report - Test Enterprise Project</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 2.5em;
        }
        .header .subtitle {
            color: #666;
            font-size: 1.1em;
            margin-top: 10px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #007bff;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .metric-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        .finding {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .finding-header {
            font-weight: bold;
            color: #495057;
            margin-bottom: 10px;
        }
        .issue {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-left: 3px solid #dc3545;
            border-radius: 3px;
        }
        .issue.warning {
            border-left-color: #ffc107;
        }
        .issue.info {
            border-left-color: #17a2b8;
        }
        .recommendation {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 15px;
        }
        .recommendation.high {
            border-left: 4px solid #28a745;
        }
        .recommendation.medium {
            border-left: 4px solid #ffc107;
        }
        .footer {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #666;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Enterprise Analysis Report</h1>
            <div class="subtitle">
                Project: Test Enterprise Project | 
                Generated: 2025-06-22T18:40:10.081135 |
                Report ID: ent_1750610410_html
            </div>
        </div>
        
        <div class="section">
            <h2>Executive Summary</h2>
            <div class="metric-grid">
                <div class="metric-card">
                    <div class="metric-value">3</div>
                    <div class="metric-label">Files Analyzed</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">3</div>
                    <div class="metric-label">Issues Found</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">100.0%</div>
                    <div class="metric-label">Success Rate</div>
                </div>
                <div class="metric-card">
                    <div class="metric-value">None</div>
                    <div class="metric-label">Quality Score</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h2>Detailed Findings</h2>
            
            <div class="finding">
                <div class="finding-header">
def main():
    password = "secret123"  # Security issue
    x=1;y=2  # Style issue
    return x + y

if __name__ == "__main__":
    main()
 (1 issues)</div>
                
            <div class="issue info">
                <strong>D001</strong>: Module missing docstring
                <br><small>Line 1, Column 0</small>
            </div>
              <!-- Show first 5 issues -->
            </div>
            
            <div class="finding">
                <div class="finding-header">
def helper_function():
    """Helper function."""
    # Missing type hints
    return True
 (1 issues)</div>
                
            <div class="issue info">
                <strong>D001</strong>: Module missing docstring
                <br><small>Line 1, Column 0</small>
            </div>
              <!-- Show first 5 issues -->
            </div>
            
            <div class="finding">
                <div class="finding-header">
class User:
    def __init__(self, name):
        self.name = name
        self.password = "default123"  # Security issue
 (1 issues)</div>
                
            <div class="issue info">
                <strong>D001</strong>: Module missing docstring
                <br><small>Line 1, Column 0</small>
            </div>
              <!-- Show first 5 issues -->
            </div>
            
        </div>
        
        <div class="section">
            <h2>Recommendations</h2>
            
            <div class="recommendation medium">
                <strong>Tactical Recommendation</strong>
                <p>Address 3 instances of D001</p>
                <small>Priority: Medium | Impact: code quality</small>
            </div>
            
        </div>
        
        <div class="footer">
            <p>Generated by Vibe Check Enterprise Reporting Engine</p>
            <p>Confidentiality Level: internal</p>
        </div>
    </div>
</body>
</html>
        
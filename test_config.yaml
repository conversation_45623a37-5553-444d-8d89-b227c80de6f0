# Custom test configuration for Vibe Check
file_extensions:
  - .py

exclude_patterns:
  - "**/__pycache__/**"
  - "**/venv/**"

tools:
  ruff:
    enabled: true
    args:
      - "--select=E,F,W"
  
  mypy:
    enabled: true
    args:
      - "--ignore-missing-imports"
  
  bandit:
    enabled: true
  
  complexity:
    enabled: true

reporting:
  formats:
    - json
  generate_summary: true
  generate_issue_report: true

performance:
  parallel: false
  timeout: 30

"""Test file for VCS CLI testing."""

import os
import hashlib

# Security issues
password = "secret123"  # SEC001: Hardcoded password

def bad_function_name(a,b,c,d,e):  # S004: Bad naming, C004: Too many params
    x=1;y=2  # S006: Multiple statements - fixable
    result=eval("1+1")  # SEC003: Unsafe eval
    return result

# Missing docstring - D001
class TestClass:
    def method_without_docs(self):
        pass

def no_type_hints(param):  # T001: Missing type hints
    return param

# Trailing whitespace issues (fixable)
def function_with_trailing_spaces():
    value = "test"
    return value

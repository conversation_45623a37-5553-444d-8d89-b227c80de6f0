# Project Analysis Report

**Project:** /Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis

**Date:** 2025-06-21 19:45:52

## Summary

- **Total Files:** 9
- **Total Directories:** 1
- **Total Issues:** 9
- **Max Complexity:** 0

## Files

| File | Lines | Issues | Complexity |
|------|-------|--------|------------|
| __init__.py | 24 | 0 | 0 |
| adaptive_config.py | 337 | 1 | 0 |
| file_analyzer.py | 124 | 0 | 0 |
| import_analyzer.py | 495 | 2 | 0 |
| import_visualizer.py | 569 | 6 | 0 |
| metrics_aggregator.py | 161 | 0 | 0 |
| project_analyzer.py | 103 | 0 | 0 |
| result_processor.py | 165 | 0 | 0 |
| tool_executor.py | 113 | 0 | 0 |

## Issues

### adaptive_config.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | custom.print_statement | LOW | Print statement in production code |

### import_analyzer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 256 | B110 | LOW | Try, Except, Pass detected. |
| 0 | custom.hardcoded_path | MEDIUM | Hardcoded file path |

### import_visualizer.py

| Line | Code | Severity | Message |
|------|------|----------|--------|
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |
| 0 | custom.print_statement | LOW | Print statement in production code |


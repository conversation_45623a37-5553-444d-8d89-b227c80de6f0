<!DOCTYPE html>
<html>
<head>
    <title>Project Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .severity-high { color: #d9534f; }
        .severity-medium { color: #f0ad4e; }
        .severity-low { color: #5bc0de; }
    </style>
</head>
<body>
    <h1>Project Analysis Report</h1>
    <p><strong>Project:</strong> /Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check/core/analysis</p>
    <p><strong>Date:</strong> 2025-06-21 19:33:40</p>

    <h2>Summary</h2>
    <ul>
        <li><strong>Total Files:</strong> 8</li>
        <li><strong>Total Directories:</strong> 1</li>
        <li><strong>Total Issues:</strong> 0</li>
        <li><strong>Max Complexity:</strong> 0</li>
    </ul>

    <h2>Files</h2>
    <table>
        <tr>
            <th>File</th>
            <th>Lines</th>
            <th>Issues</th>
            <th>Complexity</th>
        </tr>
        <tr>
            <td>__init__.py</td>
            <td>24</td>
            <td>0</td>
            <td>0</td>
        </tr>
        <tr>
            <td>file_analyzer.py</td>
            <td>124</td>
            <td>0</td>
            <td>0</td>
        </tr>
        <tr>
            <td>import_analyzer.py</td>
            <td>495</td>
            <td>0</td>
            <td>0</td>
        </tr>
        <tr>
            <td>import_visualizer.py</td>
            <td>569</td>
            <td>0</td>
            <td>0</td>
        </tr>
        <tr>
            <td>metrics_aggregator.py</td>
            <td>161</td>
            <td>0</td>
            <td>0</td>
        </tr>
        <tr>
            <td>project_analyzer.py</td>
            <td>103</td>
            <td>0</td>
            <td>0</td>
        </tr>
        <tr>
            <td>result_processor.py</td>
            <td>165</td>
            <td>0</td>
            <td>0</td>
        </tr>
        <tr>
            <td>tool_executor.py</td>
            <td>113</td>
            <td>0</td>
            <td>0</td>
        </tr>
    </table>

    <h2>Issues</h2>
</body>
</html>

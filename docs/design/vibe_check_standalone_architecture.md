# Vibe Check Standalone - Architecture Design

## Overview

Vibe Check Standalone is a comprehensive analysis engine designed for dual operation modes:
- **Integrated Mode**: Embedded within Vibe Check's analysis pipeline
- **Standalone Mode**: Independent CLI tool (`vibe-lint`, `vibe-format`, etc.)

## Core Architecture

### 1. Dual-Mode Engine Core

```
┌─────────────────────────────────────────────────────────────┐
│                    Vibe Check Standalone                    │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Engine Core   │  │  Rule Registry  │  │ Config Mgr  │  │
│  │                 │  │                 │  │             │  │
│  │ • AST Analysis  │  │ • Built-in Rules│  │ • CLI Args  │  │
│  │ • Type Checking │  │ • Custom Rules  │  │ • Config    │  │
│  │ • Security Scan │  │ • Rule Groups   │  │ • Profiles  │  │
│  │ • Formatting    │  │ • Severity Mgmt │  │ • Inherit   │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │ Integrated Mode │  │ Standalone Mode │  │ API Layer   │  │
│  │                 │  │                 │  │             │  │
│  │ • Vibe Check    │  │ • CLI Commands  │  │ • REST API  │  │
│  │   Integration   │  │ • File Watcher  │  │ • LSP Proto │  │
│  │ • Meta Analysis │  │ • Batch Process │  │ • Plugin IF │  │
│  │ • Tool Coord    │  │ • Auto-fix      │  │ • Editor IF │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 2. Integration with Existing Vibe Check

```
┌─────────────────────────────────────────────────────────────┐
│                     Vibe Check System                      │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │  Tool Executor  │  │  Meta Analyzer  │  │ Report Gen  │  │
│  │                 │  │                 │  │             │  │
│  │ • External Tools│  │ • Cross-tool    │  │ • Unified   │  │
│  │   (ruff, mypy)  │  │   Correlation   │  │   Reports   │  │
│  │ • VCS Engine    │◄─┤ • Pattern Det   │  │ • Enhanced  │  │
│  │   (integrated)  │  │ • Insights      │  │   Insights  │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │      CLI        │  │      TUI        │  │   Web UI    │  │
│  │                 │  │                 │  │             │  │
│  │ • vibe-check    │  │ • Interactive   │  │ • Dashboard │  │
│  │ • vibe-lint     │  │   Analysis      │  │ • Real-time │  │
│  │ • vibe-format   │  │ • Live Results  │  │ • Config    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 3. Engine Core Components

#### A. Analysis Engine
```python
class VibeCheckEngine:
    """Core analysis engine supporting dual operation modes."""
    
    def __init__(self, mode: EngineMode, config: EngineConfig):
        self.mode = mode  # INTEGRATED | STANDALONE
        self.config = config
        self.rule_registry = RuleRegistry()
        self.analyzers = self._initialize_analyzers()
    
    async def analyze(self, target: AnalysisTarget) -> AnalysisResult:
        """Main analysis entry point."""
        
    def format_code(self, code: str, options: FormatOptions) -> str:
        """Code formatting with auto-fix capabilities."""
        
    def check_types(self, code: str, context: TypeContext) -> TypeResult:
        """Basic type checking and inference."""
```

#### B. Rule System
```python
class Rule:
    """Base class for all analysis rules."""
    
    rule_id: str
    severity: Severity
    category: RuleCategory
    description: str
    auto_fixable: bool
    
    def check(self, node: ast.AST, context: AnalysisContext) -> List[Issue]:
        """Check rule against AST node."""
        
    def fix(self, node: ast.AST, context: AnalysisContext) -> Optional[Fix]:
        """Auto-fix if possible."""

class RuleRegistry:
    """Registry for managing analysis rules."""
    
    def register_rule(self, rule: Rule, categories: List[RuleCategory]):
        """Register a rule with categories."""
        
    def get_rules_for_category(self, category: RuleCategory) -> List[Rule]:
        """Get all rules for a category."""
        
    def get_enabled_rules(self, config: RuleConfig) -> List[Rule]:
        """Get enabled rules based on configuration."""
```

### 4. Dual Operation Modes

#### Integrated Mode
- **Purpose**: Seamless integration within Vibe Check analysis pipeline
- **Features**:
  - Coordinates with external tools (ruff, mypy, bandit)
  - Provides complementary analysis not covered by external tools
  - Contributes to meta-analysis and cross-tool correlation
  - Shares configuration and reporting infrastructure

#### Standalone Mode
- **Purpose**: Independent operation as a complete analysis tool
- **Features**:
  - Full CLI interface with subcommands
  - Independent configuration management
  - File watching and incremental analysis
  - Auto-fixing and code formatting
  - Editor integration capabilities

### 5. Integration Points

#### A. With Tool Executor
```python
class EnhancedToolExecutor(ToolExecutor):
    """Extended tool executor with VCS integration."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.vcs_engine = VibeCheckEngine(
            mode=EngineMode.INTEGRATED,
            config=self._create_vcs_config()
        )
    
    async def run_tools(self, file_path: Path, content: str) -> Dict[str, Any]:
        """Run all tools including VCS engine."""
        results = await super().run_tools(file_path, content)
        
        # Add VCS analysis
        vcs_result = await self.vcs_engine.analyze(
            AnalysisTarget.from_content(file_path, content)
        )
        results["vibe_check_standalone"] = vcs_result
        
        return results
```

#### B. With Meta Analyzer
```python
class EnhancedMetaAnalyzer(MetaAnalyzer):
    """Enhanced meta analyzer with VCS insights."""
    
    def analyze_combined_results(self, tool_results: Dict[str, Any], 
                                file_path: Path) -> Dict[str, Any]:
        """Enhanced analysis including VCS insights."""
        base_analysis = super().analyze_combined_results(tool_results, file_path)
        
        # Add VCS-specific insights
        vcs_insights = self._analyze_vcs_patterns(tool_results)
        base_analysis["vcs_insights"] = vcs_insights
        
        return base_analysis
```

### 6. Configuration Architecture

#### Hierarchical Configuration System
```yaml
# Global configuration
global:
  vibe_check_standalone:
    enabled: true
    mode: "integrated"  # integrated | standalone | both
    
# Project-specific configuration  
project:
  vibe_check_standalone:
    rules:
      style: "enabled"
      security: "enabled"
      complexity: "warning"
    auto_fix: true
    format_on_save: true
```

#### Configuration Inheritance
1. **Global defaults** (`~/.vibe-check/config.yaml`)
2. **Project configuration** (`./vibe-check.yaml`)
3. **CLI arguments** (highest priority)
4. **Environment variables** (VCS_* prefix)

### 7. Performance Architecture

#### Incremental Analysis
```python
class IncrementalAnalyzer:
    """Handles incremental analysis for performance."""
    
    def __init__(self, cache_dir: Path):
        self.cache = AnalysisCache(cache_dir)
        self.dependency_graph = DependencyGraph()
    
    async def analyze_changes(self, changed_files: List[Path]) -> AnalysisResult:
        """Analyze only changed files and their dependencies."""
        
    def invalidate_cache(self, file_path: Path):
        """Invalidate cache for file and dependents."""
```

#### Caching Strategy
- **AST caching**: Cache parsed ASTs for unchanged files
- **Rule result caching**: Cache rule results with file hash
- **Dependency tracking**: Track file dependencies for smart invalidation
- **Incremental updates**: Only re-analyze affected files

### 8. Extensibility Architecture

#### Plugin System
```python
class VibeCheckPlugin:
    """Base class for VCS plugins."""
    
    def register_rules(self, registry: RuleRegistry):
        """Register custom rules."""
        
    def register_formatters(self, formatter_registry: FormatterRegistry):
        """Register custom formatters."""
        
    def register_analyzers(self, analyzer_registry: AnalyzerRegistry):
        """Register custom analyzers."""
```

#### Custom Rule Development
```python
@rule("custom-naming-convention", severity=Severity.WARNING)
class CustomNamingRule(Rule):
    """Example custom rule."""
    
    def check(self, node: ast.AST, context: AnalysisContext) -> List[Issue]:
        if isinstance(node, ast.FunctionDef):
            if not node.name.startswith("handle_"):
                return [Issue(
                    rule_id=self.rule_id,
                    message=f"Function {node.name} should start with 'handle_'",
                    line=node.lineno,
                    column=node.col_offset
                )]
        return []
```

## Benefits of This Architecture

### 1. **Complementary Analysis**
- VCS provides unique insights not available in external tools
- Fills gaps in analysis coverage
- Offers alternative perspectives on code quality

### 2. **Reliability**
- Always available regardless of external tool installation
- Consistent behavior across environments
- Reduced dependency management complexity

### 3. **Integration**
- Seamless integration with existing Vibe Check infrastructure
- Enhanced meta-analysis capabilities
- Unified reporting and configuration

### 4. **Flexibility**
- Can operate independently or alongside external tools
- Configurable analysis depth and scope
- Extensible through plugin system

### 5. **Performance**
- Incremental analysis for large codebases
- Intelligent caching and dependency tracking
- Optimized for Vibe Check's analysis patterns

## Next Steps

1. **Phase 1**: Core engine implementation
2. **Phase 2**: CLI interface and standalone mode
3. **Phase 3**: Advanced features and optimizations
4. **Phase 4**: Plugin system and extensibility
5. **Phase 5**: Editor integration and LSP support

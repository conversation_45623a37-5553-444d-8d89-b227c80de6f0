# Vibe Check Standalone - Performance Strategy

## Overview

This document outlines the performance optimization strategy for Vibe Check Standalone, focusing on incremental analysis, intelligent caching, parallel processing, and resource management to ensure excellent performance across projects of all sizes.

## Performance Architecture

### 1. Incremental Analysis System

#### Core Concept
Only analyze files that have changed and their dependencies, dramatically reducing analysis time for large projects.

```python
class IncrementalAnalyzer:
    """Manages incremental analysis with dependency tracking."""
    
    def __init__(self, cache_dir: Path, project_root: Path):
        self.cache_dir = cache_dir
        self.project_root = project_root
        self.dependency_graph = DependencyGraph()
        self.file_tracker = FileChangeTracker()
        self.analysis_cache = AnalysisCache(cache_dir)
        
    async def analyze_incremental(self, target_files: List[Path]) -> AnalysisResult:
        """Perform incremental analysis on changed files."""
        
        # 1. Detect changed files
        changed_files = await self.file_tracker.get_changed_files(target_files)
        
        # 2. Build dependency graph
        await self.dependency_graph.update(changed_files)
        
        # 3. Find affected files (changed + dependents)
        affected_files = self.dependency_graph.get_affected_files(changed_files)
        
        # 4. Invalidate cache for affected files
        self.analysis_cache.invalidate_files(affected_files)
        
        # 5. Analyze only affected files
        results = await self._analyze_files(affected_files)
        
        # 6. Merge with cached results for unchanged files
        complete_results = await self._merge_with_cached_results(
            results, target_files, affected_files
        )
        
        return complete_results
```

#### Dependency Tracking
```python
class DependencyGraph:
    """Tracks file dependencies for smart invalidation."""
    
    def __init__(self):
        self.graph = networkx.DiGraph()
        self.import_resolver = ImportResolver()
        
    async def update(self, files: List[Path]):
        """Update dependency graph for changed files."""
        for file_path in files:
            # Parse imports and dependencies
            dependencies = await self.import_resolver.resolve_dependencies(file_path)
            
            # Update graph
            self.graph.add_node(str(file_path))
            for dep in dependencies:
                self.graph.add_edge(str(file_path), str(dep))
                
    def get_affected_files(self, changed_files: List[Path]) -> Set[Path]:
        """Get all files affected by changes (including dependents)."""
        affected = set(changed_files)
        
        for file_path in changed_files:
            # Add all files that depend on this file
            dependents = self._get_dependents(file_path)
            affected.update(dependents)
            
        return affected
        
    def _get_dependents(self, file_path: Path) -> Set[Path]:
        """Get all files that depend on the given file."""
        dependents = set()
        
        # Use networkx to find all nodes that have a path to this file
        for node in self.graph.nodes():
            if networkx.has_path(self.graph, node, str(file_path)):
                dependents.add(Path(node))
                
        return dependents
```

### 2. Intelligent Caching System

#### Multi-Level Caching
```python
class AnalysisCache:
    """Multi-level caching system for analysis results."""
    
    def __init__(self, cache_dir: Path):
        self.cache_dir = cache_dir
        self.memory_cache = LRUCache(maxsize=1000)
        self.disk_cache = DiskCache(cache_dir)
        self.metadata_cache = MetadataCache(cache_dir / "metadata")
        
    async def get_cached_result(self, file_path: Path, 
                              rules: List[str]) -> Optional[AnalysisResult]:
        """Get cached analysis result if valid."""
        
        # 1. Check memory cache first (fastest)
        cache_key = self._generate_cache_key(file_path, rules)
        if cache_key in self.memory_cache:
            return self.memory_cache[cache_key]
            
        # 2. Check disk cache
        disk_result = await self.disk_cache.get(cache_key)
        if disk_result and self._is_cache_valid(file_path, disk_result):
            # Promote to memory cache
            self.memory_cache[cache_key] = disk_result
            return disk_result
            
        return None
        
    async def cache_result(self, file_path: Path, rules: List[str], 
                         result: AnalysisResult):
        """Cache analysis result at multiple levels."""
        cache_key = self._generate_cache_key(file_path, rules)
        
        # Cache in memory
        self.memory_cache[cache_key] = result
        
        # Cache on disk
        await self.disk_cache.set(cache_key, result)
        
        # Update metadata
        await self.metadata_cache.update_file_metadata(file_path, result)
        
    def _is_cache_valid(self, file_path: Path, cached_result: AnalysisResult) -> bool:
        """Check if cached result is still valid."""
        # Check file modification time
        current_mtime = file_path.stat().st_mtime
        cached_mtime = cached_result.metadata.get("file_mtime", 0)
        
        if current_mtime > cached_mtime:
            return False
            
        # Check rule configuration hash
        current_config_hash = self._get_config_hash()
        cached_config_hash = cached_result.metadata.get("config_hash", "")
        
        return current_config_hash == cached_config_hash
```

#### Cache Invalidation Strategy
```python
class CacheInvalidationManager:
    """Manages intelligent cache invalidation."""
    
    def __init__(self, cache: AnalysisCache, dependency_graph: DependencyGraph):
        self.cache = cache
        self.dependency_graph = dependency_graph
        
    async def invalidate_for_changes(self, changed_files: List[Path]):
        """Invalidate cache for changed files and their dependents."""
        
        # Get all affected files
        affected_files = self.dependency_graph.get_affected_files(changed_files)
        
        # Invalidate cache for affected files
        for file_path in affected_files:
            await self.cache.invalidate_file(file_path)
            
    async def invalidate_for_config_change(self, config_changes: Dict[str, Any]):
        """Invalidate cache when configuration changes."""
        
        # Determine which rules were affected
        affected_rules = self._get_affected_rules(config_changes)
        
        # Invalidate cache for files analyzed with affected rules
        await self.cache.invalidate_by_rules(affected_rules)
        
    async def cleanup_stale_cache(self, max_age_days: int = 7):
        """Clean up stale cache entries."""
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
        await self.cache.cleanup_older_than(cutoff_time)
```

### 3. Parallel Processing Architecture

#### File-Level Parallelization
```python
class ParallelAnalyzer:
    """Manages parallel analysis of multiple files."""
    
    def __init__(self, max_workers: Optional[int] = None):
        self.max_workers = max_workers or self._calculate_optimal_workers()
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.semaphore = asyncio.Semaphore(self.max_workers)
        
    async def analyze_files_parallel(self, files: List[Path], 
                                   rules: List[Rule]) -> List[AnalysisResult]:
        """Analyze multiple files in parallel."""
        
        # Create analysis tasks
        tasks = []
        for file_path in files:
            task = self._analyze_file_with_semaphore(file_path, rules)
            tasks.append(task)
            
        # Execute tasks in parallel with progress tracking
        results = []
        with tqdm(total=len(tasks), desc="Analyzing files") as pbar:
            for coro in asyncio.as_completed(tasks):
                result = await coro
                results.append(result)
                pbar.update(1)
                
        return results
        
    async def _analyze_file_with_semaphore(self, file_path: Path, 
                                         rules: List[Rule]) -> AnalysisResult:
        """Analyze single file with semaphore for resource control."""
        async with self.semaphore:
            return await self._analyze_file(file_path, rules)
            
    def _calculate_optimal_workers(self) -> int:
        """Calculate optimal number of worker threads."""
        # Consider CPU cores, memory, and I/O characteristics
        cpu_count = os.cpu_count() or 4
        
        # For I/O bound analysis, we can use more workers than CPU cores
        # For CPU bound analysis, limit to CPU cores
        return min(cpu_count * 2, 16)  # Cap at 16 workers
```

#### Rule-Level Parallelization
```python
class ParallelRuleExecutor:
    """Executes rules in parallel for a single file."""
    
    def __init__(self):
        self.rule_groups = self._group_rules_by_dependency()
        
    async def execute_rules_parallel(self, ast_tree: ast.AST, 
                                   context: AnalysisContext,
                                   rules: List[Rule]) -> List[Issue]:
        """Execute rules in parallel where possible."""
        
        all_issues = []
        
        # Execute independent rule groups in parallel
        for rule_group in self.rule_groups:
            group_rules = [r for r in rules if r in rule_group]
            if not group_rules:
                continue
                
            # Execute rules in this group in parallel
            tasks = [
                self._execute_rule_async(rule, ast_tree, context)
                for rule in group_rules
            ]
            
            group_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Collect results and handle exceptions
            for result in group_results:
                if isinstance(result, Exception):
                    logger.error(f"Rule execution failed: {result}")
                else:
                    all_issues.extend(result)
                    
        return all_issues
```

### 4. Memory Management

#### Memory-Efficient AST Processing
```python
class MemoryEfficientAnalyzer:
    """Analyzes code with minimal memory footprint."""
    
    def __init__(self, max_memory_mb: int = 512):
        self.max_memory_mb = max_memory_mb
        self.memory_monitor = MemoryMonitor(max_memory_mb)
        
    async def analyze_with_memory_limit(self, files: List[Path]) -> AnalysisResult:
        """Analyze files while respecting memory limits."""
        
        # Sort files by size (analyze smaller files first)
        sorted_files = sorted(files, key=lambda f: f.stat().st_size)
        
        results = []
        current_batch = []
        
        for file_path in sorted_files:
            # Check memory usage
            if self.memory_monitor.should_process_batch():
                # Process current batch
                if current_batch:
                    batch_results = await self._process_file_batch(current_batch)
                    results.extend(batch_results)
                    current_batch = []
                    
                    # Force garbage collection
                    gc.collect()
                    
            current_batch.append(file_path)
            
        # Process remaining files
        if current_batch:
            batch_results = await self._process_file_batch(current_batch)
            results.extend(batch_results)
            
        return self._combine_results(results)
        
    async def _process_file_batch(self, files: List[Path]) -> List[AnalysisResult]:
        """Process a batch of files efficiently."""
        results = []
        
        for file_path in files:
            try:
                # Use streaming AST parsing for large files
                if file_path.stat().st_size > 1024 * 1024:  # 1MB
                    result = await self._analyze_large_file(file_path)
                else:
                    result = await self._analyze_normal_file(file_path)
                    
                results.append(result)
                
            except MemoryError:
                logger.warning(f"Skipping {file_path} due to memory constraints")
                continue
                
        return results
```

#### Resource Monitoring
```python
class MemoryMonitor:
    """Monitors memory usage during analysis."""
    
    def __init__(self, max_memory_mb: int):
        self.max_memory_mb = max_memory_mb
        self.process = psutil.Process()
        
    def should_process_batch(self) -> bool:
        """Check if we should process the current batch."""
        current_memory_mb = self.process.memory_info().rss / 1024 / 1024
        return current_memory_mb > (self.max_memory_mb * 0.8)  # 80% threshold
        
    def get_memory_stats(self) -> Dict[str, float]:
        """Get current memory statistics."""
        memory_info = self.process.memory_info()
        return {
            "rss_mb": memory_info.rss / 1024 / 1024,
            "vms_mb": memory_info.vms / 1024 / 1024,
            "percent": self.process.memory_percent()
        }
```

### 5. Performance Benchmarks and Targets

#### Target Performance Metrics

| Project Size | Files | Target Time | Memory Usage | Cache Hit Rate |
|--------------|-------|-------------|--------------|----------------|
| Small | <100 | <5s | <50MB | >60% |
| Medium | 100-1000 | <30s | <200MB | >70% |
| Large | 1000-5000 | <2m | <500MB | >80% |
| Extra Large | >5000 | <5m | <1GB | >85% |

#### Incremental Analysis Targets

| Change Type | Files Changed | Target Time | Speedup |
|-------------|---------------|-------------|---------|
| Single file | 1 | <1s | 10-50x |
| Small change | 2-5 | <3s | 5-20x |
| Medium change | 5-20 | <10s | 3-10x |
| Large change | >20 | <30s | 2-5x |

### 6. Performance Optimization Techniques

#### AST Optimization
```python
class OptimizedASTProcessor:
    """Optimized AST processing for better performance."""
    
    def __init__(self):
        self.ast_cache = {}
        self.visitor_pool = VisitorPool()
        
    def parse_with_cache(self, content: str, filename: str) -> ast.AST:
        """Parse AST with caching."""
        content_hash = hashlib.md5(content.encode()).hexdigest()
        
        if content_hash in self.ast_cache:
            return self.ast_cache[content_hash]
            
        tree = ast.parse(content, filename)
        self.ast_cache[content_hash] = tree
        return tree
        
    def visit_optimized(self, tree: ast.AST, visitors: List[ast.NodeVisitor]) -> List[Any]:
        """Visit AST with optimized visitor pattern."""
        # Use single-pass visiting for multiple visitors
        combined_visitor = CombinedVisitor(visitors)
        combined_visitor.visit(tree)
        return combined_visitor.get_results()
```

#### I/O Optimization
```python
class OptimizedFileReader:
    """Optimized file reading for better I/O performance."""
    
    async def read_files_batch(self, files: List[Path]) -> Dict[Path, str]:
        """Read multiple files efficiently."""
        results = {}
        
        # Use aiofiles for async I/O
        async with aiofiles.open_batch(files) as file_handles:
            tasks = [
                self._read_file_async(file_path, handle)
                for file_path, handle in file_handles.items()
            ]
            
            file_contents = await asyncio.gather(*tasks)
            
            for file_path, content in zip(files, file_contents):
                results[file_path] = content
                
        return results
```

### 7. Performance Monitoring and Profiling

#### Performance Profiler
```python
class PerformanceProfiler:
    """Profiles analysis performance for optimization."""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.start_times = {}
        
    @contextmanager
    def profile_operation(self, operation_name: str):
        """Profile a specific operation."""
        start_time = time.perf_counter()
        self.start_times[operation_name] = start_time
        
        try:
            yield
        finally:
            end_time = time.perf_counter()
            duration = end_time - start_time
            self.metrics[operation_name].append(duration)
            
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate performance report."""
        report = {}
        
        for operation, times in self.metrics.items():
            report[operation] = {
                "count": len(times),
                "total_time": sum(times),
                "avg_time": sum(times) / len(times),
                "min_time": min(times),
                "max_time": max(times)
            }
            
        return report
```

### 8. Configuration for Performance

#### Performance Configuration Options
```yaml
vibe_check_standalone:
  performance:
    # Parallel processing
    parallel_workers: 4          # Number of worker threads
    max_memory: "512MB"          # Maximum memory usage
    
    # Caching
    cache_enabled: true
    cache_dir: ".vibe-cache"
    cache_max_size: "1GB"
    cache_ttl: "7d"
    
    # Incremental analysis
    incremental: true
    dependency_tracking: true
    
    # I/O optimization
    batch_size: 50               # Files per batch
    read_buffer_size: "64KB"     # File read buffer
    
    # Analysis optimization
    ast_cache_size: 1000         # AST cache entries
    rule_parallelization: true   # Parallel rule execution
    
    # Monitoring
    profile_enabled: false       # Performance profiling
    memory_monitoring: true      # Memory usage monitoring
```

This performance strategy ensures that Vibe Check Standalone can handle projects of any size efficiently while providing excellent user experience through fast analysis times and intelligent resource management.

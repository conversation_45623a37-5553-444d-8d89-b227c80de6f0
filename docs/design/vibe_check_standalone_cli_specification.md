# Vibe Check Standalone - CLI Interface Specification

## Overview

The Vibe Check Standalone CLI provides a comprehensive interface for code analysis, formatting, and quality checking. It operates in both standalone mode and as an integrated component of the broader Vibe Check ecosystem.

## Command Structure

### Primary Commands

#### 1. `vibe-lint` - Main Analysis Command
```bash
vibe-lint [COMMAND] [OPTIONS] [PATH...]
```

**Subcommands:**
- `check` - Analyze code for issues
- `format` - Format code according to style rules
- `fix` - Auto-fix issues where possible
- `watch` - Continuous analysis with file watching
- `config` - Configuration management
- `rules` - Rule management and information

#### 2. `vibe-format` - Dedicated Formatting
```bash
vibe-format [OPTIONS] [PATH...]
```

#### 3. `vibe-check-standalone` - Full Analysis Suite
```bash
vibe-check-standalone [COMMAND] [OPTIONS] [PATH...]
```

## Detailed Command Specifications

### `vibe-lint check` - Code Analysis

#### Basic Usage
```bash
# Analyze current directory
vibe-lint check

# Analyze specific files/directories
vibe-lint check src/ tests/main.py

# Analyze with specific rules
vibe-lint check --rules style,security src/

# Analyze with minimum severity
vibe-lint check --severity error src/
```

#### Options
```bash
# Rule Selection
--rules RULES              # Comma-separated rule categories (style,security,complexity,docs,imports,types)
--enable-rule RULE          # Enable specific rule (can be used multiple times)
--disable-rule RULE         # Disable specific rule (can be used multiple times)
--severity LEVEL            # Minimum severity level (error,warning,info,hint)

# Output Control
--format FORMAT             # Output format (text,json,yaml,sarif,github)
--output FILE               # Write output to file
--quiet                     # Suppress non-error output
--verbose                   # Increase verbosity
--show-source               # Show source code context
--show-fixes                # Show available auto-fixes
--no-color                  # Disable colored output

# Analysis Control
--fix                       # Auto-fix issues where possible
--diff                      # Show diff for fixes
--check-only                # Don't write fixes, just check
--incremental               # Only analyze changed files
--parallel WORKERS          # Number of parallel workers (default: auto)

# Configuration
--config FILE               # Configuration file path
--profile PROFILE           # Use configuration profile
--project-root PATH         # Project root directory

# Integration
--external-tools TOOLS      # Run alongside external tools (ruff,mypy,bandit)
--meta-analysis             # Enable cross-tool meta-analysis
--unified-report            # Generate unified report with external tools

# Performance
--cache-dir DIR             # Cache directory (default: .vibe-cache)
--no-cache                  # Disable caching
--timeout SECONDS           # Analysis timeout (default: 300)
--max-memory SIZE           # Maximum memory usage (default: 512MB)

# File Selection
--include PATTERN           # Include files matching pattern
--exclude PATTERN           # Exclude files matching pattern
--ignore-gitignore          # Don't respect .gitignore
--follow-symlinks           # Follow symbolic links
```

#### Examples
```bash
# Basic analysis
vibe-lint check src/

# Security-focused analysis
vibe-lint check --rules security --severity error src/

# Analysis with auto-fix
vibe-lint check --fix --show-fixes src/

# Fast incremental analysis
vibe-lint check --incremental --parallel 8 src/

# Integration with external tools
vibe-lint check --external-tools ruff,mypy --meta-analysis src/

# Custom output format
vibe-lint check --format json --output results.json src/

# Strict analysis with profile
vibe-lint check --profile strict --severity error src/
```

### `vibe-lint format` - Code Formatting

#### Basic Usage
```bash
# Format files in-place
vibe-lint format src/

# Show formatting diff without applying
vibe-lint format --diff src/

# Check if files are formatted
vibe-lint format --check src/
```

#### Options
```bash
# Formatting Control
--diff                      # Show formatting diff
--check                     # Check if files are formatted (exit 1 if not)
--line-length LENGTH        # Maximum line length (default: 88)
--indent-size SIZE          # Indentation size (default: 4)
--quote-style STYLE         # Quote style (single,double)
--trailing-commas           # Add trailing commas

# File Handling
--in-place                  # Format files in-place (default)
--backup                    # Create backup files
--output-dir DIR            # Write formatted files to directory

# Integration
--config FILE               # Configuration file
--profile PROFILE           # Formatting profile
```

#### Examples
```bash
# Format with diff preview
vibe-lint format --diff src/

# Check formatting compliance
vibe-lint format --check src/

# Custom formatting options
vibe-lint format --line-length 100 --quote-style single src/
```

### `vibe-lint fix` - Auto-Fix Issues

#### Basic Usage
```bash
# Auto-fix all fixable issues
vibe-lint fix src/

# Fix specific rule categories
vibe-lint fix --rules style,imports src/

# Preview fixes without applying
vibe-lint fix --dry-run src/
```

#### Options
```bash
# Fix Control
--dry-run                   # Show fixes without applying
--interactive               # Prompt for each fix
--rules RULES               # Only fix specific rule categories
--severity LEVEL            # Only fix issues of minimum severity

# Safety
--backup                    # Create backup files
--verify                    # Verify fixes don't break syntax
--max-fixes COUNT           # Maximum fixes per file
```

### `vibe-lint watch` - Continuous Analysis

#### Basic Usage
```bash
# Watch current directory
vibe-lint watch

# Watch specific directories
vibe-lint watch src/ tests/

# Watch with auto-fix
vibe-lint watch --fix src/
```

#### Options
```bash
# Watch Control
--recursive                 # Watch subdirectories recursively
--ignore PATTERN            # Ignore file patterns
--debounce SECONDS          # Debounce delay (default: 0.5)

# Analysis Options
--fix                       # Auto-fix on changes
--rules RULES               # Rule categories to check
--severity LEVEL            # Minimum severity level

# Output Control
--quiet                     # Only show errors
--clear                     # Clear screen on changes
--bell                      # Ring bell on errors
```

### `vibe-lint config` - Configuration Management

#### Subcommands
```bash
# Show configuration
vibe-lint config show [SECTION]

# Set configuration values
vibe-lint config set KEY VALUE

# Initialize configuration
vibe-lint config init [--profile PROFILE]

# Validate configuration
vibe-lint config validate [FILE]

# List available profiles
vibe-lint config profiles

# Show configuration sources
vibe-lint config sources

# Migrate from external tools
vibe-lint config migrate --from TOOL
```

#### Examples
```bash
# Show current configuration
vibe-lint config show

# Show rule configuration
vibe-lint config show rules

# Set rule severity
vibe-lint config set rules.style.severity warning

# Initialize with strict profile
vibe-lint config init --profile strict

# Migrate from ruff configuration
vibe-lint config migrate --from ruff
```

### `vibe-lint rules` - Rule Management

#### Subcommands
```bash
# List all rules
vibe-lint rules list [--category CATEGORY]

# Show rule information
vibe-lint rules info RULE_ID

# Test rule against code
vibe-lint rules test RULE_ID --code CODE

# Enable/disable rules
vibe-lint rules enable RULE_ID
vibe-lint rules disable RULE_ID
```

#### Examples
```bash
# List style rules
vibe-lint rules list --category style

# Get information about specific rule
vibe-lint rules info line-too-long

# Test rule against code snippet
vibe-lint rules test missing-docstring --code "def func(): pass"
```

## Integration Commands

### Standalone Mode Commands
```bash
# Complete standalone analysis
vibe-check-standalone analyze PROJECT_PATH

# Standalone formatting
vibe-format --standalone src/

# Standalone with external tool comparison
vibe-lint check --compare-with ruff,mypy src/
```

### Integrated Mode Commands
```bash
# Run as part of Vibe Check pipeline
vibe-check analyze --include-standalone PROJECT_PATH

# Enhanced meta-analysis
vibe-check analyze --meta-analysis --tools ruff,mypy,vcs PROJECT_PATH
```

## Output Formats

### Text Format (Default)
```
src/main.py:15:1: E501 line too long (92 > 88 characters)
src/main.py:23:5: W291 trailing whitespace
src/utils.py:8:1: D100 Missing docstring in public function
```

### JSON Format
```json
{
  "files": [
    {
      "path": "src/main.py",
      "issues": [
        {
          "rule_id": "E501",
          "message": "line too long (92 > 88 characters)",
          "line": 15,
          "column": 1,
          "severity": "warning",
          "category": "style",
          "fixable": true
        }
      ]
    }
  ],
  "summary": {
    "total_files": 2,
    "total_issues": 3,
    "by_severity": {
      "error": 0,
      "warning": 2,
      "info": 1
    }
  }
}
```

### SARIF Format
```json
{
  "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
  "version": "2.1.0",
  "runs": [
    {
      "tool": {
        "driver": {
          "name": "vibe-check-standalone",
          "version": "1.0.0"
        }
      },
      "results": [...]
    }
  ]
}
```

## Exit Codes

| Code | Meaning |
|------|---------|
| 0 | Success, no issues found |
| 1 | Issues found |
| 2 | Configuration error |
| 3 | File not found or permission error |
| 4 | Internal error |
| 5 | Timeout or resource limit exceeded |

## Environment Variables

```bash
# Engine configuration
VCS_CONFIG_FILE=/path/to/config.yaml
VCS_CACHE_DIR=/path/to/cache
VCS_PARALLEL_WORKERS=4

# Rule configuration
VCS_RULES=style,security,complexity
VCS_SEVERITY=warning
VCS_AUTO_FIX=true

# Output configuration
VCS_FORMAT=json
VCS_OUTPUT_FILE=results.json
VCS_VERBOSE=true

# Integration configuration
VCS_MODE=standalone
VCS_EXTERNAL_TOOLS=ruff,mypy
VCS_META_ANALYSIS=true
```

## Shell Completion

### Bash Completion
```bash
# Install completion
vibe-lint --install-completion bash

# Manual completion
eval "$(_VIBE_LINT_COMPLETE=bash_source vibe-lint)"
```

### Zsh Completion
```bash
# Install completion
vibe-lint --install-completion zsh

# Manual completion
eval "$(_VIBE_LINT_COMPLETE=zsh_source vibe-lint)"
```

## Editor Integration

### VS Code Integration
```bash
# Generate VS Code settings
vibe-lint config vscode --output .vscode/settings.json

# LSP server mode
vibe-lint lsp --stdio
```

### Vim/Neovim Integration
```bash
# Generate ALE configuration
vibe-lint config ale --output ale-config.vim

# Syntastic configuration
vibe-lint config syntastic --output syntastic-config.vim
```

## Performance Considerations

### Optimization Flags
```bash
# Fast mode (reduced rule set)
vibe-lint check --fast src/

# Incremental analysis
vibe-lint check --incremental src/

# Parallel processing
vibe-lint check --parallel 8 src/

# Memory optimization
vibe-lint check --max-memory 256MB src/
```

### Caching
```bash
# Enable caching
vibe-lint check --cache-dir .vibe-cache src/

# Clear cache
vibe-lint cache clear

# Cache statistics
vibe-lint cache stats
```

This CLI specification provides a comprehensive interface that supports both standalone operation and seamless integration with the broader Vibe Check ecosystem, while maintaining familiar patterns from existing tools like ruff and mypy.

# VCS Architecture Design Document

## Overview

This document outlines the architecture design for the Vibe Check Standalone (VCS) engine, implementing Sprint VCS-1.1 requirements for dual-mode operation, rule registry system, and enhanced analysis capabilities.

## Core Components

### 1. VibeCheckEngine

The central engine that orchestrates analysis in both integrated and standalone modes.

```python
class VibeCheckEngine:
    def __init__(self, mode: EngineMode, config: VCSConfig):
        self.mode = mode  # INTEGRATED | STANDALONE
        self.config = config
        self.rule_registry = RuleRegistry()
        self.cache_manager = CacheManager(config.cache_dir)
        self.performance_monitor = PerformanceMonitor()
    
    async def analyze(self, target: AnalysisTarget, context: AnalysisContext) -> AnalysisResult:
        """Main analysis entry point for dual-mode operation"""
```

**Key Features:**
- Dual-mode operation with seamless switching
- Lifecycle management (start/stop/restart)
- Configuration inheritance and validation
- Performance monitoring and optimization
- Integration with existing ToolExecutor

### 2. RuleRegistry

Extensible system for managing analysis rules with categorization and dependencies.

```python
class RuleRegistry:
    def __init__(self):
        self.rules: Dict[str, AnalysisRule] = {}
        self.categories: Dict[RuleCategory, Set[str]] = {}
        self.dependencies: Dict[str, Set[str]] = {}
    
    def register_rule(self, rule: AnalysisRule) -> None:
        """Register a new analysis rule"""
    
    def get_rules_for_category(self, category: RuleCategory) -> List[AnalysisRule]:
        """Get all rules for a specific category"""
    
    def resolve_dependencies(self, rule_ids: Set[str]) -> List[str]:
        """Resolve rule dependencies and return execution order"""
```

**Rule Categories:**
- **Style**: PEP 8 compliance, formatting, naming conventions
- **Security**: Vulnerability detection, unsafe patterns
- **Complexity**: Cyclomatic/cognitive complexity, function length
- **Documentation**: Docstring coverage, comment quality
- **Imports**: Organization, unused imports, circular dependencies
- **Types**: Type annotation coverage, consistency

### 3. Enhanced StandaloneCodeAnalyzer

Extended version of the existing analyzer with VCS integration.

```python
class EnhancedStandaloneCodeAnalyzer(StandaloneCodeAnalyzer):
    def __init__(self, vcs_engine: VibeCheckEngine):
        super().__init__()
        self.vcs_engine = vcs_engine
        self.built_in_rules_enabled = True
    
    async def analyze_with_vcs(self, target: AnalysisTarget) -> AnalysisResult:
        """Analyze using both legacy and VCS capabilities"""
```

## Configuration Architecture

### Configuration Inheritance

Priority order (highest to lowest):
1. **CLI arguments/overrides** (immediate)
2. **Explicit config file** (`--config path`)
3. **Project configuration** (`./vibe-check.yaml`)
4. **User configuration** (`~/.vibe-check/config.yaml`)
5. **Environment variables** (`VCS_*` prefix)
6. **Default configuration** (built-in)

### Configuration Schema

```yaml
# .vibe-check.yaml
vcs:
  mode: integrated  # integrated | standalone
  cache_enabled: true
  cache_dir: ~/.vibe-check/cache
  performance_mode: false
  auto_fix_enabled: false
  
  # Rule configuration
  enabled_categories: [style, security, complexity, docs, imports, types]
  enabled_rules: []  # Empty = all rules in enabled categories
  disabled_rules: []  # Specific rules to disable
  
  rule_config:
    max_line_length: 88
    complexity_threshold: 10
    cognitive_complexity_threshold: 15
    documentation_threshold: 0.8
  
  # Performance settings
  parallel_analysis: true
  max_workers: null  # Auto-detect
  timeout_seconds: 300.0
  
  # Integration settings
  integrate_with_external_tools: true
  external_tool_coordination: true
  meta_analysis_enabled: true
```

## Integration Points

### 1. CLI Integration

Add `--vcs-mode` flag to existing analyze command:

```python
@cli.command()
@click.option("--vcs-mode", is_flag=True, help="Enable VCS standalone mode")
def analyze(project_path: str, vcs_mode: bool = False, ...):
    """Analyze a Python project with optional VCS mode."""
```

### 2. ToolExecutor Integration

Extend existing ToolExecutor to include VCS engine:

```python
class EnhancedToolExecutor(ToolExecutor):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.vcs_engine = self._initialize_vcs_engine()
    
    async def run_tools(self, file_path: Path, content: str) -> Dict[str, Any]:
        """Run all tools including VCS engine."""
        results = await super().run_tools(file_path, content)
        
        # Add VCS analysis if enabled
        if self.vcs_engine and self.vcs_engine.is_enabled():
            vcs_result = await self.vcs_engine.analyze(
                AnalysisTarget.from_content(file_path, content)
            )
            results["vibe_check_standalone"] = vcs_result.to_dict()
        
        return results
```

### 3. Backward Compatibility

Maintain full backward compatibility with existing StandaloneCodeAnalyzer:

```python
# Legacy usage continues to work
analyzer = StandaloneCodeAnalyzer()
result = analyzer.analyze_file(file_path, content)

# Enhanced usage with VCS
vcs_engine = VibeCheckEngine(mode=EngineMode.INTEGRATED, config=config)
enhanced_analyzer = EnhancedStandaloneCodeAnalyzer(vcs_engine)
result = await enhanced_analyzer.analyze_with_vcs(target)
```

## Performance Architecture

### Caching Strategy

Multi-level caching for optimal performance:

```python
class CacheManager:
    def __init__(self, cache_dir: Path):
        self.memory_cache = LRUCache(maxsize=1000)
        self.disk_cache = DiskCache(cache_dir)
        self.metadata_cache = MetadataCache()
    
    async def get_cached_result(self, target: AnalysisTarget) -> Optional[AnalysisResult]:
        """Get cached analysis result if valid"""
    
    async def cache_result(self, target: AnalysisTarget, result: AnalysisResult) -> None:
        """Cache analysis result with metadata"""
```

### Performance Monitoring

Built-in performance tracking and optimization:

```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}
        self.benchmarks = {}
    
    def track_analysis(self, target: AnalysisTarget, execution_time: float) -> None:
        """Track analysis performance"""
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Generate performance report"""
```

## Success Criteria

### Must Have (Exit Criteria)
- [ ] VibeCheckEngine operational in both integrated and standalone modes
- [ ] RuleRegistry system functional with rule management
- [ ] Enhanced StandaloneCodeAnalyzer with built-in capabilities
- [ ] CLI integration working with --vcs-mode flag
- [ ] Configuration system supports multiple sources
- [ ] Backward compatibility maintained

### Quality Gates
- [ ] Test coverage >90% for all new components
- [ ] Performance benchmarks established (<30% overhead)
- [ ] Error handling comprehensive and user-friendly
- [ ] Documentation complete for all new APIs
- [ ] Integration tests cover all major workflows

## Implementation Timeline

### Week 1: Core Architecture (Days 1-5)
- **Days 1-2**: Architecture design and interface specification ✅
- **Days 3-5**: Core implementation with dual-mode support

### Week 2: Rule Registry (Days 6-10)
- **Days 1-3**: Registry architecture and rule management
- **Days 4-5**: Integration and testing

### Week 3: CLI Integration (Days 11-15)
- **Days 1-2**: Analyzer enhancement with VCS integration
- **Days 3-5**: CLI integration and end-to-end testing

This architecture provides a solid foundation for the VCS engine while maintaining compatibility with existing Vibe Check functionality and enabling future extensibility through the plugin system.

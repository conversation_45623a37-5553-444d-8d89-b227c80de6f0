<!DOCTYPE html>
<html>
<head>
    <title>Project Analysis Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        h1, h2, h3 { color: #333; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        tr:nth-child(even) { background-color: #f9f9f9; }
        .severity-high { color: #d9534f; }
        .severity-medium { color: #f0ad4e; }
        .severity-low { color: #5bc0de; }
    </style>
</head>
<body>
    <h1>Project Analysis Report</h1>
    <p><strong>Project:</strong> /Users/<USER>/Lokalne_Kody/PAT_project_analysis/vibe_check</p>
    <p><strong>Date:</strong> 2025-06-22 23:09:20</p>

    <h2>Summary</h2>
    <ul>
        <li><strong>Total Files:</strong> 264</li>
        <li><strong>Total Directories:</strong> 42</li>
        <li><strong>Total Issues:</strong> 685</li>
        <li><strong>Max Complexity:</strong> 48</li>
    </ul>

    <h2>Files</h2>
    <table>
        <tr>
            <th>File</th>
            <th>Lines</th>
            <th>Issues</th>
            <th>Complexity</th>
        </tr>
        <tr>
            <td>__init__.py</td>
            <td>37</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>__main__.py</td>
            <td>11</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ai/__init__.py</td>
            <td>27</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ai/explanation/__init__.py</td>
            <td>16</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ai/explanation/comment_analyzer.py</td>
            <td>526</td>
            <td>8</td>
            <td>41</td>
        </tr>
        <tr>
            <td>ai/explanation/documentation_generator.py</td>
            <td>636</td>
            <td>5</td>
            <td>41</td>
        </tr>
        <tr>
            <td>ai/explanation/explanation_engine.py</td>
            <td>496</td>
            <td>6</td>
            <td>40</td>
        </tr>
        <tr>
            <td>ai/infrastructure/__init__.py</td>
            <td>24</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ai/infrastructure/model_manager.py</td>
            <td>517</td>
            <td>4</td>
            <td>39</td>
        </tr>
        <tr>
            <td>ai/infrastructure/model_optimizer.py</td>
            <td>503</td>
            <td>3</td>
            <td>39</td>
        </tr>
        <tr>
            <td>ai/infrastructure/privacy_processor.py</td>
            <td>422</td>
            <td>2</td>
            <td>33</td>
        </tr>
        <tr>
            <td>ai/refactoring/__init__.py</td>
            <td>26</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ai/refactoring/code_smell_detector.py</td>
            <td>575</td>
            <td>5</td>
            <td>39</td>
        </tr>
        <tr>
            <td>ai/refactoring/impact_analyzer.py</td>
            <td>648</td>
            <td>5</td>
            <td>39</td>
        </tr>
        <tr>
            <td>ai/refactoring/pattern_recommender.py</td>
            <td>621</td>
            <td>5</td>
            <td>38</td>
        </tr>
        <tr>
            <td>ai/refactoring/refactoring_engine.py</td>
            <td>603</td>
            <td>4</td>
            <td>39</td>
        </tr>
        <tr>
            <td>ai/temporal/__init__.py</td>
            <td>26</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ai/temporal/debt_predictor.py</td>
            <td>553</td>
            <td>4</td>
            <td>34</td>
        </tr>
        <tr>
            <td>ai/temporal/productivity_analyzer.py</td>
            <td>726</td>
            <td>5</td>
            <td>41</td>
        </tr>
        <tr>
            <td>ai/temporal/temporal_engine.py</td>
            <td>580</td>
            <td>7</td>
            <td>40</td>
        </tr>
        <tr>
            <td>ai/temporal/trend_visualizer.py</td>
            <td>640</td>
            <td>5</td>
            <td>38</td>
        </tr>
        <tr>
            <td>ai/visualization/__init__.py</td>
            <td>26</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ai/visualization/dashboard_engine.py</td>
            <td>778</td>
            <td>3</td>
            <td>36</td>
        </tr>
        <tr>
            <td>ai/visualization/data_aggregator.py</td>
            <td>552</td>
            <td>6</td>
            <td>42</td>
        </tr>
        <tr>
            <td>ai/visualization/interactive_charts.py</td>
            <td>641</td>
            <td>4</td>
            <td>34</td>
        </tr>
        <tr>
            <td>ai/visualization/report_generator.py</td>
            <td>712</td>
            <td>5</td>
            <td>40</td>
        </tr>
        <tr>
            <td>cli/__init__.py</td>
            <td>18</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>cli/commands.py</td>
            <td>692</td>
            <td>6</td>
            <td>43</td>
        </tr>
        <tr>
            <td>cli/completion.py</td>
            <td>217</td>
            <td>3</td>
            <td>28</td>
        </tr>
        <tr>
            <td>cli/error_handler.py</td>
            <td>198</td>
            <td>3</td>
            <td>34</td>
        </tr>
        <tr>
            <td>cli/format_tool.py</td>
            <td>144</td>
            <td>4</td>
            <td>33</td>
        </tr>
        <tr>
            <td>cli/formatters.py</td>
            <td>292</td>
            <td>4</td>
            <td>42</td>
        </tr>
        <tr>
            <td>cli/handlers.py</td>
            <td>301</td>
            <td>5</td>
            <td>38</td>
        </tr>
        <tr>
            <td>cli/knowledge_manager.py</td>
            <td>323</td>
            <td>3</td>
            <td>34</td>
        </tr>
        <tr>
            <td>cli/main.py</td>
            <td>286</td>
            <td>3</td>
            <td>31</td>
        </tr>
        <tr>
            <td>cli/output_formats.py</td>
            <td>290</td>
            <td>1</td>
            <td>23</td>
        </tr>
        <tr>
            <td>cli/parallel_processing.py</td>
            <td>375</td>
            <td>3</td>
            <td>34</td>
        </tr>
        <tr>
            <td>cli/standalone.py</td>
            <td>471</td>
            <td>6</td>
            <td>42</td>
        </tr>
        <tr>
            <td>cli/standalone_suite.py</td>
            <td>259</td>
            <td>4</td>
            <td>37</td>
        </tr>
        <tr>
            <td>cli/watch_mode.py</td>
            <td>260</td>
            <td>1</td>
            <td>34</td>
        </tr>
        <tr>
            <td>compat.py</td>
            <td>259</td>
            <td>2</td>
            <td>32</td>
        </tr>
        <tr>
            <td>core/__init__.py</td>
            <td>49</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/analysis/__init__.py</td>
            <td>23</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/analysis/dependency_analyzer.py</td>
            <td>697</td>
            <td>7</td>
            <td>43</td>
        </tr>
        <tr>
            <td>core/analysis/file_analyzer.py</td>
            <td>161</td>
            <td>2</td>
            <td>28</td>
        </tr>
        <tr>
            <td>core/analysis/framework_detector.py</td>
            <td>421</td>
            <td>9</td>
            <td>40</td>
        </tr>
        <tr>
            <td>core/analysis/framework_rules.py</td>
            <td>476</td>
            <td>2</td>
            <td>34</td>
        </tr>
        <tr>
            <td>core/analysis/import_analyzer.py</td>
            <td>495</td>
            <td>6</td>
            <td>39</td>
        </tr>
        <tr>
            <td>core/analysis/import_visualizer.py</td>
            <td>68</td>
            <td>1</td>
            <td>20</td>
        </tr>
        <tr>
            <td>core/analysis/meta_analyzer.py</td>
            <td>540</td>
            <td>2</td>
            <td>40</td>
        </tr>
        <tr>
            <td>core/analysis/metrics_aggregator.py</td>
            <td>161</td>
            <td>1</td>
            <td>27</td>
        </tr>
        <tr>
            <td>core/analysis/performance_optimizer.py</td>
            <td>436</td>
            <td>6</td>
            <td>37</td>
        </tr>
        <tr>
            <td>core/analysis/project_analyzer.py</td>
            <td>193</td>
            <td>2</td>
            <td>32</td>
        </tr>
        <tr>
            <td>core/analysis/project_meritocracy_analyzer.py</td>
            <td>520</td>
            <td>5</td>
            <td>40</td>
        </tr>
        <tr>
            <td>core/analysis/python_semantic_analyzer.py</td>
            <td>499</td>
            <td>10</td>
            <td>40</td>
        </tr>
        <tr>
            <td>core/analysis/python_version_analyzer.py</td>
            <td>375</td>
            <td>9</td>
            <td>33</td>
        </tr>
        <tr>
            <td>core/analysis/result_processor.py</td>
            <td>165</td>
            <td>1</td>
            <td>33</td>
        </tr>
        <tr>
            <td>core/analysis/semantic_output_formatter.py</td>
            <td>409</td>
            <td>1</td>
            <td>36</td>
        </tr>
        <tr>
            <td>core/analysis/semantic_rules.py</td>
            <td>453</td>
            <td>1</td>
            <td>29</td>
        </tr>
        <tr>
            <td>core/analysis/standalone_analyzer.py</td>
            <td>421</td>
            <td>1</td>
            <td>38</td>
        </tr>
        <tr>
            <td>core/analysis/tool_executor.py</td>
            <td>140</td>
            <td>1</td>
            <td>27</td>
        </tr>
        <tr>
            <td>core/analysis/type_analyzer.py</td>
            <td>370</td>
            <td>7</td>
            <td>39</td>
        </tr>
        <tr>
            <td>core/analysis/visualization/__init__.py</td>
            <td>47</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/analysis/visualization/chart_generators.py</td>
            <td>257</td>
            <td>1</td>
            <td>30</td>
        </tr>
        <tr>
            <td>core/analysis/visualization/html_generators.py</td>
            <td>240</td>
            <td>1</td>
            <td>28</td>
        </tr>
        <tr>
            <td>core/analysis/visualization/interactive_dashboard.py</td>
            <td>234</td>
            <td>1</td>
            <td>25</td>
        </tr>
        <tr>
            <td>core/compatibility.py</td>
            <td>81</td>
            <td>1</td>
            <td>15</td>
        </tr>
        <tr>
            <td>core/config.py</td>
            <td>229</td>
            <td>2</td>
            <td>34</td>
        </tr>
        <tr>
            <td>core/dependency_manager.py</td>
            <td>358</td>
            <td>5</td>
            <td>33</td>
        </tr>
        <tr>
            <td>core/docs/__init__.py</td>
            <td>48</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/docs/templates.py</td>
            <td>97</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/docs/utils.py</td>
            <td>349</td>
            <td>6</td>
            <td>34</td>
        </tr>
        <tr>
            <td>core/error_handling.py</td>
            <td>320</td>
            <td>2</td>
            <td>32</td>
        </tr>
        <tr>
            <td>core/error_handling/__init__.py</td>
            <td>55</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/error_handling/decorators.py</td>
            <td>126</td>
            <td>1</td>
            <td>23</td>
        </tr>
        <tr>
            <td>core/error_handling/error_manager.py</td>
            <td>115</td>
            <td>1</td>
            <td>25</td>
        </tr>
        <tr>
            <td>core/error_handling/exceptions.py</td>
            <td>193</td>
            <td>1</td>
            <td>13</td>
        </tr>
        <tr>
            <td>core/error_handling/handlers.py</td>
            <td>185</td>
            <td>1</td>
            <td>28</td>
        </tr>
        <tr>
            <td>core/fs_utils.py</td>
            <td>285</td>
            <td>2</td>
            <td>36</td>
        </tr>
        <tr>
            <td>core/knowledge/framework_knowledge_base.py</td>
            <td>626</td>
            <td>3</td>
            <td>35</td>
        </tr>
        <tr>
            <td>core/knowledge/rule_engine.py</td>
            <td>396</td>
            <td>7</td>
            <td>41</td>
        </tr>
        <tr>
            <td>core/logging.py</td>
            <td>261</td>
            <td>1</td>
            <td>29</td>
        </tr>
        <tr>
            <td>core/logging/__init__.py</td>
            <td>49</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/logging/contextual_logger.py</td>
            <td>134</td>
            <td>1</td>
            <td>20</td>
        </tr>
        <tr>
            <td>core/logging/correlation.py</td>
            <td>214</td>
            <td>1</td>
            <td>26</td>
        </tr>
        <tr>
            <td>core/logging/setup.py</td>
            <td>94</td>
            <td>1</td>
            <td>22</td>
        </tr>
        <tr>
            <td>core/logging/structured_logger.py</td>
            <td>220</td>
            <td>2</td>
            <td>25</td>
        </tr>
        <tr>
            <td>core/models/__init__.py</td>
            <td>23</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/models/directory_metrics.py</td>
            <td>286</td>
            <td>1</td>
            <td>31</td>
        </tr>
        <tr>
            <td>core/models/file_metrics.py</td>
            <td>400</td>
            <td>3</td>
            <td>33</td>
        </tr>
        <tr>
            <td>core/models/progress_tracker.py</td>
            <td>566</td>
            <td>4</td>
            <td>29</td>
        </tr>
        <tr>
            <td>core/models/project_metrics.py</td>
            <td>450</td>
            <td>3</td>
            <td>38</td>
        </tr>
        <tr>
            <td>core/progress.py</td>
            <td>997</td>
            <td>2</td>
            <td>35</td>
        </tr>
        <tr>
            <td>core/simple_analyzer.py</td>
            <td>144</td>
            <td>2</td>
            <td>28</td>
        </tr>
        <tr>
            <td>core/trend_analysis/__init__.py</td>
            <td>16</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/trend_analysis/trend_analyzer.py</td>
            <td>340</td>
            <td>2</td>
            <td>36</td>
        </tr>
        <tr>
            <td>core/trend_analysis/trend_storage.py</td>
            <td>196</td>
            <td>1</td>
            <td>26</td>
        </tr>
        <tr>
            <td>core/trend_analysis/trend_visualizer.py</td>
            <td>476</td>
            <td>3</td>
            <td>31</td>
        </tr>
        <tr>
            <td>core/utils/__init__.py</td>
            <td>99</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/utils/async_utils.py</td>
            <td>264</td>
            <td>1</td>
            <td>31</td>
        </tr>
        <tr>
            <td>core/utils/config_utils.py</td>
            <td>139</td>
            <td>1</td>
            <td>25</td>
        </tr>
        <tr>
            <td>core/utils/dict_utils.py</td>
            <td>211</td>
            <td>1</td>
            <td>29</td>
        </tr>
        <tr>
            <td>core/utils/error_handling.py</td>
            <td>127</td>
            <td>1</td>
            <td>19</td>
        </tr>
        <tr>
            <td>core/utils/error_utils.py</td>
            <td>309</td>
            <td>1</td>
            <td>28</td>
        </tr>
        <tr>
            <td>core/utils/file_utils.py</td>
            <td>175</td>
            <td>3</td>
            <td>31</td>
        </tr>
        <tr>
            <td>core/utils/fs_utils.py</td>
            <td>392</td>
            <td>3</td>
            <td>39</td>
        </tr>
        <tr>
            <td>core/utils/gitignore_utils.py</td>
            <td>75</td>
            <td>1</td>
            <td>24</td>
        </tr>
        <tr>
            <td>core/utils/preset_manager.py</td>
            <td>113</td>
            <td>1</td>
            <td>21</td>
        </tr>
        <tr>
            <td>core/utils/report_utils.py</td>
            <td>239</td>
            <td>1</td>
            <td>27</td>
        </tr>
        <tr>
            <td>core/utils/tool_utils.py</td>
            <td>145</td>
            <td>1</td>
            <td>29</td>
        </tr>
        <tr>
            <td>core/vcs/__init__.py</td>
            <td>40</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/vcs/auto_fix.py</td>
            <td>345</td>
            <td>2</td>
            <td>39</td>
        </tr>
        <tr>
            <td>core/vcs/cache.py</td>
            <td>423</td>
            <td>3</td>
            <td>38</td>
        </tr>
        <tr>
            <td>core/vcs/config.py</td>
            <td>283</td>
            <td>4</td>
            <td>36</td>
        </tr>
        <tr>
            <td>core/vcs/dependency_tracker.py</td>
            <td>382</td>
            <td>1</td>
            <td>39</td>
        </tr>
        <tr>
            <td>core/vcs/engine.py</td>
            <td>1103</td>
            <td>5</td>
            <td>48</td>
        </tr>
        <tr>
            <td>core/vcs/incremental_analysis.py</td>
            <td>332</td>
            <td>2</td>
            <td>38</td>
        </tr>
        <tr>
            <td>core/vcs/integration/__init__.py</td>
            <td>18</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/vcs/integration/ecosystem_integrator.py</td>
            <td>39</td>
            <td>1</td>
            <td>16</td>
        </tr>
        <tr>
            <td>core/vcs/integration/integration_manager.py</td>
            <td>311</td>
            <td>3</td>
            <td>33</td>
        </tr>
        <tr>
            <td>core/vcs/integration/meta_analyzer.py</td>
            <td>308</td>
            <td>2</td>
            <td>34</td>
        </tr>
        <tr>
            <td>core/vcs/integration/unified_reporter.py</td>
            <td>417</td>
            <td>2</td>
            <td>34</td>
        </tr>
        <tr>
            <td>core/vcs/memory_manager.py</td>
            <td>355</td>
            <td>2</td>
            <td>35</td>
        </tr>
        <tr>
            <td>core/vcs/models.py</td>
            <td>246</td>
            <td>4</td>
            <td>27</td>
        </tr>
        <tr>
            <td>core/vcs/performance.py</td>
            <td>410</td>
            <td>3</td>
            <td>35</td>
        </tr>
        <tr>
            <td>core/vcs/plugins/__init__.py</td>
            <td>20</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/vcs/plugins/plugin_interface.py</td>
            <td>306</td>
            <td>1</td>
            <td>29</td>
        </tr>
        <tr>
            <td>core/vcs/plugins/plugin_loader.py</td>
            <td>264</td>
            <td>1</td>
            <td>37</td>
        </tr>
        <tr>
            <td>core/vcs/plugins/plugin_manager.py</td>
            <td>280</td>
            <td>1</td>
            <td>37</td>
        </tr>
        <tr>
            <td>core/vcs/plugins/plugin_registry.py</td>
            <td>301</td>
            <td>2</td>
            <td>38</td>
        </tr>
        <tr>
            <td>core/vcs/registry.py</td>
            <td>362</td>
            <td>3</td>
            <td>35</td>
        </tr>
        <tr>
            <td>core/vcs/rules/__init__.py</td>
            <td>19</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/vcs/rules/advanced_python_rules.py</td>
            <td>412</td>
            <td>1</td>
            <td>38</td>
        </tr>
        <tr>
            <td>core/vcs/rules/complexity_rules.py</td>
            <td>350</td>
            <td>24</td>
            <td>31</td>
        </tr>
        <tr>
            <td>core/vcs/rules/documentation_rules.py</td>
            <td>337</td>
            <td>10</td>
            <td>32</td>
        </tr>
        <tr>
            <td>core/vcs/rules/framework_rules/__init__.py</td>
            <td>19</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>core/vcs/rules/framework_rules/django_rules.py</td>
            <td>326</td>
            <td>2</td>
            <td>37</td>
        </tr>
        <tr>
            <td>core/vcs/rules/framework_rules/fastapi_rules.py</td>
            <td>389</td>
            <td>1</td>
            <td>38</td>
        </tr>
        <tr>
            <td>core/vcs/rules/framework_rules/flask_rules.py</td>
            <td>351</td>
            <td>4</td>
            <td>31</td>
        </tr>
        <tr>
            <td>core/vcs/rules/framework_rules/framework_detector.py</td>
            <td>322</td>
            <td>3</td>
            <td>38</td>
        </tr>
        <tr>
            <td>core/vcs/rules/import_rules.py</td>
            <td>382</td>
            <td>13</td>
            <td>28</td>
        </tr>
        <tr>
            <td>core/vcs/rules/performance_rules.py</td>
            <td>400</td>
            <td>2</td>
            <td>39</td>
        </tr>
        <tr>
            <td>core/vcs/rules/rule_loader.py</td>
            <td>445</td>
            <td>2</td>
            <td>34</td>
        </tr>
        <tr>
            <td>core/vcs/rules/security_rules.py</td>
            <td>298</td>
            <td>7</td>
            <td>27</td>
        </tr>
        <tr>
            <td>core/vcs/rules/style_rules.py</td>
            <td>300</td>
            <td>5</td>
            <td>31</td>
        </tr>
        <tr>
            <td>core/vcs/rules/type_rules.py</td>
            <td>487</td>
            <td>15</td>
            <td>33</td>
        </tr>
        <tr>
            <td>core/vcs/type_checking.py</td>
            <td>335</td>
            <td>4</td>
            <td>39</td>
        </tr>
        <tr>
            <td>core/version.py</td>
            <td>8</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/__init__.py</td>
            <td>25</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/api/__init__.py</td>
            <td>30</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/api/graphql.py</td>
            <td>523</td>
            <td>2</td>
            <td>38</td>
        </tr>
        <tr>
            <td>enterprise/api/models.py</td>
            <td>289</td>
            <td>4</td>
            <td>13</td>
        </tr>
        <tr>
            <td>enterprise/api/rest.py</td>
            <td>500</td>
            <td>4</td>
            <td>37</td>
        </tr>
        <tr>
            <td>enterprise/api/unified.py</td>
            <td>390</td>
            <td>1</td>
            <td>37</td>
        </tr>
        <tr>
            <td>enterprise/api/websocket.py</td>
            <td>369</td>
            <td>2</td>
            <td>35</td>
        </tr>
        <tr>
            <td>enterprise/cicd/__init__.py</td>
            <td>31</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/cicd/azure_devops.py</td>
            <td>549</td>
            <td>1</td>
            <td>32</td>
        </tr>
        <tr>
            <td>enterprise/cicd/github_actions.py</td>
            <td>368</td>
            <td>1</td>
            <td>31</td>
        </tr>
        <tr>
            <td>enterprise/cicd/gitlab_ci.py</td>
            <td>436</td>
            <td>1</td>
            <td>32</td>
        </tr>
        <tr>
            <td>enterprise/cicd/jenkins.py</td>
            <td>413</td>
            <td>2</td>
            <td>32</td>
        </tr>
        <tr>
            <td>enterprise/cicd/manager.py</td>
            <td>409</td>
            <td>1</td>
            <td>37</td>
        </tr>
        <tr>
            <td>enterprise/cicd/models.py</td>
            <td>274</td>
            <td>5</td>
            <td>18</td>
        </tr>
        <tr>
            <td>enterprise/collaboration.py</td>
            <td>875</td>
            <td>5</td>
            <td>43</td>
        </tr>
        <tr>
            <td>enterprise/collaboration/__init__.py</td>
            <td>31</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/collaboration/analysis.py</td>
            <td>457</td>
            <td>5</td>
            <td>32</td>
        </tr>
        <tr>
            <td>enterprise/collaboration/shared_config.py</td>
            <td>347</td>
            <td>2</td>
            <td>33</td>
        </tr>
        <tr>
            <td>enterprise/collaboration/team_manager.py</td>
            <td>365</td>
            <td>3</td>
            <td>36</td>
        </tr>
        <tr>
            <td>enterprise/dashboard/__init__.py</td>
            <td>32</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/dashboard/components.py</td>
            <td>504</td>
            <td>2</td>
            <td>23</td>
        </tr>
        <tr>
            <td>enterprise/dashboard/models.py</td>
            <td>259</td>
            <td>4</td>
            <td>6</td>
        </tr>
        <tr>
            <td>enterprise/dashboard/static_assets.py</td>
            <td>487</td>
            <td>1</td>
            <td>22</td>
        </tr>
        <tr>
            <td>enterprise/dashboard/templates.py</td>
            <td>778</td>
            <td>1</td>
            <td>29</td>
        </tr>
        <tr>
            <td>enterprise/dashboard/web_server.py</td>
            <td>519</td>
            <td>2</td>
            <td>38</td>
        </tr>
        <tr>
            <td>enterprise/integration.py</td>
            <td>30</td>
            <td>1</td>
            <td>12</td>
        </tr>
        <tr>
            <td>enterprise/monitoring.py</td>
            <td>62</td>
            <td>1</td>
            <td>18</td>
        </tr>
        <tr>
            <td>enterprise/monitoring/__init__.py</td>
            <td>35</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/monitoring/alerting.py</td>
            <td>536</td>
            <td>2</td>
            <td>37</td>
        </tr>
        <tr>
            <td>enterprise/monitoring/dashboards.py</td>
            <td>530</td>
            <td>3</td>
            <td>40</td>
        </tr>
        <tr>
            <td>enterprise/monitoring/models.py</td>
            <td>300</td>
            <td>6</td>
            <td>23</td>
        </tr>
        <tr>
            <td>enterprise/monitoring/monitoring.py</td>
            <td>486</td>
            <td>3</td>
            <td>39</td>
        </tr>
        <tr>
            <td>enterprise/monitoring/quality_gates.py</td>
            <td>515</td>
            <td>3</td>
            <td>35</td>
        </tr>
        <tr>
            <td>enterprise/performance/__init__.py</td>
            <td>30</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/performance/cache_manager.py</td>
            <td>404</td>
            <td>4</td>
            <td>38</td>
        </tr>
        <tr>
            <td>enterprise/performance/distributed_processor.py</td>
            <td>450</td>
            <td>4</td>
            <td>37</td>
        </tr>
        <tr>
            <td>enterprise/performance/load_balancer.py</td>
            <td>59</td>
            <td>2</td>
            <td>16</td>
        </tr>
        <tr>
            <td>enterprise/performance/performance_optimizer.py</td>
            <td>376</td>
            <td>5</td>
            <td>39</td>
        </tr>
        <tr>
            <td>enterprise/performance/scalability_manager.py</td>
            <td>71</td>
            <td>1</td>
            <td>18</td>
        </tr>
        <tr>
            <td>enterprise/reporting/__init__.py</td>
            <td>30</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/reporting/customization.py</td>
            <td>109</td>
            <td>1</td>
            <td>25</td>
        </tr>
        <tr>
            <td>enterprise/reporting/engine.py</td>
            <td>448</td>
            <td>3</td>
            <td>34</td>
        </tr>
        <tr>
            <td>enterprise/reporting/executive.py</td>
            <td>434</td>
            <td>4</td>
            <td>35</td>
        </tr>
        <tr>
            <td>enterprise/reporting/formats.py</td>
            <td>448</td>
            <td>1</td>
            <td>29</td>
        </tr>
        <tr>
            <td>enterprise/reporting/templates.py</td>
            <td>391</td>
            <td>2</td>
            <td>31</td>
        </tr>
        <tr>
            <td>enterprise/security/__init__.py</td>
            <td>36</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>enterprise/security/access_control.py</td>
            <td>475</td>
            <td>2</td>
            <td>38</td>
        </tr>
        <tr>
            <td>enterprise/security/audit_trail.py</td>
            <td>470</td>
            <td>9</td>
            <td>31</td>
        </tr>
        <tr>
            <td>enterprise/security/compliance_manager.py</td>
            <td>409</td>
            <td>4</td>
            <td>31</td>
        </tr>
        <tr>
            <td>enterprise/security/encryption_manager.py</td>
            <td>103</td>
            <td>2</td>
            <td>23</td>
        </tr>
        <tr>
            <td>enterprise/security/security_monitor.py</td>
            <td>336</td>
            <td>4</td>
            <td>32</td>
        </tr>
        <tr>
            <td>plugins/__init__.py</td>
            <td>26</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>plugins/base_plugin.py</td>
            <td>17</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>plugins/manager.py</td>
            <td>536</td>
            <td>6</td>
            <td>42</td>
        </tr>
        <tr>
            <td>plugins/plugin_base.py</td>
            <td>246</td>
            <td>1</td>
            <td>25</td>
        </tr>
        <tr>
            <td>plugins/plugin_interface.py</td>
            <td>194</td>
            <td>1</td>
            <td>23</td>
        </tr>
        <tr>
            <td>plugins/plugin_registry.py</td>
            <td>256</td>
            <td>1</td>
            <td>30</td>
        </tr>
        <tr>
            <td>tools/custom_rules/__init__.py</td>
            <td>12</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>tools/custom_rules/python_rules.py</td>
            <td>328</td>
            <td>3</td>
            <td>36</td>
        </tr>
        <tr>
            <td>tools/parsers/__init__.py</td>
            <td>35</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>tools/parsers/bandit_parser.py</td>
            <td>185</td>
            <td>1</td>
            <td>24</td>
        </tr>
        <tr>
            <td>tools/parsers/base_parser.py</td>
            <td>81</td>
            <td>1</td>
            <td>20</td>
        </tr>
        <tr>
            <td>tools/parsers/complexity_parser.py</td>
            <td>191</td>
            <td>2</td>
            <td>26</td>
        </tr>
        <tr>
            <td>tools/parsers/custom_rules_parser.py</td>
            <td>158</td>
            <td>1</td>
            <td>22</td>
        </tr>
        <tr>
            <td>tools/parsers/doc_analyzer_parser.py</td>
            <td>259</td>
            <td>2</td>
            <td>33</td>
        </tr>
        <tr>
            <td>tools/parsers/mypy_parser.py</td>
            <td>171</td>
            <td>1</td>
            <td>23</td>
        </tr>
        <tr>
            <td>tools/parsers/parser_registry.py</td>
            <td>64</td>
            <td>1</td>
            <td>14</td>
        </tr>
        <tr>
            <td>tools/parsers/pyflakes_parser.py</td>
            <td>158</td>
            <td>1</td>
            <td>25</td>
        </tr>
        <tr>
            <td>tools/parsers/pylint_parser.py</td>
            <td>151</td>
            <td>1</td>
            <td>22</td>
        </tr>
        <tr>
            <td>tools/parsers/ruff_parser.py</td>
            <td>187</td>
            <td>1</td>
            <td>21</td>
        </tr>
        <tr>
            <td>tools/runners/__init__.py</td>
            <td>38</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>tools/runners/bandit_runner.py</td>
            <td>167</td>
            <td>2</td>
            <td>27</td>
        </tr>
        <tr>
            <td>tools/runners/base_runner.py</td>
            <td>215</td>
            <td>4</td>
            <td>30</td>
        </tr>
        <tr>
            <td>tools/runners/complexity_runner.py</td>
            <td>362</td>
            <td>12</td>
            <td>30</td>
        </tr>
        <tr>
            <td>tools/runners/custom_rules_runner.py</td>
            <td>101</td>
            <td>1</td>
            <td>19</td>
        </tr>
        <tr>
            <td>tools/runners/doc_analyzer_runner.py</td>
            <td>345</td>
            <td>6</td>
            <td>35</td>
        </tr>
        <tr>
            <td>tools/runners/mypy_runner.py</td>
            <td>226</td>
            <td>2</td>
            <td>33</td>
        </tr>
        <tr>
            <td>tools/runners/pyflakes_runner.py</td>
            <td>135</td>
            <td>1</td>
            <td>26</td>
        </tr>
        <tr>
            <td>tools/runners/pylint_runner.py</td>
            <td>204</td>
            <td>1</td>
            <td>30</td>
        </tr>
        <tr>
            <td>tools/runners/ruff_runner.py</td>
            <td>209</td>
            <td>3</td>
            <td>30</td>
        </tr>
        <tr>
            <td>tools/runners/tool_registry.py</td>
            <td>119</td>
            <td>1</td>
            <td>22</td>
        </tr>
        <tr>
            <td>ui/cli/__init__.py</td>
            <td>20</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ui/cli/commands.py</td>
            <td>142</td>
            <td>1</td>
            <td>23</td>
        </tr>
        <tr>
            <td>ui/cli/formatter.py</td>
            <td>258</td>
            <td>1</td>
            <td>29</td>
        </tr>
        <tr>
            <td>ui/gui/__init__.py</td>
            <td>22</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ui/gui/app.py</td>
            <td>67</td>
            <td>1</td>
            <td>15</td>
        </tr>
        <tr>
            <td>ui/gui/main_window.py</td>
            <td>364</td>
            <td>2</td>
            <td>33</td>
        </tr>
        <tr>
            <td>ui/gui/simple_gui.py</td>
            <td>463</td>
            <td>3</td>
            <td>33</td>
        </tr>
        <tr>
            <td>ui/gui/themes.py</td>
            <td>325</td>
            <td>1</td>
            <td>23</td>
        </tr>
        <tr>
            <td>ui/reporting/__init__.py</td>
            <td>34</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ui/reporting/custom_report_generator.py</td>
            <td>185</td>
            <td>2</td>
            <td>28</td>
        </tr>
        <tr>
            <td>ui/reporting/formatters.py</td>
            <td>159</td>
            <td>2</td>
            <td>28</td>
        </tr>
        <tr>
            <td>ui/reporting/generators.py</td>
            <td>192</td>
            <td>2</td>
            <td>28</td>
        </tr>
        <tr>
            <td>ui/reporting/markdown.py</td>
            <td>141</td>
            <td>1</td>
            <td>23</td>
        </tr>
        <tr>
            <td>ui/reporting/report_generator.py</td>
            <td>577</td>
            <td>1</td>
            <td>22</td>
        </tr>
        <tr>
            <td>ui/reporting/templates.py</td>
            <td>99</td>
            <td>1</td>
            <td>23</td>
        </tr>
        <tr>
            <td>ui/tui/__init__.py</td>
            <td>22</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ui/tui/app.py</td>
            <td>256</td>
            <td>4</td>
            <td>35</td>
        </tr>
        <tr>
            <td>ui/tui/components.py</td>
            <td>70</td>
            <td>1</td>
            <td>21</td>
        </tr>
        <tr>
            <td>ui/tui/config_components.py</td>
            <td>107</td>
            <td>1</td>
            <td>20</td>
        </tr>
        <tr>
            <td>ui/tui/header_footer.py</td>
            <td>78</td>
            <td>1</td>
            <td>20</td>
        </tr>
        <tr>
            <td>ui/tui/menu_components.py</td>
            <td>102</td>
            <td>1</td>
            <td>20</td>
        </tr>
        <tr>
            <td>ui/tui/progress_components.py</td>
            <td>105</td>
            <td>1</td>
            <td>24</td>
        </tr>
        <tr>
            <td>ui/tui/results_components.py</td>
            <td>309</td>
            <td>2</td>
            <td>34</td>
        </tr>
        <tr>
            <td>ui/tui/state_manager.py</td>
            <td>269</td>
            <td>3</td>
            <td>30</td>
        </tr>
        <tr>
            <td>ui/visualization/__init__.py</td>
            <td>51</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ui/visualization/charts.py</td>
            <td>284</td>
            <td>1</td>
            <td>21</td>
        </tr>
        <tr>
            <td>ui/visualization/exporters.py</td>
            <td>135</td>
            <td>1</td>
            <td>18</td>
        </tr>
        <tr>
            <td>ui/visualization/generators.py</td>
            <td>338</td>
            <td>1</td>
            <td>24</td>
        </tr>
        <tr>
            <td>ui/visualization/interactive_charts.py</td>
            <td>1080</td>
            <td>2</td>
            <td>39</td>
        </tr>
        <tr>
            <td>ui/visualization/visualization_generator.py</td>
            <td>282</td>
            <td>1</td>
            <td>27</td>
        </tr>
        <tr>
            <td>ui/web/__init__.py</td>
            <td>23</td>
            <td>1</td>
            <td>0</td>
        </tr>
        <tr>
            <td>ui/web/app.py</td>
            <td>218</td>
            <td>2</td>
            <td>30</td>
        </tr>
        <tr>
            <td>ui/web/components.py</td>
            <td>631</td>
            <td>3</td>
            <td>38</td>
        </tr>
        <tr>
            <td>ui/web/run_web_ui.py</td>
            <td>84</td>
            <td>1</td>
            <td>6</td>
        </tr>
        <tr>
            <td>ui/web/state_manager.py</td>
            <td>265</td>
            <td>3</td>
            <td>29</td>
        </tr>
    </table>

    <h2>Issues</h2>
    <h3>__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>__main__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/explanation/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/explanation/comment_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'CommentType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'CommentQuality' has only data, no behavior</td>
        </tr>
        <tr>
            <td>107</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'extract_comments' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>253</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_calculate_quality_score' has complexity 16, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>306</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_identify_issues' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>230</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
        <tr>
            <td>392</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/explanation/documentation_generator.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DocumentationFormat' has only data, no behavior</td>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DocumentationStyle' has only data, no behavior</td>
        </tr>
        <tr>
            <td>152</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_code_elements' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>324</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/explanation/explanation_engine.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ExplanationLevel' has only data, no behavior</td>
        </tr>
        <tr>
            <td>29</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ExplanationType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>86</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_code_structure' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>140</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'detect_patterns' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>230</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/infrastructure/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/infrastructure/model_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ModelType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>31</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ModelStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>510</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/infrastructure/model_optimizer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'OptimizationStrategy' has only data, no behavior</td>
        </tr>
        <tr>
            <td>300</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'optimize_model_for_target' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/infrastructure/privacy_processor.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'PrivacyLevel' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/refactoring/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/refactoring/code_smell_detector.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'CodeSmell' has only data, no behavior</td>
        </tr>
        <tr>
            <td>44</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'SmellSeverity' has only data, no behavior</td>
        </tr>
        <tr>
            <td>363</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'detect_smells' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>375</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/refactoring/impact_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ImpactLevel' has only data, no behavior</td>
        </tr>
        <tr>
            <td>28</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ImpactType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>112</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_dependencies' has complexity 17, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>405</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/refactoring/pattern_recommender.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DesignPattern' has only data, no behavior</td>
        </tr>
        <tr>
            <td>44</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'PatternCategory' has only data, no behavior</td>
        </tr>
        <tr>
            <td>429</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_patterns' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>441</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/refactoring/refactoring_engine.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'RefactoringType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>37</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'RefactoringSeverity' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>281</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/temporal/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/temporal/debt_predictor.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DebtCategory' has only data, no behavior</td>
        </tr>
        <tr>
            <td>32</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DebtSeverity' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>426</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/temporal/productivity_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ProductivityMetric' has only data, no behavior</td>
        </tr>
        <tr>
            <td>34</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DeveloperInsight' has only data, no behavior</td>
        </tr>
        <tr>
            <td>392</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_generate_trend_insights' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>466</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/temporal/temporal_engine.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'EvolutionMetric' has only data, no behavior</td>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AnalysisTimeframe' has only data, no behavior</td>
        </tr>
        <tr>
            <td>457</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_generate_insights' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>494</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_generate_recommendations' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>137</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
        <tr>
            <td>397</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/temporal/trend_visualizer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'VisualizationType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>34</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'TrendDirection' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>298</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
        <tr>
            <td>367</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/visualization/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/visualization/dashboard_engine.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DashboardType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>33</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'LayoutType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/visualization/data_aggregator.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AggregationType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>273</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_aggregate_simple' has complexity 20, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>448</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_time_series_aggregation' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>448</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_time_series_aggregation' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>216</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>ai/visualization/interactive_charts.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ChartInteraction' has only data, no behavior</td>
        </tr>
        <tr>
            <td>34</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AnimationType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>345</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_interactive_chart' has 8 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ai/visualization/report_generator.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ReportFormat' has only data, no behavior</td>
        </tr>
        <tr>
            <td>32</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ReportStyle' has only data, no behavior</td>
        </tr>
        <tr>
            <td>444</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'generate_report' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>470</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>cli/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/commands.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_command' has complexity 50, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_command' has 10 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>188</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'run_vcs_analysis' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>336</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>336</td>
            <td>B110</td>
            <td class="severity-low">LOW</td>
            <td>Try, Except, Pass detected.</td>
        </tr>
    </table>
    <h3>cli/completion.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>46</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>46</td>
            <td>B110</td>
            <td class="severity-low">LOW</td>
            <td>Try, Except, Pass detected.</td>
        </tr>
    </table>
    <h3>cli/error_handler.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>17</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'format_error_results' has complexity 17, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>98</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'handle_analysis_error' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/format_tool.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'vibe_format' has complexity 25, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'vibe_format' has 8 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>39</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'run_format' has complexity 23, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/formatters.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>97</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_format_line_count' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>150</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_format_issue_count' has complexity 16, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>260</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_format_generated_reports' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/handlers.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'handle_analyze_command' has complexity 23, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'handle_analyze_command' has 18 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>235</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'handle_debug_command' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>235</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'handle_debug_command' has 7 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/knowledge_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>136</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'add_rule' has 7 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>275</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'validate' has complexity 14, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/main.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>46</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has 18 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>205</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'debug' has 7 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/output_formats.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/parallel_processing.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_files_parallel' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>302</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_files_parallel' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/standalone.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>128</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'check' has complexity 15, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>128</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'check' has 8 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>131</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'run_check' has complexity 15, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>227</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'fix' has complexity 20, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>229</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'run_fix' has complexity 20, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/standalone_suite.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>43</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'vibe_check_standalone' has complexity 40, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>43</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'vibe_check_standalone' has 12 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>48</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'run_analysis' has complexity 40, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>cli/watch_mode.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>compat.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>135</td>
            <td>B108</td>
            <td class="severity-medium">MEDIUM</td>
            <td>Probable insecure usage of temp file/directory.</td>
        </tr>
    </table>
    <h3>core/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/dependency_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ImportInfo' has only data, no behavior</td>
        </tr>
        <tr>
            <td>32</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DependencyNode' has only data, no behavior</td>
        </tr>
        <tr>
            <td>43</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'CircularDependency' has only data, no behavior</td>
        </tr>
        <tr>
            <td>53</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DependencyAnalysisResult' has only data, no behavior</td>
        </tr>
        <tr>
            <td>71</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Import' should use snake_case naming</td>
        </tr>
        <tr>
            <td>83</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/file_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>33</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '__init__' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/framework_detector.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'FrameworkSignature' has only data, no behavior</td>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'FrameworkDetection' has only data, no behavior</td>
        </tr>
        <tr>
            <td>47</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'FrameworkAnalysisResult' has only data, no behavior</td>
        </tr>
        <tr>
            <td>221</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_evaluate_framework' has complexity 23, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>372</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Import' should use snake_case naming</td>
        </tr>
        <tr>
            <td>378</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>387</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>398</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/framework_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>29</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'check' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/import_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ImportInfo' has only data, no behavior</td>
        </tr>
        <tr>
            <td>42</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'CircularDependency' has only data, no behavior</td>
        </tr>
        <tr>
            <td>51</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ImportAnalysisResult' has only data, no behavior</td>
        </tr>
        <tr>
            <td>256</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>256</td>
            <td>B110</td>
            <td class="severity-low">LOW</td>
            <td>Try, Except, Pass detected.</td>
        </tr>
    </table>
    <h3>core/analysis/import_visualizer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/meta_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>25</td>
            <td></td>
            <td class="">error</td>
            <td>God class detected: 'MetaAnalyzer' has 21 methods</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/metrics_aggregator.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/performance_optimizer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>23</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AnalysisCache' has only data, no behavior</td>
        </tr>
        <tr>
            <td>34</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'PerformanceMetrics' has only data, no behavior</td>
        </tr>
        <tr>
            <td>79</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'cache_result' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>11</td>
            <td>B403</td>
            <td class="severity-low">LOW</td>
            <td>Consider possible security implications associated with pickle module.</td>
        </tr>
        <tr>
            <td>103</td>
            <td>B301</td>
            <td class="severity-medium">MEDIUM</td>
            <td>Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.</td>
        </tr>
    </table>
    <h3>core/analysis/project_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>81</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze_project' has complexity 16, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/project_meritocracy_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ArchitecturalPattern' has only data, no behavior</td>
        </tr>
        <tr>
            <td>32</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'QualityIndicator' has only data, no behavior</td>
        </tr>
        <tr>
            <td>43</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ProjectMeritocracyResult' has only data, no behavior</td>
        </tr>
        <tr>
            <td>424</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_analyze_success_factors' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/python_semantic_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>18</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'SemanticContext' has only data, no behavior</td>
        </tr>
        <tr>
            <td>32</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'SemanticIssue' has only data, no behavior</td>
        </tr>
        <tr>
            <td>44</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'SemanticAnalysisResult' has only data, no behavior</td>
        </tr>
        <tr>
            <td>384</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Import' should use snake_case naming</td>
        </tr>
        <tr>
            <td>397</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>412</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>438</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>464</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>490</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Name' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/python_version_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'VersionFeature' has only data, no behavior</td>
        </tr>
        <tr>
            <td>31</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'CompatibilityIssue' has only data, no behavior</td>
        </tr>
        <tr>
            <td>42</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'VersionCompatibilityResult' has only data, no behavior</td>
        </tr>
        <tr>
            <td>152</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_NamedExpr' should use snake_case naming</td>
        </tr>
        <tr>
            <td>164</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>177</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_BinOp' should use snake_case naming</td>
        </tr>
        <tr>
            <td>187</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Subscript' should use snake_case naming</td>
        </tr>
        <tr>
            <td>203</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Match' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/result_processor.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/semantic_output_formatter.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/semantic_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/standalone_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/tool_executor.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/type_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'TypeInfo' has only data, no behavior</td>
        </tr>
        <tr>
            <td>101</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>126</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>149</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_analyze_function_args' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>188</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_all_parameters_typed' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>307</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'check' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/visualization/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/visualization/chart_generators.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/visualization/html_generators.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/analysis/visualization/interactive_dashboard.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/compatibility.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/config.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>194</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'validate_config' has complexity 15, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/dependency_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DependencyStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DependencyInfo' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>9</td>
            <td>B404</td>
            <td class="severity-low">LOW</td>
            <td>Consider possible security implications associated with the subprocess module.</td>
        </tr>
        <tr>
            <td>225</td>
            <td>B603</td>
            <td class="severity-low">LOW</td>
            <td>subprocess call - check for execution of untrusted input.</td>
        </tr>
    </table>
    <h3>core/docs/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/docs/templates.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/docs/utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>104</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'generate_function_docstring' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>104</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'generate_function_docstring' has 7 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>175</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'generate_async_function_docstring' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>175</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'generate_async_function_docstring' has 7 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>311</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'generate_file_header' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/error_handling.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>41</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ConfigurationError' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/error_handling/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/error_handling/decorators.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/error_handling/error_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/error_handling/exceptions.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/error_handling/handlers.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/fs_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>98</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'find_files' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/knowledge/framework_knowledge_base.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'FrameworkRule' has only data, no behavior</td>
        </tr>
        <tr>
            <td>52</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'FrameworkKnowledge' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/knowledge/rule_engine.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'RuleExecutionContext' has only data, no behavior</td>
        </tr>
        <tr>
            <td>218</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>224</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>230</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Call' should use snake_case naming</td>
        </tr>
        <tr>
            <td>236</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Assign' should use snake_case naming</td>
        </tr>
        <tr>
            <td>285</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_check_function_rule' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/logging.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/logging/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/logging/contextual_logger.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/logging/correlation.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/logging/setup.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/logging/structured_logger.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>18</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'LogLevel' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/models/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/models/directory_metrics.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/models/file_metrics.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>90</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '__init__' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>90</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '__init__' has 33 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/models/progress_tracker.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>31</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>165</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>165</td>
            <td>B110</td>
            <td class="severity-low">LOW</td>
            <td>Try, Except, Pass detected.</td>
        </tr>
    </table>
    <h3>core/models/project_metrics.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">error</td>
            <td>God class detected: 'ProjectMetrics' has 24 methods</td>
        </tr>
        <tr>
            <td>386</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'from_dict' has complexity 17, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/progress.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ProgressState' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/simple_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>31</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'simple_analyze_project' has complexity 14, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/trend_analysis/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/trend_analysis/trend_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>223</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_generate_trend_summary' has complexity 18, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/trend_analysis/trend_storage.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/trend_analysis/trend_visualizer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>33</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'visualize_trends' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>145</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_create_trend_chart' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/async_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/config_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/dict_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/error_handling.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/error_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/file_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>54</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>92</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'list_files' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/fs_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>117</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>168</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'list_files' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/gitignore_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/preset_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/report_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/utils/tool_utils.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/auto_fix.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'FixResult' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/cache.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>11</td>
            <td>B403</td>
            <td class="severity-low">LOW</td>
            <td>Consider possible security implications associated with pickle module.</td>
        </tr>
        <tr>
            <td>100</td>
            <td>B301</td>
            <td class="severity-medium">MEDIUM</td>
            <td>Pickle and modules that wrap it can be unsafe when used to deserialize untrusted data, possible security issue.</td>
        </tr>
    </table>
    <h3>core/vcs/config.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>88</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'from_dict' has complexity 19, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>246</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>251</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/dependency_tracker.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/engine.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>47</td>
            <td></td>
            <td class="">error</td>
            <td>God class detected: 'VibeCheckEngine' has 55 methods</td>
        </tr>
        <tr>
            <td>116</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'initialize' has complexity 16, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>211</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'stop' has complexity 14, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>818</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_analysis_request' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/incremental_analysis.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>23</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ChangeType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/integration/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/integration/ecosystem_integrator.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/integration/integration_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>36</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'perform_comprehensive_analysis' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>36</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'perform_comprehensive_analysis' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/integration/meta_analyzer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>223</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_generate_recommendations' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/integration/unified_reporter.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>58</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_unified_report' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/memory_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>31</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'MemoryPressure' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/models.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>16</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'EngineMode' has only data, no behavior</td>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'RuleCategory' has only data, no behavior</td>
        </tr>
        <tr>
            <td>32</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'IssueSeverity' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/performance.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>23</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'PerformanceMetric' has only data, no behavior</td>
        </tr>
        <tr>
            <td>34</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AnalysisPerformance' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/plugins/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/plugins/plugin_interface.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/plugins/plugin_loader.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/plugins/plugin_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/plugins/plugin_registry.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">error</td>
            <td>God class detected: 'PluginRegistry' has 23 methods</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/registry.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '__init__' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>101</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_issue' has 8 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/advanced_python_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/complexity_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>34</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>47</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>58</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_If' should use snake_case naming</td>
        </tr>
        <tr>
            <td>65</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_While' should use snake_case naming</td>
        </tr>
        <tr>
            <td>69</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_For' should use snake_case naming</td>
        </tr>
        <tr>
            <td>73</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ExceptHandler' should use snake_case naming</td>
        </tr>
        <tr>
            <td>77</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_With' should use snake_case naming</td>
        </tr>
        <tr>
            <td>81</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Assert' should use snake_case naming</td>
        </tr>
        <tr>
            <td>85</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_BoolOp' should use snake_case naming</td>
        </tr>
        <tr>
            <td>90</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Compare' should use snake_case naming</td>
        </tr>
        <tr>
            <td>125</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>150</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>198</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>202</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>222</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_If' should use snake_case naming</td>
        </tr>
        <tr>
            <td>225</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_While' should use snake_case naming</td>
        </tr>
        <tr>
            <td>228</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_For' should use snake_case naming</td>
        </tr>
        <tr>
            <td>231</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_With' should use snake_case naming</td>
        </tr>
        <tr>
            <td>234</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Try' should use snake_case naming</td>
        </tr>
        <tr>
            <td>237</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ExceptHandler' should use snake_case naming</td>
        </tr>
        <tr>
            <td>278</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>302</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>330</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/documentation_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>44</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>56</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>69</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>91</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has complexity 14, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>96</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>102</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>105</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>283</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>318</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/framework_rules/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/framework_rules/django_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>212</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_check_setting_security' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/framework_rules/fastapi_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/framework_rules/flask_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>79</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>133</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>245</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/framework_rules/framework_detector.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'FrameworkType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>148</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_detect_from_ast' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/import_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>39</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Import' should use snake_case naming</td>
        </tr>
        <tr>
            <td>44</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>53</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Name' should use snake_case naming</td>
        </tr>
        <tr>
            <td>56</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Attribute' should use snake_case naming</td>
        </tr>
        <tr>
            <td>121</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Import' should use snake_case naming</td>
        </tr>
        <tr>
            <td>126</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>206</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>242</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>287</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>304</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Import' should use snake_case naming</td>
        </tr>
        <tr>
            <td>345</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Import' should use snake_case naming</td>
        </tr>
        <tr>
            <td>348</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ImportFrom' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/performance_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>157</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_check_function_calls' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/rule_loader.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>355</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'load_framework_rules' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/security_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>73</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>78</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_BinOp' should use snake_case naming</td>
        </tr>
        <tr>
            <td>92</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Call' should use snake_case naming</td>
        </tr>
        <tr>
            <td>150</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Call' should use snake_case naming</td>
        </tr>
        <tr>
            <td>207</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Call' should use snake_case naming</td>
        </tr>
        <tr>
            <td>270</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Call' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/style_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>151</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>163</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>175</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Name' should use snake_case naming</td>
        </tr>
        <tr>
            <td>260</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/rules/type_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>33</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>69</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>101</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>140</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>177</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>182</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>212</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>266</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>276</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>323</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>337</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>381</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'analyze' has complexity 16, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>386</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>402</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>core/vcs/type_checking.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>9</td>
            <td>B404</td>
            <td class="severity-low">LOW</td>
            <td>Consider possible security implications associated with the subprocess module.</td>
        </tr>
        <tr>
            <td>250</td>
            <td>B603</td>
            <td class="severity-low">LOW</td>
            <td>subprocess call - check for execution of untrusted input.</td>
        </tr>
        <tr>
            <td>266</td>
            <td>B603</td>
            <td class="severity-low">LOW</td>
            <td>subprocess call - check for execution of untrusted input.</td>
        </tr>
    </table>
    <h3>core/version.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/api/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/api/graphql.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">error</td>
            <td>God class detected: 'GraphQLServer' has 22 methods</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/api/models.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>15</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'APIMethod' has only data, no behavior</td>
        </tr>
        <tr>
            <td>24</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'APIStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>31</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'WebSocketMessageType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/api/rest.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>23</td>
            <td></td>
            <td class="">error</td>
            <td>God class detected: 'RestAPIServer' has 23 methods</td>
        </tr>
        <tr>
            <td>175</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_register_endpoint' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>220</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/api/unified.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/api/websocket.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>124</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/cicd/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/cicd/azure_devops.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/cicd/github_actions.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/cicd/gitlab_ci.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/cicd/jenkins.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>8</td>
            <td>B405</td>
            <td class="severity-low">LOW</td>
            <td>Using xml.etree.ElementTree to parse untrusted XML data is known to be vulnerable to XML attacks. Replace xml.etree.ElementTree with the equivalent defusedxml package, or make sure defusedxml.defuse_stdlib() is called.</td>
        </tr>
    </table>
    <h3>enterprise/cicd/manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/cicd/models.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>15</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'CICDPlatform' has only data, no behavior</td>
        </tr>
        <tr>
            <td>25</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'PipelineStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'BuildResult' has only data, no behavior</td>
        </tr>
        <tr>
            <td>43</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'QualityGateStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/collaboration.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'UserRole' has only data, no behavior</td>
        </tr>
        <tr>
            <td>29</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'TeamPermission' has only data, no behavior</td>
        </tr>
        <tr>
            <td>361</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'add_team_member' has 7 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>628</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'save_configuration' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/collaboration/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/collaboration/analysis.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AnalysisStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>31</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AnalysisPriority' has only data, no behavior</td>
        </tr>
        <tr>
            <td>247</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_analysis_request' has 11 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>332</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'add_comment' has 8 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/collaboration/shared_config.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>100</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'save_configuration' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/collaboration/team_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'UserRole' has only data, no behavior</td>
        </tr>
        <tr>
            <td>29</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'TeamPermission' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/dashboard/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/dashboard/components.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>83</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'get_data' has complexity 15, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/dashboard/models.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>15</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'DashboardTheme' has only data, no behavior</td>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ChartType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>34</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'WidgetSize' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/dashboard/static_assets.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/dashboard/templates.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/dashboard/web_server.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>367</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_get_metrics_card_data' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/integration.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/monitoring.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/monitoring/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/monitoring/alerting.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>533</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/monitoring/dashboards.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>170</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_get_system_health_data' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>285</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/monitoring/models.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>15</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AlertSeverity' has only data, no behavior</td>
        </tr>
        <tr>
            <td>23</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AlertStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>31</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'QualityGateStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>40</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'MetricType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>48</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ThresholdOperator' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/monitoring/monitoring.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>53</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'record_metric' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>211</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/monitoring/quality_gates.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>378</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_gate' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>224</td>
            <td>B307</td>
            <td class="severity-medium">MEDIUM</td>
            <td>Use of possibly insecure function - consider using safer ast.literal_eval.</td>
        </tr>
    </table>
    <h3>enterprise/performance/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/performance/cache_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'CacheStrategy' has only data, no behavior</td>
        </tr>
        <tr>
            <td>400</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>239</td>
            <td>B324</td>
            <td class="severity-high">HIGH</td>
            <td>Use of weak MD5 hash for security. Consider usedforsecurity=False</td>
        </tr>
    </table>
    <h3>enterprise/performance/distributed_processor.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'TaskStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'NodeStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>447</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/performance/load_balancer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>17</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'LoadBalancingStrategy' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/performance/performance_optimizer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>21</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'OptimizationProfile' has only data, no behavior</td>
        </tr>
        <tr>
            <td>221</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_auto_optimize' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>359</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>366</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/performance/scalability_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/reporting/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/reporting/customization.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/reporting/engine.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ReportFormat' has only data, no behavior</td>
        </tr>
        <tr>
            <td>32</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ReportType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/reporting/executive.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'RiskLevel' has only data, no behavior</td>
        </tr>
        <tr>
            <td>28</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'BusinessImpact' has only data, no behavior</td>
        </tr>
        <tr>
            <td>255</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_assess_risks' has complexity 15, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/reporting/formats.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/reporting/templates.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'TemplateType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/security/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/security/access_control.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'Permission' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/security/audit_trail.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>20</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AuditEventType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>43</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AuditSeverity' has only data, no behavior</td>
        </tr>
        <tr>
            <td>138</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'search_events' has complexity 17, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>138</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'search_events' has 9 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>295</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'log_user_event' has 12 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>347</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'log_system_event' has 7 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>385</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'log_api_call' has 10 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>415</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'log_analysis_event' has 7 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/security/compliance_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ComplianceFramework' has only data, no behavior</td>
        </tr>
        <tr>
            <td>29</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ComplianceStatus' has only data, no behavior</td>
        </tr>
        <tr>
            <td>205</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'update_control_status' has 6 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/security/encryption_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>18</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'EncryptionAlgorithm' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>enterprise/security/security_monitor.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>19</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'ThreatLevel' has only data, no behavior</td>
        </tr>
        <tr>
            <td>27</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AlertType' has only data, no behavior</td>
        </tr>
        <tr>
            <td>156</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_alert' has 9 arguments, consider reducing (max: 5)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>plugins/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>plugins/base_plugin.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>plugins/manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>143</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_scan_module_for_plugins' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>167</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>180</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>167</td>
            <td>B110</td>
            <td class="severity-low">LOW</td>
            <td>Try, Except, Pass detected.</td>
        </tr>
        <tr>
            <td>180</td>
            <td>B110</td>
            <td class="severity-low">LOW</td>
            <td>Try, Except, Pass detected.</td>
        </tr>
    </table>
    <h3>plugins/plugin_base.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>plugins/plugin_interface.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>plugins/plugin_registry.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/custom_rules/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/custom_rules/python_rules.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>85</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>153</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_check_resource_management' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/bandit_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/base_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/complexity_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'parse' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/custom_rules_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/doc_analyzer_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'parse' has complexity 27, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/mypy_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/parser_registry.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/pyflakes_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/pylint_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/parsers/ruff_parser.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/bandit_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'run' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/base_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>184</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>10</td>
            <td>B404</td>
            <td class="severity-low">LOW</td>
            <td>Consider possible security implications associated with the subprocess module.</td>
        </tr>
        <tr>
            <td>184</td>
            <td>B110</td>
            <td class="severity-low">LOW</td>
            <td>Try, Except, Pass detected.</td>
        </tr>
    </table>
    <h3>tools/runners/complexity_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>75</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>84</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>104</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_If' should use snake_case naming</td>
        </tr>
        <tr>
            <td>116</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_For' should use snake_case naming</td>
        </tr>
        <tr>
            <td>128</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFor' should use snake_case naming</td>
        </tr>
        <tr>
            <td>140</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_While' should use snake_case naming</td>
        </tr>
        <tr>
            <td>152</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Try' should use snake_case naming</td>
        </tr>
        <tr>
            <td>169</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_With' should use snake_case naming</td>
        </tr>
        <tr>
            <td>180</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncWith' should use snake_case naming</td>
        </tr>
        <tr>
            <td>191</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_BoolOp' should use snake_case naming</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/custom_rules_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/doc_analyzer_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>36</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_Module' should use snake_case naming</td>
        </tr>
        <tr>
            <td>62</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_ClassDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>100</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_FunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>133</td>
            <td></td>
            <td class="">info</td>
            <td>Function 'visit_AsyncFunctionDef' should use snake_case naming</td>
        </tr>
        <tr>
            <td>204</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'run' has complexity 17, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/mypy_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>208</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/pyflakes_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/pylint_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>tools/runners/ruff_runner.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>188</td>
            <td></td>
            <td class="">error</td>
            <td>Empty except block silently ignores exceptions</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>188</td>
            <td>B110</td>
            <td class="severity-low">LOW</td>
            <td>Try, Except, Pass detected.</td>
        </tr>
    </table>
    <h3>tools/runners/tool_registry.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/cli/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/cli/commands.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/cli/formatter.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/gui/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/gui/app.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/gui/main_window.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">error</td>
            <td>God class detected: 'VibeCheckGUI' has 21 methods</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/gui/simple_gui.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>15</td>
            <td>B404</td>
            <td class="severity-low">LOW</td>
            <td>Consider possible security implications associated with the subprocess module.</td>
        </tr>
        <tr>
            <td>332</td>
            <td>B603</td>
            <td class="severity-low">LOW</td>
            <td>subprocess call - check for execution of untrusted input.</td>
        </tr>
    </table>
    <h3>ui/gui/themes.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/reporting/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/reporting/custom_report_generator.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>119</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_create_json_report' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/reporting/formatters.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>97</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'format_analysis_results' has complexity 12, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/reporting/generators.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>118</td>
            <td></td>
            <td class="">warning</td>
            <td>Function '_generate_top_issues' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/reporting/markdown.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/reporting/report_generator.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/reporting/templates.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/tui/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/tui/app.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>43</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'handle_input' has complexity 16, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
        <tr>
            <td>102</td>
            <td>B605</td>
            <td class="severity-high">HIGH</td>
            <td>Starting a process with a shell, possible injection detected, security issue.</td>
        </tr>
        <tr>
            <td>208</td>
            <td>B605</td>
            <td class="severity-high">HIGH</td>
            <td>Starting a process with a shell, possible injection detected, security issue.</td>
        </tr>
    </table>
    <h3>ui/tui/components.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/tui/config_components.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/tui/header_footer.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/tui/menu_components.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/tui/progress_components.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/tui/results_components.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>141</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'render_issues_view' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/tui/state_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>22</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'TUIScreen' has only data, no behavior</td>
        </tr>
        <tr>
            <td>33</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AnalysisState' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/visualization/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/visualization/charts.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/visualization/exporters.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/visualization/generators.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/visualization/interactive_charts.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>703</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_import_dependency_graph' has complexity 17, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/visualization/visualization_generator.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/web/__init__.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/web/app.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>30</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'create_app' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/web/components.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>203</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'render_issues' has complexity 13, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>487</td>
            <td></td>
            <td class="">warning</td>
            <td>Function 'render_dependency_graph' has complexity 11, consider refactoring (max: 10)</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/web/run_web_ui.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
    <h3>ui/web/state_manager.py</h3>
    <table>
        <tr>
            <th>Line</th>
            <th>Code</th>
            <th>Severity</th>
            <th>Message</th>
        </tr>
        <tr>
            <td>26</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'AnalysisState' has only data, no behavior</td>
        </tr>
        <tr>
            <td>35</td>
            <td></td>
            <td class="">info</td>
            <td>Potential anemic model: 'UIState' has only data, no behavior</td>
        </tr>
        <tr>
            <td>0</td>
            <td>mypy.error</td>
            <td class="severity-low">LOW</td>
            <td></td>
        </tr>
    </table>
</body>
</html>
